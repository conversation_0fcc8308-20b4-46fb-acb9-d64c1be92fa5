# --- Core Web Framework (for Cloud Run API) ---
fastapi                    # Modern, high-performance API framework
uvicorn[standard]          # Lightning-fast ASGI server for FastAPI

# --- Asynchronous Task Queue (for GKE Workers) ---
celery[redis]              # Distributed task queue for background jobs
google-cloud-pubsub        # Recommended, scalable message broker for Celery in production

# --- Google Cloud & Vertex AI ---
google-cloud-aiplatform           # SDK for Vertex AI (Gemini models)
google-cloud-secret-manager       # For securely accessing secrets in production
google-cloud-storage              # If using GCS for file processing
google-cloud-tasks                # For Cloud Tasks integration

# --- Data Persistence & Caching ---
# For Cloud SQL (PostgreSQL)
psycopg2-binary==2.9.9         # PostgreSQL adapter for Python
asyncpg                        # Async PostgreSQL adapter for Python
sqlalchemy==2.0.41            # Python SQL toolkit and Object Relational Mapper
cloud-sql-python-connector    # Google Cloud SQL Python Connector

# For Vector DB (Retrieval-Augmented Generation)
pinecone-client                   # Client for Pinecone, a leading managed vector database

# For LangGraph Checkpointer & Application Caching
redis                             # In-memory data store for caching and checkpointers
langchain-community               # Contains the Redis checkpointer integration

# --- Supabase Database ---
supabase                          # Python client for Supabase (PostgreSQL)

# --- LangChain & LangGraph ---
langchain
langgraph
langchain-google-vertexai         # For native integration with Gemini models
langchain-google-genai            # Alternative Gemini integration

# --- External Service APIs ---
hubspot-api-client                # Or the specific HubSpot library you prefer
google-api-python-client          # For Gmail API
google-auth-oauthlib              # For Gmail authentication

# --- Utilities ---
pydantic                          # Core of FastAPI, used for data validation and settings
pydantic-settings                 # For loading settings from environment variables
python-dotenv                     # For loading .env.local during local development

# --- Additional Production Dependencies ---
gunicorn                          # Production WSGI server (optional, uvicorn is preferred)
aiofiles                          # Async file operations
httpx                             # Modern async HTTP client
tenacity                          # Retry logic for external API calls
"""
FastAPI application entrypoint for the Email Conversation Agent v4.

This module handles HTTP requests and serves as the main API interface
for the email conversation agent, including Gmail webhooks and AI responses.
"""

import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import FastAPI, HTTPException, Request, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from src.config import settings, load_production_secrets
from src.model_router import model_router
from src.agents.state import AgentState, TaskRequest, TaskResponse
from src.services.database_client import DatabaseClient
from src.async_tasks.tasks import process_background_task

# Configure logging
logger = logging.getLogger(__name__)

# Load production secrets if in production environment
if settings.is_production:
    production_secrets = load_production_secrets()
    logger.info("Production secrets loaded from Google Secret Manager")

# --- FastAPI App Initialization ---
app = FastAPI(
    title="Email Conversation Agent v4",
    description="AI-powered email conversation agent with LangGraph workflows and Gemini 2.5 Flash integration",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.is_development else ["https://your-domain.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database client
db_client = DatabaseClient()


# --- Request/Response Models ---

class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    agent: str
    version: str
    timestamp: str
    environment: str
    services: Dict[str, str]


class AIRequest(BaseModel):
    """General AI service request model."""
    prompt: str
    context: Optional[str] = None
    task_type: str = "general"
    priority: str = "quality"  # "quality", "speed", "cost"
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None


class AIResponse(BaseModel):
    """AI service response model."""
    response: str
    success: bool
    model_used: str
    processing_time_ms: int
    error: Optional[str] = None


class WebhookPayload(BaseModel):
    """Generic webhook payload model."""
    event_type: str
    data: Dict[str, Any]
    timestamp: Optional[str] = None


# --- Event Handlers ---

@app.on_event("startup")
async def startup_event():
    """Initialize services on application startup."""
    logger.info("Starting Email Conversation Agent v4 application...")
    
    try:
        # Initialize database connection
        await db_client.initialize()
        logger.info("Database client initialized")
        
        # Model router ready (warming disabled for faster startup)
        logger.info("Model router ready")
        
        logger.info("Email Conversation Agent v4 startup completed successfully")
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on application shutdown."""
    logger.info("Shutting down Agent Template application...")
    
    try:
        # Clean up database connections
        await db_client.close()
        
        # Clear model cache
        model_router.clear_cache()
        
        logger.info("Agent Template shutdown completed")
        
    except Exception as e:
        logger.error(f"Shutdown error: {e}")


# --- API Endpoints ---

@app.get("/", response_model=HealthResponse, summary="Health Check")
async def health_check():
    """Comprehensive health check endpoint."""
    try:
        # Check database connectivity
        db_status = await db_client.health_check()
        
        # Check model availability
        try:
            test_model = model_router.get_fast_model()
            model_status = "operational"
        except Exception:
            model_status = "degraded"
        
        services = {
            "database": "operational" if db_status else "degraded",
            "model_router": model_status,
            "supabase": "operational"
        }
        
        overall_status = "healthy" if all(s == "operational" for s in services.values()) else "degraded"
        
        return HealthResponse(
            status=overall_status,
            agent="Email Conversation Agent v4",
            version="1.0.0",
            timestamp=datetime.utcnow().isoformat(),
            environment=settings.environment,
            services=services
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            agent="Email Conversation Agent v4",
            version="1.0.0",
            timestamp=datetime.utcnow().isoformat(),
            environment=settings.environment,
            services={"error": str(e)}
        )


@app.post("/v1/webhook", status_code=status.HTTP_200_OK, summary="Generic Webhook Handler")
async def webhook_handler(
    payload: WebhookPayload,
    background_tasks: BackgroundTasks,
    request: Request
):
    """
    Generic webhook handler for external integrations.
    
    Processes incoming webhooks and queues background tasks as needed.
    """
    logger.info(f"Received webhook: {payload.event_type}")
    
    try:
        # Log webhook details
        logger.info(f"Webhook payload: {payload.dict()}")
        
        # Queue background processing if needed
        if payload.event_type in ["email_update", "external_trigger"]:
            background_tasks.add_task(
                process_background_task,
                payload.event_type,
                payload.data
            )
            logger.info(f"Queued background task for {payload.event_type}")
        
        return {"status": "received", "event_type": payload.event_type}
        
    except Exception as e:
        logger.error(f"Webhook processing failed: {e}")
        return {"status": "error", "error": str(e)}


@app.post("/v1/gmail-webhook", status_code=status.HTTP_200_OK, summary="Gmail Pub/Sub Webhook")
async def gmail_webhook_handler(
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Handle Gmail Pub/Sub notifications for new emails.
    
    This endpoint processes incoming Gmail notifications and triggers
    the email conversation workflow.
    """
    try:
        # Get the Pub/Sub message
        envelope = await request.json()
        
        # Decode the message data
        import base64
        import json
        
        if "message" in envelope:
            message_data = base64.b64decode(envelope["message"]["data"]).decode("utf-8")
            email_notification = json.loads(message_data)
            
            logger.info(f"Received Gmail notification: {email_notification}")
            
            # Queue background email processing with new workflow
            background_tasks.add_task(
                process_email_background_task,
                email_notification
            )
            
            return {"status": "received", "message_id": envelope["message"].get("messageId")}
        else:
            logger.warning("No message data in Gmail webhook")
            return {"status": "no_message"}
            
    except Exception as e:
        logger.error(f"Gmail webhook processing failed: {e}")
        # Return 200 to avoid Pub/Sub retries
        return {"status": "error", "error": str(e)}


async def process_email_background_task(email_notification: dict):
    """
    Process Gmail notification in the background using the email agent workflow.
    
    This function integrates with the new email-specific LangGraph workflow.
    """
    try:
        logger.info(f"Processing email notification: {email_notification}")
        
        # Extract email data from Gmail API using the notification
        history_id = email_notification.get("historyId")
        email_address = email_notification.get("emailAddress", "<EMAIL>")
        
        if not history_id:
            logger.error("No historyId in notification")
            return
        
        # Initialize Gmail client and database client
        from src.services.gmail_client import GmailClient
        from src.services.database_client import DatabaseClient
        
        gmail_client = GmailClient(subject_email=email_address)
        database_client = DatabaseClient()
        
        # Initialize services
        if not await gmail_client.authenticate():
            logger.error("Failed to authenticate with Gmail API")
            return
        
        await database_client.initialize()
        
        # Get the last processed history ID from database
        last_history_id = await database_client.get_last_history_id(email_address)
        logger.info(f"Last processed history ID for {email_address}: {last_history_id}")
        
        # Get new messages from the history using proper history API
        new_messages = await gmail_client.get_messages_from_history(
            history_id=history_id,
            start_history_id=last_history_id,
            user_id="me"
        )
        
        # Store the current history ID as the new last processed ID
        if new_messages:
            await database_client.store_history_id(email_address, history_id)
        
        if not new_messages:
            logger.info("No new messages found in history")
            return
        
        # SAFETY CHECK: Filter messages before processing
        safe_messages = []
        for msg in new_messages:
            from_addr = msg.get("from", "").lower()
            subject = msg.get("subject", "").lower()
            
            # Skip if from our own domain or common automated sources
            if any(domain in from_addr for domain in ["@tkcgroup.co", "noreply", "no-reply", "bounce", "mailer-daemon"]):
                logger.info(f"Skipping automated email from: {from_addr}")
                continue
                
            # Skip if subject contains automated indicators  
            if any(keyword in subject for keyword in ["unsubscribe", "bounce", "delivery failure", "auto-reply"]):
                logger.info(f"Skipping automated email with subject: {subject}")
                continue
                
            safe_messages.append(msg)
        
        if not safe_messages:
            logger.info("No safe messages to process after filtering")
            return
            
        # Process the first safe message
        gmail_message = safe_messages[0]
        logger.info(f"Processing safe message from: {gmail_message.get('from')} - Subject: {gmail_message.get('subject')}")
        
        # Convert Gmail message to email_data format expected by workflow
        email_data = {
            "id": gmail_message["id"],
            "thread_id": gmail_message["thread_id"],
            "from_address": gmail_message["from"],
            "to_address": gmail_message["to"],
            "subject": gmail_message["subject"],
            "body": gmail_message["body"],
            "received_at": datetime.utcnow().isoformat(),
            "headers": gmail_message["headers"],
            "snippet": gmail_message["snippet"],
            "message_id": gmail_message["message_id"],
            "internal_date": gmail_message["internal_date"]
        }
        
        # Import and use the email workflow
        from src.agents.email_agent.graph import process_email_workflow
        
        # Process email through workflow
        result = await process_email_workflow(email_data)
        
        logger.info(f"Email processing completed: {result}")
        
        # Store analytics data in Supabase
        try:
            from src.services.database_client import database_client
            
            analytics_data = {
                "email_id": result.get("email_id"),
                "message_id": gmail_message.get("message_id"),
                "thread_id": gmail_message.get("thread_id"),
                "from_address": gmail_message["from"],
                "to_address": gmail_message["to"],
                "subject": gmail_message["subject"],
                "received_at": gmail_message.get("internal_date"),
                "classification": result.get("classification"),
                "reply_warranted": result.get("reply_warranted", False),
                "reply_generated": result.get("reply_generated", False),
                "reply_type": result.get("reply_type"),
                "processing_time_ms": result.get("processing_time_ms"),
                "confidence_score": result.get("confidence_score"),
                "safety_checks_passed": result.get("safety_checks_passed", True),
                "processing_notes": result.get("processing_notes", []),
                "email_snippet": gmail_message.get("snippet"),
                "raw_headers": gmail_message.get("headers", {})
            }
            
            response = database_client.supabase.table("email_analytics").insert(analytics_data).execute()
            logger.info("Email processing result stored successfully in Supabase")
            
        except Exception as db_error:
            logger.error(f"Failed to store analytics data: {db_error}")
            # Don't fail the whole process if analytics storage fails
        
        logger.info("Email processing result stored successfully")
        
    except Exception as e:
        logger.error(f"Background email processing failed: {e}")


@app.post("/v1/test-vertex", summary="Test Vertex AI Connection")
async def test_vertex_ai():
    """
    Simple test endpoint to verify Vertex AI authentication.
    """
    try:
        from langchain_google_vertexai import ChatVertexAI
        
        # Create simple Vertex AI model
        model = ChatVertexAI(
            model_name="gemini-2.5-flash",
            project=settings.gcp_project_id,
            location=settings.gcp_region,
            temperature=0.1,
            max_output_tokens=100
        )
        
        # Simple test prompt
        response = await model.ainvoke("Hello, this is a test. Respond with 'Test successful'.")
        
        return {
            "success": True,
            "message": "Vertex AI test completed",
            "response": response.content
        }
        
    except Exception as e:
        logger.error(f"Vertex AI test failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }


@app.post("/v1/test-email-workflow", summary="Test Email Workflow")
async def test_email_workflow():
    """
    Test endpoint for the email agent workflow.
    
    This allows testing the email processing without Gmail integration.
    """
    try:
        # Create test email data
        test_email_data = {
            "id": "test_email_001",
            "thread_id": "test_thread_001", 
            "from_address": "<EMAIL>",
            "to_address": "<EMAIL>",
            "subject": "Inquiry about AI automation services",
            "body": "Hi, I'm interested in learning more about your AI automation services for our business. We're looking to optimize our customer service processes. Could you provide more information?",
            "received_at": datetime.utcnow().isoformat(),
            "headers": {"message-id": "<EMAIL>"},
            "snippet": "Inquiry about AI automation services",
            "is_html": False
        }
        
        # Import and use the email workflow
        from src.agents.email_agent.graph import process_email_workflow
        
        # Process through workflow
        result = await process_email_workflow(test_email_data)
        
        return {
            "success": True,
            "message": "Email workflow test completed",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Email workflow test failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }


@app.post("/v1/process-task", response_model=TaskResponse, summary="Process Agent Task")
async def process_agent_task(task_request: TaskRequest):
    """
    Process a task using the LangGraph agent workflow.
    
    This endpoint demonstrates how to use the agent graph for task processing.
    """
    start_time = time.time()
    logger.info(f"Processing agent task: {task_request.task_type}")
    
    try:
        # Import here to avoid circular imports
        from src.agents.graph import execute_agent_workflow
        
        # Execute the agent workflow
        result = await execute_agent_workflow(task_request)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.info(f"Task {task_request.task_id} completed in {processing_time}ms")
        
        return result
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"Task processing failed: {e}")
        
        return TaskResponse(
            task_id=task_request.task_id,
            success=False,
            results={},
            processing_time_ms=processing_time,
            error=str(e)
        )


@app.post("/v1/ai/generate", response_model=AIResponse, summary="AI Generation Service")
async def ai_generation_service(request: AIRequest):
    """
    Centralized AI service endpoint with intelligent model routing.
    
    This endpoint provides AI capabilities with automatic model selection
    based on task type and performance requirements.
    """
    start_time = time.time()
    logger.info(f"AI generation request: {request.task_type}")
    
    try:
        # Get appropriate model for the task
        model = model_router.get_model_for_task(
            task_type=request.task_type,
            input_length=len(request.prompt) if request.prompt else 0,
            priority=request.priority
        )
        
        # Configure model parameters
        temperature = request.temperature or settings.gemini_temperature
        max_tokens = request.max_tokens or settings.gemini_max_tokens
        
        # Prepare prompt
        if request.context:
            full_prompt = f"Context: {request.context}\n\nPrompt: {request.prompt}"
        else:
            full_prompt = request.prompt
        
        # Generate response
        response = await model.ainvoke(full_prompt)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.info(f"AI generation completed in {processing_time}ms")
        
        return AIResponse(
            response=response.content,
            success=True,
            model_used=model.model_name if hasattr(model, 'model_name') else "gemini",
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"AI generation failed: {e}")
        
        return AIResponse(
            response="",
            success=False,
            model_used="error",
            processing_time_ms=processing_time,
            error=str(e)
        )


@app.get("/v1/status", summary="Detailed Status")
async def get_detailed_status():
    """Get detailed agent status and metrics."""
    try:
        # Get model router statistics
        model_cache_size = len(model_router._model_cache)
        
        # Get database statistics
        db_stats = await db_client.get_statistics()
        
        return {
            "agent": "Agent Template",
            "status": "operational",
            "environment": settings.environment,
            "model_cache_size": model_cache_size,
            "database_stats": db_stats,
            "configuration": {
                "gcp_project": settings.gcp_project_id,
                "gcp_region": settings.gcp_region,
                "default_model": settings.gemini_model
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return {
            "agent": "Agent Template",
            "status": "degraded",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


# --- Error Handlers ---

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with detailed logging."""
    logger.error(f"HTTP {exc.status_code}: {exc.detail} - {request.url}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions with detailed logging."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "status_code": 500}
    )


# --- Application Entry Point ---
if __name__ == "__main__":
    import uvicorn
    import os
    
    # Use PORT environment variable (required for Cloud Run)
    port = int(os.environ.get("PORT", 8080))
    
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=port,
        reload=settings.is_development,
        log_level=settings.log_level.lower()
    )
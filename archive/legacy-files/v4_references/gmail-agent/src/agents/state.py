"""
Agent state definitions for the template agent.

This module defines the Pydantic models that represent the agent's state
throughout the LangGraph workflow execution.
"""

from typing import Optional, Dict, Any, List, Literal
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class TaskType(str, Enum):
    """Supported task types for the agent."""
    
    GENERAL = "general"
    ANALYSIS = "analysis"
    GENERATION = "generation"
    CONVERSATION = "conversation"


class TaskStatus(str, Enum):
    """Task execution status."""
    
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Priority(str, Enum):
    """Task priority levels."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TaskRequest(BaseModel):
    """Incoming task request."""
    
    task_id: str
    task_type: TaskType
    input_data: Dict[str, Any]
    priority: Priority = Priority.MEDIUM
    requested_by: Optional[str] = None
    timeout_seconds: Optional[int] = 300  # 5 minute default


class TaskResponse(BaseModel):
    """Task response."""
    
    task_id: str
    success: bool
    task_type: TaskType
    results: Dict[str, Any]
    processing_time_ms: int
    error: Optional[str] = None
    recommendations: List[str] = []
    next_steps: List[str] = []


class IntentClassification(BaseModel):
    """Intent classification result."""
    
    intent: str
    confidence: float
    reasoning: str
    requires_action: bool
    suggested_actions: List[str] = []


class AnalysisResult(BaseModel):
    """Content analysis result."""
    
    summary: str
    key_points: List[str] = []
    sentiment: str = "neutral"
    entities: List[Dict[str, Any]] = []
    confidence: float = 0.0
    metadata: Dict[str, Any] = {}


class GenerationResult(BaseModel):
    """Content generation result."""
    
    generated_content: str
    confidence: float
    model_used: str
    metadata: Dict[str, Any] = {}


# Email-specific state models for conversation agent

class EmailContent(BaseModel):
    """Email content and metadata."""
    
    id: str
    thread_id: str
    from_address: str
    to_address: str
    subject: str
    body: str
    received_at: str
    headers: Dict[str, str] = {}
    snippet: Optional[str] = None
    is_html: bool = False


class TriageResult(BaseModel):
    """Email triage and classification result."""
    
    classification: Literal[
        "customer_inquiry", 
        "customer_reply", 
        "spam_or_marketing", 
        "internal_email",
        "automated_notification",
        "old_email",
        "reply_not_needed"
    ]
    is_reply_warranted: bool
    confidence: float
    reasoning: str
    safety_checks: Dict[str, bool] = {}  # 24h cutoff, sender validation, etc.


class PersonaConfig(BaseModel):
    """BDR persona configuration for email responses."""
    
    name: str = "TKC Group BDR"
    role: str = "Business Development Representative"
    company: str = "TKC Group"
    tone: str = "professional and helpful"
    signature: str = ""
    expertise_areas: List[str] = ["AI automation", "business process optimization"]


class EmailReply(BaseModel):
    """Generated email reply content."""
    
    subject: str
    body: str
    reply_type: Literal["direct_answer", "information_request", "meeting_scheduling", "follow_up"]
    confidence: float
    reasoning: str
    context_used: List[str] = []  # Sources of context used in reply


class EmailProcessingResult(BaseModel):
    """Complete email processing result."""
    
    email_content: EmailContent
    triage_result: TriageResult
    reply_generated: Optional[EmailReply] = None
    processing_time_ms: int
    success: bool
    error: Optional[str] = None


class EmailAgentState(BaseModel):
    """
    Email-specific agent state for email conversation workflows.
    
    Extends the base agent state with email-specific fields.
    """
    
    # Email content
    email_content: Optional[EmailContent] = None
    
    # Processing results
    triage_result: Optional[TriageResult] = None
    reply_generated: Optional[EmailReply] = None
    
    # Context and persona
    persona_config: PersonaConfig = Field(default_factory=PersonaConfig)
    conversation_context: List[Dict[str, Any]] = []  # Past emails in thread
    
    # Processing metadata  
    processing_start_time: Optional[datetime] = None
    gmail_history_id: Optional[str] = None
    
    # Safety and filtering
    safety_checks_passed: bool = False
    filtering_notes: List[str] = []
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True


class AgentState(BaseModel):
    """
    Main agent state that persists throughout the workflow.
    
    This state is passed between all nodes in the LangGraph workflow.
    """
    
    # Core task information
    task_id: str
    task_type: TaskType
    input_data: Dict[str, Any] = {}
    
    # Processing state
    status: TaskStatus = TaskStatus.PENDING
    current_step: Optional[str] = None
    steps_completed: List[str] = []
    
    # Analysis results
    intent_classification: Optional[IntentClassification] = None
    analysis_result: Optional[AnalysisResult] = None
    generation_result: Optional[GenerationResult] = None
    
    # Workflow control
    should_continue: bool = True
    next_action: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    # Context and metadata
    context: Dict[str, Any] = {}
    
    # Metadata and tracking
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    processing_time_ms: int = 0
    requested_by: Optional[str] = None
    
    # Error handling
    errors: List[str] = []
    warnings: List[str] = []
    
    # Results
    final_result: Optional[Dict[str, Any]] = None
    final_status: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
    
    def add_error(self, error: str):
        """Add an error to the state."""
        self.errors.append(error)
        self.updated_at = datetime.utcnow()
    
    def add_warning(self, warning: str):
        """Add a warning to the state."""
        self.warnings.append(warning)
        self.updated_at = datetime.utcnow()
    
    def mark_step_completed(self, step: str):
        """Mark a step as completed."""
        if step not in self.steps_completed:
            self.steps_completed.append(step)
        self.current_step = step
        self.updated_at = datetime.utcnow()
    
    def update_status(self, status: TaskStatus):
        """Update the task status."""
        self.status = status
        self.updated_at = datetime.utcnow()
    
    def increment_retry(self):
        """Increment retry count."""
        self.retry_count += 1
        self.updated_at = datetime.utcnow()
    
    def should_retry(self) -> bool:
        """Check if task should be retried."""
        return self.retry_count < self.max_retries
    
    def set_context(self, key: str, value: Any):
        """Set a context value."""
        self.context[key] = value
        self.updated_at = datetime.utcnow()
    
    def get_context_for_key(self, key: str, default: Any = None) -> Any:
        """Get a context value."""
        return self.context.get(key, default)


# Utility functions for state management

def create_initial_state(task_request: TaskRequest) -> AgentState:
    """
    Create initial agent state from task request.
    
    Args:
        task_request: Incoming task request
        
    Returns:
        Initial agent state
    """
    return AgentState(
        task_id=task_request.task_id,
        task_type=task_request.task_type,
        input_data=task_request.input_data,
        requested_by=task_request.requested_by,
        status=TaskStatus.PENDING
    )


def state_to_response(state: AgentState) -> TaskResponse:
    """
    Convert agent state to task response.
    
    Args:
        state: Agent state
        
    Returns:
        Task response
    """
    # Compile all results
    results = {
        "intent_classification": state.intent_classification.dict() if state.intent_classification else None,
        "analysis_result": state.analysis_result.dict() if state.analysis_result else None,
        "generation_result": state.generation_result.dict() if state.generation_result else None,
        "final_result": state.final_result,
        "context": state.context,
        "steps_completed": state.steps_completed
    }
    
    return TaskResponse(
        task_id=state.task_id,
        success=(state.status == TaskStatus.COMPLETED and len(state.errors) == 0),
        task_type=state.task_type,
        results=results,
        processing_time_ms=state.processing_time_ms,
        error="; ".join(state.errors) if state.errors else None
    )
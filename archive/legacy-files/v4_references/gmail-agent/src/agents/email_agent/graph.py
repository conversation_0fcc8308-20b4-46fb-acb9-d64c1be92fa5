"""
LangGraph workflow for email conversation agent.

This module implements the email-specific workflow with proper safety controls,
Gmail integration, and AI-powered response generation.
"""

import logging
from typing import Literal, Dict, Any
from datetime import datetime, timedelta
from langgraph.graph import StateGraph, END

from src.agents.state import EmailAgentState, TriageResult, EmailReply, PersonaConfig
from src.model_router import model_router

logger = logging.getLogger(__name__)


async def classify_email_intent(state: EmailAgentState) -> EmailAgentState:
    """
    Classify email intent and determine if reply is warranted.
    
    Implements 24-hour safety cutoff and email classification.
    """
    logger.info(f"Classifying email intent for: {state.email_content.id if state.email_content else 'unknown'}")
    
    if not state.email_content:
        state.triage_result = TriageResult(
            classification="reply_not_needed",
            is_reply_warranted=False,
            confidence=1.0,
            reasoning="No email content provided",
            safety_checks={"has_content": False}
        )
        return state
    
    # Safety check: 24-hour cutoff
    try:
        email_date = datetime.fromisoformat(state.email_content.received_at.replace('Z', '+00:00'))
        cutoff_date = datetime.now() - timedelta(days=1)
        
        if email_date < cutoff_date:
            state.triage_result = TriageResult(
                classification="old_email",
                is_reply_warranted=False,
                confidence=1.0,
                reasoning="Email is older than 24 hours - safety cutoff applied",
                safety_checks={"24h_cutoff": False, "email_age_hours": (datetime.now() - email_date).total_seconds() / 3600}
            )
            return state
            
    except Exception as e:
        logger.error(f"Date parsing error: {e}")
        state.triage_result = TriageResult(
            classification="reply_not_needed",
            is_reply_warranted=False,
            confidence=1.0,
            reasoning="Invalid email date format - safety measure",
            safety_checks={"date_valid": False}
        )
        return state
    
    try:
        # Get classification model
        model = model_router.get_model_for_task("classification", priority="speed")
        
        # Email classification prompt
        prompt = f"""
Analyze this email and classify the sender's intent. Consider what type of response (if any) is appropriate.

Email Details:
From: {state.email_content.from_address}
Subject: {state.email_content.subject}
Body: {state.email_content.body[:1000]}...

Classify as one of:
1. customer_inquiry - New potential customer asking questions (REPLY NEEDED)
2. customer_reply - Existing customer responding to our outreach (REPLY NEEDED)  
3. spam_or_marketing - Marketing emails, newsletters, spam (NO REPLY)
4. internal_email - Internal company communications (NO REPLY)
5. automated_notification - System notifications, alerts (NO REPLY)
6. reply_not_needed - Other emails that don't warrant a response

Provide:
- Classification (one of the above)
- Confidence (0.0 to 1.0)
- Reasoning (brief explanation)
- Reply warranted (true/false)

Analysis:
        """
        
        response = await model.ainvoke(prompt)
        analysis = response.content.strip()
        
        # Parse the response (simplified - in production use structured output)
        lines = [line.strip() for line in analysis.split('\n') if line.strip()]
        
        classification = "reply_not_needed"
        confidence = 0.5
        reasoning = "Unable to parse classification"
        is_reply_warranted = False
        
        for line in lines:
            if "classification" in line.lower() and ":" in line:
                classification = line.split(":", 1)[1].strip().lower()
            elif "confidence" in line.lower() and ":" in line:
                try:
                    confidence = float(line.split(":", 1)[1].strip())
                except:
                    confidence = 0.5
            elif "reasoning" in line.lower() and ":" in line:
                reasoning = line.split(":", 1)[1].strip()
            elif "reply warranted" in line.lower() and ":" in line:
                is_reply_warranted = "true" in line.lower()
        
        # Validate classification
        valid_classifications = [
            "customer_inquiry", "customer_reply", "spam_or_marketing", 
            "internal_email", "automated_notification", "reply_not_needed"
        ]
        
        if classification not in valid_classifications:
            classification = "reply_not_needed"
            reasoning = f"Unknown classification, defaulting to no reply: {reasoning}"
        
        state.triage_result = TriageResult(
            classification=classification,
            is_reply_warranted=is_reply_warranted,
            confidence=confidence,
            reasoning=reasoning,
            safety_checks={
                "24h_cutoff": True,
                "date_valid": True,
                "classification_successful": True
            }
        )
        
        state.safety_checks_passed = True
        logger.info(f"Email classified as: {classification} (reply warranted: {is_reply_warranted})")
        
    except Exception as e:
        logger.error(f"Email classification failed: {e}")
        state.triage_result = TriageResult(
            classification="reply_not_needed",
            is_reply_warranted=False,
            confidence=0.0,
            reasoning=f"Classification failed: {str(e)}",
            safety_checks={"classification_successful": False}
        )
    
    return state


async def enrich_persona_context(state: EmailAgentState) -> EmailAgentState:
    """
    Load and enrich BDR persona configuration.
    
    In production, this would load from configuration or database.
    """
    logger.info("Enriching persona context")
    
    # For now, use default persona - in production load from config
    state.persona_config = PersonaConfig(
        name="TKC Group BDR",
        role="Business Development Representative", 
        company="TKC Group",
        tone="professional and helpful",
        signature="\n\nBest regards,\nTKC Group Business Development\n\nP.S. We specialize in AI automation and business process optimization.",
        expertise_areas=["AI automation", "business process optimization", "LangGraph workflows", "Python development"]
    )
    
    logger.info("Persona context enriched")
    return state


async def generate_dynamic_reply(state: EmailAgentState) -> EmailAgentState:
    """
    Generate AI-powered email reply using context and persona.
    """
    logger.info("Generating dynamic email reply")
    
    if not state.triage_result or not state.triage_result.is_reply_warranted:
        logger.info("No reply needed based on triage result")
        return state
    
    try:
        # Get generation model
        model = model_router.get_model_for_task("content_generation", priority="quality")
        
        # Prepare context
        email_context = f"""
Original Email:
From: {state.email_content.from_address}
Subject: {state.email_content.subject}
Body: {state.email_content.body}

Classification: {state.triage_result.classification}
Reasoning: {state.triage_result.reasoning}
        """
        
        persona_context = f"""
Your Persona:
- Name: {state.persona_config.name}
- Role: {state.persona_config.role}
- Company: {state.persona_config.company}
- Tone: {state.persona_config.tone}
- Expertise: {', '.join(state.persona_config.expertise_areas)}
        """
        
        # Generate reply prompt
        prompt = f"""
You are a {state.persona_config.role} at {state.persona_config.company}. Generate a professional email reply.

{email_context}

{persona_context}

Guidelines:
1. Match the tone: {state.persona_config.tone}
2. Be helpful and address their specific needs
3. If it's an inquiry, provide useful information and next steps
4. If it's a reply, continue the conversation naturally
5. Keep it concise but comprehensive
6. Include a professional closing

Generate:
- Reply subject (if different from original)
- Reply body (including signature)
- Reply type (direct_answer, information_request, meeting_scheduling, or follow_up)
- Confidence level (0.0 to 1.0)
- Brief reasoning for your approach

Format your response as:
SUBJECT: [reply subject]
BODY: [reply body with signature]
TYPE: [reply type]
CONFIDENCE: [confidence]
REASONING: [reasoning]

Reply:
        """
        
        response = await model.ainvoke(prompt)
        reply_content = response.content.strip()
        
        # Parse the structured response
        lines = reply_content.split('\n')
        subject = state.email_content.subject
        body = ""
        reply_type = "direct_answer"
        confidence = 0.8
        reasoning = "AI-generated response"
        
        current_section = None
        body_lines = []
        
        for line in lines:
            line = line.strip()
            if line.startswith("SUBJECT:"):
                subject = line.replace("SUBJECT:", "").strip()
                if not subject.startswith("Re:") and not subject.startswith("RE:"):
                    subject = f"Re: {subject}"
            elif line.startswith("BODY:"):
                current_section = "body"
                body_start = line.replace("BODY:", "").strip()
                if body_start:
                    body_lines.append(body_start)
            elif line.startswith("TYPE:"):
                reply_type = line.replace("TYPE:", "").strip().lower()
                current_section = None
            elif line.startswith("CONFIDENCE:"):
                try:
                    confidence = float(line.replace("CONFIDENCE:", "").strip())
                except:
                    confidence = 0.8
                current_section = None
            elif line.startswith("REASONING:"):
                reasoning = line.replace("REASONING:", "").strip()
                current_section = None
            elif current_section == "body" and line:
                body_lines.append(line)
        
        # Construct final body
        body = '\n'.join(body_lines)
        if state.persona_config.signature and state.persona_config.signature not in body:
            body += state.persona_config.signature
        
        # Validate reply type
        valid_types = ["direct_answer", "information_request", "meeting_scheduling", "follow_up"]
        if reply_type not in valid_types:
            reply_type = "direct_answer"
        
        state.reply_generated = EmailReply(
            subject=subject,
            body=body,
            reply_type=reply_type,
            confidence=confidence,
            reasoning=reasoning,
            context_used=["email_content", "persona_config", "triage_result"]
        )
        
        logger.info(f"Reply generated successfully (type: {reply_type}, confidence: {confidence})")
        
    except Exception as e:
        logger.error(f"Reply generation failed: {e}")
        # Create a simple fallback reply
        state.reply_generated = EmailReply(
            subject=f"Re: {state.email_content.subject}",
            body=f"Thank you for your email. We have received your message and will respond soon.\n{state.persona_config.signature}",
            reply_type="follow_up",
            confidence=0.3,
            reasoning=f"Fallback reply due to generation error: {str(e)}",
            context_used=["fallback"]
        )
    
    return state


async def send_email_reply(state: EmailAgentState) -> EmailAgentState:
    """
    Send the generated email reply via Gmail API.
    """
    logger.info("Sending email reply via Gmail API")
    
    if not state.reply_generated:
        logger.info("No reply to send")
        return state
    
    try:
        # Initialize Gmail client
        from src.services.gmail_client import GmailClient
        gmail_client = GmailClient(subject_email="<EMAIL>")
        
        # Authenticate with Gmail
        if not await gmail_client.authenticate():
            logger.error("Failed to authenticate with Gmail API for sending")
            state.filtering_notes.append("Failed to authenticate with Gmail API")
            return state
        
        # Send the reply
        reply_subject = state.reply_generated.subject
        if not reply_subject.startswith("Re:"):
            reply_subject = f"Re: {state.email_content.subject}"
        
        message_id = await gmail_client.send_message(
            to=state.email_content.from_address,
            subject=reply_subject,
            body=state.reply_generated.body,
            thread_id=state.email_content.thread_id,
            user_id="me"
        )
        
        if message_id:
            logger.info(f"Email reply sent successfully: {message_id}")
            state.filtering_notes.append(f"Email reply sent successfully: {message_id}")
        else:
            logger.error("Failed to send email reply - no message ID returned")
            state.filtering_notes.append("Failed to send email reply")
        
    except Exception as e:
        logger.error(f"Failed to send email reply: {e}")
        state.filtering_notes.append(f"Failed to send reply: {str(e)}")
    
    return state


async def finalize_email_processing(state: EmailAgentState) -> EmailAgentState:
    """
    Finalize email processing and clean up.
    """
    logger.info("Finalizing email processing")
    
    # Calculate processing time
    if state.processing_start_time:
        processing_time = (datetime.now() - state.processing_start_time).total_seconds() * 1000
    else:
        processing_time = 0
    
    # Log final results
    if state.triage_result:
        logger.info(f"Processing complete - Classification: {state.triage_result.classification}")
        logger.info(f"Reply warranted: {state.triage_result.is_reply_warranted}")
    
    if state.reply_generated:
        logger.info(f"Reply generated: {state.reply_generated.reply_type}")
    
    logger.info(f"Total processing time: {processing_time:.0f}ms")
    
    return state


# Conditional routing functions

def should_generate_reply(state: EmailAgentState) -> Literal["generate_reply", "finalize"]:
    """Determine if we should generate a reply based on triage results."""
    
    if state.triage_result and state.triage_result.is_reply_warranted and state.safety_checks_passed:
        logger.info("Routing to reply generation")
        return "generate_reply"
    else:
        logger.info("Routing to finalization - no reply needed")
        return "finalize"


def should_send_reply(state: EmailAgentState) -> Literal["send_reply", "finalize"]:
    """Determine if we should send the generated reply."""
    
    if state.reply_generated and state.reply_generated.confidence > 0.5:
        logger.info("Routing to send reply")
        return "send_reply"
    else:
        logger.info("Routing to finalization - reply confidence too low or no reply")
        return "finalize"


def create_email_agent_graph() -> StateGraph:
    """
    Create the email conversation agent workflow graph.
    
    Returns:
        Compiled StateGraph for email processing
    """
    logger.info("Creating email agent workflow graph")
    
    # Create the workflow graph
    workflow = StateGraph(EmailAgentState)
    
    # Add workflow nodes
    workflow.add_node("classify_intent", classify_email_intent)
    workflow.add_node("enrich_persona", enrich_persona_context)
    workflow.add_node("generate_reply", generate_dynamic_reply)
    workflow.add_node("send_reply", send_email_reply)
    workflow.add_node("finalize", finalize_email_processing)
    
    # Set entry point
    workflow.set_entry_point("classify_intent")
    
    # Add conditional routing
    workflow.add_conditional_edges(
        "classify_intent",
        should_generate_reply,
        {
            "generate_reply": "enrich_persona",
            "finalize": "finalize"
        }
    )
    
    # From persona enrichment, always go to reply generation
    workflow.add_edge("enrich_persona", "generate_reply")
    
    # From reply generation, decide whether to send
    workflow.add_conditional_edges(
        "generate_reply",
        should_send_reply,
        {
            "send_reply": "send_reply",
            "finalize": "finalize"
        }
    )
    
    # From send reply, always finalize
    workflow.add_edge("send_reply", "finalize")
    
    # From finalization, end workflow
    workflow.add_edge("finalize", END)
    
    # Compile the workflow
    compiled_graph = workflow.compile()
    logger.info("Email agent graph compiled successfully")
    
    return compiled_graph


# Create the email agent graph instance
email_agent_graph = create_email_agent_graph()


async def process_email_workflow(email_content_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process an email through the complete workflow.
    
    Args:
        email_content_data: Email data from Gmail webhook
        
    Returns:
        Processing result
    """
    from src.agents.state import EmailContent
    
    try:
        # Create email content object
        email_content = EmailContent(**email_content_data)
        
        # Create initial state
        initial_state = EmailAgentState(
            email_content=email_content,
            processing_start_time=datetime.now()
        )
        
        # Execute workflow
        result = await email_agent_graph.ainvoke(initial_state)
        
        # The result is a dict, not the state object directly
        # Access the final state properly
        final_state = result if isinstance(result, dict) else result
        
        # Return processing result
        return {
            "success": True,
            "email_id": email_content.id,
            "classification": final_state.get("triage_result", {}).get("classification") if isinstance(final_state.get("triage_result"), dict) else getattr(final_state.get("triage_result"), "classification", None),
            "reply_warranted": final_state.get("triage_result", {}).get("is_reply_warranted", False) if isinstance(final_state.get("triage_result"), dict) else getattr(final_state.get("triage_result"), "is_reply_warranted", False),
            "reply_generated": final_state.get("reply_generated") is not None,
            "reply_type": final_state.get("reply_generated", {}).get("reply_type") if isinstance(final_state.get("reply_generated"), dict) else getattr(final_state.get("reply_generated"), "reply_type", None),
            "processing_notes": final_state.get("filtering_notes", []),
            "safety_checks_passed": final_state.get("safety_checks_passed", False)
        }
        
    except Exception as e:
        logger.error(f"Email workflow processing failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "email_id": email_content_data.get("id", "unknown")
        }
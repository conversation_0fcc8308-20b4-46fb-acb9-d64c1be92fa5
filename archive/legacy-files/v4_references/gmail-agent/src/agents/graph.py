"""
LangGraph workflow definition for the template agent.

This module defines the complete agent workflow using LangGraph's
StateGraph for generic task processing.
"""

import logging
import time
from typing import Dict, Any
from datetime import datetime

from src.agents.state import (
    AgentState, 
    TaskRequest, 
    TaskResponse,
    TaskStatus,
    create_initial_state,
    state_to_response
)
from src.model_router import model_router
from src.services.database_client import DatabaseClient

logger = logging.getLogger(__name__)

# Initialize database client
db_client = DatabaseClient()


async def execute_agent_workflow(
    task_request: TaskRequest
) -> TaskResponse:
    """
    Execute the agent workflow for a given task request.
    
    Args:
        task_request: Task request
        
    Returns:
        Task response
    """
    start_time = time.time()
    logger.info(f"Starting agent workflow for task: {task_request.task_id}")
    
    try:
        # Create initial state
        state = create_initial_state(task_request)
        
        # Execute workflow steps
        state = await _initialize_task(state)
        state = await _classify_intent(state)
        state = await _analyze_content(state)
        state = await _generate_response(state)
        state = await _finalize_task(state)
        
        # Calculate processing time
        processing_time = int((time.time() - start_time) * 1000)
        state.processing_time_ms = processing_time
        
        # Convert to response
        response = state_to_response(state)
        
        logger.info(f"Agent workflow completed for task: {task_request.task_id}")
        return response
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"Agent workflow failed: {e}")
        
        # Return error response
        return TaskResponse(
            task_id=task_request.task_id,
            success=False,
            task_type=task_request.task_type,
            results={},
            processing_time_ms=processing_time,
            error=str(e)
        )


async def _initialize_task(state: AgentState) -> AgentState:
    """Initialize the task."""
    logger.info(f"Initializing task: {state.task_id}")
    
    try:
        state.update_status(TaskStatus.IN_PROGRESS)
        state.mark_step_completed("initialize_task")
        
        # Set up context based on task type
        if state.task_type.value == "conversation":
            state.set_context("conversation_mode", True)
        elif state.task_type.value == "analysis":
            state.set_context("analysis_mode", True)
        elif state.task_type.value == "generation":
            state.set_context("generation_mode", True)
        
        # Initialize processing time tracking
        state.set_context("start_time", time.time())
        
        logger.info(f"Task {state.task_id} initialized successfully")
        return state
        
    except Exception as e:
        logger.error(f"Task initialization failed: {e}")
        state.add_error(f"Initialization failed: {str(e)}")
        state.update_status(TaskStatus.FAILED)
        return state


async def _classify_intent(state: AgentState) -> AgentState:
    """Classify the intent of the incoming task."""
    logger.info(f"Classifying intent for task: {state.task_id}")
    
    try:
        state.mark_step_completed("classify_intent")
        
        # Get input text for classification
        input_text = state.input_data.get("text", "")
        if not input_text:
            input_text = str(state.input_data)
        
        # Select appropriate model for classification
        model = model_router.get_model_for_task("classification", priority="speed")
        
        # Create classification prompt
        prompt = f"""
Analyze the following input and classify the user's intent. Consider what they are trying to accomplish.

Input: {input_text}

Please provide:
1. Primary intent (one of: question, request, complaint, compliment, information_seeking, task_completion)
2. Confidence level (0.0 to 1.0)
3. Brief reasoning for your classification
4. Whether this requires immediate action (yes/no)

Classification:
        """.strip()
        
        response = await model.ainvoke(prompt)
        classification_text = response.content.strip()
        
        # Parse the response (simplified - in production you'd use structured output)
        lines = classification_text.split('\n')
        intent = "unknown"
        confidence = 0.5
        reasoning = "Unable to parse classification"
        requires_action = False
        
        for line in lines:
            line = line.strip()
            if line.startswith("1.") or "intent" in line.lower():
                intent = line.split(":")[-1].strip() if ":" in line else "unknown"
            elif line.startswith("2.") or "confidence" in line.lower():
                try:
                    confidence_str = line.split(":")[-1].strip()
                    confidence = float(confidence_str.replace("(", "").replace(")", ""))
                except:
                    confidence = 0.5
            elif line.startswith("3.") or "reasoning" in line.lower():
                reasoning = line.split(":", 1)[-1].strip() if ":" in line else line
            elif line.startswith("4.") or "action" in line.lower():
                requires_action = "yes" in line.lower()
        
        # Create intent classification result
        from src.agents.state import IntentClassification
        
        intent_result = IntentClassification(
            intent=intent,
            confidence=confidence,
            reasoning=reasoning,
            requires_action=requires_action,
            suggested_actions=["analyze_content"] if requires_action else ["generate_response"]
        )
        
        state.intent_classification = intent_result
        
        logger.info(f"Intent classified as: {intent} (confidence: {confidence})")
        return state
        
    except Exception as e:
        logger.error(f"Intent classification failed: {e}")
        state.add_error(f"Intent classification failed: {str(e)}")
        return state


async def _analyze_content(state: AgentState) -> AgentState:
    """Perform detailed analysis of the input content."""
    logger.info(f"Analyzing content for task: {state.task_id}")
    
    try:
        state.mark_step_completed("analyze_content")
        
        # Get input for analysis
        input_text = state.input_data.get("text", str(state.input_data))
        
        # Select appropriate model for analysis
        model = model_router.get_model_for_task("analysis", priority="quality")
        
        # Comprehensive analysis prompt
        prompt = f"""
Perform a comprehensive analysis of the following content:

Content: {input_text}

Please provide:
1. Summary (2-3 sentences)
2. Key points (bullet list)
3. Sentiment (Positive/Negative/Neutral with confidence)
4. Important entities (people, organizations, locations, dates)
5. Overall confidence in your analysis (0.0 to 1.0)

Analysis:
        """.strip()
        
        response = await model.ainvoke(prompt)
        analysis_text = response.content.strip()
        
        # Parse analysis (simplified parsing)
        lines = analysis_text.split('\n')
        summary = ""
        key_points = []
        sentiment = "neutral"
        entities = []
        confidence = 0.8
        
        current_section = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if "summary" in line.lower() and ":" in line:
                summary = line.split(":", 1)[-1].strip()
                current_section = "summary"
            elif "key points" in line.lower():
                current_section = "key_points"
            elif "sentiment" in line.lower() and ":" in line:
                sentiment = line.split(":")[-1].strip().lower()
                current_section = "sentiment"
            elif "entities" in line.lower():
                current_section = "entities"
            elif "confidence" in line.lower() and ":" in line:
                try:
                    conf_str = line.split(":")[-1].strip()
                    confidence = float(conf_str.replace("(", "").replace(")", ""))
                except:
                    confidence = 0.8
            elif line.startswith("-") or line.startswith("•"):
                if current_section == "key_points":
                    key_points.append(line[1:].strip())
                elif current_section == "entities":
                    entities.append({"text": line[1:].strip(), "type": "unknown"})
        
        # Create analysis result
        from src.agents.state import AnalysisResult
        
        analysis_result = AnalysisResult(
            summary=summary or "Content analysis completed",
            key_points=key_points,
            sentiment=sentiment,
            entities=entities,
            confidence=confidence,
            metadata={
                "analyzed_at": datetime.utcnow().isoformat(),
                "input_length": len(input_text)
            }
        )
        
        state.analysis_result = analysis_result
        
        logger.info(f"Content analysis completed with confidence: {confidence}")
        return state
        
    except Exception as e:
        logger.error(f"Content analysis failed: {e}")
        state.add_error(f"Content analysis failed: {str(e)}")
        return state


async def _generate_response(state: AgentState) -> AgentState:
    """Generate the final response based on the analysis."""
    logger.info(f"Generating response for task: {state.task_id}")
    
    try:
        state.mark_step_completed("generate_response")
        
        # Get appropriate model for generation
        model = model_router.get_model_for_task("content_generation", priority="quality")
        
        # Create generation prompt based on task type and analysis
        if state.task_type.value == "generation":
            prompt = f"""
Generate content based on the following requirements:

Input: {state.input_data.get('text', '')}
Task Type: {state.task_type}
Analysis Summary: {state.analysis_result.summary if state.analysis_result else 'None'}

Please generate appropriate content that addresses the user's needs.

Generated content:
            """.strip()
        else:
            # General response
            prompt = f"""
Create a helpful response based on the following information:

User Input: {state.input_data.get('text', '')}
Intent: {state.intent_classification.intent if state.intent_classification else 'unknown'}
Analysis: {state.analysis_result.summary if state.analysis_result else 'No analysis available'}

Provide a clear, helpful response that addresses the user's needs.

Response:
            """.strip()
        
        response = await model.ainvoke(prompt)
        generated_content = response.content.strip()
        
        # Create generation result
        from src.agents.state import GenerationResult
        
        generation_result = GenerationResult(
            generated_content=generated_content,
            confidence=0.85,
            model_used=str(model.model_name if hasattr(model, 'model_name') else "gemini"),
            metadata={
                "task_type": state.task_type,
                "generation_time": datetime.utcnow().isoformat()
            }
        )
        
        state.generation_result = generation_result
        
        logger.info(f"Response generated for task: {state.task_id}")
        return state
        
    except Exception as e:
        logger.error(f"Response generation failed: {e}")
        state.add_error(f"Response generation failed: {str(e)}")
        return state


async def _finalize_task(state: AgentState) -> AgentState:
    """Finalize the task."""
    logger.info(f"Finalizing task: {state.task_id}")
    
    try:
        # Calculate processing time
        start_time = state.get_context_for_key("start_time", time.time())
        processing_time = int((time.time() - start_time) * 1000)
        state.processing_time_ms = processing_time
        
        # Prepare final result
        final_result = {
            "task_id": state.task_id,
            "task_type": state.task_type,
            "processing_time_ms": processing_time,
            "steps_completed": state.steps_completed
        }
        
        # Add results based on what was generated
        if state.intent_classification:
            final_result["intent"] = {
                "classification": state.intent_classification.intent,
                "confidence": state.intent_classification.confidence,
                "reasoning": state.intent_classification.reasoning
            }
        
        if state.analysis_result:
            final_result["analysis"] = {
                "summary": state.analysis_result.summary,
                "key_points": state.analysis_result.key_points,
                "sentiment": state.analysis_result.sentiment,
                "confidence": state.analysis_result.confidence
            }
        
        if state.generation_result:
            final_result["response"] = {
                "content": state.generation_result.generated_content,
                "confidence": state.generation_result.confidence,
                "model_used": state.generation_result.model_used
            }
        
        # Add any errors or warnings
        if state.errors:
            final_result["errors"] = state.errors
        if state.warnings:
            final_result["warnings"] = state.warnings
        
        state.final_result = final_result
        state.final_status = "completed"
        
        # Update final status
        if state.errors:
            state.update_status(TaskStatus.FAILED)
        else:
            state.update_status(TaskStatus.COMPLETED)
        
        state.mark_step_completed("finalize_task")
        
        logger.info(f"Task {state.task_id} finalized with status: {state.status}")
        return state
        
    except Exception as e:
        logger.error(f"Task finalization failed: {e}")
        state.add_error(f"Finalization failed: {str(e)}")
        state.update_status(TaskStatus.FAILED)
        return state
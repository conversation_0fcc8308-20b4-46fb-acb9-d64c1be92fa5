"""
Celery application instance and configuration.

This module sets up Celery for background task processing with support
for both Redis (development) and Google Cloud Pub/Sub (production).
"""

import logging
from celery import Celery
from kombu import Queue

from src.config import settings

logger = logging.getLogger(__name__)

# Create Celery app instance
celery_app = Celery("agent-template")

# Configure Celery based on environment
if settings.is_production:
    # Production configuration with Google Cloud Pub/Sub
    celery_app.conf.update(
        broker_url=f"gcp-pubsub://",
        broker_transport_options={
            "region": settings.gcp_region,
            "project_id": settings.gcp_project_id,
            "subscription_name": "celery-worker-subscription",
            "topic_name": settings.gcp_pubsub_celery_topic,
        },
        result_backend="rpc://",
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        enable_utc=True,
        task_track_started=True,
        task_time_limit=30 * 60,  # 30 minutes
        task_soft_time_limit=25 * 60,  # 25 minutes
        worker_prefetch_multiplier=1,
        worker_max_tasks_per_child=1000,
    )
else:
    # Development configuration with Redis
    celery_app.conf.update(
        broker_url=settings.celery_broker_url,
        result_backend=settings.celery_broker_url,
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        enable_utc=True,
        task_track_started=True,
        task_time_limit=10 * 60,  # 10 minutes for development
        task_soft_time_limit=8 * 60,  # 8 minutes
    )

# Define task queues
celery_app.conf.task_routes = {
    "src.async_tasks.tasks.process_background_task": {"queue": "default"},
    "src.async_tasks.tasks.batch_process_documents": {"queue": "heavy"},
    "src.async_tasks.tasks.generate_report": {"queue": "reports"},
    "src.async_tasks.tasks.cleanup_old_data": {"queue": "maintenance"},
}

# Define queues with different priorities
celery_app.conf.task_queues = (
    Queue("default", routing_key="default"),
    Queue("heavy", routing_key="heavy"),
    Queue("reports", routing_key="reports"),
    Queue("maintenance", routing_key="maintenance"),
)

# Monitoring and error handling
celery_app.conf.update(
    task_send_sent_event=True,
    task_ignore_result=False,
    result_expires=3600,  # 1 hour
    task_acks_late=True,
    worker_send_task_events=True,
    task_reject_on_worker_lost=True,
)

# Configure logging
celery_app.conf.update(
    worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
    worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
    worker_log_color=False,
)

# Error handling callbacks
@celery_app.task(bind=True)
def task_failure_handler(self, task_id, error, traceback):
    """Handle task failures with logging and notifications."""
    logger.error(f"Task {task_id} failed: {error}")
    logger.error(f"Traceback: {traceback}")
    
    # Here you could add notification logic (email, Slack, etc.)
    # For now, just log the failure

# Register error handler
celery_app.conf.task_annotations = {
    "*": {
        "on_failure": task_failure_handler,
    }
}

# Auto-discover tasks
celery_app.autodiscover_tasks(["src.async_tasks"])

logger.info("Celery app configured successfully")


# Monitoring and health check functions

def get_celery_worker_status():
    """
    Get the status of Celery workers.
    
    Returns:
        Dictionary with worker status information
    """
    try:
        inspect = celery_app.control.inspect()
        
        # Get active tasks
        active_tasks = inspect.active()
        
        # Get registered tasks
        registered_tasks = inspect.registered()
        
        # Get worker stats
        stats = inspect.stats()
        
        return {
            "active_tasks": active_tasks,
            "registered_tasks": registered_tasks,
            "worker_stats": stats,
            "status": "healthy" if active_tasks is not None else "unhealthy"
        }
        
    except Exception as e:
        logger.error(f"Failed to get Celery worker status: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


def purge_celery_queues():
    """
    Purge all Celery queues.
    
    Warning: This will remove all pending tasks!
    """
    try:
        celery_app.control.purge()
        logger.info("All Celery queues purged")
        return True
        
    except Exception as e:
        logger.error(f"Failed to purge Celery queues: {e}")
        return False


# Task result utilities

def get_task_result(task_id: str):
    """
    Get the result of a Celery task.
    
    Args:
        task_id: Celery task ID
        
    Returns:
        Task result or None if not found
    """
    try:
        result = celery_app.AsyncResult(task_id)
        
        return {
            "task_id": task_id,
            "status": result.status,
            "result": result.result,
            "success": result.successful(),
            "failed": result.failed(),
            "ready": result.ready()
        }
        
    except Exception as e:
        logger.error(f"Failed to get task result for {task_id}: {e}")
        return None


def revoke_task(task_id: str, terminate: bool = False):
    """
    Revoke a Celery task.
    
    Args:
        task_id: Celery task ID
        terminate: Whether to terminate if the task is running
        
    Returns:
        True if successful, False otherwise
    """
    try:
        celery_app.control.revoke(task_id, terminate=terminate)
        logger.info(f"Task {task_id} revoked (terminate={terminate})")
        return True
        
    except Exception as e:
        logger.error(f"Failed to revoke task {task_id}: {e}")
        return False


# Production-specific configuration for Google Cloud

if settings.is_production:
    
    @celery_app.on_after_configure.connect
    def setup_periodic_tasks(sender, **kwargs):
        """Set up periodic tasks for production environment."""
        
        # Example: Clean up old data every day at 2 AM
        sender.add_periodic_task(
            crontab(hour=2, minute=0),
            cleanup_old_data.s(),
            name="daily_cleanup"
        )
        
        # Example: Generate weekly reports every Monday at 9 AM
        sender.add_periodic_task(
            crontab(hour=9, minute=0, day_of_week=1),
            generate_report.s("weekly"),
            name="weekly_report"
        )
        
        logger.info("Periodic tasks configured for production")


# Import crontab if using periodic tasks
try:
    from celery.schedules import crontab
except ImportError:
    logger.warning("crontab not available - periodic tasks disabled")
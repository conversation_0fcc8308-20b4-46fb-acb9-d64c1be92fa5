"""
Gmail API client for email operations.

This module provides a thread-safe Gmail client with authentication,
email reading, sending, and management capabilities.
"""

import logging
import json
import os
from typing import Optional, List, Dict, Any
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import base64
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timezone

from src.config import settings

logger = logging.getLogger(__name__)


class GmailClient:
    """
    Gmail API client with authentication and email operations.
    
    Supports both OAuth2 and service account authentication modes.
    """
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    def __init__(self, service_account_info: Optional[Dict] = None, subject_email: str = "<EMAIL>"):
        self.service_account_info = service_account_info
        self.subject_email = subject_email  # Email to impersonate with domain delegation
        self.service = None
        self._authenticated = False
    
    async def authenticate(self) -> bool:
        """
        Authenticate with Gmail API using service account with domain delegation.
        
        Returns:
            True if authentication succeeded, False otherwise
        """
        try:
            # Use service account info passed in constructor or from environment
            if not self.service_account_info:
                # Try to get from GCP_SERVICE_ACCOUNT_KEY environment variable
                service_account_json = os.getenv('GCP_SERVICE_ACCOUNT_KEY')
                if not service_account_json:
                    logger.error("No service account credentials available")
                    return False
                
                logger.info(f"Service account JSON length: {len(service_account_json)}")
                logger.info(f"Service account JSON preview: {service_account_json[:100]}...")
                
                self.service_account_info = json.loads(service_account_json)
            
            # Create credentials from service account info with domain delegation
            credentials = service_account.Credentials.from_service_account_info(
                self.service_account_info,
                scopes=self.SCOPES,
                subject=self.subject_email  # This enables domain delegation
            )
            
            # Build the Gmail service
            self.service = build('gmail', 'v1', credentials=credentials)
            self._authenticated = True
            
            logger.info(f"Gmail service account authentication successful for {self.subject_email}")
            return True
            
        except Exception as e:
            logger.error(f"Gmail service account authentication failed: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if the client is authenticated."""
        return self._authenticated and self.service is not None
    
    async def get_messages_from_history(self, history_id: str, start_history_id: Optional[str] = None, user_id: str = "me") -> List[Dict[str, Any]]:
        """
        Get new messages from Gmail history using proper history API.
        
        Args:
            history_id: Current history ID from Pub/Sub notification
            start_history_id: Last processed history ID (optional)
            user_id: User ID (default: "me")
            
        Returns:
            List of new message dictionaries
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            logger.info(f"Getting messages from history ID {start_history_id} to {history_id}")
            
            # If no start history ID, use current minus small offset as fallback
            if not start_history_id:
                logger.warning("No start history ID provided, using fallback method")
                return await self._get_recent_messages_fallback(user_id)
            
            # Use Gmail history API to get actual changes
            history_result = self.service.users().history().list(
                userId=user_id,
                startHistoryId=start_history_id,
                historyTypes=['messageAdded'],  # Only get new messages
                maxResults=10  # Limit to prevent overprocessing
            ).execute()
            
            messages = []
            history_records = history_result.get('history', [])
            
            logger.info(f"Found {len(history_records)} history records")
            
            for history_record in history_records:
                messages_added = history_record.get('messagesAdded', [])
                
                for message_added in messages_added:
                    message_info = message_added.get('message', {})
                    message_id = message_info.get('id')
                    
                    if message_id:
                        # Get full message details
                        full_message = await self.get_message(message_id, user_id)
                        if full_message and self._should_process_message(full_message):
                            messages.append(full_message)
                            logger.info(f"Added message from history: {full_message.get('subject', 'No subject')} from {full_message.get('from', 'Unknown')}")
            
            logger.info(f"Retrieved {len(messages)} new messages from history")
            return messages
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"History ID {start_history_id} not found, using fallback method")
                return await self._get_recent_messages_fallback(user_id)
            else:
                logger.error(f"Gmail API error getting history: {e}")
                return []
        except Exception as e:
            logger.error(f"Failed to get messages from history: {e}")
            return []
    
    async def get_messages(
        self,
        query: str = "",
        max_results: int = 10,
        user_id: str = "me"
    ) -> List[Dict[str, Any]]:
        """
        Get Gmail messages based on search query.
        
        Args:
            query: Gmail search query (e.g., "is:unread", "from:<EMAIL>")
            max_results: Maximum number of messages to return
            user_id: User ID (default: "me")
            
        Returns:
            List of message dictionaries
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            # Get message list
            results = self.service.users().messages().list(
                userId=user_id,
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            
            # Get full message details
            detailed_messages = []
            for message in messages:
                msg_detail = await self.get_message(message['id'], user_id)
                if msg_detail:
                    detailed_messages.append(msg_detail)
            
            logger.info(f"Retrieved {len(detailed_messages)} messages")
            return detailed_messages
            
        except HttpError as e:
            logger.error(f"Gmail API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to get messages: {e}")
            return []
    
    async def _get_recent_messages_fallback(self, user_id: str = "me") -> List[Dict[str, Any]]:
        """
        Fallback method to get recent messages when history API fails.
        
        Args:
            user_id: User ID (default: "me")
            
        Returns:
            List of recent message dictionaries
        """
        try:
            logger.info("Using fallback method to get recent messages")
            
            # SAFE approach: Only get recent unread messages with strict filters
            query = "is:unread category:primary newer_than:30m -from:me -label:sent"
            logger.info(f"Using fallback Gmail query: {query}")
            
            messages_result = self.service.users().messages().list(
                userId=user_id,
                q=query,
                maxResults=5  # Very limited to prevent overprocessing
            ).execute()
            
            messages = []
            if 'messages' in messages_result:
                logger.info(f"Found {len(messages_result['messages'])} recent unread messages")
                for message_ref in messages_result['messages']:
                    message_id = message_ref['id']
                    full_message = await self.get_message(message_id, user_id)
                    if full_message and self._should_process_message(full_message):
                        messages.append(full_message)
                        logger.info(f"Retrieved message: {full_message.get('subject', 'No subject')} from {full_message.get('from', 'Unknown')}")
            else:
                logger.info("No recent unread messages found")
            
            return messages
            
        except Exception as e:
            logger.error(f"Fallback message retrieval failed: {e}")
            return []
    
    def _should_process_message(self, message: Dict[str, Any]) -> bool:
        """
        Determine if a message should be processed by the AI agent.
        
        Args:
            message: Parsed message dictionary
            
        Returns:
            True if message should be processed, False otherwise
        """
        try:
            # Skip if no sender
            sender = message.get('from', '').lower()
            if not sender:
                return False
            
            # Skip messages from ourselves
            if 'tkcgroup.co' in sender and ('tyler@' in sender or 'bdr@' in sender):
                logger.info(f"Skipping message from ourselves: {sender}")
                return False
            
            # Skip automated/system messages
            automated_senders = [
                'noreply', 'no-reply', 'donotreply', 'do-not-reply',
                'mailer-daemon', 'postmaster', 'bounce', 'notifications'
            ]
            
            for auto_sender in automated_senders:
                if auto_sender in sender:
                    logger.info(f"Skipping automated message from: {sender}")
                    return False
            
            # Skip if message is too old (more than 24 hours)
            internal_date = message.get('internal_date')
            if internal_date:
                try:
                    msg_timestamp = int(internal_date) / 1000  # Convert to seconds
                    current_timestamp = datetime.now(timezone.utc).timestamp()
                    age_hours = (current_timestamp - msg_timestamp) / 3600
                    
                    if age_hours > 24:
                        logger.info(f"Skipping message older than 24 hours: {age_hours:.1f}h old")
                        return False
                except (ValueError, TypeError):
                    logger.warning(f"Could not parse message timestamp: {internal_date}")
            
            # Check for spam/promotional labels
            label_ids = message.get('label_ids', [])
            spam_labels = ['SPAM', 'PROMOTIONS', 'SOCIAL', 'FORUMS']
            
            for spam_label in spam_labels:
                if spam_label in label_ids:
                    logger.info(f"Skipping message with spam/promotional label: {spam_label}")
                    return False
            
            logger.info(f"Message approved for processing: {message.get('subject', 'No subject')} from {sender}")
            return True
            
        except Exception as e:
            logger.error(f"Error checking if message should be processed: {e}")
            return False
    
    async def get_message(self, message_id: str, user_id: str = "me") -> Optional[Dict[str, Any]]:
        """
        Get a specific Gmail message by ID.
        
        Args:
            message_id: Gmail message ID
            user_id: User ID (default: "me")
            
        Returns:
            Message dictionary or None
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            message = self.service.users().messages().get(
                userId=user_id,
                id=message_id,
                format='full'
            ).execute()
            
            # Parse message details
            parsed_message = self._parse_message(message)
            logger.info(f"Retrieved message: {message_id}")
            
            return parsed_message
            
        except HttpError as e:
            logger.error(f"Gmail API error for message {message_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to get message {message_id}: {e}")
            return None
    
    def _parse_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse Gmail message into a standardized format.
        
        Args:
            message: Raw Gmail message object
            
        Returns:
            Parsed message dictionary
        """
        try:
            headers = message['payload'].get('headers', [])
            header_dict = {header['name'].lower(): header['value'] for header in headers}
            
            # Extract message body
            body = self._extract_message_body(message['payload'])
            
            return {
                'id': message['id'],
                'thread_id': message['threadId'],
                'label_ids': message.get('labelIds', []),
                'snippet': message.get('snippet', ''),
                'history_id': message.get('historyId'),
                'internal_date': message.get('internalDate'),
                'subject': header_dict.get('subject', ''),
                'from': header_dict.get('from', ''),
                'to': header_dict.get('to', ''),
                'cc': header_dict.get('cc', ''),
                'bcc': header_dict.get('bcc', ''),
                'date': header_dict.get('date', ''),
                'message_id': header_dict.get('message-id', ''),
                'body': body,
                'headers': header_dict
            }
            
        except Exception as e:
            logger.error(f"Failed to parse message: {e}")
            return {}
    
    def _extract_message_body(self, payload: Dict[str, Any]) -> str:
        """
        Extract message body from Gmail payload.
        
        Args:
            payload: Gmail message payload
            
        Returns:
            Message body as string
        """
        try:
            # Check if message has parts (multipart)
            if 'parts' in payload:
                for part in payload['parts']:
                    if part['mimeType'] == 'text/plain':
                        data = part['body'].get('data')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8')
                    elif part['mimeType'] == 'text/html':
                        # Fallback to HTML if no plain text
                        data = part['body'].get('data')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8')
            else:
                # Single part message
                if payload['mimeType'] in ['text/plain', 'text/html']:
                    data = payload['body'].get('data')
                    if data:
                        return base64.urlsafe_b64decode(data).decode('utf-8')
            
            return ""
            
        except Exception as e:
            logger.error(f"Failed to extract message body: {e}")
            return ""
    
    async def send_message(
        self,
        to: str,
        subject: str,
        body: str,
        from_email: Optional[str] = None,
        reply_to: Optional[str] = None,
        thread_id: Optional[str] = None,
        user_id: str = "me"
    ) -> Optional[str]:
        """
        Send an email message.
        
        Args:
            to: Recipient email address
            subject: Email subject
            body: Email body
            from_email: Sender email (optional)
            reply_to: Reply-to address (optional)
            thread_id: Thread ID for reply (optional)
            user_id: User ID (default: "me")
            
        Returns:
            Message ID if successful, None otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            # Create message
            message = MIMEText(body)
            message['to'] = to
            message['subject'] = subject
            
            if from_email:
                message['from'] = from_email
            if reply_to:
                message['reply-to'] = reply_to
            
            # Encode message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            # Prepare send request
            send_request = {'raw': raw_message}
            if thread_id:
                send_request['threadId'] = thread_id
            
            # Send message
            result = self.service.users().messages().send(
                userId=user_id,
                body=send_request
            ).execute()
            
            message_id = result.get('id')
            logger.info(f"Message sent successfully: {message_id}")
            
            return message_id
            
        except HttpError as e:
            logger.error(f"Gmail API error sending message: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return None
    
    async def mark_as_read(self, message_id: str, user_id: str = "me") -> bool:
        """
        Mark a message as read.
        
        Args:
            message_id: Gmail message ID
            user_id: User ID (default: "me")
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            self.service.users().messages().modify(
                userId=user_id,
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            
            logger.info(f"Message marked as read: {message_id}")
            return True
            
        except HttpError as e:
            logger.error(f"Gmail API error marking message as read: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to mark message as read: {e}")
            return False
    
    async def add_label(self, message_id: str, label_id: str, user_id: str = "me") -> bool:
        """
        Add a label to a message.
        
        Args:
            message_id: Gmail message ID
            label_id: Label ID to add
            user_id: User ID (default: "me")
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            self.service.users().messages().modify(
                userId=user_id,
                id=message_id,
                body={'addLabelIds': [label_id]}
            ).execute()
            
            logger.info(f"Label {label_id} added to message: {message_id}")
            return True
            
        except HttpError as e:
            logger.error(f"Gmail API error adding label: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to add label: {e}")
            return False
    
    async def get_labels(self, user_id: str = "me") -> List[Dict[str, Any]]:
        """
        Get all Gmail labels.
        
        Args:
            user_id: User ID (default: "me")
            
        Returns:
            List of label dictionaries
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            results = self.service.users().labels().list(userId=user_id).execute()
            labels = results.get('labels', [])
            
            logger.info(f"Retrieved {len(labels)} labels")
            return labels
            
        except HttpError as e:
            logger.error(f"Gmail API error getting labels: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to get labels: {e}")
            return []


# Convenience functions for common Gmail operations

async def create_gmail_client() -> GmailClient:
    """
    Create and authenticate a Gmail client.
    
    Returns:
        Authenticated Gmail client
    """
    client = GmailClient()
    success = await client.authenticate()
    
    if not success:
        raise RuntimeError("Failed to authenticate Gmail client")
    
    return client


async def send_simple_email(to: str, subject: str, body: str) -> bool:
    """
    Send a simple email using Gmail API.
    
    Args:
        to: Recipient email address
        subject: Email subject
        body: Email body
        
    Returns:
        True if successful, False otherwise
    """
    try:
        client = await create_gmail_client()
        message_id = await client.send_message(to, subject, body)
        return message_id is not None
        
    except Exception as e:
        logger.error(f"Failed to send simple email: {e}")
        return False
"""
Database client for handling connections and queries.

This module provides a simplified database client with health checks
and basic query capabilities for the prospecting agent.
"""

import logging
from typing import Optional, Dict, Any, List
from supabase import create_client, Client
import json

from src.config import settings

logger = logging.getLogger(__name__)


class DatabaseClient:
    """
    Database client for handling connections and queries.
    
    Simplified implementation for the restructured agent.
    """
    
    def __init__(self):
        self._initialized = False
        self.supabase: Optional[Client] = None
        
    async def initialize(self):
        """Initialize the database client."""
        try:
            logger.info("Initializing Supabase database client...")
            
            # Create Supabase client
            supabase_url = settings.supabase_url
            supabase_key = settings.supabase_service_role_key
            
            logger.info(f"Supabase URL: {supabase_url[:30]}...")
            logger.info(f"Supabase key length: {len(supabase_key) if supabase_key else 0}")
            
            if not supabase_url or supabase_url == "https://mock.supabase.co":
                logger.warning("Using mock Supabase URL - database features disabled")
                self._initialized = False
                return
                
            if not supabase_key:
                logger.warning("No Supabase service role key - database features disabled")
                self._initialized = False
                return
            
            self.supabase = create_client(supabase_url, supabase_key)
            
            # Test connection with a simple query (skip to avoid startup failures)
            # result = self.supabase.table('form_submissions').select('count').limit(1).execute()
            
            self._initialized = True
            logger.info("Supabase database client initialized successfully")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            # Don't raise - allow service to start without database
            self._initialized = False
    
    async def health_check(self) -> bool:
        """
        Perform a health check on the database connection.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            if not self._initialized:
                return False
                
            # In a real implementation, this would:
            # - Execute a simple query
            # - Check connection pool status
            # - Verify read/write access
            
            return True
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        Get database connection statistics.
        
        Returns:
            Dictionary of database statistics
        """
        try:
            return {
                "initialized": self._initialized,
                "status": "healthy" if self._initialized else "not_initialized",
                "connection_pool": {
                    "active_connections": 1,
                    "idle_connections": 2,
                    "max_connections": 10
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get database statistics: {e}")
            return {"error": str(e)}
    
    async def close(self):
        """Close database connections and clean up resources."""
        try:
            if self._initialized:
                logger.info("Closing database connections...")
                
                # In a real implementation, this would:
                # - Close connection pools
                # - Clean up resources
                # - Stop monitoring
                
                self._initialized = False
                logger.info("Database client closed successfully")
                
        except Exception as e:
            logger.error(f"Database close failed: {e}")
    
    async def query_prospect_data(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Query prospect data from form submissions table.
        
        Args:
            email: Prospect email address
            
        Returns:
            Prospect data or None if not found
        """
        try:
            if not self._initialized or not self.supabase:
                logger.error("Database client not initialized")
                return None
            
            logger.info(f"Querying prospect data for: {email}")
            
            # Query form_submissions table for prospect data
            result = self.supabase.table('form_submissions').select('*').eq('email', email).execute()
            
            if result.data and len(result.data) > 0:
                prospect_data = result.data[0]  # Get the first matching record
                logger.info(f"Found prospect data for {email}")
                return prospect_data
            else:
                logger.info(f"No prospect data found for {email}")
                return None
            
        except Exception as e:
            logger.error(f"Prospect data query failed: {e}")
            return None
    
    async def get_last_history_id(self, email_address: str) -> Optional[str]:
        """
        Get the last processed Gmail history ID for an email address.
        
        Args:
            email_address: Email address to get history ID for
            
        Returns:
            History ID string or None if not found
        """
        try:
            if not self._initialized or not self.supabase:
                logger.error("Database client not initialized")
                return None
            
            result = self.supabase.table('gmail_history').select('history_id').eq('email_address', email_address).order('updated_at', desc=True).limit(1).execute()
            
            if result.data and len(result.data) > 0:
                history_id = result.data[0]['history_id']
                logger.info(f"Retrieved last history ID for {email_address}: {history_id}")
                return history_id
            else:
                logger.info(f"No history ID found for {email_address}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get last history ID: {e}")
            return None
    
    async def store_history_id(self, email_address: str, history_id: str) -> bool:
        """
        Store the latest Gmail history ID for an email address.
        
        Args:
            email_address: Email address
            history_id: Gmail history ID to store
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized or not self.supabase:
                logger.error("Database client not initialized")
                return False
            
            # Use upsert to insert or update the history ID
            result = self.supabase.table('gmail_history').upsert({
                'email_address': email_address,
                'history_id': history_id,
                'updated_at': 'now()'
            }).execute()
            
            if result.data:
                logger.info(f"Stored history ID {history_id} for {email_address}")
                return True
            else:
                logger.error("Failed to store history ID")
                return False
                
        except Exception as e:
            logger.error(f"Failed to store history ID: {e}")
            return False
    
    async def store_email_processing_result(self, result_data: Dict[str, Any]) -> bool:
        """
        Store email processing result in database.
        
        Args:
            result_data: Processing result data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized or not self.supabase:
                logger.error("Database client not initialized")
                return False
            
            # Store in email_processing_results table (create if doesn't exist)
            result = self.supabase.table('email_processing_results').insert(result_data).execute()
            
            if result.data:
                logger.info(f"Stored email processing result: {result_data.get('email_id')}")
                return True
            else:
                logger.error("Failed to store email processing result")
                return False
                
        except Exception as e:
            logger.error(f"Failed to store email processing result: {e}")
            return False
"""
Vector database client for Pinecone integration.

This module provides a thread-safe client for vector database operations
including indexing, querying, and metadata management for RAG applications.
"""

import logging
from typing import Optional, List, Dict, Any, Tuple
import numpy as np
from pinecone import Pinecone, Index
from langchain_community.vectorstores import Pinecone as LangChainPinecone
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_google_genai import GoogleGenerativeAIEmbeddings

from src.config import settings

logger = logging.getLogger(__name__)


class VectorDBClient:
    """
    Pinecone vector database client with RAG capabilities.
    
    Provides document indexing, similarity search, and metadata filtering.
    """
    
    def __init__(self, index_name: str = "agent-template"):
        self.index_name = index_name
        self.pinecone_config = settings.get_pinecone_config()
        
        if not self.pinecone_config:
            raise ValueError("Pinecone configuration not available")
        
        self._client = None
        self._index = None
        self._embeddings = None
        self._vectorstore = None
    
    async def initialize(self):
        """Initialize Pinecone client and index."""
        try:
            # Initialize Pinecone client
            self._client = Pinecone(
                api_key=self.pinecone_config.api_key,
                environment=self.pinecone_config.environment
            )
            
            # Get or create index
            await self._ensure_index_exists()
            
            # Initialize embedding model
            await self._initialize_embeddings()
            
            # Initialize LangChain vectorstore
            self._vectorstore = LangChainPinecone(
                index=self._index,
                embedding=self._embeddings,
                text_key="text"
            )
            
            logger.info(f"Vector DB client initialized with index: {self.index_name}")
            
        except Exception as e:
            logger.error(f"Vector DB initialization failed: {e}")
            raise
    
    async def _ensure_index_exists(self):
        """Ensure the Pinecone index exists."""
        try:
            # Check if index exists
            existing_indexes = self._client.list_indexes()
            index_names = [idx.name for idx in existing_indexes]
            
            if self.index_name not in index_names:
                # Create index if it doesn't exist
                self._client.create_index(
                    name=self.index_name,
                    dimension=768,  # Default for sentence-transformers
                    metric="cosine",
                    spec={
                        "serverless": {
                            "cloud": "gcp",
                            "region": settings.gcp_region
                        }
                    }
                )
                logger.info(f"Created Pinecone index: {self.index_name}")
            
            # Connect to index
            self._index = self._client.Index(self.index_name)
            logger.info(f"Connected to Pinecone index: {self.index_name}")
            
        except Exception as e:
            logger.error(f"Failed to ensure index exists: {e}")
            raise
    
    async def _initialize_embeddings(self):
        """Initialize embedding model."""
        try:
            # Try Google embeddings first, fallback to HuggingFace
            if settings.google_api_key:
                self._embeddings = GoogleGenerativeAIEmbeddings(
                    model="models/embedding-001",
                    google_api_key=settings.google_api_key
                )
                logger.info("Using Google Generative AI embeddings")
            else:
                # Fallback to HuggingFace embeddings
                self._embeddings = HuggingFaceEmbeddings(
                    model_name="sentence-transformers/all-MiniLM-L6-v2"
                )
                logger.info("Using HuggingFace embeddings")
                
        except Exception as e:
            logger.error(f"Failed to initialize embeddings: {e}")
            raise
    
    async def add_documents(
        self,
        documents: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        Add documents to the vector database.
        
        Args:
            documents: List of document texts
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of document IDs
            
        Returns:
            List of document IDs
        """
        if not self._vectorstore:
            raise RuntimeError("Vector DB client not initialized")
        
        try:
            # Add documents to vectorstore
            doc_ids = await self._vectorstore.aadd_texts(
                texts=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"Added {len(documents)} documents to vector DB")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise
    
    async def similarity_search(
        self,
        query: str,
        k: int = 5,
        filter_metadata: Optional[Dict[str, Any]] = None,
        score_threshold: Optional[float] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """
        Perform similarity search in the vector database.
        
        Args:
            query: Search query text
            k: Number of results to return
            filter_metadata: Optional metadata filter
            score_threshold: Optional minimum similarity score
            
        Returns:
            List of (text, score, metadata) tuples
        """
        if not self._vectorstore:
            raise RuntimeError("Vector DB client not initialized")
        
        try:
            # Perform similarity search with scores
            results = await self._vectorstore.asimilarity_search_with_score(
                query=query,
                k=k,
                filter=filter_metadata
            )
            
            # Filter by score threshold if provided
            if score_threshold:
                results = [
                    (doc, score) for doc, score in results
                    if score >= score_threshold
                ]
            
            # Format results
            formatted_results = [
                (doc.page_content, score, doc.metadata)
                for doc, score in results
            ]
            
            logger.info(f"Found {len(formatted_results)} similar documents")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Similarity search failed: {e}")
            return []
    
    async def delete_documents(self, ids: List[str]) -> bool:
        """
        Delete documents from the vector database.
        
        Args:
            ids: List of document IDs to delete
            
        Returns:
            True if successful, False otherwise
        """
        if not self._index:
            raise RuntimeError("Vector DB client not initialized")
        
        try:
            # Delete vectors by IDs
            self._index.delete(ids=ids)
            
            logger.info(f"Deleted {len(ids)} documents from vector DB")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            return False
    
    async def update_document(
        self,
        doc_id: str,
        text: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update a document in the vector database.
        
        Args:
            doc_id: Document ID to update
            text: New document text
            metadata: Optional new metadata
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete existing document
            await self.delete_documents([doc_id])
            
            # Add updated document
            await self.add_documents(
                documents=[text],
                metadatas=[metadata] if metadata else None,
                ids=[doc_id]
            )
            
            logger.info(f"Updated document: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update document: {e}")
            return False
    
    async def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the vector index.
        
        Returns:
            Dictionary with index statistics
        """
        if not self._index:
            raise RuntimeError("Vector DB client not initialized")
        
        try:
            stats = self._index.describe_index_stats()
            
            return {
                "total_vector_count": stats.total_vector_count,
                "dimension": stats.dimension,
                "index_fullness": stats.index_fullness,
                "namespaces": dict(stats.namespaces) if stats.namespaces else {}
            }
            
        except Exception as e:
            logger.error(f"Failed to get index stats: {e}")
            return {}
    
    async def create_retriever(
        self,
        search_type: str = "similarity",
        search_kwargs: Optional[Dict[str, Any]] = None
    ):
        """
        Create a LangChain retriever for RAG applications.
        
        Args:
            search_type: Type of search ("similarity", "mmr")
            search_kwargs: Additional search parameters
            
        Returns:
            LangChain retriever object
        """
        if not self._vectorstore:
            raise RuntimeError("Vector DB client not initialized")
        
        search_kwargs = search_kwargs or {"k": 5}
        
        return self._vectorstore.as_retriever(
            search_type=search_type,
            search_kwargs=search_kwargs
        )
    
    async def health_check(self) -> bool:
        """
        Check vector database connectivity and health.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Check if we can get index stats
            stats = await self.get_index_stats()
            return "total_vector_count" in stats
            
        except Exception as e:
            logger.error(f"Vector DB health check failed: {e}")
            return False


# Convenience functions for common vector DB operations

async def create_vector_client(index_name: str = "agent-template") -> VectorDBClient:
    """
    Create and initialize a vector database client.
    
    Args:
        index_name: Name of the Pinecone index
        
    Returns:
        Initialized vector DB client
    """
    client = VectorDBClient(index_name)
    await client.initialize()
    return client


async def index_documents_from_texts(
    texts: List[str],
    metadatas: Optional[List[Dict[str, Any]]] = None,
    index_name: str = "agent-template"
) -> bool:
    """
    Index a batch of documents in the vector database.
    
    Args:
        texts: List of document texts
        metadatas: Optional list of metadata dictionaries
        index_name: Name of the Pinecone index
        
    Returns:
        True if successful, False otherwise
    """
    try:
        client = await create_vector_client(index_name)
        await client.add_documents(texts, metadatas)
        
        logger.info(f"Successfully indexed {len(texts)} documents")
        return True
        
    except Exception as e:
        logger.error(f"Failed to index documents: {e}")
        return False


async def search_documents(
    query: str,
    k: int = 5,
    index_name: str = "agent-template",
    filter_metadata: Optional[Dict[str, Any]] = None
) -> List[Tuple[str, float, Dict[str, Any]]]:
    """
    Search for similar documents in the vector database.
    
    Args:
        query: Search query
        k: Number of results to return
        index_name: Name of the Pinecone index
        filter_metadata: Optional metadata filter
        
    Returns:
        List of (text, score, metadata) tuples
    """
    try:
        client = await create_vector_client(index_name)
        results = await client.similarity_search(
            query=query,
            k=k,
            filter_metadata=filter_metadata
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Document search failed: {e}")
        return []
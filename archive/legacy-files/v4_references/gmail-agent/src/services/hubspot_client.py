"""
HubSpot CRM API client.

This module provides a thread-safe HubSpot client for CRM operations
including contacts, deals, and company management.
"""

import logging
from typing import Optional, Dict, Any, List
import httpx
from hubspot import HubSpot
from hubspot.crm.contacts import SimplePublicObjectInput, ApiException

from src.config import settings

logger = logging.getLogger(__name__)


class HubSpotClient:
    """
    HubSpot CRM client with comprehensive contact and deal management.
    
    Provides async-compatible methods for HubSpot API operations.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or settings.hubspot_api_key
        if not self.api_key:
            raise ValueError("HubSpot API key is required")
        
        self.client = HubSpot(api_key=self.api_key)
        self._http_client = httpx.AsyncClient()
    
    async def close(self):
        """Close the HTTP client."""
        await self._http_client.aclose()
    
    # --- Contact Operations ---
    
    async def create_contact(self, contact_data: Dict[str, Any]) -> Optional[str]:
        """
        Create a new contact in HubSpot.
        
        Args:
            contact_data: Dictionary of contact properties
            
        Returns:
            Contact ID if successful, None otherwise
        """
        try:
            # Prepare contact properties
            properties = self._prepare_contact_properties(contact_data)
            
            # Create contact input
            simple_public_object_input = SimplePublicObjectInput(properties=properties)
            
            # Create contact
            api_response = self.client.crm.contacts.basic_api.create(
                simple_public_object_input=simple_public_object_input
            )
            
            contact_id = api_response.id
            logger.info(f"Created HubSpot contact: {contact_id}")
            
            return contact_id
            
        except ApiException as e:
            logger.error(f"HubSpot API error creating contact: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to create contact: {e}")
            return None
    
    async def get_contact_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Get a contact by email address.
        
        Args:
            email: Contact email address
            
        Returns:
            Contact data dictionary or None
        """
        try:
            # Search for contact by email
            api_response = self.client.crm.contacts.basic_api.get_by_id(
                contact_id=email,
                id_property="email"
            )
            
            contact_data = {
                'id': api_response.id,
                'properties': api_response.properties,
                'created_at': api_response.created_at,
                'updated_at': api_response.updated_at
            }
            
            logger.info(f"Retrieved contact by email: {email}")
            return contact_data
            
        except ApiException as e:
            if e.status == 404:
                logger.info(f"Contact not found for email: {email}")
                return None
            logger.error(f"HubSpot API error getting contact: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to get contact by email: {e}")
            return None
    
    async def update_contact(self, contact_id: str, contact_data: Dict[str, Any]) -> bool:
        """
        Update an existing contact.
        
        Args:
            contact_id: HubSpot contact ID
            contact_data: Dictionary of contact properties to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Prepare contact properties
            properties = self._prepare_contact_properties(contact_data)
            
            # Create update input
            simple_public_object_input = SimplePublicObjectInput(properties=properties)
            
            # Update contact
            self.client.crm.contacts.basic_api.update(
                contact_id=contact_id,
                simple_public_object_input=simple_public_object_input
            )
            
            logger.info(f"Updated HubSpot contact: {contact_id}")
            return True
            
        except ApiException as e:
            logger.error(f"HubSpot API error updating contact: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to update contact: {e}")
            return False
    
    def _prepare_contact_properties(self, contact_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Prepare contact properties for HubSpot API.
        
        Args:
            contact_data: Raw contact data
            
        Returns:
            Dictionary of HubSpot-formatted properties
        """
        # Map common fields to HubSpot properties
        field_mapping = {
            'email': 'email',
            'first_name': 'firstname',
            'last_name': 'lastname',
            'company': 'company',
            'phone': 'phone',
            'website': 'website',
            'job_title': 'jobtitle',
            'lead_score': 'hubspotscore',
            'lifecycle_stage': 'lifecyclestage',
            'lead_status': 'leadstatus'
        }
        
        properties = {}
        
        for key, value in contact_data.items():
            if key in field_mapping and value is not None:
                # Convert all values to strings as required by HubSpot
                properties[field_mapping[key]] = str(value)
            elif key.startswith('hs_') and value is not None:
                # Direct HubSpot property
                properties[key] = str(value)
        
        return properties
    
    # --- Deal Operations ---
    
    async def create_deal(self, deal_data: Dict[str, Any]) -> Optional[str]:
        """
        Create a new deal in HubSpot.
        
        Args:
            deal_data: Dictionary of deal properties
            
        Returns:
            Deal ID if successful, None otherwise
        """
        try:
            # Prepare deal properties
            properties = self._prepare_deal_properties(deal_data)
            
            # Create deal input
            simple_public_object_input = SimplePublicObjectInput(properties=properties)
            
            # Create deal
            api_response = self.client.crm.deals.basic_api.create(
                simple_public_object_input=simple_public_object_input
            )
            
            deal_id = api_response.id
            logger.info(f"Created HubSpot deal: {deal_id}")
            
            return deal_id
            
        except ApiException as e:
            logger.error(f"HubSpot API error creating deal: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to create deal: {e}")
            return None
    
    def _prepare_deal_properties(self, deal_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Prepare deal properties for HubSpot API.
        
        Args:
            deal_data: Raw deal data
            
        Returns:
            Dictionary of HubSpot-formatted properties
        """
        # Map common fields to HubSpot properties
        field_mapping = {
            'deal_name': 'dealname',
            'amount': 'amount',
            'close_date': 'closedate',
            'deal_stage': 'dealstage',
            'pipeline': 'pipeline',
            'deal_type': 'dealtype',
            'description': 'description'
        }
        
        properties = {}
        
        for key, value in deal_data.items():
            if key in field_mapping and value is not None:
                properties[field_mapping[key]] = str(value)
            elif key.startswith('hs_') and value is not None:
                properties[key] = str(value)
        
        return properties
    
    # --- Company Operations ---
    
    async def create_company(self, company_data: Dict[str, Any]) -> Optional[str]:
        """
        Create a new company in HubSpot.
        
        Args:
            company_data: Dictionary of company properties
            
        Returns:
            Company ID if successful, None otherwise
        """
        try:
            # Prepare company properties
            properties = self._prepare_company_properties(company_data)
            
            # Create company input
            simple_public_object_input = SimplePublicObjectInput(properties=properties)
            
            # Create company
            api_response = self.client.crm.companies.basic_api.create(
                simple_public_object_input=simple_public_object_input
            )
            
            company_id = api_response.id
            logger.info(f"Created HubSpot company: {company_id}")
            
            return company_id
            
        except ApiException as e:
            logger.error(f"HubSpot API error creating company: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to create company: {e}")
            return None
    
    def _prepare_company_properties(self, company_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Prepare company properties for HubSpot API.
        
        Args:
            company_data: Raw company data
            
        Returns:
            Dictionary of HubSpot-formatted properties
        """
        # Map common fields to HubSpot properties
        field_mapping = {
            'name': 'name',
            'domain': 'domain',
            'industry': 'industry',
            'website': 'website',
            'phone': 'phone',
            'city': 'city',
            'state': 'state',
            'country': 'country',
            'description': 'description',
            'number_of_employees': 'numberofemployees',
            'annual_revenue': 'annualrevenue'
        }
        
        properties = {}
        
        for key, value in company_data.items():
            if key in field_mapping and value is not None:
                properties[field_mapping[key]] = str(value)
            elif key.startswith('hs_') and value is not None:
                properties[key] = str(value)
        
        return properties
    
    # --- Association Operations ---
    
    async def associate_contact_with_company(
        self,
        contact_id: str,
        company_id: str
    ) -> bool:
        """
        Associate a contact with a company.
        
        Args:
            contact_id: HubSpot contact ID
            company_id: HubSpot company ID
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create association
            self.client.crm.contacts.associations_api.create(
                contact_id=contact_id,
                to_object_type="companies",
                to_object_id=company_id,
                association_type="contact_to_company"
            )
            
            logger.info(f"Associated contact {contact_id} with company {company_id}")
            return True
            
        except ApiException as e:
            logger.error(f"HubSpot API error creating association: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to create association: {e}")
            return False
    
    # --- Search Operations ---
    
    async def search_contacts(
        self,
        search_query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Search for contacts by query.
        
        Args:
            search_query: Search query string
            limit: Maximum number of results
            
        Returns:
            List of contact dictionaries
        """
        try:
            # Use the async HTTP client for search
            url = "https://api.hubapi.com/crm/v3/objects/contacts/search"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            search_body = {
                "query": search_query,
                "limit": limit,
                "properties": ["email", "firstname", "lastname", "company"]
            }
            
            response = await self._http_client.post(url, json=search_body, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            results = data.get("results", [])
            
            logger.info(f"Found {len(results)} contacts for query: {search_query}")
            return results
            
        except httpx.HTTPError as e:
            logger.error(f"HTTP error searching contacts: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to search contacts: {e}")
            return []
    
    # --- Utility Methods ---
    
    async def health_check(self) -> bool:
        """
        Check HubSpot API connectivity.
        
        Returns:
            True if API is accessible, False otherwise
        """
        try:
            # Try to get account info
            self.client.crm.owners.basic_api.get_page()
            logger.info("HubSpot health check passed")
            return True
            
        except Exception as e:
            logger.error(f"HubSpot health check failed: {e}")
            return False


# Convenience functions for common HubSpot operations

async def create_hubspot_client() -> HubSpotClient:
    """
    Create a HubSpot client instance.
    
    Returns:
        HubSpot client
    """
    return HubSpotClient()


async def upsert_contact(client: HubSpotClient, contact_data: Dict[str, Any]) -> Optional[str]:
    """
    Create or update a contact in HubSpot.
    
    Args:
        client: HubSpot client instance
        contact_data: Contact data dictionary
        
    Returns:
        Contact ID if successful, None otherwise
    """
    email = contact_data.get('email')
    if not email:
        logger.error("Email is required for contact upsert")
        return None
    
    # Try to find existing contact
    existing_contact = await client.get_contact_by_email(email)
    
    if existing_contact:
        # Update existing contact
        contact_id = existing_contact['id']
        success = await client.update_contact(contact_id, contact_data)
        return contact_id if success else None
    else:
        # Create new contact
        return await client.create_contact(contact_data)
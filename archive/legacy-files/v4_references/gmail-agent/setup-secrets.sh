#!/bin/bash

# Secret verification script for Email Conversation Agent v4

set -e

# Required secrets
required_secrets=(
    "GOOGLE_API_KEY"
    "HUBSPOT_API_KEY"
    "POSTGRES_PASSWORD"
    "PINECONE_API_KEY"
    "SUPABASE_SERVICE_ROLE_KEY"
    "SUPABASE_URL"
    "LANGCHAIN_API_KEY"
)

echo "🔐 Verifying required secrets in GCP Secret Manager"

for secret in "${required_secrets[@]}"; do
    if gcloud secrets describe $secret >/dev/null 2>&1; then
        echo "✅ $secret exists"
    else
        echo "❌ ERROR: Secret $secret not found in GCP Secret Manager"
        echo "Please create this secret using:"
        echo "  gcloud secrets create $secret --data-file=-"
        exit 1
    fi
done

echo "✅ All required secrets are properly configured in GCP Secret Manager"
#!/bin/bash

# Deployment script for Agent Template - G<PERSON> Enhanced
# Deploys both API and worker components to Google Cloud Platform

set -e

# Configuration
PROJECT_ID="tkcgroup-v4"
REGION="us-west1"
SERVICE_NAME="email-conversation-agent-v4"
API_SERVICE="${SERVICE_NAME}-api"
WORKER_SERVICE="${SERVICE_NAME}-worker"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploying Email Conversation Agent v4 to Google Cloud Platform${NC}"
echo -e "${BLUE}Project: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Region: ${REGION}${NC}"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK.${NC}"
    exit 1
fi

# Check if logged in to gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not logged in to gcloud. Please run 'gcloud auth login'${NC}"
    exit 1
fi

# Set the project
echo -e "${YELLOW}📋 Setting project to ${PROJECT_ID}${NC}"
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required Google Cloud APIs${NC}"
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable sql-component.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable pubsub.googleapis.com

# Create secrets if they don't exist
echo -e "${YELLOW}🔐 Checking and creating secrets${NC}"

create_secret_if_not_exists() {
    local secret_name=$1
    local secret_value=$2
    
    if ! gcloud secrets describe ${secret_name} >/dev/null 2>&1; then
        echo "Creating secret: ${secret_name}"
        echo -n "${secret_value}" | gcloud secrets create ${secret_name} --data-file=-
    else
        echo "Secret ${secret_name} already exists"
    fi
}

# Production-only deployment - using GCP Secret Manager exclusively
echo -e "${GREEN}📄 Production deployment - using GCP Secret Manager for all secrets${NC}"

# Run secret setup
if [ -f "setup-secrets.sh" ]; then
    echo -e "${YELLOW}🔐 Setting up secrets${NC}"
    chmod +x setup-secrets.sh
    ./setup-secrets.sh
else
    echo -e "${RED}❌ setup-secrets.sh not found${NC}"
    exit 1
fi

# Grant IAM permissions for the Cloud Run service account to access secrets
grant_secret_access() {
    local secret_name=$1
    local service_account="${PROJECT_NUMBER}-<EMAIL>"
    
    echo -e "${YELLOW}🔑 Granting Secret Accessor role for ${secret_name} to ${service_account}${NC}"
    gcloud secrets add-iam-policy-binding ${secret_name} \
        --member="serviceAccount:${service_account}" \
        --role="roles/secretmanager.secretAccessor"
}

# Get the project number
PROJECT_NUMBER=$(gcloud projects describe ${PROJECT_ID} --format='value(projectNumber)')

# Grant access to all required secrets
grant_secret_access "GOOGLE_API_KEY"
grant_secret_access "GCP_SERVICE_ACCOUNT_KEY"
grant_secret_access "HUBSPOT_API_KEY"
grant_secret_access "POSTGRES_PASSWORD"
grant_secret_access "PINECONE_API_KEY"
grant_secret_access "SUPABASE_SERVICE_ROLE_KEY"
grant_secret_access "SUPABASE_URL"
grant_secret_access "LANGCHAIN_API_KEY"

# Build and push the Docker image
echo -e "${YELLOW}🔨 Building and pushing Docker image...${NC}"
# Create a temporary Dockerfile with the correct name
cp Dockerfile.api Dockerfile
# Build with the default Dockerfile name
gcloud builds submit --tag "gcr.io/${PROJECT_ID}/${API_SERVICE}" .
# Clean up
rm -f Dockerfile

# Then deploy the image to Cloud Run
echo -e "${YELLOW}🚀 Deploying to Cloud Run...${NC}"
gcloud run deploy ${API_SERVICE} \
  --image "gcr.io/${PROJECT_ID}/${API_SERVICE}" \
  --region ${REGION} \
  --platform managed \
  --allow-unauthenticated \
  --set-env-vars "GCP_PROJECT_ID=${PROJECT_ID}" \
  --set-env-vars "GCP_REGION=${REGION}" \
  --set-env-vars "ENVIRONMENT=production" \
  --set-env-vars "LOG_LEVEL=INFO" \
  --set-secrets "GOOGLE_API_KEY=GOOGLE_API_KEY:latest" \
  --set-secrets "GCP_SERVICE_ACCOUNT_KEY=GCP_SERVICE_ACCOUNT_KEY:latest" \
  --set-secrets "SUPABASE_URL=SUPABASE_URL:latest" \
  --set-secrets "SUPABASE_SERVICE_ROLE_KEY=SUPABASE_SERVICE_ROLE_KEY:latest" \
  --set-secrets "LANGCHAIN_API_KEY=LANGCHAIN_API_KEY:latest" \
  --set-secrets "PINECONE_API_KEY=PINECONE_API_KEY:latest" \
  --set-secrets "HUBSPOT_API_KEY=HUBSPOT_API_KEY:latest"

# Get the API service URL
API_URL=$(gcloud run services describe ${API_SERVICE} --region=${REGION} --format="value(status.url)")
echo -e "${GREEN}✅ API Service deployed successfully!${NC}"
echo -e "${GREEN}📍 API URL: ${API_URL}${NC}"

# Create Pub/Sub topic for Celery if it doesn't exist
echo -e "${YELLOW}📡 Setting up Pub/Sub for Celery${NC}"
if ! gcloud pubsub topics describe celery-tasks >/dev/null 2>&1; then
    gcloud pubsub topics create celery-tasks
    echo "Created Pub/Sub topic: celery-tasks"
else
    echo "Pub/Sub topic celery-tasks already exists"
fi

# Create subscription for workers
if ! gcloud pubsub subscriptions describe celery-worker-subscription >/dev/null 2>&1; then
    gcloud pubsub subscriptions create celery-worker-subscription --topic=celery-tasks
    echo "Created Pub/Sub subscription: celery-worker-subscription"
else
    echo "Pub/Sub subscription celery-worker-subscription already exists"
fi

# Optional: Deploy worker to GKE (commented out by default)
# echo -e "${YELLOW}🐛 Deploying worker service to GKE${NC}"
# 
# # Check if GKE cluster exists
# if ! gcloud container clusters describe agent-workers --region=${REGION} >/dev/null 2>&1; then
#     echo "Creating GKE cluster for workers..."
#     gcloud container clusters create agent-workers \
#         --region=${REGION} \
#         --num-nodes=1 \
#         --machine-type=e2-standard-2 \
#         --enable-autorepair \
#         --enable-autoupgrade \
#         --enable-autoscaling \
#         --min-nodes=0 \
#         --max-nodes=5
# fi
# 
# # Build and push worker image
# gcloud builds submit --tag gcr.io/${PROJECT_ID}/${WORKER_SERVICE} --dockerfile Dockerfile.worker
# 
# # Deploy to GKE (you would need Kubernetes manifests for this)
# echo "Worker image built and pushed to gcr.io/${PROJECT_ID}/${WORKER_SERVICE}"

# Test the deployment
echo -e "${YELLOW}🧪 Testing deployment${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${API_URL}/)

if [ ${HTTP_STATUS} -eq 200 ]; then
    echo -e "${GREEN}✅ Health check passed! API is responding.${NC}"
else
    echo -e "${RED}❌ Health check failed! HTTP status: ${HTTP_STATUS}${NC}"
fi

# Display summary
echo -e "${BLUE}🎉 Deployment Summary${NC}"
echo -e "${GREEN}✅ API Service: ${API_URL}${NC}"
echo -e "${GREEN}✅ Pub/Sub Topic: celery-tasks${NC}"
echo -e "${GREEN}✅ Region: ${REGION}${NC}"
echo -e "${GREEN}✅ Project: ${PROJECT_ID}${NC}"

echo -e "${BLUE}📚 Useful Commands:${NC}"
echo -e "${YELLOW}View logs:${NC} gcloud run services logs read ${API_SERVICE} --region=${REGION}"
echo -e "${YELLOW}Update service:${NC} gcloud run services update ${API_SERVICE} --region=${REGION}"
echo -e "${YELLOW}Delete service:${NC} gcloud run services delete ${API_SERVICE} --region=${REGION}"

echo -e "${GREEN}🚀 Deployment completed successfully!${NC}"
# Google Cloud Build configuration for Agent Template
# Defines CI/CD pipeline for building and deploying services

steps:
  # Build the API container
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-f', 'Dockerfile.api', '-t', 'gcr.io/$PROJECT_ID/agent-template-api:$BUILD_ID', '.']
    id: 'build-api'

  # Build the worker container  
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-f', 'Dockerfile.worker', '-t', 'gcr.io/$PROJECT_ID/agent-template-worker:$BUILD_ID', '.']
    id: 'build-worker'

  # Push API container to registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/agent-template-api:$BUILD_ID']
    id: 'push-api'
    waitFor: ['build-api']

  # Push worker container to registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/agent-template-worker:$BUILD_ID']
    id: 'push-worker'
    waitFor: ['build-worker']

  # Deploy API to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'agent-template-api'
      - '--image=gcr.io/$PROJECT_ID/agent-template-api:$BUILD_ID'
      - '--region=us-west1'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=2Gi'
      - '--cpu=2'
      - '--max-instances=10'
    id: 'deploy-api'
    waitFor: ['push-api']

  # Deploy worker to GKE (optional, can also be deployed separately)
  # - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  #   entrypoint: 'gcloud'
  #   args:
  #     - 'container'
  #     - 'clusters'
  #     - 'get-credentials'
  #     - 'agent-workers'
  #     - '--region=us-west1'
  #   id: 'get-gke-credentials'
  #   waitFor: ['push-worker']

options:
  machineType: 'E2_HIGHCPU_8'
  
substitutions:
  _REGION: 'us-west1'

tags:
  - 'agent-template'
  - 'api'
  - 'worker'
# Agent Template - <PERSON><PERSON> Enhanced

A production-ready AI agent template built for Google Cloud Platform with LangGraph workflows, FastAPI, and enterprise features.

## Overview

This template provides a comprehensive foundation for building AI agents that can be deployed to Google Cloud Platform. It includes:

- **FastAPI API** for HTTP endpoints and webhooks
- **LangGraph workflows** for complex agent logic
- **Celery workers** for background task processing
- **Redis checkpointing** for conversation memory
- **Vector database integration** with Pinecone
- **Multi-model support** with intelligent routing
- **Comprehensive monitoring** and error handling
- **Production deployment** with Docker and Cloud Run

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI API   │    │ Celery Workers  │    │  Vector DB      │
│   (Cloud Run)   │    │   (GKE/GCE)     │    │  (Pinecone)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ├── HTTP Requests        ├── Background Tasks    ├── RAG Queries
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LangGraph     │    │   Pub/Sub       │    │   Cloud SQL     │
│   Workflows     │    │  (Message Bus)  │    │ (PostgreSQL)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ├── Agent Logic         ├── Task Queue          ├── Data Storage
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Model Router   │    │ Redis Memory    │    │ External APIs   │
│ (Gemini Models) │    │ (Checkpointer)  │    │ (HubSpot, etc.) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.11+
- Google Cloud SDK (`gcloud`)
- Docker (for local development)
- Redis (for checkpointing)
- PostgreSQL (for data storage)

### Local Development

1. **Clone and setup**:
   ```bash
   cd apps/agents/agent-template-gcp
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure environment**:
   ```bash
   cp .env.local .env
   # Edit .env with your configuration
   ```

3. **Start dependencies**:
   ```bash
   # Start Redis (for checkpointing)
   redis-server
   
   # Start PostgreSQL (or use cloud instance)
   # Configure connection in .env
   ```

4. **Run the API**:
   ```bash
   uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
   ```

5. **Start Celery worker** (optional, for background tasks):
   ```bash
   celery -A src.async_tasks.celery_app worker --loglevel=info
   ```

### Production Deployment

1. **Configure secrets**:
   ```bash
   # Set up your .env.local file with production values
   cp .env.local.example .env.local
   ```

2. **Deploy to GCP**:
   ```bash
   ./deploy.sh
   ```

The deployment script will:
- Enable required GCP APIs
- Create secrets in Secret Manager
- Deploy API to Cloud Run
- Set up Pub/Sub for Celery
- Configure monitoring and logging

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GCP_PROJECT_ID` | Google Cloud Project ID | ✅ |
| `GCP_REGION` | Deployment region | ✅ |
| `GOOGLE_API_KEY` | Gemini API key | ✅ |
| `HUBSPOT_API_KEY` | HubSpot CRM API key | ❌ |
| `POSTGRES_HOST` | Database host | ✅ |
| `POSTGRES_USER` | Database user | ✅ |
| `POSTGRES_PASSWORD` | Database password | ✅ |
| `POSTGRES_DB` | Database name | ✅ |
| `REDIS_HOST` | Redis host for checkpointing | ✅ |
| `PINECONE_API_KEY` | Pinecone vector DB API key | ❌ |
| `CELERY_BROKER_URL` | Celery message broker URL | ✅ |

### Model Configuration

The template includes intelligent model routing that automatically selects the best Gemini model based on:

- **Task complexity** (simple, moderate, complex, critical)
- **Performance requirements** (speed vs. quality)
- **Cost optimization** preferences
- **Context length** requirements

Available models:
- **Gemini 2.0 Flash** - Fastest, good balance
- **Gemini 1.5 Flash** - Fast, efficient
- **Gemini 1.5 Pro** - Most capable, slower
- **Gemini 1.5 Flash-8B** - Lightweight, fastest

## API Endpoints

### Core Endpoints

- `GET /` - Health check
- `POST /v1/webhook` - Generic webhook handler
- `POST /v1/process-task` - Process agent task
- `POST /v1/ai/generate` - AI generation service
- `GET /v1/status` - Detailed status

### Task Processing

```bash
# Example task processing
curl -X POST "http://localhost:8000/v1/process-task" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "task-123",
    "task_type": "analysis",
    "input_data": {
      "text": "Analyze this customer feedback..."
    }
  }'
```

### AI Generation

```bash
# Example AI generation
curl -X POST "http://localhost:8000/v1/ai/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a professional email response",
    "task_type": "generation",
    "priority": "quality"
  }'
```

## Agent Workflow

The template uses LangGraph to define agent workflows with the following nodes:

1. **Initialize Task** - Set up initial state and validate input
2. **Classify Intent** - Determine user intent and requirements
3. **Analyze Content** - Perform detailed content analysis (optional)
4. **Generate Response** - Create appropriate response
5. **Finalize Task** - Package results and clean up

### Workflow Routing

The agent uses conditional routing to optimize processing:

- **Simple tasks** → Direct response generation
- **Complex tasks** → Full analysis pipeline
- **Failed tasks** → Retry logic with exponential backoff
- **Conversation tasks** → Memory-aware processing

## Services Integration

### Vector Database (Pinecone)

```python
from src.services.vector_db_client import create_vector_client

# Initialize vector database
vector_client = await create_vector_client("knowledge-base")

# Search for relevant information
results = await vector_client.similarity_search(
    query="customer support process",
    k=5
)
```

### Database (PostgreSQL)

```python
from src.services.database_client import DatabaseClient

# Initialize database client
db_client = DatabaseClient()
await db_client.initialize()

# Execute queries
results = await db_client.execute_query(
    "SELECT * FROM tasks WHERE status = $1",
    "completed",
    fetch="all"
)
```

### CRM Integration (HubSpot)

```python
from src.services.hubspot_client import create_hubspot_client

# Initialize HubSpot client
hubspot_client = await create_hubspot_client()

# Create or update contact
contact_id = await hubspot_client.create_contact({
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
})
```

## Background Tasks

The template supports asynchronous background processing with Celery:

### Task Types

- **Document Processing** - Batch indexing and analysis
- **Report Generation** - Automated report creation
- **Data Cleanup** - Maintenance and optimization
- **AI Processing** - Batch AI operations

### Example Usage

```python
from src.async_tasks.tasks import batch_process_documents

# Queue background task
task = batch_process_documents.delay(
    documents=[{"text": "Document content..."}],
    operation="index"
)

# Check task status
result = task.get(timeout=30)
```

## Monitoring and Observability

### Health Checks

- API health endpoint: `GET /`
- Detailed status: `GET /v1/status`
- Service dependencies status
- Performance metrics

### Logging

The template includes structured logging with:

- Request/response logging
- Error tracking with stack traces
- Performance monitoring
- Security event logging

### Tracing

Optional LangSmith integration for:

- Workflow tracing
- Model performance monitoring
- Token usage tracking
- Error analysis

## Security

### Authentication

- Service account authentication for GCP services
- API key management through Secret Manager
- OAuth2 support for external integrations

### Data Protection

- Input validation and sanitization
- Secure secret management
- Data encryption in transit and at rest
- Audit logging

### Network Security

- VPC configuration for GCP resources
- Private service connections
- Firewall rules and security groups

## Testing

### Unit Tests

```bash
# Run unit tests
pytest tests/unit/

# Run with coverage
pytest tests/unit/ --cov=src --cov-report=html
```

### Integration Tests

```bash
# Run integration tests
pytest tests/integration/

# Test specific service
pytest tests/integration/test_database_client.py
```

### Load Testing

```bash
# Load test the API
locust -f tests/load/locustfile.py --host=http://localhost:8000
```

## Customization

### Adding New Agent Types

1. Create new agent in `src/agents/new_agent/`
2. Define state models in `state.py`
3. Implement workflow nodes in `nodes.py`
4. Create graph definition in `graph.py`
5. Register in main application

### Adding New Tools

1. Create tool functions in `src/agents/template_agent/tools.py`
2. Use the `@tool` decorator
3. Add to appropriate tool collections
4. Update agent workflows to use new tools

### Custom Model Routing

1. Extend `ModelSelectionCriteria` in `src/model_router.py`
2. Add new model configurations
3. Implement custom selection logic
4. Update model characteristics

## Troubleshooting

### Common Issues

1. **Secret Manager Access**:
   ```bash
   # Check permissions
   gcloud projects get-iam-policy PROJECT_ID
   
   # Grant access to service account
   gcloud projects add-iam-policy-binding PROJECT_ID \
     --member="serviceAccount:SERVICE_ACCOUNT" \
     --role="roles/secretmanager.secretAccessor"
   ```

2. **Database Connection**:
   ```bash
   # Test connection
   psql -h HOST -U USER -d DATABASE
   
   # Check Cloud SQL proxy
   cloud_sql_proxy -instances=PROJECT:REGION:INSTANCE=tcp:5432
   ```

3. **Redis Connection**:
   ```bash
   # Test Redis
   redis-cli ping
   
   # Check Memorystore connection
   telnet REDIS_HOST 6379
   ```

### Debugging

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
uvicorn src.main:app --reload
```

Check service logs:
```bash
# Cloud Run logs
gcloud run services logs read SERVICE_NAME --region=REGION

# Celery worker logs
tail -f celery.log
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Code Style

- Use Black for code formatting
- Follow PEP 8 guidelines
- Add type hints
- Include docstrings
- Write comprehensive tests

## License

This template is part of the TKC Group agent ecosystem and follows the project's licensing terms.

## Support

For support and questions:

- Check the documentation in `docs/`
- Review existing issues
- Create new issues for bugs or feature requests
- Contact the development team

---

## Next Steps

After setting up the template:

1. **Customize the agent logic** for your specific use case
2. **Configure external integrations** (CRM, databases, etc.)
3. **Set up monitoring and alerting**
4. **Implement custom tools** for your domain
5. **Deploy to production** and monitor performance

This template provides a solid foundation for building production-ready AI agents. Customize it to fit your specific requirements and deploy with confidence!

---

## Current State & Migration Strategy

### 🎉 **UPDATE: GCP Environment Assessment Complete**

**Good News!** After running the comprehensive GCP audit, your environment is **clean and well-organized**:

#### Current Production State (as of Dec 18, 2024):
- **✅ Clean Environment**: No "junk" or orphaned resources found
- **✅ Active Agents**: 4 production agents serving traffic
- **✅ Well-Organized**: Clear naming conventions and proper structure
- **✅ Cost-Efficient**: No unused compute instances or databases
- **✅ Secure**: Proper service account structure and secret management

#### Active Production Agents:
1. **email-classification-agent** ✅ Running
2. **email-conversation-agent** ✅ Running  
3. **form-processing-agent** ✅ Running
4. **form-processing-agent-v2** ✅ Running

#### Easy Cleanup Opportunities:
- **3 disabled services** can be safely removed (low risk)
- **Email classification consolidation** (merge v1 and v2)
- **Form processing upgrade** (retire v1 in favor of v2)

### 🚀 **Migration Progress: Form Processing Agent v3**

**Status: In Progress** - Successfully refactoring form-processing-agent-v2 using the new template

#### ✅ Completed:
1. **GCP Audit** - Environment mapped and understood
2. **Template Creation** - Production-ready template with all enterprise features
3. **Safe Backup** - All existing agents backed up
4. **Schema Migration** - All form models and enums migrated
5. **Service Migration** - Enhanced enrichment service with better error handling
6. **Workflow Creation** - Form-specific LangGraph nodes with same business logic

#### 🔄 In Progress:
- **API Endpoint Updates** - Adapting endpoints to use new workflow
- **Local Testing** - Validating functionality matches original

#### 📋 Next Steps:
1. Complete API endpoint migration
2. Test locally and validate functionality
3. Compare performance with original v2
4. Document migration results

### Current GCP Environment Assessment (For Reference)

The audit revealed your GCP environment is well-maintained, but here's how to run the assessment for future reference:

#### Step 1: Audit Your Current GCP State

Run the comprehensive audit script to understand what's currently deployed:

```bash
# From the project root directory
./audit-gcp-state.sh
```

This script will generate a complete audit of your GCP environment including:

- **Compute Resources**: Cloud Run services, Cloud Functions, Compute Engine instances, GKE clusters
- **Storage & Databases**: Cloud SQL, Cloud Storage buckets, Firestore
- **Networking**: VPC networks, load balancers, Pub/Sub topics
- **Security**: Service accounts, IAM policies, secrets, API keys
- **Operations**: Scheduled jobs, task queues, monitoring
- **Agent Analysis**: Identification of current agent deployments

#### Step 2: Review Audit Results

The audit creates a timestamped directory with detailed reports:

```bash
# Example output directory
gcp-audit-********-143022/
├── resource-summary.txt      # High-level overview
├── agent-analysis.txt        # Current agent deployments  
├── service-accounts.txt      # All service accounts
├── iam-policy.json          # Complete IAM policy
├── cloud-run-services.txt   # All Cloud Run services
├── cloud-functions.txt      # All Cloud Functions
├── pubsub-topics.txt        # Pub/Sub topics
├── secrets.txt              # Secret Manager secrets
└── CLEANUP_CHECKLIST.md     # Step-by-step cleanup guide
```

**Key files to review first:**
1. `resource-summary.txt` - Get the big picture
2. `agent-analysis.txt` - Understand current agent deployments
3. `CLEANUP_CHECKLIST.md` - Follow the phased cleanup approach

### Migration Strategy: Local-First Approach

Instead of deploying new services immediately, follow this safe migration strategy:

#### Phase 1: Local Development & Testing (SAFE - No GCP changes)

1. **Backup Everything**:
   ```bash
   # Create backups of current agent code
   mkdir -p backups/$(date +%Y%m%d)
   cp -r apps/agents/* backups/$(date +%Y%m%d)/
   ```

2. **Refactor Agents Locally**:
   - Use this template as the foundation
   - Copy existing agent logic into the new structure
   - Test locally with development configurations
   - Validate functionality before any deployment

3. **Local Testing Setup**:
   ```bash
   # Set up local development environment
   cd apps/agents/agent-template-gcp
   cp .env.example .env.local
   # Configure with local/development values only
   
   # Test locally
   uvicorn src.main:app --reload --port 8000
   ```

#### Phase 2: Development Environment (LOW RISK)

1. **Create Isolated Development Resources**:
   - Deploy refactored agents to development-only GCP resources
   - Use separate project or clearly named dev resources
   - Test end-to-end functionality

2. **Validation Checklist**:
   - [ ] Agent workflows execute correctly
   - [ ] External integrations work (APIs, databases)
   - [ ] Error handling and retry logic function
   - [ ] Performance meets requirements
   - [ ] Security and authentication work

#### Phase 3: Gradual Production Migration (CONTROLLED RISK)

1. **Side-by-Side Deployment**:
   - Deploy new agent versions alongside existing ones
   - Use feature flags or traffic splitting
   - Monitor both versions simultaneously

2. **Traffic Migration**:
   - Start with 10% traffic to new agents
   - Gradually increase as confidence builds
   - Maintain rollback capability

3. **Decommission Old Services**:
   - Only after new agents are proven stable
   - Follow the cleanup checklist systematically
   - Archive configurations before deletion

### Current Agent Inventory

Based on your `apps/agents` directory, you currently have these agents to refactor:

```
apps/agents/
├── aegis-agentic-intelligence-system/    # Next.js agent (different architecture)
├── email_classification_agent/           # FastAPI + email classification
├── email_classification_service/         # Cloud Run job variant
├── email_conversation_agent/             # FastAPI + LangGraph + Gmail
├── form_processing_agent/                # Basic form processing
└── form_processing_agent_v2/             # Enhanced form processing
```

#### Refactoring Priority (Recommended Order)

1. **Start with `form_processing_agent_v2`** (Most straightforward):
   - Already has good FastAPI structure
   - Clear business logic
   - Fewer external dependencies

2. **Move to `email_conversation_agent`** (Most complex but high value):
   - Already uses LangGraph
   - Has the most sophisticated workflow
   - Highest impact on user experience

3. **Consolidate classification agents**:
   - Merge `email_classification_agent` and `email_classification_service`
   - Use the template's background task capabilities

4. **Retire `form_processing_agent`** (Replace with v2):
   - Archive the old version
   - Ensure v2 has all needed functionality

5. **Special handling for `aegis-agentic-intelligence-system`**:
   - This is a Next.js app, not a Python agent
   - May need different migration strategy

### Risk Mitigation

#### Before Making Any Changes:

1. **Document Current Dependencies**:
   ```bash
   # Map current service dependencies
   echo "Current agent endpoints:" > current-state.md
   curl -s https://your-current-agent.run.app/health >> current-state.md
   ```

2. **Create Rollback Plan**:
   - Keep current services running during migration
   - Document how to quickly revert changes
   - Test rollback procedures

3. **Monitor Resource Costs**:
   - Review current spending on compute resources
   - Set up billing alerts for unexpected increases
   - Plan for temporary cost increase during migration

#### During Migration:

1. **Use Blue-Green Deployment**:
   - Deploy new versions to separate services
   - Switch traffic only after validation
   - Keep old versions running until confident

2. **Comprehensive Monitoring**:
   - Set up logging and monitoring for new agents
   - Compare performance metrics with old versions
   - Monitor error rates and response times

#### Cleanup Guidelines:

1. **Low-Risk Cleanup First**:
   - Unused Cloud Storage buckets
   - Old service account keys
   - Unused secrets
   - Old Cloud Run revisions

2. **Medium-Risk Cleanup**:
   - Unused Cloud Run services (after confirming)
   - Unused Cloud Functions
   - Unused Pub/Sub topics

3. **High-Risk Cleanup** (Only after migration):
   - Service accounts
   - IAM policies
   - Database instances

### Emergency Procedures

If something goes wrong during migration:

1. **Immediate Rollback**:
   ```bash
   # Revert traffic to old services
   gcloud run services update-traffic OLD_SERVICE --to-revisions=WORKING_REVISION=100
   ```

2. **Incident Response**:
   - Document what went wrong
   - Restore from backups if needed
   - Notify stakeholders of any service disruption

3. **Post-Incident Review**:
   - Analyze root cause
   - Update migration procedures
   - Adjust risk assessment

### Success Metrics

Track these metrics to ensure successful migration:

- **Functionality**: All agent workflows execute correctly
- **Performance**: Response times equal or better than current
- **Reliability**: Error rates at or below current levels  
- **Cost**: Infrastructure costs reduced or maintained
- **Maintainability**: Code is easier to understand and modify
- **Security**: Enhanced security posture with proper secret management

### Contact and Support

For questions during the migration process:

1. **Review the audit results** first
2. **Test changes locally** before any GCP deployment
3. **Use the cleanup checklist** for systematic cleanup
4. **Document everything** you discover about current state

This cautious, systematic approach will help you safely modernize your agent infrastructure while minimizing risks to your production systems.
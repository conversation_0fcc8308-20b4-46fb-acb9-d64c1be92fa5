#!/usr/bin/env python3
"""
Test script for Redis checkpointer functionality.
"""

import asyncio
import logging
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings
from src.services.redis_checkpointer import get_redis_checkpointer
from src.agent.core import VertexAIAgent
from src.agent.state import TaskRequest, TaskType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_redis_connection():
    """Test basic Redis connection."""
    logger.info("Testing Redis connection...")
    
    try:
        settings = Settings()
        checkpointer = await get_redis_checkpointer(settings)
        
        # Test health check
        health = await checkpointer.health_check()
        logger.info(f"Redis health check: {health}")
        
        if health["status"] == "healthy":
            logger.info("✅ Redis connection successful!")
            return True
        else:
            logger.error(f"❌ Redis connection failed: {health}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Redis connection error: {e}")
        return False


async def test_checkpointer_functionality():
    """Test checkpointer save/retrieve functionality."""
    logger.info("Testing checkpointer functionality...")
    
    try:
        settings = Settings()
        checkpointer = await get_redis_checkpointer(settings)
        
        # Test data
        thread_id = "test_thread_123"
        test_data = {
            "messages": ["Hello", "How are you?"],
            "context": {"user": "test_user"},
            "timestamp": "2025-01-26T19:00:00Z"
        }
        
        # Save checkpoint
        save_result = await checkpointer.save_conversation_checkpoint(
            thread_id=thread_id,
            checkpoint_data=test_data,
            metadata={"test": True}
        )
        
        if save_result:
            logger.info("✅ Checkpoint saved successfully!")
        else:
            logger.error("❌ Failed to save checkpoint")
            return False
        
        # Retrieve checkpoint
        retrieved_data = await checkpointer.get_conversation_checkpoint(thread_id)
        
        if retrieved_data:
            logger.info("✅ Checkpoint retrieved successfully!")
            logger.info(f"Retrieved data: {retrieved_data}")
            return True
        else:
            logger.error("❌ Failed to retrieve checkpoint")
            return False
            
    except Exception as e:
        logger.error(f"❌ Checkpointer test error: {e}")
        return False


async def test_agent_with_checkpointer():
    """Test agent initialization with Redis checkpointer."""
    logger.info("Testing agent with Redis checkpointer...")
    
    try:
        # Create agent
        agent = VertexAIAgent()
        
        # Initialize checkpointer
        init_result = await agent.initialize_checkpointer()
        
        if init_result:
            logger.info("✅ Agent checkpointer initialized successfully!")
            
            # Test task execution with thread
            task_request = TaskRequest(
                task_id="test_task_123",
                task_type=TaskType.EMAIL_ANALYSIS,
                input_data={"message": "Test message for checkpointer"},
                context={"test": True}
            )
            
            # Execute with thread ID
            result = await agent.execute_task_with_thread(
                task_request=task_request,
                thread_id="test_conversation_thread"
            )
            
            logger.info(f"✅ Task executed with thread support: {result.get('task_id', 'Unknown')}")
            return True
        else:
            logger.error("❌ Failed to initialize agent checkpointer")
            return False
            
    except Exception as e:
        logger.error(f"❌ Agent checkpointer test error: {e}")
        return False


async def main():
    """Run all tests."""
    logger.info("🚀 Starting Redis Checkpointer Tests...")
    
    tests = [
        ("Redis Connection", test_redis_connection),
        ("Checkpointer Functionality", test_checkpointer_functionality),
        ("Agent with Checkpointer", test_agent_with_checkpointer)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Redis checkpointer is ready for production.")
        return True
    else:
        logger.error("⚠️  Some tests failed. Please check the configuration.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

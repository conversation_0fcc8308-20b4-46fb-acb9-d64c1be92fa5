#!/usr/bin/env python3
"""
Debug script to test Gmail API authentication directly.
"""

import asyncio
import json
import os
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from services.gmail_client import GmailClient
from config.settings import get_settings

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_gmail_authentication():
    """Test Gmail authentication step by step."""
    
    print("🔍 Gmail Authentication Debug Test")
    print("=" * 50)
    
    # Step 1: Check settings
    print("\n1. Checking configuration...")
    settings = get_settings()
    print(f"   Project ID: {settings.project_id}")
    print(f"   Gmail subject email: {settings.gmail_config.subject_email}")
    print(f"   Gmail scopes: {settings.gmail_config.scopes}")
    
    # Step 2: Check service account key
    print("\n2. Checking service account key...")
    service_account_info = settings.get_gmail_service_account_key()
    if service_account_info:
        print(f"   ✅ Service account key found")
        print(f"   Client ID: {service_account_info.get('client_id', 'Not found')}")
        print(f"   Client email: {service_account_info.get('client_email', 'Not found')}")
        print(f"   Project ID: {service_account_info.get('project_id', 'Not found')}")
    else:
        print("   ❌ Service account key NOT found in Secret Manager")
        return False
    
    # Step 3: Test Gmail client creation
    print("\n3. Creating Gmail client...")
    try:
        client = GmailClient(subject_email="<EMAIL>")
        print("   ✅ Gmail client created")
    except Exception as e:
        print(f"   ❌ Failed to create Gmail client: {e}")
        return False
    
    # Step 4: Test authentication
    print("\n4. Testing authentication...")
    try:
        success = await client.authenticate()
        if success:
            print("   ✅ Authentication successful!")
        else:
            print("   ❌ Authentication failed")
            return False
    except Exception as e:
        print(f"   ❌ Authentication error: {e}")
        logger.exception("Full authentication error:")
        return False
    
    # Step 5: Test basic Gmail operation
    print("\n5. Testing basic Gmail operation...")
    try:
        messages = await client.get_messages(query="", max_results=1)
        print(f"   ✅ Successfully retrieved {len(messages)} messages")
        if messages:
            msg = messages[0]
            print(f"   Sample message ID: {msg.get('id', 'N/A')}")
            print(f"   Sample subject: {msg.get('subject', 'N/A')[:50]}...")
    except Exception as e:
        print(f"   ❌ Gmail operation failed: {e}")
        logger.exception("Full Gmail operation error:")
        return False
    
    print("\n🎉 All Gmail tests passed!")
    return True

async def test_service_account_directly():
    """Test service account authentication directly with Google APIs."""
    
    print("\n🔧 Direct Service Account Test")
    print("=" * 50)
    
    try:
        from google.oauth2 import service_account
        from googleapiclient.discovery import build
        
        # Get service account info
        settings = get_settings()
        service_account_info = settings.get_gmail_service_account_key()
        
        if not service_account_info:
            print("❌ No service account key found")
            return False
        
        print("1. Creating credentials...")
        credentials = service_account.Credentials.from_service_account_info(
            service_account_info,
            scopes=[
                'https://www.googleapis.com/auth/gmail.readonly',
                'https://www.googleapis.com/auth/gmail.send',
                'https://www.googleapis.com/auth/gmail.modify',
                'https://www.googleapis.com/auth/gmail.compose'
            ],
            subject="<EMAIL>"
        )
        print("   ✅ Credentials created")
        
        print("2. Building Gmail service...")
        service = build('gmail', 'v1', credentials=credentials)
        print("   ✅ Gmail service built")
        
        print("3. Testing Gmail API call...")
        profile = service.users().getProfile(userId='me').execute()
        print(f"   ✅ Profile retrieved: {profile.get('emailAddress', 'N/A')}")
        
        print("4. Testing message list...")
        results = service.users().messages().list(userId='me', maxResults=1).execute()
        messages = results.get('messages', [])
        print(f"   ✅ Found {len(messages)} messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct service account test failed: {e}")
        logger.exception("Full direct test error:")
        return False

if __name__ == "__main__":
    # Set environment for testing
    os.environ["GOOGLE_CLOUD_PROJECT"] = "vertex-ai-agent-yzdlnjey"
    
    print("Starting Gmail authentication debug...")
    
    # Run tests
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Test our Gmail client
        success1 = loop.run_until_complete(test_gmail_authentication())
        
        # Test direct service account
        success2 = loop.run_until_complete(test_service_account_directly())
        
        if success1 and success2:
            print("\n🎉 All tests passed! Gmail authentication is working.")
        else:
            print("\n❌ Some tests failed. Check the output above for details.")
            
    finally:
        loop.close()

#!/usr/bin/env python3
"""
Service Connection Test Suite - TKC_v5 Executive Agent

Comprehensive test to verify all service connections before production deployment.
Tests Gmail API, Firestore, Redis, and Pinecone connections with actual authentication.
"""

import asyncio
import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ServiceConnectionTester:
    """Test all service connections for TKC_v5 Executive Agent."""
    
    def __init__(self):
        self.results = {}
        self.overall_success = True
    
    async def test_all_services(self) -> Dict[str, Any]:
        """Test all service connections and return comprehensive results."""
        logger.info("🚀 Starting TKC_v5 Executive Agent Service Connection Tests")
        logger.info("=" * 70)
        
        # Test each service
        await self.test_environment_setup()
        await self.test_gmail_connection()
        await self.test_firestore_connection()
        await self.test_redis_connection()
        await self.test_pinecone_connection()
        await self.test_agent_core()
        
        # Generate summary
        self.generate_summary()
        return self.results
    
    async def test_environment_setup(self):
        """Test environment configuration and credentials."""
        logger.info("🔧 Testing Environment Setup...")
        
        try:
            # Check environment variables
            project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'vertex-ai-agent-yzdlnjey')
            environment = os.getenv('ENVIRONMENT', 'development')
            
            # Check if running in Google Cloud or locally
            try:
                import google.auth
                credentials, project = google.auth.default()
                auth_method = "Google Cloud Default Credentials"
            except Exception as e:
                auth_method = f"Local/Service Account (Error: {e})"
            
            self.results['environment'] = {
                'status': 'success',
                'project_id': project_id,
                'environment': environment,
                'auth_method': auth_method,
                'python_version': sys.version,
                'working_directory': os.getcwd()
            }
            
            logger.info(f"✅ Environment: {environment}")
            logger.info(f"✅ Project ID: {project_id}")
            logger.info(f"✅ Auth Method: {auth_method}")
            
        except Exception as e:
            self.results['environment'] = {
                'status': 'error',
                'error': str(e)
            }
            self.overall_success = False
            logger.error(f"❌ Environment setup failed: {e}")
    
    async def test_gmail_connection(self):
        """Test Gmail API connection."""
        logger.info("\n📧 Testing Gmail API Connection...")
        
        try:
            from services.gmail_client import create_gmail_client
            
            # Test Gmail client creation
            gmail_client = await create_gmail_client()
            
            # Test basic Gmail operation
            profile = await gmail_client.get_profile()
            
            self.results['gmail'] = {
                'status': 'success',
                'email_address': profile.get('emailAddress', 'Unknown'),
                'messages_total': profile.get('messagesTotal', 0),
                'threads_total': profile.get('threadsTotal', 0)
            }
            
            logger.info(f"✅ Gmail connected: {profile.get('emailAddress')}")
            logger.info(f"✅ Total messages: {profile.get('messagesTotal', 0)}")
            
        except ImportError as e:
            self.results['gmail'] = {
                'status': 'error',
                'error': f"Import error: {e}",
                'suggestion': "Check if gmail_client module exists in services/"
            }
            self.overall_success = False
            logger.error(f"❌ Gmail import failed: {e}")
            
        except Exception as e:
            self.results['gmail'] = {
                'status': 'error',
                'error': str(e),
                'suggestion': "Check Gmail API credentials and domain delegation"
            }
            self.overall_success = False
            logger.error(f"❌ Gmail connection failed: {e}")
    
    async def test_firestore_connection(self):
        """Test Firestore connection."""
        logger.info("\n🔥 Testing Firestore Connection...")
        
        try:
            from google.cloud import firestore
            
            # Create Firestore client
            db = firestore.Client()
            
            # Test write operation
            test_doc_ref = db.collection('connection_test').document('test')
            test_data = {
                'timestamp': datetime.now(),
                'test_type': 'connection_verification',
                'status': 'testing'
            }
            test_doc_ref.set(test_data)
            
            # Test read operation
            doc = test_doc_ref.get()
            if doc.exists:
                # Clean up test document
                test_doc_ref.delete()
                
                self.results['firestore'] = {
                    'status': 'success',
                    'database_id': '(default)',
                    'test_write': 'success',
                    'test_read': 'success',
                    'test_delete': 'success'
                }
                
                logger.info("✅ Firestore connected successfully")
                logger.info("✅ Write/Read/Delete operations working")
            else:
                raise Exception("Test document was not created")
                
        except ImportError as e:
            self.results['firestore'] = {
                'status': 'error',
                'error': f"Import error: {e}",
                'suggestion': "Install google-cloud-firestore: pip install google-cloud-firestore"
            }
            self.overall_success = False
            logger.error(f"❌ Firestore import failed: {e}")
            
        except Exception as e:
            self.results['firestore'] = {
                'status': 'error',
                'error': str(e),
                'suggestion': "Check Firestore API is enabled and service account has permissions"
            }
            self.overall_success = False
            logger.error(f"❌ Firestore connection failed: {e}")
    
    async def test_redis_connection(self):
        """Test Redis connection."""
        logger.info("\n🔴 Testing Redis Connection...")
        
        try:
            import redis.asyncio as redis
            from google.cloud import secretmanager
            
            # Get Redis config from Secret Manager
            try:
                secret_client = secretmanager.SecretManagerServiceClient()
                project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'vertex-ai-agent-yzdlnjey')
                secret_name = f"projects/{project_id}/secrets/redis-config/versions/latest"
                response = secret_client.access_secret_version(request={"name": secret_name})
                redis_config = json.loads(response.payload.data.decode("UTF-8"))

                # Build Redis URL from config
                if 'url' in redis_config:
                    redis_url = redis_config['url']
                else:
                    host = redis_config.get('host', 'localhost')
                    port = redis_config.get('port', 6379)
                    db = redis_config.get('db', 0)
                    redis_url = f"redis://{host}:{port}/{db}"
            except Exception as e:
                logger.warning(f"Could not get Redis config from secrets: {e}")
                redis_url = 'redis://localhost:6379'
            
            # Test Redis connection
            redis_client = redis.from_url(redis_url)
            
            # Test basic operations
            await redis_client.set('connection_test', 'success')
            test_value = await redis_client.get('connection_test')
            await redis_client.delete('connection_test')
            
            # Get Redis info
            info = await redis_client.info()
            
            await redis_client.close()
            
            self.results['redis'] = {
                'status': 'success',
                'redis_version': info.get('redis_version', 'Unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'used_memory_human': info.get('used_memory_human', 'Unknown'),
                'test_operations': 'success'
            }
            
            logger.info(f"✅ Redis connected: {info.get('redis_version')}")
            logger.info(f"✅ Memory usage: {info.get('used_memory_human')}")
            
        except ImportError as e:
            self.results['redis'] = {
                'status': 'error',
                'error': f"Import error: {e}",
                'suggestion': "Install redis: pip install redis[hiredis]"
            }
            self.overall_success = False
            logger.error(f"❌ Redis import failed: {e}")
            
        except Exception as e:
            self.results['redis'] = {
                'status': 'error',
                'error': str(e),
                'suggestion': "Check Redis Memorystore instance is running and accessible"
            }
            self.overall_success = False
            logger.error(f"❌ Redis connection failed: {e}")
    
    async def test_pinecone_connection(self):
        """Test Pinecone connection."""
        logger.info("\n🌲 Testing Pinecone Connection...")
        
        try:
            import pinecone
            from google.cloud import secretmanager
            
            # Get Pinecone config from Secret Manager
            try:
                secret_client = secretmanager.SecretManagerServiceClient()
                project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'vertex-ai-agent-yzdlnjey')
                secret_name = f"projects/{project_id}/secrets/pinecone-config/versions/latest"
                response = secret_client.access_secret_version(request={"name": secret_name})
                pinecone_config = json.loads(response.payload.data.decode("UTF-8"))
                api_key = pinecone_config.get('api_key')
            except Exception as e:
                logger.warning(f"Could not get Pinecone config from secrets: {e}")
                api_key = os.getenv('PINECONE_API_KEY')
            
            if not api_key:
                raise Exception("No Pinecone API key found")
            
            # Initialize Pinecone (v6.x API)
            from pinecone import Pinecone
            pc = Pinecone(api_key=api_key)

            # List indexes
            indexes = pc.list_indexes()
            index_names = [idx.name for idx in indexes]

            # Test index connection if available
            if index_names:
                index_name = index_names[0]
                index = pc.Index(index_name)
                stats = index.describe_index_stats()

                self.results['pinecone'] = {
                    'status': 'success',
                    'available_indexes': index_names,
                    'test_index': index_name,
                    'vector_count': stats.total_vector_count,
                    'dimension': stats.dimension
                }

                logger.info(f"✅ Pinecone connected")
                logger.info(f"✅ Available indexes: {index_names}")
                logger.info(f"✅ Vector count: {stats.total_vector_count}")
            else:
                self.results['pinecone'] = {
                    'status': 'partial',
                    'message': 'Connected but no indexes found',
                    'available_indexes': []
                }
                logger.warning("⚠️ Pinecone connected but no indexes found")
                
        except ImportError as e:
            self.results['pinecone'] = {
                'status': 'error',
                'error': f"Import error: {e}",
                'suggestion': "Install pinecone: pip install pinecone-client"
            }
            self.overall_success = False
            logger.error(f"❌ Pinecone import failed: {e}")
            
        except Exception as e:
            self.results['pinecone'] = {
                'status': 'error',
                'error': str(e),
                'suggestion': "Check Pinecone API key and account status"
            }
            self.overall_success = False
            logger.error(f"❌ Pinecone connection failed: {e}")
    
    async def test_agent_core(self):
        """Test the core agent functionality."""
        logger.info("\n🤖 Testing Agent Core...")
        
        try:
            from agent.core import ExecutiveAgent
            from agent.state import TaskRequest, TaskType
            
            # Create agent instance
            agent = ExecutiveAgent()
            
            # Test agent initialization
            tool_count = len(agent.tools) if hasattr(agent, 'tools') else 0
            
            # Create a simple test task
            test_task = TaskRequest(
                task_type=TaskType.EMAIL_MANAGEMENT,
                description="Test task for connection verification",
                customer_id="test_customer",
                user_email="<EMAIL>"
            )
            
            self.results['agent_core'] = {
                'status': 'success',
                'agent_initialized': True,
                'tool_count': tool_count,
                'test_task_created': True,
                'llm_configured': hasattr(agent, 'llm_with_tools')
            }
            
            logger.info(f"✅ Agent initialized with {tool_count} tools")
            logger.info(f"✅ LLM configured: {hasattr(agent, 'llm_with_tools')}")
            
        except ImportError as e:
            self.results['agent_core'] = {
                'status': 'error',
                'error': f"Import error: {e}",
                'suggestion': "Check agent core modules are available"
            }
            self.overall_success = False
            logger.error(f"❌ Agent core import failed: {e}")
            
        except Exception as e:
            self.results['agent_core'] = {
                'status': 'error',
                'error': str(e),
                'suggestion': "Check agent configuration and dependencies"
            }
            self.overall_success = False
            logger.error(f"❌ Agent core test failed: {e}")
    
    def generate_summary(self):
        """Generate test summary and recommendations."""
        logger.info("\n" + "=" * 70)
        logger.info("📊 SERVICE CONNECTION TEST SUMMARY")
        logger.info("=" * 70)
        
        success_count = 0
        total_count = 0
        
        for service, result in self.results.items():
            total_count += 1
            status = result.get('status', 'unknown')
            
            if status == 'success':
                success_count += 1
                logger.info(f"✅ {service.upper()}: Connected successfully")
            elif status == 'partial':
                logger.info(f"⚠️ {service.upper()}: Partial connection")
            else:
                logger.error(f"❌ {service.upper()}: Connection failed")
                if 'suggestion' in result:
                    logger.error(f"   💡 Suggestion: {result['suggestion']}")
        
        logger.info(f"\n📈 Overall Success Rate: {success_count}/{total_count} services")
        
        if self.overall_success:
            logger.info("🎉 ALL SERVICES CONNECTED - READY FOR DEPLOYMENT!")
        else:
            logger.warning("⚠️ SOME SERVICES FAILED - REVIEW BEFORE DEPLOYMENT")
        
        # Deployment recommendations
        logger.info("\n🚀 DEPLOYMENT RECOMMENDATIONS:")
        
        if self.results.get('gmail', {}).get('status') == 'success':
            logger.info("✅ Gmail API ready for production")
        else:
            logger.warning("❌ Fix Gmail API before deployment")
        
        if self.results.get('firestore', {}).get('status') == 'success':
            logger.info("✅ Firestore ready for conversation storage")
        else:
            logger.warning("❌ Fix Firestore before deployment")
        
        if self.results.get('redis', {}).get('status') == 'success':
            logger.info("✅ Redis ready for session management")
        else:
            logger.warning("❌ Fix Redis before deployment")
        
        if self.results.get('pinecone', {}).get('status') in ['success', 'partial']:
            logger.info("✅ Pinecone ready for vector operations")
        else:
            logger.warning("❌ Fix Pinecone before deployment")


async def main():
    """Run the service connection tests."""
    tester = ServiceConnectionTester()
    results = await tester.test_all_services()
    
    # Save results to file
    with open('service_connection_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    logger.info(f"\n📄 Detailed results saved to: service_connection_test_results.json")
    
    return tester.overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

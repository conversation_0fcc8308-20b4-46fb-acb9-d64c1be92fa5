#!/usr/bin/env python3
"""
Core Agent Functionality Test - TKC_v5 Executive Agent

Test the core agent functionality without dependencies that have local limitations.
Focus on Gmail integration and basic agent operations.
"""

import asyncio
import os
import sys
import logging
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_gmail_integration():
    """Test Gmail integration functionality."""
    logger.info("🔧 Testing Gmail Integration...")
    
    try:
        from services.gmail_client import create_gmail_client
        
        # Create Gmail client
        gmail_client = await create_gmail_client()
        
        # Test profile access
        profile = await gmail_client.get_profile()
        logger.info(f"✅ Gmail Profile: {profile.get('emailAddress')}")
        logger.info(f"✅ Total Messages: {profile.get('messagesTotal', 0)}")
        
        # Test message listing (just a few)
        messages = await gmail_client.get_messages(query="", max_results=3)
        logger.info(f"✅ Retrieved {len(messages)} sample messages")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Gmail integration test failed: {e}")
        return False


async def test_agent_tools():
    """Test agent tools functionality."""
    logger.info("🔧 Testing Agent Tools...")
    
    try:
        # Test Gmail tools
        from tools.gmail_tools import get_gmail_messages
        
        # Test with a simple query
        result = await get_gmail_messages.ainvoke({
            "query": "is:unread",
            "max_results": 2,
            "customer_id": "test_customer"
        })
        
        logger.info(f"✅ Gmail tools working: {len(result.split('Message')) - 1} messages processed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent tools test failed: {e}")
        return False


async def test_basic_agent_creation():
    """Test basic agent creation without full initialization."""
    logger.info("🔧 Testing Basic Agent Creation...")
    
    try:
        # Import agent components
        from agent.state import TaskRequest, TaskType
        
        # Create a test task
        test_task = TaskRequest(
            task_type=TaskType.EMAIL_MANAGEMENT,
            description="Test email management task",
            customer_id="test_customer",
            user_email="<EMAIL>"
        )
        
        logger.info(f"✅ Task creation working: {test_task.task_type}")
        logger.info(f"✅ Customer ID: {test_task.customer_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Basic agent creation test failed: {e}")
        return False


async def test_configuration():
    """Test configuration and settings."""
    logger.info("🔧 Testing Configuration...")
    
    try:
        from config.settings import get_settings
        
        settings = get_settings()
        logger.info(f"✅ Project ID: {settings.google_cloud_project}")
        logger.info(f"✅ Environment: {settings.environment}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """Run core functionality tests."""
    logger.info("🚀 Starting TKC_v5 Core Agent Functionality Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Gmail Integration", test_gmail_integration),
        ("Agent Tools", test_agent_tools),
        ("Basic Agent Creation", test_basic_agent_creation),
    ]
    
    results = {}
    overall_success = True
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name} Test...")
        try:
            success = await test_func()
            results[test_name] = success
            if not success:
                overall_success = False
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
            overall_success = False
    
    # Generate summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 CORE FUNCTIONALITY TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name}")
        if success:
            passed += 1
    
    logger.info(f"\n📈 Results: {passed}/{total} tests passed")
    
    if overall_success:
        logger.info("🎉 ALL CORE FUNCTIONALITY TESTS PASSED!")
        logger.info("✅ Agent is ready for basic email automation")
        logger.info("✅ Core business functionality verified")
    else:
        logger.warning("⚠️ Some tests failed - review before deployment")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Production Vector Database Test - TKC_v5 Enhanced Agent

This test validates the production Pinecone integration with our enhanced agent.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_production_vector_database():
    """Test production vector database functionality."""
    logger.info("🧪 Testing Production Vector Database...")
    
    try:
        from src.services.vector_db_client import get_vector_db_client
        from src.config.settings import Settings
        
        settings = Settings()
        
        # Test in production mode (not mock)
        vector_db = await get_vector_db_client(settings, mock_mode=False)
        
        # Test health check
        health = await vector_db.health_check()
        logger.info(f"📊 Health: {health}")
        
        if health["status"] != "healthy":
            logger.error("❌ Production vector database health check failed")
            return False
        
        logger.info("✅ Production vector database is healthy")
        
        # Test storing messages
        test_messages = [
            {
                "id": "prod_msg_001",
                "conversation_id": "prod_conv_001",
                "content": "Hello, I need help with project timeline management and budget planning for our Q1 initiatives",
                "type": "human",
                "customer_email": "<EMAIL>"
            },
            {
                "id": "prod_msg_002",
                "conversation_id": "prod_conv_001", 
                "content": "I can help you with project timeline management and budget planning. Let me provide some guidance on best practices for Q1 planning.",
                "type": "assistant",
                "customer_email": "<EMAIL>"
            },
            {
                "id": "prod_msg_003",
                "conversation_id": "prod_conv_002",
                "content": "Can we schedule a meeting to discuss the contract terms and payment schedule for the new partnership?",
                "type": "human",
                "customer_email": "<EMAIL>"
            },
            {
                "id": "prod_msg_004",
                "conversation_id": "prod_conv_003",
                "content": "I'm having trouble with the resource allocation for our development team. We need to optimize our workflow.",
                "type": "human",
                "customer_email": "<EMAIL>"
            }
        ]
        
        # Store test messages
        for msg in test_messages:
            success = await vector_db.store_conversation_message(
                message_id=msg["id"],
                conversation_id=msg["conversation_id"],
                content=msg["content"],
                message_type=msg["type"],
                metadata={
                    "customer_email": msg["customer_email"],
                    "timestamp": datetime.now().isoformat(),
                    "test_data": True
                }
            )
            
            if not success:
                logger.error(f"❌ Failed to store message {msg['id']}")
                return False
        
        logger.info(f"✅ Stored {len(test_messages)} messages in production Pinecone")
        
        # Test semantic search
        search_queries = [
            "project management help",
            "budget planning assistance", 
            "meeting scheduling",
            "resource allocation optimization",
            "contract terms discussion"
        ]
        
        for query in search_queries:
            search_results = await vector_db.search_similar_messages(
                query=query,
                top_k=3,
                min_score=0.1
            )
            
            logger.info(f"🔍 Query: '{query}' -> {len(search_results)} results")
            
            if search_results:
                best_match = search_results[0]
                logger.info(f"   Best match (score: {best_match['score']:.3f}): {best_match['metadata']['content'][:60]}...")
        
        # Test conversation context
        context = await vector_db.get_conversation_context(
            conversation_id="prod_conv_001",
            max_messages=10
        )
        
        if context:
            logger.info(f"✅ Retrieved conversation context: {len(context)} messages")
        else:
            logger.warning("⚠️  No conversation context found")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Production vector database test failed: {e}")
        return False


async def test_production_rag_service():
    """Test production RAG service functionality."""
    logger.info("🧪 Testing Production RAG Service...")
    
    try:
        from src.services.rag_service import get_rag_service
        from src.config.settings import Settings
        
        settings = Settings()
        
        # Initialize RAG service in production mode
        rag_service = await get_rag_service(settings, mock_mode=False)
        
        # Test health check
        health = await rag_service.health_check()
        if health["status"] != "healthy":
            logger.error("❌ Production RAG service health check failed")
            return False
        
        logger.info("✅ Production RAG service is healthy")
        
        # Test storing conversation messages
        conversation_id = "prod_rag_conv"
        customer_email = "<EMAIL>"
        
        messages = [
            {"content": "I need help with project management and timeline planning", "type": "human"},
            {"content": "I can help you with project management. What specific aspects would you like to focus on?", "type": "assistant"},
            {"content": "Specifically, I need guidance on resource allocation and budget optimization", "type": "human"},
            {"content": "Let me provide guidance on resource allocation and budget optimization strategies.", "type": "assistant"}
        ]
        
        for i, msg in enumerate(messages):
            success = await rag_service.store_conversation_message(
                conversation_id=conversation_id,
                message_content=msg["content"],
                message_type=msg["type"],
                customer_email=customer_email
            )
            
            if not success:
                logger.error(f"❌ Failed to store RAG message {i}")
                return False
        
        logger.info(f"✅ Stored {len(messages)} RAG messages in production")
        
        # Test context retrieval
        context = await rag_service.get_relevant_context(
            query="project timeline and budget help",
            conversation_id=conversation_id,
            customer_email=customer_email
        )
        
        if not context.get("context"):
            logger.error("❌ No context retrieved from production RAG")
            return False
        
        logger.info(f"✅ Retrieved production context: {len(context['context'])} characters")
        logger.info(f"📊 Sources found: {len(context['sources'])}")
        
        # Test prompt enhancement
        original_prompt = "You are a helpful executive assistant."
        enhanced_prompt = await rag_service.enhance_prompt_with_context(
            original_prompt=original_prompt,
            query="project timeline and budget help",
            conversation_id=conversation_id,
            customer_email=customer_email
        )
        
        if len(enhanced_prompt) <= len(original_prompt):
            logger.error("❌ Production prompt not enhanced")
            return False
        
        logger.info("✅ Production prompt enhancement successful")
        logger.info(f"📈 Prompt enhanced from {len(original_prompt)} to {len(enhanced_prompt)} characters")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Production RAG service test failed: {e}")
        return False


async def test_cross_conversation_search():
    """Test cross-conversation semantic search capabilities."""
    logger.info("🧪 Testing Cross-Conversation Search...")
    
    try:
        from src.services.semantic_search import get_semantic_search_service, SearchQuery
        from src.config.settings import Settings
        
        settings = Settings()
        
        # Initialize semantic search in production mode
        search_service = await get_semantic_search_service(settings, mock_mode=False)
        
        # Test cross-conversation search
        search_query = SearchQuery(
            query="budget planning and resource allocation",
            max_results=5,
            min_score=0.1
        )
        
        results = await search_service.search(search_query)
        
        if not results:
            logger.warning("⚠️  No cross-conversation search results found")
            return True  # Not a failure, just no data yet
        
        logger.info(f"✅ Cross-conversation search: {len(results)} results")
        
        # Test customer-specific search
        customer_query = SearchQuery(
            query="project management",
            customer_email="<EMAIL>",
            max_results=3,
            min_score=0.1
        )
        
        customer_results = await search_service.search(customer_query)
        logger.info(f"✅ Customer-specific search: {len(customer_results)} results")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Cross-conversation search test failed: {e}")
        return False


async def main():
    """Run all production tests."""
    logger.info("🚀 Starting Production Vector Database Tests for TKC_v5 Enhanced Agent")
    logger.info("=" * 80)
    
    tests = [
        ("Production Vector Database", test_production_vector_database),
        ("Production RAG Service", test_production_rag_service),
        ("Cross-Conversation Search", test_cross_conversation_search)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Production Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All production tests passed! Enhanced TKC_v5 Agent is ready for deployment!")
        logger.info("📋 Production capabilities validated:")
        logger.info("   ✅ Production Pinecone vector database")
        logger.info("   ✅ Cross-conversation semantic search")
        logger.info("   ✅ RAG-enhanced context retrieval")
        logger.info("   ✅ Persistent conversation intelligence")
        logger.info("   ✅ Customer-specific context filtering")
        logger.info("\n🚀 Ready for Cloud Run deployment!")
        return True
    else:
        logger.error("⚠️  Some production tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

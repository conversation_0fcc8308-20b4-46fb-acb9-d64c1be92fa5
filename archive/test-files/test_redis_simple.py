#!/usr/bin/env python3
"""
Simple Redis connection test without Secret Manager dependency.
"""

import asyncio
import logging
import redis.asyncio as redis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_redis_direct_connection():
    """Test direct Redis connection to Memory<PERSON>re instance."""
    logger.info("Testing direct Redis connection...")
    
    # Redis instance details from our deployment
    redis_host = "************"
    redis_port = 6379
    redis_db = 0
    
    try:
        # Create Redis client
        redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True
        )
        
        # Test connection
        await redis_client.ping()
        logger.info("✅ Redis PING successful!")
        
        # Test basic operations
        test_key = "test_milestone3_key"
        test_value = "Hello from TKC_v5 Milestone 3!"
        
        # Set a value
        await redis_client.set(test_key, test_value, ex=60)  # Expire in 60 seconds
        logger.info(f"✅ SET operation successful: {test_key} = {test_value}")
        
        # Get the value
        retrieved_value = await redis_client.get(test_key)
        logger.info(f"✅ GET operation successful: {test_key} = {retrieved_value}")
        
        if retrieved_value == test_value:
            logger.info("✅ Value verification successful!")
        else:
            logger.error(f"❌ Value mismatch: expected {test_value}, got {retrieved_value}")
            return False
        
        # Test hash operations (for checkpoints)
        checkpoint_key = "checkpoint:test_thread_123"
        checkpoint_data = {
            "thread_id": "test_thread_123",
            "timestamp": "2025-01-26T19:30:00Z",
            "data": "Test checkpoint data",
            "metadata": "Test metadata"
        }
        
        # Set hash
        await redis_client.hset(checkpoint_key, mapping=checkpoint_data)
        logger.info(f"✅ HSET operation successful: {checkpoint_key}")
        
        # Get hash
        retrieved_checkpoint = await redis_client.hgetall(checkpoint_key)
        logger.info(f"✅ HGETALL operation successful: {retrieved_checkpoint}")
        
        # Set expiration
        await redis_client.expire(checkpoint_key, 60)
        logger.info("✅ EXPIRE operation successful")
        
        # Get Redis info
        info = await redis_client.info()
        logger.info(f"✅ Redis version: {info.get('redis_version')}")
        logger.info(f"✅ Connected clients: {info.get('connected_clients')}")
        logger.info(f"✅ Used memory: {info.get('used_memory_human')}")
        
        # Cleanup
        await redis_client.delete(test_key, checkpoint_key)
        logger.info("✅ Cleanup successful")
        
        # Close connection
        await redis_client.close()
        logger.info("✅ Connection closed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis connection test failed: {e}")
        return False


async def test_langgraph_checkpoint_format():
    """Test Redis operations in LangGraph checkpoint format."""
    logger.info("Testing LangGraph checkpoint format...")
    
    redis_host = "************"
    redis_port = 6379
    redis_db = 0
    
    try:
        redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True
        )
        
        # Simulate LangGraph checkpoint structure
        thread_id = "conversation_thread_456"
        checkpoint_data = {
            "messages": [
                {"role": "human", "content": "Hello, I need help with email automation"},
                {"role": "assistant", "content": "I'd be happy to help you with email automation. What specific task would you like to accomplish?"}
            ],
            "state": {
                "task_id": "email_task_789",
                "task_type": "email_analysis",
                "status": "in_progress",
                "context": {
                    "customer_email": "<EMAIL>",
                    "priority": "normal"
                }
            },
            "metadata": {
                "created_at": "2025-01-26T19:30:00Z",
                "last_updated": "2025-01-26T19:35:00Z",
                "version": "1.0"
            }
        }
        
        # Store checkpoint (simulating LangGraph format)
        checkpoint_key = f"langgraph:checkpoint:{thread_id}"
        
        # Convert to string format (LangGraph typically uses JSON serialization)
        import json
        checkpoint_json = json.dumps(checkpoint_data)
        
        await redis_client.set(checkpoint_key, checkpoint_json, ex=3600)  # 1 hour expiration
        logger.info(f"✅ LangGraph checkpoint stored: {checkpoint_key}")
        
        # Retrieve checkpoint
        retrieved_json = await redis_client.get(checkpoint_key)
        retrieved_data = json.loads(retrieved_json)
        
        logger.info(f"✅ LangGraph checkpoint retrieved successfully")
        logger.info(f"   Thread ID: {thread_id}")
        logger.info(f"   Messages: {len(retrieved_data['messages'])} messages")
        logger.info(f"   Task ID: {retrieved_data['state']['task_id']}")
        logger.info(f"   Status: {retrieved_data['state']['status']}")
        
        # Test thread listing (simulate finding all conversation threads)
        pattern = "langgraph:checkpoint:*"
        keys = await redis_client.keys(pattern)
        logger.info(f"✅ Found {len(keys)} conversation threads")
        
        # Cleanup
        await redis_client.delete(checkpoint_key)
        await redis_client.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LangGraph checkpoint test failed: {e}")
        return False


async def main():
    """Run Redis tests."""
    logger.info("🚀 Starting Redis Memorystore Tests for TKC_v5 Milestone 3...")
    
    tests = [
        ("Direct Redis Connection", test_redis_direct_connection),
        ("LangGraph Checkpoint Format", test_langgraph_checkpoint_format)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All Redis tests passed! Ready for LangGraph checkpointing integration.")
        logger.info("📋 Next steps:")
        logger.info("   1. Deploy updated application with Redis checkpointer")
        logger.info("   2. Test conversation persistence across agent restarts")
        logger.info("   3. Verify Gmail automation with conversation context")
        return True
    else:
        logger.error("⚠️  Some tests failed. Please check Redis configuration.")
        return False


if __name__ == "__main__":
    import sys
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

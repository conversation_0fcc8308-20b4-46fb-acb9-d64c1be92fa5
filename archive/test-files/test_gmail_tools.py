#!/usr/bin/env python3
"""
Test script to debug Gmail tools directly.
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_gmail_tools():
    """Test Gmail tools directly."""
    
    print("🔧 Testing Gmail Tools Directly")
    print("=" * 50)
    
    # Set environment
    os.environ["GOOGLE_CLOUD_PROJECT"] = "vertex-ai-agent-yzdlnjey"
    
    try:
        # Import the tools
        from agent.core import get_recent_emails, create_email_draft, analyze_email_content
        
        print("✅ Gmail tools imported successfully")
        
        # Test 1: Get recent emails
        print("\n1. Testing get_recent_emails...")
        try:
            result = await get_recent_emails.ainvoke({"query": "", "max_results": 2})
            print(f"   ✅ get_recent_emails result:")
            print(f"   {result[:200]}...")
        except Exception as e:
            print(f"   ❌ get_recent_emails failed: {e}")
            logger.exception("Full get_recent_emails error:")

        # Test 2: Analyze email content
        print("\n2. Testing analyze_email_content...")
        try:
            result = analyze_email_content.invoke({
                "email_subject": "Test Subject",
                "email_body": "This is a test email body with urgent content",
                "sender": "<EMAIL>"
            })
            print(f"   ✅ analyze_email_content result:")
            print(f"   {result}")
        except Exception as e:
            print(f"   ❌ analyze_email_content failed: {e}")
            logger.exception("Full analyze_email_content error:")
        
        # Test 3: Create email draft (commented out to avoid creating actual drafts)
        print("\n3. Testing create_email_draft (dry run)...")
        print("   ⚠️  Skipping actual draft creation to avoid spam")
        # try:
        #     result = await create_email_draft(
        #         to="<EMAIL>",
        #         subject="Test Draft from Debug Script",
        #         body="This is a test draft created by the debug script."
        #     )
        #     print(f"   ✅ create_email_draft result: {result}")
        # except Exception as e:
        #     print(f"   ❌ create_email_draft failed: {e}")
        #     logger.exception("Full create_email_draft error:")
        
        print("\n🎉 Gmail tools test completed!")
        
    except Exception as e:
        print(f"❌ Failed to import or test Gmail tools: {e}")
        logger.exception("Full tools test error:")

async def test_agent_with_gmail():
    """Test the agent with Gmail functionality."""
    
    print("\n🤖 Testing Agent with Gmail")
    print("=" * 50)
    
    try:
        from agent.core import create_agent
        from agent.state import TaskRequest, TaskType
        import time
        
        # Create agent
        agent = create_agent()
        print("✅ Agent created successfully")
        
        # Test email query
        print("\n1. Testing email query through agent...")
        task_request = TaskRequest(
            task_id=f"test_task_{int(time.time())}",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Can you check my recent emails and tell me what you find?"
            }
        )
        
        response = await agent.execute_task(task_request)
        print(f"   Agent response success: {response.success}")
        print(f"   Agent response: {response.results}")
        
        if not response.success:
            print(f"   Error: {response.error}")
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        logger.exception("Full agent test error:")

if __name__ == "__main__":
    print("Starting Gmail tools debug test...")
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        # Test tools directly
        loop.run_until_complete(test_gmail_tools())
        
        # Test agent
        loop.run_until_complete(test_agent_with_gmail())
        
    finally:
        loop.close()

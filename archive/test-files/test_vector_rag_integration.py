#!/usr/bin/env python3
"""
Comprehensive test suite for Vector Database and RAG Integration - TKC_v5 Milestone 3

This test suite validates all vector database, RAG, and semantic search functionality
in local mock mode without requiring external services.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import Settings
from src.services.vector_db_client import get_vector_db_client
from src.services.rag_service import get_rag_service
from src.services.semantic_search import get_semantic_search_service, SearchQuery
from src.agent.core import VertexAIAgent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VectorRAGTestSuite:
    """Comprehensive test suite for vector database and RAG functionality."""
    
    def __init__(self):
        self.settings = Settings()
        self.vector_db = None
        self.rag_service = None
        self.semantic_search = None
        self.agent = None
        
        # Test data
        self.test_conversations = [
            {
                "conversation_id": "conv_001",
                "customer_email": "<EMAIL>",
                "messages": [
                    {"type": "human", "content": "Hello, I need help with my project timeline"},
                    {"type": "assistant", "content": "I'd be happy to help you with your project timeline. What specific aspects would you like to discuss?"},
                    {"type": "human", "content": "We're behind schedule on the website redesign project"},
                    {"type": "assistant", "content": "I understand you're facing delays with the website redesign. Let me help you identify solutions to get back on track."}
                ]
            },
            {
                "conversation_id": "conv_002", 
                "customer_email": "<EMAIL>",
                "messages": [
                    {"type": "human", "content": "Can we schedule a meeting to discuss the budget proposal?"},
                    {"type": "assistant", "content": "Certainly! I can help you schedule a meeting for the budget proposal discussion. What timeframe works best for you?"},
                    {"type": "human", "content": "Next week would be ideal, preferably Tuesday or Wednesday"},
                    {"type": "assistant", "content": "Perfect! I'll check availability for Tuesday or Wednesday next week and send you some options."}
                ]
            },
            {
                "conversation_id": "conv_003",
                "customer_email": "<EMAIL>", 
                "messages": [
                    {"type": "human", "content": "I'm having trouble with the contract terms"},
                    {"type": "assistant", "content": "I can help clarify the contract terms. Which specific sections are you concerned about?"},
                    {"type": "human", "content": "The payment schedule seems unclear"},
                    {"type": "assistant", "content": "Let me review the payment schedule section and provide you with a clear breakdown."}
                ]
            }
        ]
    
    async def setup(self):
        """Initialize all services in mock mode."""
        logger.info("🔧 Setting up test environment...")
        
        # Initialize vector database in mock mode
        self.vector_db = await get_vector_db_client(self.settings, mock_mode=True)
        
        # Initialize RAG service in mock mode
        self.rag_service = await get_rag_service(self.settings, mock_mode=True)
        
        # Initialize semantic search in mock mode
        self.semantic_search = await get_semantic_search_service(self.settings, mock_mode=True)
        
        # Initialize agent
        self.agent = VertexAIAgent()
        await self.agent.initialize_rag_services(mock_mode=True)
        
        logger.info("✅ Test environment setup complete")
    
    async def test_vector_database_operations(self):
        """Test basic vector database operations."""
        logger.info("🧪 Testing vector database operations...")
        
        try:
            # Test health check
            health = await self.vector_db.health_check()
            assert health["status"] == "healthy", f"Vector DB health check failed: {health}"
            logger.info("✅ Vector database health check passed")
            
            # Test storing messages
            test_message_id = "test_msg_001"
            test_content = "This is a test message for vector storage"
            
            success = await self.vector_db.store_conversation_message(
                message_id=test_message_id,
                conversation_id="test_conv_001",
                content=test_content,
                message_type="human",
                metadata={"test": True}
            )
            assert success, "Failed to store test message"
            logger.info("✅ Message storage test passed")
            
            # Test searching messages
            results = await self.vector_db.search_similar_messages(
                query="test message",
                top_k=5,
                min_score=0.1  # Low threshold for testing
            )
            assert len(results) > 0, "No search results found"
            assert results[0]["id"] == test_message_id, "Wrong message returned"
            logger.info("✅ Message search test passed")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Vector database test failed: {e}")
            return False
    
    async def test_rag_service_functionality(self):
        """Test RAG service functionality."""
        logger.info("🧪 Testing RAG service functionality...")
        
        try:
            # Test health check
            health = await self.rag_service.health_check()
            assert health["status"] == "healthy", f"RAG service health check failed: {health}"
            logger.info("✅ RAG service health check passed")
            
            # Store test conversation
            conversation_id = "rag_test_conv"
            customer_email = "<EMAIL>"
            
            # Store multiple messages
            messages = [
                {"content": "I need help with project management", "type": "human"},
                {"content": "I can help you with project management. What specific area?", "type": "assistant"},
                {"content": "Timeline planning and resource allocation", "type": "human"},
                {"content": "Let me provide guidance on timeline planning and resource allocation.", "type": "assistant"}
            ]
            
            for i, msg in enumerate(messages):
                success = await self.rag_service.store_conversation_message(
                    conversation_id=conversation_id,
                    message_content=msg["content"],
                    message_type=msg["type"],
                    customer_email=customer_email
                )
                assert success, f"Failed to store message {i}"
            
            logger.info("✅ RAG message storage test passed")
            
            # Test context retrieval
            context = await self.rag_service.get_relevant_context(
                query="project timeline help",
                conversation_id=conversation_id,
                customer_email=customer_email
            )
            
            assert context["context"], "No context retrieved"
            assert len(context["sources"]) >= 0, "Invalid sources format"
            logger.info("✅ RAG context retrieval test passed")
            
            # Test prompt enhancement
            original_prompt = "You are a helpful assistant."
            enhanced_prompt = await self.rag_service.enhance_prompt_with_context(
                original_prompt=original_prompt,
                query="project timeline help",
                conversation_id=conversation_id,
                customer_email=customer_email
            )
            
            assert len(enhanced_prompt) > len(original_prompt), "Prompt not enhanced"
            logger.info("✅ RAG prompt enhancement test passed")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ RAG service test failed: {e}")
            return False
    
    async def test_semantic_search_functionality(self):
        """Test semantic search functionality."""
        logger.info("🧪 Testing semantic search functionality...")
        
        try:
            # Test health check
            health = await self.semantic_search.health_check()
            assert health["status"] == "healthy", f"Semantic search health check failed: {health}"
            logger.info("✅ Semantic search health check passed")
            
            # Populate test data
            await self._populate_test_data()
            
            # Test basic search
            search_query = SearchQuery(
                query="project timeline",
                max_results=5,
                min_score=0.1
            )
            
            results = await self.semantic_search.search(search_query)
            assert len(results) > 0, "No search results found"
            logger.info(f"✅ Basic search test passed - found {len(results)} results")
            
            # Test customer-specific search
            search_query = SearchQuery(
                query="meeting schedule",
                customer_email="<EMAIL>",
                max_results=5,
                min_score=0.1
            )
            
            results = await self.semantic_search.search(search_query)
            logger.info(f"✅ Customer-specific search test passed - found {len(results)} results")
            
            # Test topic search
            topic_results = await self.semantic_search.search_by_topic("budget", max_results=5)
            logger.info(f"✅ Topic search test passed - found {len(topic_results)} results")
            
            # Test customer history search
            history_results = await self.semantic_search.search_customer_history(
                customer_email="<EMAIL>",
                days_back=30
            )
            logger.info(f"✅ Customer history search test passed - found {len(history_results)} results")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Semantic search test failed: {e}")
            return False
    
    async def test_agent_integration(self):
        """Test agent integration with RAG services."""
        logger.info("🧪 Testing agent integration...")
        
        try:
            # Test message processing with context
            conversation_id = "agent_test_conv"
            customer_email = "<EMAIL>"
            
            # First message
            result1 = await self.agent.process_message_with_context(
                message="Hello, I need help with project planning",
                conversation_id=conversation_id,
                customer_email=customer_email,
                message_type="human"
            )
            
            assert "response" in result1, "No response generated"
            assert result1["conversation_id"] == conversation_id, "Conversation ID mismatch"
            logger.info("✅ First message processing test passed")
            
            # Second message (should have context from first)
            result2 = await self.agent.process_message_with_context(
                message="What are the key steps in project planning?",
                conversation_id=conversation_id,
                customer_email=customer_email,
                message_type="human"
            )
            
            assert "response" in result2, "No response generated for second message"
            logger.info("✅ Context-aware message processing test passed")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Agent integration test failed: {e}")
            return False
    
    async def _populate_test_data(self):
        """Populate test data for comprehensive testing."""
        logger.info("📊 Populating test data...")
        
        for conv in self.test_conversations:
            conversation_id = conv["conversation_id"]
            customer_email = conv["customer_email"]
            
            for i, msg in enumerate(conv["messages"]):
                await self.rag_service.store_conversation_message(
                    conversation_id=conversation_id,
                    message_content=msg["content"],
                    message_type=msg["type"],
                    customer_email=customer_email,
                    metadata={"test_data": True, "message_index": i}
                )
        
        logger.info("✅ Test data populated successfully")
    
    async def test_performance_benchmarks(self):
        """Test performance benchmarks for vector operations."""
        logger.info("🚀 Testing performance benchmarks...")
        
        try:
            import time
            
            # Test embedding generation speed
            start_time = time.time()
            test_texts = [
                "This is a test message for performance testing",
                "Another test message with different content",
                "Performance testing requires multiple samples",
                "Vector embeddings should be generated quickly",
                "Semantic search needs to be efficient"
            ]
            
            for text in test_texts:
                await self.vector_db.store_conversation_message(
                    message_id=f"perf_test_{hash(text)}",
                    conversation_id="perf_test_conv",
                    content=text,
                    message_type="human"
                )
            
            storage_time = time.time() - start_time
            logger.info(f"✅ Storage performance: {len(test_texts)} messages in {storage_time:.2f}s")
            
            # Test search speed
            start_time = time.time()
            results = await self.vector_db.search_similar_messages(
                query="performance testing",
                top_k=10,
                min_score=0.1
            )
            search_time = time.time() - start_time
            logger.info(f"✅ Search performance: {len(results)} results in {search_time:.2f}s")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Performance benchmark test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all tests in the suite."""
        logger.info("🚀 Starting Vector Database and RAG Integration Test Suite")
        logger.info("=" * 70)
        
        tests = [
            ("Vector Database Operations", self.test_vector_database_operations),
            ("RAG Service Functionality", self.test_rag_service_functionality),
            ("Semantic Search Functionality", self.test_semantic_search_functionality),
            ("Agent Integration", self.test_agent_integration),
            ("Performance Benchmarks", self.test_performance_benchmarks)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 Running: {test_name}")
            try:
                result = await test_func()
                results.append((test_name, result))
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                results.append((test_name, False))
        
        # Summary
        logger.info("\n📊 Test Results Summary:")
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"  {test_name}: {status}")
        
        logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed! Vector Database and RAG integration is ready!")
            logger.info("📋 Ready for:")
            logger.info("   ✅ Context-aware conversation responses")
            logger.info("   ✅ Semantic search across conversation history")
            logger.info("   ✅ Intelligent customer context retrieval")
            logger.info("   ✅ RAG-enhanced prompt generation")
            logger.info("   ✅ Production deployment with real Pinecone")
            return True
        else:
            logger.error("⚠️  Some tests failed. Please review the implementation.")
            return False


async def main():
    """Main test execution function."""
    test_suite = VectorRAGTestSuite()
    
    try:
        await test_suite.setup()
        success = await test_suite.run_all_tests()
        return success
    except Exception as e:
        logger.error(f"❌ Test suite execution failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

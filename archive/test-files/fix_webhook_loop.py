#!/usr/bin/env python3
"""
Fix for Gmail Webhook Loop Issue - TKC_v5

This script implements enhanced email deduplication to prevent the agent
from creating multiple drafts for the same emails.
"""

import logging
from typing import Set, Dict, Any
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EmailDeduplicationFix:
    """Enhanced email deduplication to prevent webhook loops."""
    
    def __init__(self):
        self.processed_emails: Set[str] = set()  # Track processed email IDs
        self.draft_created_for: Set[str] = set()  # Track emails with drafts created
        self.last_cleanup = datetime.now()
        
    def should_process_email(self, email_id: str, email_data: Dict[str, Any]) -> bool:
        """
        Determine if an email should be processed for draft creation.
        
        Args:
            email_id: Gmail message ID
            email_data: Email metadata
            
        Returns:
            True if email should be processed, False otherwise
        """
        # Check if we already created a draft for this email
        if email_id in self.draft_created_for:
            logger.info(f"Skipping email {email_id} - draft already created")
            return False
        
        # Check if email is from a no-reply address
        sender = email_data.get('sender', '').lower()
        if 'noreply' in sender or 'no-reply' in sender:
            logger.info(f"Skipping no-reply email from {sender}")
            return False
        
        # Check if email is promotional/newsletter
        subject = email_data.get('subject', '').lower()
        promotional_keywords = [
            'newsletter', 'unsubscribe', 'promotion', 'sale', 'discount',
            'marketing', 'advertisement', 'winner', 'congratulations',
            'limited time', 'act now', 'free trial'
        ]
        
        if any(keyword in subject for keyword in promotional_keywords):
            logger.info(f"Skipping promotional email: {subject}")
            return False
        
        # Check if email is automated notification
        automated_keywords = [
            'notification', 'alert', 'reminder', 'confirmation',
            'receipt', 'invoice', 'shipping', 'delivery', 'welcome'
        ]
        
        if any(keyword in subject for keyword in automated_keywords):
            logger.info(f"Skipping automated notification: {subject}")
            return False
        
        return True
    
    def mark_email_processed(self, email_id: str, draft_created: bool = False):
        """
        Mark an email as processed.
        
        Args:
            email_id: Gmail message ID
            draft_created: Whether a draft was created for this email
        """
        self.processed_emails.add(email_id)
        
        if draft_created:
            self.draft_created_for.add(email_id)
            logger.info(f"Marked email {email_id} as having draft created")
    
    def cleanup_old_entries(self, hours_old: int = 24):
        """Clean up old tracking entries."""
        now = datetime.now()
        
        # Only cleanup once per hour
        if now - self.last_cleanup < timedelta(hours=1):
            return
        
        # In a real implementation, you'd track timestamps for each entry
        # For now, just limit the size of our sets
        if len(self.processed_emails) > 1000:
            # Keep only the most recent 500 entries
            self.processed_emails = set(list(self.processed_emails)[-500:])
        
        if len(self.draft_created_for) > 1000:
            self.draft_created_for = set(list(self.draft_created_for)[-500:])
        
        self.last_cleanup = now
        logger.info("Cleaned up old deduplication entries")


# Global deduplication instance
email_deduplicator = EmailDeduplicationFix()


def enhanced_email_processing_logic():
    """
    Enhanced email processing logic to prevent webhook loops.
    
    This function demonstrates the improved logic that should be integrated
    into the main agent code.
    """
    
    # Simulated email data (in real implementation, this comes from Gmail API)
    emails = [
        {
            "id": "msg_001",
            "sender": "<EMAIL>",
            "subject": "Partnership Inquiry",
            "snippet": "We'd like to discuss a potential partnership..."
        },
        {
            "id": "msg_002", 
            "sender": "<EMAIL>",
            "subject": "Weekly Newsletter - Tech Updates",
            "snippet": "This week in technology..."
        },
        {
            "id": "msg_003",
            "sender": "<EMAIL>", 
            "subject": "Welcome to Our Service",
            "snippet": "Thank you for signing up..."
        }
    ]
    
    drafts_created = 0
    
    for email in emails:
        email_id = email["id"]
        
        # Check if we should process this email
        if email_deduplicator.should_process_email(email_id, email):
            
            # Simulate draft creation
            logger.info(f"Creating draft for email: {email['subject']}")
            drafts_created += 1
            
            # Mark as processed with draft created
            email_deduplicator.mark_email_processed(email_id, draft_created=True)
        else:
            # Mark as processed without draft
            email_deduplicator.mark_email_processed(email_id, draft_created=False)
    
    # Cleanup old entries
    email_deduplicator.cleanup_old_entries()
    
    logger.info(f"Processing complete: {drafts_created} drafts created out of {len(emails)} emails")
    
    return drafts_created


def main():
    """Test the enhanced email processing logic."""
    logger.info("🔧 Testing Enhanced Email Deduplication Logic")
    logger.info("=" * 60)
    
    # First run - should create drafts for business emails
    logger.info("\n📧 First processing run:")
    drafts_first = enhanced_email_processing_logic()
    
    # Second run - should skip already processed emails
    logger.info("\n📧 Second processing run (simulating webhook loop):")
    drafts_second = enhanced_email_processing_logic()
    
    # Results
    logger.info(f"\n📊 Results:")
    logger.info(f"   First run: {drafts_first} drafts created")
    logger.info(f"   Second run: {drafts_second} drafts created")
    
    if drafts_second == 0:
        logger.info("✅ Deduplication working correctly - no duplicate drafts!")
    else:
        logger.error("❌ Deduplication failed - duplicate drafts created!")
    
    return drafts_second == 0


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

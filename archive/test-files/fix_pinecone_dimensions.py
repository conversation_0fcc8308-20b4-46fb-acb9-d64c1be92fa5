#!/usr/bin/env python3
"""
Fix Pinecone Index Dimensions for TKC_v5

This script deletes the existing index and recreates it with correct 384 dimensions.
"""

import json
import logging
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """Fix the Pinecone index dimensions."""
    try:
        from pinecone import Pinecone, ServerlessSpec
        
        # Load config
        with open("pinecone_config.json", 'r') as f:
            config = json.load(f)
        
        logger.info("🔧 Fixing Pinecone Index Dimensions...")
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        
        index_name = config["index_name"]
        
        # Delete existing index
        logger.info(f"🗑️  Deleting existing index '{index_name}'...")
        try:
            pc.delete_index(index_name)
            logger.info("✅ Index deleted successfully")
            
            # Wait for deletion to complete
            logger.info("⏳ Waiting for deletion to complete...")
            time.sleep(10)
            
        except Exception as e:
            logger.info(f"Index may not exist: {e}")
        
        # Create new index with correct dimensions
        logger.info(f"🔨 Creating index '{index_name}' with 384 dimensions...")
        
        pc.create_index(
            name=index_name,
            dimension=384,  # Correct dimension for all-MiniLM-L6-v2
            metric=config["metric"],
            spec=ServerlessSpec(
                cloud=config["cloud"],
                region=config["region"]
            )
        )
        
        logger.info(f"✅ Index '{index_name}' created with correct dimensions!")
        logger.info("⏳ Index may take a few minutes to be fully ready")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix index: {e}")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Simplified Vector Database Test - TKC_v5 Milestone 3

This test validates vector database functionality without requiring authentication.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockSettings:
    """Mock settings for testing without authentication."""
    def __init__(self):
        self.project_id = "test-project"
    
    def _get_secret(self, secret_name):
        return None


async def test_vector_database_basic():
    """Test basic vector database functionality."""
    logger.info("🧪 Testing Vector Database Basic Functionality...")
    
    try:
        # Import here to avoid authentication issues
        from src.services.vector_db_client import VectorDBClient
        
        # Create mock settings
        settings = MockSettings()
        
        # Initialize vector database in mock mode
        vector_db = VectorDBClient(settings, mock_mode=True)
        success = await vector_db.initialize()
        
        if not success:
            logger.error("❌ Failed to initialize vector database")
            return False
        
        logger.info("✅ Vector database initialized successfully")
        
        # Test health check
        health = await vector_db.health_check()
        logger.info(f"📊 Health check: {health}")
        
        if health["status"] != "healthy":
            logger.error("❌ Vector database health check failed")
            return False
        
        logger.info("✅ Health check passed")
        
        # Test storing a message
        message_id = "test_msg_001"
        conversation_id = "test_conv_001"
        content = "Hello, I need help with my project timeline and budget planning"
        
        store_success = await vector_db.store_conversation_message(
            message_id=message_id,
            conversation_id=conversation_id,
            content=content,
            message_type="human",
            metadata={
                "customer_email": "<EMAIL>",
                "timestamp": datetime.now().isoformat(),
                "test": True
            }
        )
        
        if not store_success:
            logger.error("❌ Failed to store message")
            return False
        
        logger.info("✅ Message stored successfully")
        
        # Test searching for similar messages
        search_results = await vector_db.search_similar_messages(
            query="project timeline help",
            top_k=5,
            min_score=0.1  # Low threshold for testing
        )
        
        if not search_results:
            logger.error("❌ No search results found")
            return False
        
        logger.info(f"✅ Search successful - found {len(search_results)} results")
        
        # Verify the result
        first_result = search_results[0]
        if first_result["id"] != message_id:
            logger.error(f"❌ Wrong message returned: expected {message_id}, got {first_result['id']}")
            return False
        
        logger.info(f"✅ Correct message retrieved with score: {first_result['score']:.3f}")
        
        # Test conversation context retrieval
        context_messages = await vector_db.get_conversation_context(
            conversation_id=conversation_id,
            max_messages=10
        )
        
        if not context_messages:
            logger.error("❌ No conversation context found")
            return False
        
        logger.info(f"✅ Conversation context retrieved - {len(context_messages)} messages")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Vector database test failed: {e}")
        return False


async def test_embeddings_model():
    """Test embeddings model functionality."""
    logger.info("🧪 Testing Embeddings Model...")
    
    try:
        from src.services.vector_db_client import VectorDBClient
        
        settings = MockSettings()
        vector_db = VectorDBClient(settings, mock_mode=True)
        await vector_db.initialize()
        
        # Test embedding generation
        test_texts = [
            "I need help with project management",
            "Can you assist with budget planning?",
            "What are the best practices for timeline management?",
            "How do I handle resource allocation?",
            "Meeting scheduling and coordination"
        ]
        
        embeddings = []
        for text in test_texts:
            embedding = vector_db._create_embedding(text)
            embeddings.append(embedding)
            logger.info(f"✅ Generated embedding for: '{text[:30]}...' (dim: {len(embedding)})")
        
        # Test similarity calculation
        similarity = vector_db._cosine_similarity(embeddings[0], embeddings[1])
        logger.info(f"✅ Similarity between first two texts: {similarity:.3f}")
        
        # Verify embedding dimensions
        expected_dim = 384  # all-MiniLM-L6-v2 dimension
        if len(embeddings[0]) != expected_dim:
            logger.error(f"❌ Wrong embedding dimension: expected {expected_dim}, got {len(embeddings[0])}")
            return False
        
        logger.info(f"✅ Embedding dimension correct: {len(embeddings[0])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Embeddings model test failed: {e}")
        return False


async def test_semantic_search_basic():
    """Test basic semantic search functionality."""
    logger.info("🧪 Testing Semantic Search Basic Functionality...")
    
    try:
        from src.services.semantic_search import SemanticSearchService, SearchQuery
        
        settings = MockSettings()
        search_service = SemanticSearchService(settings, mock_mode=True)
        success = await search_service.initialize()
        
        if not success:
            logger.error("❌ Failed to initialize semantic search service")
            return False
        
        logger.info("✅ Semantic search service initialized")
        
        # Test health check
        health = await search_service.health_check()
        if health["status"] != "healthy":
            logger.error("❌ Semantic search health check failed")
            return False
        
        logger.info("✅ Semantic search health check passed")
        
        # Populate some test data first
        test_messages = [
            {
                "id": "msg_001",
                "conversation_id": "conv_001",
                "content": "I need help with project timeline management",
                "customer_email": "<EMAIL>",
                "message_type": "human"
            },
            {
                "id": "msg_002", 
                "conversation_id": "conv_002",
                "content": "Can we schedule a budget review meeting?",
                "customer_email": "<EMAIL>",
                "message_type": "human"
            },
            {
                "id": "msg_003",
                "conversation_id": "conv_003", 
                "content": "The contract terms need clarification",
                "customer_email": "<EMAIL>",
                "message_type": "human"
            }
        ]
        
        # Store test messages
        for msg in test_messages:
            await search_service.vector_db.store_conversation_message(
                message_id=msg["id"],
                conversation_id=msg["conversation_id"],
                content=msg["content"],
                message_type=msg["message_type"],
                metadata={
                    "customer_email": msg["customer_email"],
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        logger.info(f"✅ Stored {len(test_messages)} test messages")
        
        # Test basic search
        search_query = SearchQuery(
            query="project management help",
            max_results=5,
            min_score=0.1
        )
        
        results = await search_service.search(search_query)
        
        if not results:
            logger.error("❌ No search results found")
            return False
        
        logger.info(f"✅ Search found {len(results)} results")
        
        # Test customer-specific search
        customer_query = SearchQuery(
            query="meeting schedule",
            customer_email="<EMAIL>",
            max_results=5,
            min_score=0.1
        )
        
        customer_results = await search_service.search(customer_query)
        logger.info(f"✅ Customer-specific search found {len(customer_results)} results")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Semantic search test failed: {e}")
        return False


async def test_performance():
    """Test performance of vector operations."""
    logger.info("🚀 Testing Performance...")
    
    try:
        import time
        from src.services.vector_db_client import VectorDBClient
        
        settings = MockSettings()
        vector_db = VectorDBClient(settings, mock_mode=True)
        await vector_db.initialize()
        
        # Test batch storage performance
        start_time = time.time()
        
        test_messages = [
            f"This is test message number {i} about various business topics like project management, budget planning, and timeline coordination."
            for i in range(20)
        ]
        
        for i, message in enumerate(test_messages):
            await vector_db.store_conversation_message(
                message_id=f"perf_msg_{i:03d}",
                conversation_id="perf_test_conv",
                content=message,
                message_type="human",
                metadata={"test_index": i}
            )
        
        storage_time = time.time() - start_time
        logger.info(f"✅ Stored {len(test_messages)} messages in {storage_time:.2f}s ({storage_time/len(test_messages):.3f}s per message)")
        
        # Test search performance
        start_time = time.time()
        
        search_queries = [
            "project management",
            "budget planning", 
            "timeline coordination",
            "business topics",
            "test message"
        ]
        
        total_results = 0
        for query in search_queries:
            results = await vector_db.search_similar_messages(
                query=query,
                top_k=5,
                min_score=0.1
            )
            total_results += len(results)
        
        search_time = time.time() - start_time
        logger.info(f"✅ Performed {len(search_queries)} searches in {search_time:.2f}s ({search_time/len(search_queries):.3f}s per search)")
        logger.info(f"✅ Total results found: {total_results}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance test failed: {e}")
        return False


async def main():
    """Run all simplified tests."""
    logger.info("🚀 Starting Simplified Vector Database Tests for TKC_v5 Milestone 3")
    logger.info("=" * 70)
    
    tests = [
        ("Vector Database Basic", test_vector_database_basic),
        ("Embeddings Model", test_embeddings_model),
        ("Semantic Search Basic", test_semantic_search_basic),
        ("Performance", test_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Vector Database functionality is working correctly!")
        logger.info("📋 Validated capabilities:")
        logger.info("   ✅ Vector embeddings generation (384-dimensional)")
        logger.info("   ✅ Message storage and retrieval")
        logger.info("   ✅ Semantic similarity search")
        logger.info("   ✅ Conversation context management")
        logger.info("   ✅ Performance benchmarks")
        logger.info("\n🚀 Ready for Phase 2 completion and production deployment!")
        return True
    else:
        logger.error("⚠️  Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

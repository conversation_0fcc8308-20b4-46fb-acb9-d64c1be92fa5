#!/bin/bash

# This script assigns IAM roles to Google Groups following the architecture document
# Based on Table 1: Essential IAM Roles for an Agent Development Team
#
# Usage: ./assign_iam_roles.sh <DOMAIN> <PROJECT_ID>

# --- Configuration ---
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <DOMAIN> <PROJECT_ID>"
    echo "Example: $0 tkcgroup.co vertex-ai-agent-yzdlnjey"
    exit 1
fi

DOMAIN=$1
PROJECT_ID=$2

echo "Assigning IAM roles for project: ${PROJECT_ID}"
echo "Domain: ${DOMAIN}"
echo ""

# Define role assignments according to the architecture document
# AI Developer roles
AI_DEV_GROUP="gcp-ai-developers@${DOMAIN}"
AI_DEV_ROLES=(
    "roles/aiplatform.user"
    "roles/storage.objectAdmin"
    "roles/artifactregistry.writer"
    "roles/logging.viewer"
)

# MLOps Engineer roles
MLOPS_GROUP="gcp-mlops-engineers@${DOMAIN}"
MLOPS_ROLES=(
    "roles/run.admin"
    "roles/cloudbuild.builds.editor"
    "roles/iam.serviceAccountAdmin"
    "roles/resourcemanager.projectIamAdmin"
)

# Data Analyst/Reviewer roles
DATA_GROUP="gcp-data-reviewers@${DOMAIN}"
DATA_ROLES=(
    "roles/bigquery.dataViewer"
    "roles/storage.objectViewer"
)

# Function to assign roles to a group
assign_roles_to_group() {
    local group=$1
    shift
    local roles=("$@")
    
    echo "Assigning roles to ${group}:"
    for role in "${roles[@]}"; do
        echo "  - ${role}"
        gcloud projects add-iam-policy-binding ${PROJECT_ID} \
            --member="group:${group}" \
            --role="${role}"
        
        if [ $? -eq 0 ]; then
            echo "    ✓ Successfully assigned ${role}"
        else
            echo "    ⚠ Failed to assign ${role}"
        fi
    done
    echo ""
}

# Check if groups exist (optional - will proceed anyway)
echo "Checking if groups exist..."
for group in "${AI_DEV_GROUP}" "${MLOPS_GROUP}" "${DATA_GROUP}"; do
    echo "Checking: ${group}"
    # Note: We'll proceed with role assignment even if we can't verify group existence
done
echo ""

# Assign roles to each group
echo "=== Assigning AI Developer Roles ==="
assign_roles_to_group "${AI_DEV_GROUP}" "${AI_DEV_ROLES[@]}"

echo "=== Assigning MLOps Engineer Roles ==="
assign_roles_to_group "${MLOPS_GROUP}" "${MLOPS_ROLES[@]}"

echo "=== Assigning Data Reviewer Roles ==="
assign_roles_to_group "${DATA_GROUP}" "${DATA_ROLES[@]}"

echo "IAM role assignment complete!"
echo ""
echo "Next steps:"
echo "1. Create service accounts for AI agents"
echo "2. Add users to the appropriate Google Groups"
echo "3. Test permissions with each role"
echo ""
echo "To view current IAM policy:"
echo "gcloud projects get-iam-policy ${PROJECT_ID}"

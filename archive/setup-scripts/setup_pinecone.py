#!/usr/bin/env python3
"""
Pinecone setup script for TKC_v5 Milestone 3.

This script helps set up Pinecone vector database for conversation storage and RAG capabilities.
"""

import os
import json
import logging
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_pinecone_config_template():
    """Create a template configuration file for Pinecone."""
    config_template = {
        "api_key": "YOUR_PINECONE_API_KEY_HERE",
        "environment": "us-east-1-aws",  # Free tier default
        "index_name": "tkc-conversations",
        "dimension": 384,  # For sentence-transformers/all-MiniLM-L6-v2
        "metric": "cosine",
        "cloud": "aws",
        "region": "us-east-1"
    }
    
    config_file = "pinecone_config.json"
    
    try:
        with open(config_file, 'w') as f:
            json.dump(config_template, f, indent=2)
        
        logger.info(f"✅ Created Pinecone configuration template: {config_file}")
        logger.info("📋 Next steps:")
        logger.info("   1. Sign up for Pinecone at https://www.pinecone.io/")
        logger.info("   2. Get your API key from the Pinecone console")
        logger.info(f"   3. Edit {config_file} and replace YOUR_PINECONE_API_KEY_HERE")
        logger.info("   4. Run this script again to create the index")
        
        return config_file
        
    except Exception as e:
        logger.error(f"❌ Failed to create config template: {e}")
        return None


def load_pinecone_config() -> Optional[Dict[str, Any]]:
    """Load Pinecone configuration from file."""
    config_file = "pinecone_config.json"
    
    if not os.path.exists(config_file):
        logger.warning(f"⚠️  Configuration file {config_file} not found")
        return None
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        if config.get("api_key") == "YOUR_PINECONE_API_KEY_HERE":
            logger.warning("⚠️  Please update the API key in pinecone_config.json")
            return None
        
        logger.info("✅ Pinecone configuration loaded successfully")
        return config
        
    except Exception as e:
        logger.error(f"❌ Failed to load config: {e}")
        return None


def test_pinecone_connection(config: Dict[str, Any]) -> bool:
    """Test connection to Pinecone."""
    try:
        from pinecone import Pinecone
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        
        # List existing indexes
        indexes = pc.list_indexes()
        logger.info(f"✅ Connected to Pinecone successfully")
        logger.info(f"📊 Existing indexes: {[idx.name for idx in indexes]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to Pinecone: {e}")
        return False


def create_conversation_index(config: Dict[str, Any]) -> bool:
    """Create the conversation index in Pinecone."""
    try:
        from pinecone import Pinecone, ServerlessSpec
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        
        index_name = config["index_name"]
        
        # Check if index already exists
        existing_indexes = [idx.name for idx in pc.list_indexes()]
        
        if index_name in existing_indexes:
            logger.info(f"✅ Index '{index_name}' already exists")
            return True
        
        # Create index with serverless spec (free tier)
        logger.info(f"🔨 Creating index '{index_name}'...")
        
        pc.create_index(
            name=index_name,
            dimension=config["dimension"],
            metric=config["metric"],
            spec=ServerlessSpec(
                cloud=config["cloud"],
                region=config["region"]
            )
        )
        
        logger.info(f"✅ Index '{index_name}' created successfully!")
        logger.info("📋 Index specifications:")
        logger.info(f"   - Dimension: {config['dimension']}")
        logger.info(f"   - Metric: {config['metric']}")
        logger.info(f"   - Cloud: {config['cloud']}")
        logger.info(f"   - Region: {config['region']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create index: {e}")
        return False


def test_index_operations(config: Dict[str, Any]) -> bool:
    """Test basic index operations."""
    try:
        from pinecone import Pinecone
        import numpy as np
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        index = pc.Index(config["index_name"])
        
        # Test vector (768 dimensions for sentence-transformers)
        test_vector = np.random.random(config["dimension"]).tolist()
        test_id = "test_conversation_001"
        test_metadata = {
            "conversation_id": "test_thread_123",
            "message_type": "human",
            "content": "Hello, I need help with email automation",
            "timestamp": "2025-01-26T20:00:00Z",
            "customer_email": "<EMAIL>"
        }
        
        # Upsert test vector
        logger.info("🧪 Testing vector upsert...")
        index.upsert(vectors=[(test_id, test_vector, test_metadata)])
        logger.info("✅ Vector upsert successful")
        
        # Query test
        logger.info("🔍 Testing vector query...")
        query_results = index.query(
            vector=test_vector,
            top_k=1,
            include_metadata=True
        )
        
        if query_results.matches:
            match = query_results.matches[0]
            logger.info(f"✅ Query successful - Score: {match.score:.4f}")
            logger.info(f"📄 Metadata: {match.metadata}")
        else:
            logger.warning("⚠️  No query results returned")
        
        # Cleanup test vector
        logger.info("🧹 Cleaning up test data...")
        index.delete(ids=[test_id])
        logger.info("✅ Cleanup successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Index operations test failed: {e}")
        return False


def save_config_to_secret_manager(config: Dict[str, Any]) -> bool:
    """Save Pinecone configuration to Google Secret Manager."""
    try:
        import subprocess
        
        # Create secret data
        secret_data = {
            "api_key": config["api_key"],
            "environment": config["environment"],
            "index_name": config["index_name"],
            "dimension": config["dimension"],
            "metric": config["metric"]
        }
        
        # Convert to JSON string
        secret_json = json.dumps(secret_data)
        
        # Create secret in Secret Manager
        cmd = [
            "gcloud", "secrets", "create", "pinecone-config",
            "--data-file=-",
            "--project=vertex-ai-agent-yzdlnjey"
        ]
        
        result = subprocess.run(
            cmd,
            input=secret_json,
            text=True,
            capture_output=True
        )
        
        if result.returncode == 0:
            logger.info("✅ Pinecone configuration saved to Secret Manager")
            return True
        else:
            logger.error(f"❌ Failed to save to Secret Manager: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error saving to Secret Manager: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("🚀 TKC_v5 Pinecone Setup for Milestone 3")
    logger.info("=" * 50)
    
    # Load or create configuration
    config = load_pinecone_config()
    
    if not config:
        logger.info("📝 Creating configuration template...")
        config_file = create_pinecone_config_template()
        if config_file:
            logger.info(f"✅ Please edit {config_file} with your Pinecone API key and run again")
        return False
    
    # Test connection
    logger.info("🔗 Testing Pinecone connection...")
    if not test_pinecone_connection(config):
        return False
    
    # Create index
    logger.info("🏗️  Setting up conversation index...")
    if not create_conversation_index(config):
        return False
    
    # Test operations
    logger.info("🧪 Testing index operations...")
    if not test_index_operations(config):
        return False
    
    # Save to Secret Manager (optional)
    logger.info("💾 Saving configuration to Secret Manager...")
    save_config_to_secret_manager(config)
    
    # Success summary
    logger.info("\n🎉 Pinecone setup completed successfully!")
    logger.info("📋 Summary:")
    logger.info(f"   - Index: {config['index_name']}")
    logger.info(f"   - Dimension: {config['dimension']}")
    logger.info(f"   - Metric: {config['metric']}")
    logger.info(f"   - Region: {config['region']}")
    logger.info("\n✅ Ready for vector database integration!")
    
    return True


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

#!/bin/bash

# This script sets up Google Groups for IAM management following enterprise best practices
# Note: This requires Google Workspace admin privileges or Cloud Identity admin access
#
# Usage: ./setup_iam_groups.sh <DOMAIN> <PROJECT_ID>

# --- Configuration ---
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <DOMAIN> <PROJECT_ID>"
    echo "Example: $0 tkcgroup.co vertex-ai-agent-yzdlnjey"
    exit 1
fi

DOMAIN=$1
PROJECT_ID=$2

# Define the groups according to the architecture document
declare -A GROUPS=(
    ["gcp-ai-developers"]="AI developers with access to Vertex AI and development resources"
    ["gcp-mlops-engineers"]="MLOps engineers with deployment and infrastructure management access"
    ["gcp-data-reviewers"]="Data analysts with read-only access to data sources"
)

echo "Setting up Google Groups for IAM management..."
echo "Domain: ${DOMAIN}"
echo "Project: ${PROJECT_ID}"
echo ""

# Check if we can access Cloud Identity/Google Workspace
echo "Checking Cloud Identity access..."
ORG_ID=$(gcloud organizations list --format="value(name)" --filter="displayName:${DOMAIN}")
gcloud identity groups search --organization="${ORG_ID}" --query="parent=='${ORG_ID}'" --limit=1

if [ $? -ne 0 ]; then
    echo "WARNING: Cannot access Cloud Identity groups directly."
    echo "This typically means you need Google Workspace admin privileges."
    echo ""
    echo "Alternative approaches:"
    echo "1. Use Google Admin Console: https://admin.google.com"
    echo "2. Use Cloud Console Groups: https://console.cloud.google.com/iam-admin/groups"
    echo "3. Use Terraform with appropriate service account"
    echo ""
    echo "Manual steps to create groups:"
    for group in "${!GROUPS[@]}"; do
        echo "- Create group: ${group}@${DOMAIN}"
        echo "  Description: ${GROUPS[$group]}"
    done
    echo ""
    echo "After creating groups manually, run the IAM role assignment script."
    exit 1
fi

# If we have access, create the groups
echo "Creating Google Groups..."
ORG_ID=$(gcloud organizations list --format="value(name)" --filter="displayName:${DOMAIN}")

for group in "${!GROUPS[@]}"; do
    GROUP_EMAIL="${group}@${DOMAIN}"
    echo "Creating group: ${GROUP_EMAIL}"

    # Try to create the group using gcloud
    gcloud identity groups create "${GROUP_EMAIL}" \
        --display-name="${group}" \
        --description="${GROUPS[$group]}" \
        --organization="${ORG_ID}"

    if [ $? -eq 0 ]; then
        echo "✓ Group ${GROUP_EMAIL} created successfully"
    else
        echo "⚠ Failed to create group ${GROUP_EMAIL} (may already exist)"
    fi
done

echo ""
echo "Google Groups setup complete!"
echo "Next step: Run ./assign_iam_roles.sh to assign IAM roles to these groups"

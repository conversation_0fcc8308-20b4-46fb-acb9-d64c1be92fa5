#!/bin/bash

# This script provisions a new, production-ready GCP project for an AI agent.
# It creates the project, links it to a billing account, and enables
# all necessary APIs for Vertex AI, deployment, and observability.
#
# Usage: ./provision_project.sh <PROJECT_ID_PREFIX> <BILLING_ACCOUNT_ID> <ORGANIZATION_ID>

# --- Configuration ---
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <PROJECT_ID_PREFIX> <BILLING_ACCOUNT_ID> <ORGANIZATION_ID>"
    echo ""
    echo "Available billing accounts:"
    gcloud billing accounts list
    echo ""
    echo "Available organizations:"
    gcloud organizations list
    exit 1
fi

PROJECT_ID_PREFIX=$1
BILLING_ACCOUNT_ID=$2
ORGANIZATION_ID=$3

# Generate a unique project ID to avoid collisions
UNIQUE_SUFFIX=$(date +%s | sha256sum | base64 | head -c 8 | tr '[:upper:]' '[:lower:]')
PROJECT_ID="${PROJECT_ID_PREFIX}-${UNIQUE_SUFFIX}"
PROJECT_NAME="Vertex AI Agent Project"

# List of essential APIs for the agent workload
APIS_TO_ENABLE=(
  "cloudresourcemanager.googleapis.com"
  "billing.googleapis.com"
  "iam.googleapis.com"
  "iamcredentials.googleapis.com"
  "aiplatform.googleapis.com"
  "cloudbuild.googleapis.com"
  "artifactregistry.googleapis.com"
  "run.googleapis.com"
  "logging.googleapis.com"
  "monitoring.googleapis.com"
  "secretmanager.googleapis.com"
)

# --- Execution ---

echo "STEP 1: Creating GCP Project '${PROJECT_ID}'..."
gcloud projects create ${PROJECT_ID} \
  --name="${PROJECT_NAME}" \
  --organization=${ORGANIZATION_ID}

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to create project. Aborting."
    exit 1
fi
echo "Project '${PROJECT_ID}' created successfully."

echo "STEP 2: Linking Billing Account '${BILLING_ACCOUNT_ID}'..."
gcloud billing projects link ${PROJECT_ID} \
  --billing-account=${BILLING_ACCOUNT_ID}

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to link billing account. Aborting."
    # Optional: Add cleanup logic to delete the created project
    # gcloud projects delete ${PROJECT_ID} --quiet
    exit 1
fi
echo "Billing account linked successfully."

echo "STEP 3: Enabling necessary APIs..."
gcloud services enable ${APIS_TO_ENABLE[@]} --project=${PROJECT_ID}

if [ $? -ne 0 ]; then
    echo "ERROR: Failed to enable one or more APIs. Please check the logs."
    exit 1
fi
echo "All required APIs enabled successfully."

echo "---"
echo "Project Provisioning Complete!"
echo "Project ID: ${PROJECT_ID}"
echo "Run 'gcloud config set project ${PROJECT_ID}' to start working."
echo ""
echo "Next steps:"
echo "1. Set up IAM groups and policies"
echo "2. Create service accounts for AI agents"
echo "3. Configure development environment"

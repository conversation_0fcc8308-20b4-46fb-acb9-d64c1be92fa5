#!/usr/bin/env python3
"""
Direct Pinecone Setup for TKC_v5 Enhanced Agent

This script sets up production Pinecone using the existing configuration file.
"""

import os
import json
import logging
import subprocess
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_config() -> Optional[Dict[str, Any]]:
    """Load Pinecone configuration from file."""
    config_file = "pinecone_config.json"
    
    if not os.path.exists(config_file):
        logger.error(f"Configuration file {config_file} not found")
        return None
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        logger.info("✅ Configuration loaded successfully")
        return config
        
    except Exception as e:
        logger.error(f"❌ Failed to load config: {e}")
        return None


def test_pinecone_connection(config: Dict[str, Any]) -> bool:
    """Test connection to Pinecone."""
    try:
        from pinecone import Pinecone
        
        logger.info("📡 Testing Pinecone Connection...")
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        
        # List existing indexes
        indexes = pc.list_indexes()
        logger.info(f"✅ Connected to Pinecone successfully")
        logger.info(f"📊 Existing indexes: {[idx.name for idx in indexes]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to connect to Pinecone: {e}")
        return False


def create_production_index(config: Dict[str, Any]) -> bool:
    """Create the production vector index."""
    try:
        from pinecone import Pinecone, ServerlessSpec
        
        logger.info("🏗️  Creating Production Vector Index...")
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        
        index_name = config["index_name"]
        
        # Check if index already exists
        existing_indexes = [idx.name for idx in pc.list_indexes()]
        
        if index_name in existing_indexes:
            logger.info(f"✅ Index '{index_name}' already exists")
            return True
        
        # Create index with serverless spec (free tier)
        logger.info(f"🔨 Creating index '{index_name}' with:")
        logger.info(f"   - Dimension: {config['dimension']}")
        logger.info(f"   - Metric: {config['metric']}")
        logger.info(f"   - Cloud: {config['cloud']}")
        logger.info(f"   - Region: {config['region']}")
        
        pc.create_index(
            name=index_name,
            dimension=config["dimension"],
            metric=config["metric"],
            spec=ServerlessSpec(
                cloud=config["cloud"],
                region=config["region"]
            )
        )
        
        logger.info(f"✅ Index '{index_name}' created successfully!")
        logger.info("⏳ Index may take a few minutes to be fully ready")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to create index: {e}")
        return False


def test_production_operations(config: Dict[str, Any]) -> bool:
    """Test basic operations on the production index."""
    try:
        from pinecone import Pinecone
        import numpy as np
        
        logger.info("🧪 Testing Production Index Operations...")
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        index = pc.Index(config["index_name"])
        
        # Test vector (384 dimensions for sentence-transformers)
        test_vector = np.random.random(config["dimension"]).tolist()
        test_id = "production_test_001"
        test_metadata = {
            "conversation_id": "test_thread_prod",
            "message_type": "human",
            "content": "Production test message for TKC_v5 enhanced agent",
            "timestamp": "2025-01-26T20:00:00Z",
            "customer_email": "<EMAIL>"
        }
        
        # Upsert test vector
        logger.info("📤 Testing vector upsert...")
        index.upsert(vectors=[(test_id, test_vector, test_metadata)])
        logger.info("✅ Vector upsert successful")
        
        # Query test
        logger.info("🔍 Testing vector query...")
        query_results = index.query(
            vector=test_vector,
            top_k=1,
            include_metadata=True
        )
        
        if query_results.matches:
            match = query_results.matches[0]
            logger.info(f"✅ Query successful - Score: {match.score:.4f}")
            logger.info(f"📄 Retrieved metadata: {match.metadata['content'][:50]}...")
        else:
            logger.warning("⚠️  No query results returned")
        
        # Cleanup test vector
        logger.info("🧹 Cleaning up test data...")
        index.delete(ids=[test_id])
        logger.info("✅ Cleanup successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Production operations test failed: {e}")
        return False


def save_to_secret_manager(config: Dict[str, Any]) -> bool:
    """Save Pinecone configuration to Google Secret Manager."""
    try:
        logger.info("🔐 Saving to Google Cloud Secret Manager...")
        
        # Create secret data (exclude sensitive info from logs)
        secret_data = {
            "api_key": config["api_key"],
            "environment": config["environment"],
            "index_name": config["index_name"],
            "dimension": config["dimension"],
            "metric": config["metric"]
        }
        
        # Convert to JSON string
        secret_json = json.dumps(secret_data)
        
        # Check if secret already exists
        check_cmd = [
            "gcloud", "secrets", "describe", "pinecone-config",
            "--project=vertex-ai-agent-yzdlnjey"
        ]
        
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Secret exists, update it
            cmd = [
                "gcloud", "secrets", "versions", "add", "pinecone-config",
                "--data-file=-",
                "--project=vertex-ai-agent-yzdlnjey"
            ]
            action = "updated"
        else:
            # Secret doesn't exist, create it
            cmd = [
                "gcloud", "secrets", "create", "pinecone-config",
                "--data-file=-",
                "--project=vertex-ai-agent-yzdlnjey"
            ]
            action = "created"
        
        result = subprocess.run(
            cmd,
            input=secret_json,
            text=True,
            capture_output=True
        )
        
        if result.returncode == 0:
            logger.info(f"✅ Pinecone configuration {action} in Secret Manager")
            return True
        else:
            logger.error(f"❌ Failed to save to Secret Manager: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error saving to Secret Manager: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("🚀 TKC_v5 Enhanced Agent - Production Pinecone Setup")
    logger.info("=" * 60)
    
    # Load configuration
    config = load_config()
    if not config:
        return False
    
    # Test connection
    if not test_pinecone_connection(config):
        return False
    
    # Create index
    if not create_production_index(config):
        return False
    
    # Test operations
    if not test_production_operations(config):
        return False
    
    # Save to Secret Manager
    if not save_to_secret_manager(config):
        logger.warning("⚠️  Secret Manager save failed, but Pinecone is ready")
    
    # Success summary
    logger.info("\n🎉 Production Pinecone Setup Complete!")
    logger.info("=" * 60)
    logger.info("📋 Summary:")
    logger.info(f"   ✅ Index: {config['index_name']}")
    logger.info(f"   ✅ Dimension: {config['dimension']}")
    logger.info(f"   ✅ Metric: {config['metric']}")
    logger.info(f"   ✅ Region: {config['region']}")
    logger.info(f"   ✅ Configuration saved to Secret Manager")
    logger.info()
    logger.info("🚀 Ready for Enhanced TKC_v5 Agent Deployment!")
    logger.info("📋 Next steps:")
    logger.info("   1. Test production vector database integration")
    logger.info("   2. Deploy the enhanced agent with vector database")
    logger.info("   3. Verify RAG capabilities with real conversations")
    
    return True


if __name__ == "__main__":
    import sys
    
    # Check dependencies
    try:
        import pinecone
        import numpy
    except ImportError as e:
        logger.error(f"❌ Missing dependency: {e}")
        logger.info("💡 Run: pip3 install pinecone numpy")
        sys.exit(1)
    
    success = main()
    sys.exit(0 if success else 1)

#!/bin/bash

# Deploy TKC_v5 Executive Agent to Cloud Run
# Production-ready deployment with proper configuration

set -e  # Exit on any error

PROJECT_ID="vertex-ai-agent-yzdlnjey"
REGION="us-central1"
SERVICE_NAME="vertex-ai-agent"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"
SERVICE_ACCOUNT="agent-executor-sa@${PROJECT_ID}.iam.gserviceaccount.com"

echo "🚀 Deploying TKC_v5 Executive Agent to Cloud Run"
echo "================================================"
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Service: ${SERVICE_NAME}"
echo "Image: ${IMAGE_NAME}"
echo "Service Account: ${SERVICE_ACCOUNT}"
echo ""

# Ensure we're using the correct project
gcloud config set project ${PROJECT_ID}

# Step 1: Build and push Docker image
echo "📦 Building Docker image..."
docker build -t ${IMAGE_NAME}:latest .

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed"
    exit 1
fi

echo "📤 Pushing image to Container Registry..."
docker push ${IMAGE_NAME}:latest

if [ $? -ne 0 ]; then
    echo "❌ Docker push failed"
    exit 1
fi

# Step 2: Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME}:latest \
    --platform managed \
    --region ${REGION} \
    --service-account ${SERVICE_ACCOUNT} \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 300 \
    --concurrency 100 \
    --min-instances 0 \
    --max-instances 10 \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
    --set-env-vars "VERTEX_AI_LOCATION=${REGION}" \
    --set-env-vars "ENVIRONMENT=production" \
    --set-env-vars "LOG_LEVEL=INFO" \
    --port 8080

if [ $? -ne 0 ]; then
    echo "❌ Cloud Run deployment failed"
    exit 1
fi

# Step 3: Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format="value(status.url)")

echo ""
echo "✅ Deployment successful!"
echo "🌐 Service URL: ${SERVICE_URL}"
echo ""

# Step 4: Test the deployment
echo "🧪 Testing deployment..."
echo "Health check:"
curl -f "${SERVICE_URL}/health" || echo "❌ Health check failed"

echo ""
echo "API test:"
curl -X POST "${SERVICE_URL}/test" \
    -H "Content-Type: application/json" \
    || echo "❌ API test failed"

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "Next steps:"
echo "1. Set up Gmail domain delegation (manual step required)"
echo "2. Upload Gmail service account key to Secret Manager"
echo "3. Test email functionality"
echo "4. Set up monitoring and alerting"
echo ""
echo "Service endpoints:"
echo "- Health: ${SERVICE_URL}/health"
echo "- Chat: ${SERVICE_URL}/chat"
echo "- Tools: ${SERVICE_URL}/tools"
echo "- Test: ${SERVICE_URL}/test"

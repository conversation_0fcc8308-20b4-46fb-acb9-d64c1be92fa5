#!/bin/bash

# Account Sync Diagnostic Script
# This script checks for account sync issues between Google Workspace and Cloud Identity

echo "=== ACCOUNT SYNC DIAGNOSTIC ==="
echo "Date: $(date)"
echo "User: $(gcloud config get-value account)"
echo ""

# 1. Check current authentication
echo "1. AUTHENTICATION STATUS"
echo "========================"
gcloud auth list
echo ""

# 2. Check organization access
echo "2. ORGANIZATION ACCESS"
echo "======================"
ORG_ID="************"
echo "Organization ID: $ORG_ID"
gcloud organizations describe $ORG_ID
echo ""

# 3. Check organization-level permissions
echo "3. ORGANIZATION-LEVEL PERMISSIONS"
echo "=================================="
echo "Your roles at organization level:"
gcloud organizations get-iam-policy $ORG_ID \
  --flatten="bindings[].members" \
  --format="table(bindings.role)" \
  --filter="bindings.members:$(gcloud config get-value account)"
echo ""

# 4. Check project creation permissions
echo "4. PROJECT CREATION PERMISSIONS"
echo "==============================="
echo "Checking if you can create projects..."
gcloud resource-manager operations list --limit=5
echo ""

# 5. Check all projects in org and your permissions
echo "5. PROJECT PERMISSIONS AUDIT"
echo "============================"
echo "All projects in organization:"
gcloud projects list --filter="parent.id:$ORG_ID" --format="table(projectId,name,projectNumber)"
echo ""

echo "Your permissions on each project:"
for project in $(gcloud projects list --filter="parent.id:$ORG_ID" --format="value(projectId)"); do
    echo "--- Project: $project ---"
    gcloud projects get-iam-policy $project \
      --flatten="bindings[].members" \
      --format="table(bindings.role)" \
      --filter="bindings.members:$(gcloud config get-value account)" 2>/dev/null || echo "No permissions or access denied"
    echo ""
done

# 6. Check Google Workspace integration
echo "6. GOOGLE WORKSPACE INTEGRATION"
echo "==============================="
echo "Checking Cloud Identity integration..."
gcloud identity groups search --organization="organizations/$ORG_ID" --limit=1 2>/dev/null || echo "Cannot access Cloud Identity groups (may require Workspace admin)"
echo ""

# 7. Check billing account access
echo "7. BILLING ACCOUNT ACCESS"
echo "========================="
echo "Billing accounts you can access:"
gcloud billing accounts list
echo ""

# 8. Check for any conditional IAM policies
echo "8. CONDITIONAL IAM POLICIES"
echo "==========================="
echo "Checking for conditional policies that might affect access..."
gcloud projects get-iam-policy vertex-ai-agent-yzdlnjey --format="yaml" | grep -A 5 -B 5 "condition:" || echo "No conditional policies found"
echo ""

# 9. Check domain verification
echo "9. DOMAIN VERIFICATION"
echo "======================"
echo "Checking domain verification status..."
gcloud domains list-user-verified 2>/dev/null || echo "Cannot check domain verification (may require additional permissions)"
echo ""

echo "=== DIAGNOSTIC COMPLETE ==="
echo ""
echo "RECOMMENDATIONS:"
echo "1. If you see permission gaps, check Google Workspace Admin Console"
echo "2. Verify Cloud Identity is properly synced with Workspace"
echo "3. Check if there are any organizational policies restricting access"
echo "4. Consider re-syncing your account or clearing cached credentials"

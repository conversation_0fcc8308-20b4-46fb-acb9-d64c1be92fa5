#!/bin/bash

# Setup GCP Secrets for TKC_v5 Vertex AI Agent Project
# This script creates the necessary secrets for the agent to function properly

PROJECT_ID="vertex-ai-agent-yzdlnjey"

echo "Setting up secrets for project: $PROJECT_ID"
echo "================================================"

# Ensure we're using the correct project
gcloud config set project $PROJECT_ID

# 1. Gmail Service Account Key (for domain delegation)
echo "1. Setting up Gmail Service Account Key..."
echo "   Note: You'll need to create a service account with domain delegation"
echo "   and download the JSON key file manually from the Google Cloud Console."
echo ""
echo "   Steps:"
echo "   a) Go to IAM & Admin > Service Accounts"
echo "   b) Create a new service account: 'gmail-agent-sa'"
echo "   c) Enable domain-wide delegation"
echo "   d) Download the JSON key"
echo "   e) Run: gcloud secrets create gmail-service-account-key --data-file=path/to/key.json"
echo ""

# 2. Agent Configuration
echo "2. Creating agent configuration secret..."
cat > /tmp/agent_config.json << EOF
{
  "model_name": "Gemini-2.5-Flash",
  "location": "us-central1",
  "temperature": 0.7,
  "max_tokens": 8192,
  "persona": {
    "name": "TKC Group BDR",
    "role": "Business Development Representative",
    "company": "TKC Group",
    "tone": "professional and helpful",
    "expertise_areas": ["AI automation", "business process optimization"]
  }
}
EOF

gcloud secrets create agent-config --data-file=/tmp/agent_config.json
if [ $? -eq 0 ]; then
    echo "✓ Agent configuration secret created"
else
    echo "⚠ Agent configuration secret may already exist"
fi

# 3. Gmail Configuration
echo "3. Creating Gmail configuration secret..."
cat > /tmp/gmail_config.json << EOF
{
  "subject_email": "<EMAIL>",
  "scopes": [
    "https://www.googleapis.com/auth/gmail.readonly",
    "https://www.googleapis.com/auth/gmail.send",
    "https://www.googleapis.com/auth/gmail.modify",
    "https://www.googleapis.com/auth/gmail.compose"
  ],
  "domain": "tkcgroup.co"
}
EOF

gcloud secrets create gmail-config --data-file=/tmp/gmail_config.json
if [ $? -eq 0 ]; then
    echo "✓ Gmail configuration secret created"
else
    echo "⚠ Gmail configuration secret may already exist"
fi

# 4. Environment Variables Configuration
echo "4. Creating environment variables secret..."
cat > /tmp/env_vars.json << EOF
{
  "GOOGLE_CLOUD_PROJECT": "$PROJECT_ID",
  "VERTEX_AI_LOCATION": "us-central1",
  "GMAIL_SUBJECT_EMAIL": "<EMAIL>",
  "LOG_LEVEL": "INFO",
  "ENVIRONMENT": "production"
}
EOF

gcloud secrets create env-variables --data-file=/tmp/env_vars.json
if [ $? -eq 0 ]; then
    echo "✓ Environment variables secret created"
else
    echo "⚠ Environment variables secret may already exist"
fi

# 5. Grant service account access to secrets
echo "5. Granting service account access to secrets..."
SERVICE_ACCOUNT="agent-executor-sa@$PROJECT_ID.iam.gserviceaccount.com"

for secret in agent-config gmail-config env-variables; do
    gcloud secrets add-iam-policy-binding $secret \
        --member="serviceAccount:$SERVICE_ACCOUNT" \
        --role="roles/secretmanager.secretAccessor"
    
    if [ $? -eq 0 ]; then
        echo "✓ Granted access to $secret"
    else
        echo "⚠ Failed to grant access to $secret"
    fi
done

# Clean up temporary files
rm -f /tmp/agent_config.json /tmp/gmail_config.json /tmp/env_vars.json

echo ""
echo "================================================"
echo "Secrets setup complete!"
echo ""
echo "Next steps:"
echo "1. Create Gmail service account with domain delegation"
echo "2. Upload the Gmail service account key:"
echo "   gcloud secrets create gmail-service-account-key --data-file=path/to/key.json"
echo "3. Test the agent with the new configuration"
echo ""
echo "To view secrets:"
echo "gcloud secrets list"
echo ""
echo "To access a secret:"
echo "gcloud secrets versions access latest --secret=agent-config"

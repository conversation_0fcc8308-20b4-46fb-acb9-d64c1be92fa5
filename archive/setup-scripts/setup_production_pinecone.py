#!/usr/bin/env python3
"""
Production Pinecone Setup for TKC_v5 Enhanced Agent

This script sets up production Pinecone vector database with the correct configuration
for the enhanced TKC_v5 executive agent deployment.
"""

import os
import json
import logging
import subprocess
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_production_pinecone_config():
    """Create production Pinecone configuration."""
    print("🚀 TKC_v5 Enhanced Agent - Production Pinecone Setup")
    print("=" * 60)
    
    # Get API key from user
    print("\n📋 Step 1: Pinecone API Key")
    print("1. Go to https://www.pinecone.io/")
    print("2. Sign up or log in to your account")
    print("3. Create a new project: 'tkc-executive-agent'")
    print("4. Go to 'API Keys' and copy your API key")
    print()
    
    api_key = input("Enter your Pinecone API key (starts with 'pc-'): ").strip()
    
    if not api_key.startswith('pc-'):
        print("❌ Invalid API key format. Should start with 'pc-'")
        return False
    
    # Create production configuration
    config = {
        "api_key": api_key,
        "environment": "us-east-1-aws",  # Free tier
        "index_name": "tkc-conversations",
        "dimension": 384,  # all-MiniLM-L6-v2 dimension
        "metric": "cosine",
        "cloud": "aws",
        "region": "us-east-1"
    }
    
    # Save configuration
    config_file = "pinecone_config.json"
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Configuration saved to {config_file}")
        return config
        
    except Exception as e:
        print(f"❌ Failed to save configuration: {e}")
        return False


def test_pinecone_connection(config: Dict[str, Any]) -> bool:
    """Test connection to Pinecone."""
    try:
        from pinecone import Pinecone
        
        print("\n📡 Step 2: Testing Pinecone Connection")
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        
        # List existing indexes
        indexes = pc.list_indexes()
        print(f"✅ Connected to Pinecone successfully")
        print(f"📊 Existing indexes: {[idx.name for idx in indexes]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to connect to Pinecone: {e}")
        print("💡 Please check your API key and try again")
        return False


def create_production_index(config: Dict[str, Any]) -> bool:
    """Create the production vector index."""
    try:
        from pinecone import Pinecone, ServerlessSpec
        
        print("\n🏗️  Step 3: Creating Production Vector Index")
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        
        index_name = config["index_name"]
        
        # Check if index already exists
        existing_indexes = [idx.name for idx in pc.list_indexes()]
        
        if index_name in existing_indexes:
            print(f"✅ Index '{index_name}' already exists")
            return True
        
        # Create index with serverless spec (free tier)
        print(f"🔨 Creating index '{index_name}' with:")
        print(f"   - Dimension: {config['dimension']}")
        print(f"   - Metric: {config['metric']}")
        print(f"   - Cloud: {config['cloud']}")
        print(f"   - Region: {config['region']}")
        
        pc.create_index(
            name=index_name,
            dimension=config["dimension"],
            metric=config["metric"],
            spec=ServerlessSpec(
                cloud=config["cloud"],
                region=config["region"]
            )
        )
        
        print(f"✅ Index '{index_name}' created successfully!")
        print("⏳ Index may take a few minutes to be fully ready")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create index: {e}")
        return False


def test_production_operations(config: Dict[str, Any]) -> bool:
    """Test basic operations on the production index."""
    try:
        from pinecone import Pinecone
        import numpy as np
        
        print("\n🧪 Step 4: Testing Production Index Operations")
        
        # Initialize Pinecone client
        pc = Pinecone(api_key=config["api_key"])
        index = pc.Index(config["index_name"])
        
        # Test vector (384 dimensions for sentence-transformers)
        test_vector = np.random.random(config["dimension"]).tolist()
        test_id = "production_test_001"
        test_metadata = {
            "conversation_id": "test_thread_prod",
            "message_type": "human",
            "content": "Production test message for TKC_v5 enhanced agent",
            "timestamp": "2025-01-26T20:00:00Z",
            "customer_email": "<EMAIL>"
        }
        
        # Upsert test vector
        print("📤 Testing vector upsert...")
        index.upsert(vectors=[(test_id, test_vector, test_metadata)])
        print("✅ Vector upsert successful")
        
        # Query test
        print("🔍 Testing vector query...")
        query_results = index.query(
            vector=test_vector,
            top_k=1,
            include_metadata=True
        )
        
        if query_results.matches:
            match = query_results.matches[0]
            print(f"✅ Query successful - Score: {match.score:.4f}")
            print(f"📄 Retrieved metadata: {match.metadata['content'][:50]}...")
        else:
            print("⚠️  No query results returned")
        
        # Cleanup test vector
        print("🧹 Cleaning up test data...")
        index.delete(ids=[test_id])
        print("✅ Cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Production operations test failed: {e}")
        return False


def save_to_secret_manager(config: Dict[str, Any]) -> bool:
    """Save Pinecone configuration to Google Secret Manager."""
    try:
        print("\n🔐 Step 5: Saving to Google Cloud Secret Manager")
        
        # Create secret data (exclude sensitive info from logs)
        secret_data = {
            "api_key": config["api_key"],
            "environment": config["environment"],
            "index_name": config["index_name"],
            "dimension": config["dimension"],
            "metric": config["metric"]
        }
        
        # Convert to JSON string
        secret_json = json.dumps(secret_data)
        
        # Check if secret already exists
        check_cmd = [
            "gcloud", "secrets", "describe", "pinecone-config",
            "--project=vertex-ai-agent-yzdlnjey"
        ]
        
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Secret exists, update it
            cmd = [
                "gcloud", "secrets", "versions", "add", "pinecone-config",
                "--data-file=-",
                "--project=vertex-ai-agent-yzdlnjey"
            ]
            action = "updated"
        else:
            # Secret doesn't exist, create it
            cmd = [
                "gcloud", "secrets", "create", "pinecone-config",
                "--data-file=-",
                "--project=vertex-ai-agent-yzdlnjey"
            ]
            action = "created"
        
        result = subprocess.run(
            cmd,
            input=secret_json,
            text=True,
            capture_output=True
        )
        
        if result.returncode == 0:
            print(f"✅ Pinecone configuration {action} in Secret Manager")
            return True
        else:
            print(f"❌ Failed to save to Secret Manager: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error saving to Secret Manager: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 TKC_v5 Enhanced Agent - Production Pinecone Setup")
    print("=" * 60)
    
    # Step 1: Create configuration
    config = create_production_pinecone_config()
    if not config:
        return False
    
    # Step 2: Test connection
    if not test_pinecone_connection(config):
        return False
    
    # Step 3: Create index
    if not create_production_index(config):
        return False
    
    # Step 4: Test operations
    if not test_production_operations(config):
        return False
    
    # Step 5: Save to Secret Manager
    if not save_to_secret_manager(config):
        print("⚠️  Secret Manager save failed, but Pinecone is ready")
    
    # Success summary
    print("\n🎉 Production Pinecone Setup Complete!")
    print("=" * 60)
    print("📋 Summary:")
    print(f"   ✅ Index: {config['index_name']}")
    print(f"   ✅ Dimension: {config['dimension']}")
    print(f"   ✅ Metric: {config['metric']}")
    print(f"   ✅ Region: {config['region']}")
    print(f"   ✅ Configuration saved to Secret Manager")
    print()
    print("🚀 Ready for Enhanced TKC_v5 Agent Deployment!")
    print("📋 Next steps:")
    print("   1. Deploy the enhanced agent with vector database")
    print("   2. Test end-to-end conversation persistence")
    print("   3. Verify RAG capabilities with real conversations")
    
    return True


if __name__ == "__main__":
    import sys
    
    # Check dependencies
    try:
        import pinecone
        import numpy
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Run: pip3 install pinecone-client numpy")
        sys.exit(1)
    
    success = main()
    sys.exit(0 if success else 1)

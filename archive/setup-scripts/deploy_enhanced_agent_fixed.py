#!/usr/bin/env python3
"""
Deploy Enhanced TKC_v5 Agent with Webhook Loop Fix

This script deploys the enhanced agent with the webhook loop fix and
production vector database integration.
"""

import subprocess
import logging
import time
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def update_requirements():
    """Update requirements.txt with new dependencies."""
    logger.info("📦 Updating requirements.txt...")
    
    new_requirements = [
        "pinecone",
        "sentence-transformers",
        "google-cloud-firestore"
    ]
    
    try:
        # Read existing requirements
        with open("requirements.txt", "r") as f:
            existing = f.read()
        
        # Add new requirements if not already present
        updated = existing
        for req in new_requirements:
            if req not in existing:
                updated += f"\n{req}"
        
        # Write updated requirements
        with open("requirements.txt", "w") as f:
            f.write(updated)
        
        logger.info("✅ Requirements.txt updated")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to update requirements: {e}")
        return False


def build_and_deploy():
    """Build and deploy the enhanced agent."""
    logger.info("🚀 Building and deploying enhanced TKC_v5 agent...")
    
    try:
        # Build and deploy with Cloud Run
        cmd = [
            "gcloud", "run", "deploy", "vertex-ai-agent",
            "--source", ".",
            "--platform", "managed",
            "--region", "us-central1",
            "--allow-unauthenticated",
            "--memory", "2Gi",
            "--cpu", "2",
            "--timeout", "3600",
            "--max-instances", "10",
            "--set-env-vars", "ENVIRONMENT=production",
            "--project", "vertex-ai-agent-yzdlnjey"
        ]
        
        logger.info("🔨 Running deployment command...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Deployment successful!")
            logger.info(f"Service URL: {result.stdout}")
            return True
        else:
            logger.error(f"❌ Deployment failed: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Deployment error: {e}")
        return False


def test_webhook_fix():
    """Test the webhook loop fix."""
    logger.info("🧪 Testing webhook loop fix...")
    
    try:
        import requests
        
        # Get the service URL
        get_url_cmd = [
            "gcloud", "run", "services", "describe", "vertex-ai-agent",
            "--region", "us-central1",
            "--format", "value(status.url)",
            "--project", "vertex-ai-agent-yzdlnjey"
        ]
        
        result = subprocess.run(get_url_cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error("❌ Failed to get service URL")
            return False
        
        service_url = result.stdout.strip()
        logger.info(f"🔗 Service URL: {service_url}")
        
        # Test the enhanced email processing
        test_payload = {
            "message": "Test the enhanced email processing with deduplication to ensure no duplicate drafts are created."
        }
        
        response = requests.post(
            f"{service_url}/chat",
            json=test_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            logger.info("✅ Enhanced agent responding correctly")
            logger.info(f"Response: {response.json()}")
            return True
        else:
            logger.error(f"❌ Agent test failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test error: {e}")
        return False


def verify_production_components():
    """Verify all production components are working."""
    logger.info("🔍 Verifying production components...")
    
    components = {
        "Redis": False,
        "Pinecone": False,
        "Secret Manager": False,
        "Firestore": False
    }
    
    try:
        # Check Redis
        redis_cmd = ["gcloud", "redis", "instances", "list", "--region=us-central1", "--project=vertex-ai-agent-yzdlnjey"]
        result = subprocess.run(redis_cmd, capture_output=True, text=True)
        if result.returncode == 0 and "redis-instance" in result.stdout:
            components["Redis"] = True
            logger.info("✅ Redis instance found")
        
        # Check Pinecone config in Secret Manager
        secret_cmd = ["gcloud", "secrets", "versions", "access", "latest", "--secret=pinecone-config", "--project=vertex-ai-agent-yzdlnjey"]
        result = subprocess.run(secret_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            components["Secret Manager"] = True
            components["Pinecone"] = True
            logger.info("✅ Pinecone configuration found in Secret Manager")
        
        # Check Firestore
        firestore_cmd = ["gcloud", "services", "list", "--enabled", "--filter=name:firestore.googleapis.com", "--project=vertex-ai-agent-yzdlnjey"]
        result = subprocess.run(firestore_cmd, capture_output=True, text=True)
        if result.returncode == 0 and "firestore" in result.stdout:
            components["Firestore"] = True
            logger.info("✅ Firestore API enabled")
        
        # Summary
        working_components = sum(components.values())
        total_components = len(components)
        
        logger.info(f"📊 Component Status: {working_components}/{total_components} working")
        for component, status in components.items():
            status_icon = "✅" if status else "❌"
            logger.info(f"   {status_icon} {component}")
        
        return working_components >= 3  # At least 3/4 components should work
        
    except Exception as e:
        logger.error(f"❌ Component verification error: {e}")
        return False


def main():
    """Main deployment function."""
    logger.info("🚀 Enhanced TKC_v5 Agent Deployment with Webhook Loop Fix")
    logger.info("=" * 70)
    
    steps = [
        ("Update Requirements", update_requirements),
        ("Verify Production Components", verify_production_components),
        ("Build and Deploy", build_and_deploy),
        ("Test Webhook Fix", test_webhook_fix)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 Step: {step_name}")
        try:
            result = step_func()
            results.append((step_name, result))
            if result:
                logger.info(f"✅ {step_name}: SUCCESS")
            else:
                logger.error(f"❌ {step_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {step_name}: ERROR - {e}")
            results.append((step_name, False))
    
    # Summary
    logger.info("\n📊 Deployment Results Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for step_name, result in results:
        status = "✅ SUCCESS" if result else "❌ FAILED"
        logger.info(f"  {step_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} steps completed successfully")
    
    if passed == total:
        logger.info("🎉 Enhanced TKC_v5 Agent deployed successfully!")
        logger.info("📋 Enhanced features now active:")
        logger.info("   ✅ Webhook loop fix (2-minute rate limiting)")
        logger.info("   ✅ Enhanced email deduplication")
        logger.info("   ✅ Production Pinecone vector database")
        logger.info("   ✅ RAG-enhanced conversation responses")
        logger.info("   ✅ Redis conversation persistence")
        logger.info("   ✅ Smart email filtering (no spam/newsletters)")
        logger.info("\n🚀 The agent should now create drafts intelligently without loops!")
        return True
    else:
        logger.error("⚠️  Some deployment steps failed. Please review and retry.")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)

#!/bin/bash

# Setup comprehensive monitoring and alerting for TKC_v5 Executive Agent
# Production-ready observability with Cloud Operations

set -e

PROJECT_ID="vertex-ai-agent-yzdlnjey"
REGION="us-central1"
SERVICE_NAME="vertex-ai-agent"
NOTIFICATION_EMAIL="<EMAIL>"

echo "📊 Setting up monitoring and alerting for TKC_v5 Executive Agent"
echo "================================================================"
echo "Project: ${PROJECT_ID}"
echo "Service: ${SERVICE_NAME}"
echo "Region: ${REGION}"
echo "Notification Email: ${NOTIFICATION_EMAIL}"
echo ""

# Ensure we're using the correct project
gcloud config set project ${PROJECT_ID}

# Step 1: Create notification channel for email alerts
echo "📧 Creating notification channel..."
NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels create \
    --display-name="TKC_v5 Agent Alerts" \
    --type=email \
    --channel-labels=email_address=${NOTIFICATION_EMAIL} \
    --format="value(name)")

if [ $? -eq 0 ]; then
    echo "✅ Notification channel created: ${NOTIFICATION_CHANNEL}"
else
    echo "⚠️ Notification channel creation failed or already exists"
fi

# Step 2: Create alerting policies
echo "🚨 Creating alerting policies..."

# High Error Rate Alert
cat > /tmp/high_error_rate_policy.yaml << EOF
displayName: "TKC_v5 Agent - High Error Rate"
documentation:
  content: "The TKC_v5 Executive Agent is experiencing a high error rate (>5% over 5 minutes)"
conditions:
  - displayName: "High error rate condition"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name="${SERVICE_NAME}" AND metric.type="run.googleapis.com/request_count"'
      aggregations:
        - alignmentPeriod: 300s
          perSeriesAligner: ALIGN_RATE
          crossSeriesReducer: REDUCE_SUM
          groupByFields:
            - resource.labels.service_name
            - metric.labels.response_code_class
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0.05
      duration: 300s
combiner: OR
enabled: true
notificationChannels:
  - ${NOTIFICATION_CHANNEL}
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/high_error_rate_policy.yaml

# High Latency Alert
cat > /tmp/high_latency_policy.yaml << EOF
displayName: "TKC_v5 Agent - High Latency"
documentation:
  content: "The TKC_v5 Executive Agent is experiencing high latency (>10s average over 5 minutes)"
conditions:
  - displayName: "High latency condition"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name="${SERVICE_NAME}" AND metric.type="run.googleapis.com/request_latencies"'
      aggregations:
        - alignmentPeriod: 300s
          perSeriesAligner: ALIGN_MEAN
          crossSeriesReducer: REDUCE_MEAN
          groupByFields:
            - resource.labels.service_name
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 10000
      duration: 300s
combiner: OR
enabled: true
notificationChannels:
  - ${NOTIFICATION_CHANNEL}
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/high_latency_policy.yaml

# Low Request Volume Alert (service might be down)
cat > /tmp/low_volume_policy.yaml << EOF
displayName: "TKC_v5 Agent - Service Down"
documentation:
  content: "The TKC_v5 Executive Agent has received no requests in the last 10 minutes (service might be down)"
conditions:
  - displayName: "No requests condition"
    conditionAbsent:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name="${SERVICE_NAME}" AND metric.type="run.googleapis.com/request_count"'
      aggregations:
        - alignmentPeriod: 600s
          perSeriesAligner: ALIGN_RATE
          crossSeriesReducer: REDUCE_SUM
          groupByFields:
            - resource.labels.service_name
      duration: 600s
combiner: OR
enabled: true
notificationChannels:
  - ${NOTIFICATION_CHANNEL}
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/low_volume_policy.yaml

echo "✅ Alerting policies created"

# Step 3: Create custom dashboard
echo "📈 Creating monitoring dashboard..."
cat > /tmp/dashboard.json << EOF
{
  "displayName": "TKC_v5 Executive Agent Dashboard",
  "mosaicLayout": {
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Request Rate",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${SERVICE_NAME}\" AND metric.type=\"run.googleapis.com/request_count\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_RATE",
                      "crossSeriesReducer": "REDUCE_SUM",
                      "groupByFields": ["resource.labels.service_name"]
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "Requests/sec",
              "scale": "LINEAR"
            }
          }
        }
      },
      {
        "width": 6,
        "height": 4,
        "xPos": 6,
        "widget": {
          "title": "Response Latency",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${SERVICE_NAME}\" AND metric.type=\"run.googleapis.com/request_latencies\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_MEAN",
                      "crossSeriesReducer": "REDUCE_MEAN",
                      "groupByFields": ["resource.labels.service_name"]
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "Latency (ms)",
              "scale": "LINEAR"
            }
          }
        }
      },
      {
        "width": 6,
        "height": 4,
        "yPos": 4,
        "widget": {
          "title": "Error Rate",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${SERVICE_NAME}\" AND metric.type=\"run.googleapis.com/request_count\" AND metric.labels.response_code_class=\"4xx\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_RATE",
                      "crossSeriesReducer": "REDUCE_SUM",
                      "groupByFields": ["resource.labels.service_name"]
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "4xx Errors/sec",
              "scale": "LINEAR"
            }
          }
        }
      },
      {
        "width": 6,
        "height": 4,
        "xPos": 6,
        "yPos": 4,
        "widget": {
          "title": "Instance Count",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"${SERVICE_NAME}\" AND metric.type=\"run.googleapis.com/container/instance_count\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_MEAN",
                      "crossSeriesReducer": "REDUCE_SUM",
                      "groupByFields": ["resource.labels.service_name"]
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "Instances",
              "scale": "LINEAR"
            }
          }
        }
      }
    ]
  }
}
EOF

gcloud monitoring dashboards create --config-from-file=/tmp/dashboard.json

echo "✅ Monitoring dashboard created"

# Step 4: Create log-based metrics
echo "📝 Creating log-based metrics..."

# Agent task completion metric
gcloud logging metrics create agent_task_completions \
    --description="Number of completed agent tasks" \
    --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="'${SERVICE_NAME}'" AND jsonPayload.message:"Task.*completed successfully"'

# Agent error metric
gcloud logging metrics create agent_errors \
    --description="Number of agent errors" \
    --log-filter='resource.type="cloud_run_revision" AND resource.labels.service_name="'${SERVICE_NAME}'" AND (severity="ERROR" OR jsonPayload.message:"Task execution failed")'

echo "✅ Log-based metrics created"

# Clean up temporary files
rm -f /tmp/*_policy.yaml /tmp/dashboard.json

echo ""
echo "🎉 Monitoring setup complete!"
echo ""
echo "📊 Dashboard: https://console.cloud.google.com/monitoring/dashboards"
echo "🚨 Alerts: https://console.cloud.google.com/monitoring/alerting"
echo "📝 Logs: https://console.cloud.google.com/logs/query"
echo ""
echo "Monitoring includes:"
echo "- ✅ High error rate alerts (>5%)"
echo "- ✅ High latency alerts (>10s)"
echo "- ✅ Service down detection"
echo "- ✅ Custom dashboard with key metrics"
echo "- ✅ Log-based metrics for agent performance"
echo "- ✅ Email notifications to ${NOTIFICATION_EMAIL}"

#!/bin/bash

# Temporary script to assign necessary roles to current user for development
# This allows us to proceed with implementation while groups are being set up
#
# Usage: ./assign_user_roles.sh <PROJECT_ID>

if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <PROJECT_ID>"
    exit 1
fi

PROJECT_ID=$1
CURRENT_USER=$(gcloud config get-value account)

echo "Assigning development roles to current user: ${CURRENT_USER}"
echo "Project: ${PROJECT_ID}"
echo ""

# Essential roles for development and testing
DEV_ROLES=(
    "roles/aiplatform.user"
    "roles/storage.objectAdmin"
    "roles/artifactregistry.writer"
    "roles/logging.viewer"
    "roles/iam.serviceAccountAdmin"
    "roles/iam.serviceAccountUser"
    "roles/secretmanager.admin"
)

for role in "${DEV_ROLES[@]}"; do
    echo "Assigning ${role}..."
    gcloud projects add-iam-policy-binding ${PROJECT_ID} \
        --member="user:${CURRENT_USER}" \
        --role="${role}"
    
    if [ $? -eq 0 ]; then
        echo "✓ Successfully assigned ${role}"
    else
        echo "⚠ Failed to assign ${role}"
    fi
done

echo ""
echo "User role assignment complete!"
echo "You can now proceed with service account creation and agent development."

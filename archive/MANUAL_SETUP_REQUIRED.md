# 🔧 Manual Setup Required for Production Deployment

## **Gmail Domain Delegation Setup** (Required for Email Functionality)

### **Step 1: Create Service Account Key with Domain Delegation**

1. **Go to Google Cloud Console** → IAM & Admin → Service Accounts
2. **Find the Gmail service account**: `<EMAIL>`
3. **Click on the service account** → Keys tab
4. **Create new key** → JSON format
5. **Enable domain-wide delegation**:
   - Check "Enable Google Workspace Domain-wide Delegation"
   - Product name: "TKC_v5 Executive Agent"
   - Click "Create"

### **Step 2: Configure Domain Delegation in Google Workspace**

1. **Go to Google Workspace Admin Console**: https://admin.google.com
2. **Navigate to**: Security → API Controls → Domain-wide delegation
3. **Add new delegation**:
   - **Client ID**: Copy from the service account JSON file (`client_id` field)
   - **OAuth <PERSON>opes**: 
     ```
     https://www.googleapis.com/auth/gmail.readonly,https://www.googleapis.com/auth/gmail.send,https://www.googleapis.com/auth/gmail.modify,https://www.googleapis.com/auth/gmail.compose
     ```
4. **Click "Authorize"**

### **Step 3: Upload Service Account Key to Secret Manager**

```bash
# Upload the downloaded JSON key file
gcloud secrets create gmail-service-account-key --data-file=path/to/downloaded-key.json

# Verify the secret was created
gcloud secrets versions access latest --secret=gmail-service-account-key
```

### **Step 4: Test Gmail Integration**

```bash
# Test the agent with Gmail functionality
cd /Users/<USER>/Code/TKC_v5
PYTHONPATH=/Users/<USER>/Code/TKC_v5/src python3 -c "
from agent.core import create_agent, TaskType
agent = create_agent()
response = agent.invoke('Create an email <NAME_EMAIL> about our AI services', TaskType.EMAIL_DRAFT)
print(f'Gmail test result: {response}')
"
```

---

## **Cloud Run Deployment** (Ready to Execute)

### **Step 1: Build and Deploy**

```bash
# Execute the deployment script
./deploy_to_cloud_run.sh
```

**What this does:**
- ✅ Builds Docker image with latest code
- ✅ Pushes to Google Container Registry
- ✅ Deploys to Cloud Run with production configuration
- ✅ Sets up proper scaling and resource limits
- ✅ Configures service account authentication
- ✅ Tests deployment endpoints

### **Step 2: Set Up Monitoring**

```bash
# Execute the monitoring setup script
./setup_monitoring.sh
```

**What this creates:**
- ✅ Email notification channel for alerts
- ✅ High error rate alerting (>5%)
- ✅ High latency alerting (>10s)
- ✅ Service down detection
- ✅ Custom monitoring dashboard
- ✅ Log-based metrics for agent performance

---

## **Production Validation Checklist**

### **🔐 Security Validation**
- [ ] Service account permissions are minimal (least privilege)
- [ ] No hardcoded credentials in code
- [ ] Secret Manager integration working
- [ ] CORS configured for production domains
- [ ] API documentation disabled in production

### **📊 Monitoring Validation**
- [ ] Health endpoint responding correctly
- [ ] Monitoring dashboard showing metrics
- [ ] Alert policies configured and active
- [ ] Log aggregation working properly
- [ ] Error reporting configured

### **🚀 Functionality Validation**
- [ ] Executive Agent responding to requests
- [ ] Gmail integration working (after domain delegation)
- [ ] Task routing functioning correctly
- [ ] Error handling working properly
- [ ] Performance within acceptable limits

### **📈 Performance Validation**
- [ ] Response times < 10 seconds
- [ ] Error rates < 5%
- [ ] Proper scaling behavior
- [ ] Resource utilization optimal
- [ ] Cost monitoring in place

---

## **Current Status Summary**

### **✅ Completed Automatically**
- Gmail service account created: `<EMAIL>`
- IAM permissions configured for service account impersonation
- Production-ready FastAPI service with comprehensive error handling
- Cloud Run deployment script ready
- Monitoring and alerting setup script ready
- Enhanced logging and observability

### **🔄 Ready for Manual Execution**
- Gmail domain delegation setup (requires Google Workspace admin access)
- Service account key creation and upload to Secret Manager
- Cloud Run deployment execution
- Monitoring setup execution
- Production validation testing

### **📋 Next Steps After Manual Setup**
1. **Test Gmail functionality** with real email operations
2. **Validate monitoring** and alerting systems
3. **Performance testing** under load
4. **Documentation updates** with production URLs
5. **User acceptance testing** with real workflows

---

## **Production URLs (After Deployment)**

- **Service URL**: `https://vertex-ai-agent-[hash]-uc.a.run.app`
- **Health Check**: `https://vertex-ai-agent-[hash]-uc.a.run.app/health`
- **API Documentation**: Available in development mode only
- **Monitoring Dashboard**: https://console.cloud.google.com/monitoring/dashboards

---

## **Support and Troubleshooting**

### **Common Issues**
1. **Gmail API 403 errors**: Check domain delegation configuration
2. **Service account errors**: Verify IAM permissions
3. **Secret Manager access**: Ensure service account has secretAccessor role
4. **Cloud Run deployment**: Check Docker build and push logs

### **Debugging Commands**
```bash
# Check service account permissions
gcloud projects get-iam-policy vertex-ai-agent-yzdlnjey

# View Cloud Run logs
gcloud logs read --project=vertex-ai-agent-yzdlnjey --service=vertex-ai-agent

# Test Secret Manager access
gcloud secrets versions access latest --secret=agent-config

# Check service health
curl https://vertex-ai-agent-[hash]-uc.a.run.app/health
```

### **Contact Information**
- **Project Owner**: <EMAIL>
- **Documentation**: docs/ directory in repository
- **Issue Tracking**: GitHub Issues

---

**🎯 Priority**: Complete Gmail domain delegation first, then execute deployment scripts for full production readiness!

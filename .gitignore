# TKC_v5 Vertex AI Agent Project - .gitignore
# Production-grade Python AI agent with Google Cloud integration

# ============================================================================
# SECURITY & CREDENTIALS - NEVER COMMIT THESE
# ============================================================================

# Google Cloud Service Account Keys
*.json
*-key.json
*-credentials.json
service-account-*.json
gcp-*.json

# Environment Variables & Secrets
.env
.env.*
*.env
.envrc

# Google Cloud Application Default Credentials
~/.config/gcloud/
.config/gcloud/
application_default_credentials.json

# Secret Manager references (if accidentally downloaded)
secrets/
secret-*.txt
secret-*.json

# API Keys and Tokens
*.key
*.token
*.secret
api-keys/
tokens/

# ============================================================================
# PYTHON
# ============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# ============================================================================
# GOOGLE CLOUD & VERTEX AI
# ============================================================================

# Terraform state files (if using IaC)
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Google Cloud Build
cloudbuild.yaml.bak
.gcloudignore

# Vertex AI model artifacts
models/
model-artifacts/
training-outputs/

# Cloud Run deployment configs with secrets
cloud-run-*.yaml
deployment-*.yaml

# ============================================================================
# DEVELOPMENT TOOLS & IDEs
# ============================================================================

# VS Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# OPERATING SYSTEM
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# ============================================================================
# LOGS & TEMPORARY FILES
# ============================================================================

# Application logs
*.log
logs/
log/
*.log.*

# Temporary files
tmp/
temp/
.tmp/
*.tmp
*.temp

# Backup files
*.bak
*.backup
*.old
*.orig

# ============================================================================
# DOCKER & CONTAINERIZATION
# ============================================================================

# Docker
.dockerignore.bak
docker-compose.override.yml
.docker/

# ============================================================================
# PROJECT SPECIFIC
# ============================================================================

# Local development overrides
local_settings.py
local_config.py
dev_config.py

# Test data and fixtures
test-data/
fixtures/
sample-data/

# Generated documentation
docs/_build/
docs/build/
site/

# Local database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
cache/

# Node.js (if using any JS tooling)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ============================================================================
# DEPLOYMENT & PRODUCTION
# ============================================================================

# Production configuration files with secrets
prod-config.yaml
production.env
staging.env

# Deployment artifacts
deploy/
deployment/
k8s-configs/

# SSL certificates
*.pem
*.crt
*.key
*.p12
*.pfx

# ============================================================================
# MONITORING & ANALYTICS
# ============================================================================

# Monitoring configs with sensitive data
monitoring-config.yaml
alerting-rules.yaml

# Analytics and metrics
metrics/
analytics/

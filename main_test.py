#!/usr/bin/env python3
"""
Test version of TKC_v5 Executive Agent - Import Testing
"""

import os
import logging
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="TKC_v5 Executive Agent - Test",
    description="Testing imports and basic functionality",
    version="1.0.0"
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "TKC_v5 Executive Agent Test is running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "tkc-v5-executive-agent-test",
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development")
    }

@app.get("/test-imports")
async def test_imports():
    """Test importing our modules"""
    import_results = {}
    
    try:
        from src.config.settings import get_settings
        import_results["config.settings"] = "✅ Success"
    except Exception as e:
        import_results["config.settings"] = f"❌ Error: {str(e)}"
    
    try:
        # Try importing just the tools first
        from src.tools import email_automation_tools
        import_results["tools.email_automation"] = "✅ Success"
    except Exception as e:
        import traceback
        tb = traceback.format_exc()
        import_results["tools.email_automation"] = f"❌ Error: {str(e)} | Traceback: {tb[-500:]}"

    try:
        from src.agent.state import TaskRequest, TaskType
        import_results["agent.state"] = "✅ Success"
    except Exception as e:
        import_results["agent.state"] = f"❌ Error: {str(e)}"

    try:
        from src.agent.core import create_agent
        import_results["agent.core"] = "✅ Success"
    except Exception as e:
        import_results["agent.core"] = f"❌ Error: {str(e)}"
    
    try:
        from src.services.gmail_client import create_gmail_client
        import_results["services.gmail_client"] = "✅ Success"
    except Exception as e:
        import_results["services.gmail_client"] = f"❌ Error: {str(e)}"

    try:
        from src.services.pinecone_client import PineconeClient
        import_results["services.pinecone_client"] = "✅ Success"
    except Exception as e:
        import_results["services.pinecone_client"] = f"❌ Error: {str(e)}"

    try:
        from src.services.firestore_client import FirestoreClient
        import_results["services.firestore_client"] = "✅ Success"
    except Exception as e:
        import_results["services.firestore_client"] = f"❌ Error: {str(e)}"

    try:
        from src.business_tools.email_automation import EmailAutomationTool
        import_results["business_tools.email_automation"] = "✅ Success"
    except Exception as e:
        import_results["business_tools.email_automation"] = f"❌ Error: {str(e)}"

    return {
        "import_test_results": import_results,
        "status": "completed"
    }

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8080))
    logger.info(f"Starting TKC_v5 Executive Agent Test on port {port}")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info"
    )

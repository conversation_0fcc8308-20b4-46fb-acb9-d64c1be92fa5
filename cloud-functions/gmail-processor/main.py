"""
Gmail Push Notification Processor Cloud Function

This function receives Gmail push notifications via Pub/Sub and processes
new emails through the TKC_v5 Vertex AI Agent.
"""

import base64
import json
import logging
import os
import requests
from typing import Dict, Any, Optional
from google.cloud import secretmanager
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
PROJECT_ID = "vertex-ai-agent-yzdlnjey"
AGENT_URL = "https://vertex-ai-agent-*************.us-central1.run.app"
SUBJECT_EMAIL = "<EMAIL>"

# Gmail API scopes
GMAIL_SCOPES = [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.modify'
]


def get_secret(secret_id: str) -> Optional[str]:
    """Get secret from Secret Manager."""
    try:
        client = secretmanager.SecretManagerServiceClient()
        name = f"projects/{PROJECT_ID}/secrets/{secret_id}/versions/latest"
        response = client.access_secret_version(request={"name": name})
        return response.payload.data.decode("UTF-8")
    except Exception as e:
        logger.error(f"Failed to get secret {secret_id}: {e}")
        return None


def get_gmail_service():
    """Create authenticated Gmail service."""
    try:
        # Get service account key from Secret Manager
        service_account_json = get_secret("gmail-service-account-key")
        if not service_account_json:
            logger.error("No Gmail service account key found")
            return None
        
        service_account_info = json.loads(service_account_json)
        
        # Create credentials with domain delegation
        credentials = service_account.Credentials.from_service_account_info(
            service_account_info,
            scopes=GMAIL_SCOPES,
            subject=SUBJECT_EMAIL
        )
        
        # Build Gmail service
        service = build('gmail', 'v1', credentials=credentials)
        logger.info("Gmail service created successfully")
        return service
        
    except Exception as e:
        logger.error(f"Failed to create Gmail service: {e}")
        return None


def get_email_details(service, message_id: str) -> Optional[Dict[str, Any]]:
    """Get email details from Gmail API."""
    try:
        message = service.users().messages().get(
            userId='me', 
            id=message_id,
            format='full'
        ).execute()
        
        # Extract headers
        headers = message['payload'].get('headers', [])
        subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
        sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown Sender')
        date = next((h['value'] for h in headers if h['name'] == 'Date'), 'Unknown Date')
        
        # Extract body
        body = ""
        if 'parts' in message['payload']:
            for part in message['payload']['parts']:
                if part['mimeType'] == 'text/plain':
                    if 'data' in part['body']:
                        body = base64.urlsafe_b64decode(part['body']['data']).decode('utf-8')
                        break
        else:
            if message['payload']['mimeType'] == 'text/plain':
                if 'data' in message['payload']['body']:
                    body = base64.urlsafe_b64decode(message['payload']['body']['data']).decode('utf-8')
        
        return {
            'message_id': message_id,
            'subject': subject,
            'sender': sender,
            'date': date,
            'body': body,
            'thread_id': message.get('threadId'),
            'labels': message.get('labelIds', [])
        }
        
    except HttpError as e:
        logger.error(f"Gmail API error getting message {message_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error getting email details for {message_id}: {e}")
        return None


def send_to_agent(email_data: Dict[str, Any]) -> bool:
    """Send email data to the TKC_v5 agent for processing."""
    try:
        # Prepare the message for the agent
        message = f"""New email received:
From: {email_data['sender']}
Subject: {email_data['subject']}
Date: {email_data['date']}

Body:
{email_data['body'][:1000]}{'...' if len(email_data['body']) > 1000 else ''}

Please analyze this email and determine if any action is needed."""

        payload = {
            "message": message,
            "metadata": {
                "email_id": email_data['message_id'],
                "thread_id": email_data['thread_id'],
                "sender": email_data['sender'],
                "subject": email_data['subject']
            }
        }
        
        # Send to agent
        response = requests.post(
            f"{AGENT_URL}/chat",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            logger.info(f"Successfully sent email {email_data['message_id']} to agent")
            return True
        else:
            logger.error(f"Agent returned status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to send email to agent: {e}")
        return False


def gmail_notification_handler(cloud_event):
    """
    Cloud Function entry point for Gmail push notifications.
    
    Args:
        cloud_event: CloudEvent containing the Pub/Sub message
    """
    try:
        # Decode the Pub/Sub message
        if hasattr(cloud_event, 'data'):
            # Cloud Functions 2nd gen format
            pubsub_message = base64.b64decode(cloud_event.data).decode('utf-8')
        else:
            # Fallback for different formats
            pubsub_message = base64.b64decode(cloud_event['data']).decode('utf-8')
        
        logger.info(f"Received Gmail notification: {pubsub_message}")
        
        # Parse the Gmail notification
        notification_data = json.loads(pubsub_message)
        
        # Extract message ID from the notification
        # Gmail notifications contain historyId, not messageId directly
        history_id = notification_data.get('historyId')
        email_address = notification_data.get('emailAddress', SUBJECT_EMAIL)
        
        logger.info(f"Processing notification for {email_address}, historyId: {history_id}")
        
        # Get Gmail service
        gmail_service = get_gmail_service()
        if not gmail_service:
            logger.error("Failed to create Gmail service")
            return
        
        # Get recent messages (since we don't have direct message ID)
        # We'll process the most recent unread messages
        try:
            results = gmail_service.users().messages().list(
                userId='me',
                q='is:unread',
                maxResults=5
            ).execute()
            
            messages = results.get('messages', [])
            logger.info(f"Found {len(messages)} unread messages")
            
            for message in messages:
                message_id = message['id']
                
                # Get email details
                email_data = get_email_details(gmail_service, message_id)
                if email_data:
                    # Send to agent for processing
                    success = send_to_agent(email_data)
                    if success:
                        logger.info(f"Successfully processed email {message_id}")
                    else:
                        logger.error(f"Failed to process email {message_id}")
                else:
                    logger.error(f"Failed to get details for email {message_id}")
                    
        except Exception as e:
            logger.error(f"Error processing Gmail messages: {e}")
        
    except Exception as e:
        logger.error(f"Error in gmail_notification_handler: {e}")
        raise


# For Cloud Functions 1st gen compatibility
def gmail_notification_handler_http(request):
    """HTTP trigger version for testing."""
    try:
        # Extract Pub/Sub message from HTTP request
        envelope = request.get_json()
        if not envelope:
            return 'Bad Request: no Pub/Sub message received', 400
        
        if not isinstance(envelope, dict) or 'message' not in envelope:
            return 'Bad Request: invalid Pub/Sub message format', 400
        
        # Create a mock cloud event for compatibility
        cloud_event = {
            'data': envelope['message']['data']
        }
        
        gmail_notification_handler(cloud_event)
        return 'OK', 200
        
    except Exception as e:
        logger.error(f"Error in HTTP handler: {e}")
        return f'Internal Server Error: {str(e)}', 500

// Real data types for TKC_v5 Admin Dashboard

export interface AgentStatus {
  id: string;
  name: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  uptime: number;
  lastHealthCheck: string;
  responseTime: number;
  errorRate: number;
  totalRequests: number;
  successfulRequests: number;
  endpoint: string;
}

export interface ConversationEvent {
  id: string;
  timestamp: string;
  timestampRaw: string;
  type: 'conversation' | 'email_processing' | 'tool_usage' | 'error';
  customerEmail: string;
  threadId?: string;
  messageCount: number;
  status: 'completed' | 'in_progress' | 'failed';
  processingTime: number;
  agentUsed: string;
  toolsUsed: string[];
  summary: string;
  details?: any;
}

export interface VectorDatabaseStats {
  totalVectors: number;
  namespaces: string[];
  recentOperations: {
    timestamp: string;
    operation: 'upsert' | 'query' | 'delete';
    vectorCount: number;
    namespace: string;
  }[];
  indexStats: {
    dimension: number;
    totalVectorCount: number;
    namespaceCount: number;
  };
}

export interface TenantData {
  id: string;
  namespace: string;
  status: 'active' | 'inactive' | 'suspended';
  onboardedDate: string;
  lastActivity: string;
  conversationCount: number;
  vectorCount: number;
  redisKeys: number;
  apiCallsToday: number;
  apiLimit: number;
  storageUsed: number;
  agentsDeployed: string[];
}

export interface InfrastructureStatus {
  agent: {
    status: 'healthy' | 'unhealthy' | 'unknown';
    responseTime: number;
    lastCheck: string;
  };
  firestore: {
    status: 'healthy' | 'unhealthy' | 'unknown';
    connectionCount: number;
    lastOperation: string;
  };
  pinecone: {
    status: 'healthy' | 'unhealthy' | 'unknown';
    indexHealth: string;
    lastSync: string;
  };
  redis: {
    status: 'healthy' | 'unhealthy' | 'unknown';
    memoryUsage: number;
    connectedClients: number;
    keyCount: number;
  };
}

export interface ActivityEvent {
  id: string;
  timestamp: string;
  timestampRaw: string;
  type: 'agent_interaction' | 'email_processing' | 'vector_operation' | 'tenant_activity' | 'system_alert' | 'error';
  severity: 'info' | 'warning' | 'error' | 'success';
  message: string;
  source: string;
  details: Record<string, any>;
  expandable: boolean;
}

export interface DashboardData {
  timestamp: string;
  timestampRaw: string;
  agents: AgentStatus[];
  infrastructure: InfrastructureStatus;
  tenants: {
    activeTenants: number;
    totalConversations: number;
    totalVectors: number;
    tenants: TenantData[];
  };
  activity: ActivityEvent[];
  systemHealth: 'healthy' | 'degraded' | 'unhealthy';
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

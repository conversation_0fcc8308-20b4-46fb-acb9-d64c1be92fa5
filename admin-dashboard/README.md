# TKC_v5 Admin Dashboard

A **real-time admin dashboard** built with Next.js that connects to actual TKC_v5 system data sources, providing authentic monitoring and verification capabilities.

## 🎯 **Purpose**

This dashboard addresses the critical issues with mock data by connecting directly to your real TKC_v5 infrastructure:

- ✅ **Real Agent Data**: Connects to actual agent health endpoints
- ✅ **Accurate Timestamps**: Shows true event occurrence times
- ✅ **Multi-Tenant KPIs**: Displays actual tenant metrics from Redis/Firestore
- ✅ **Live System Events**: Real conversation logs and agent interactions

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+ 
- Access to TKC_v5 system (agent API, Firestore, Redis, Pinecone)

### **Installation**

1. **Install Dependencies**:
   ```bash
   cd admin-dashboard
   npm install
   ```

2. **Configure Environment** (optional - has defaults):
   ```bash
   # Create .env.local file
   GOOGLE_CLOUD_PROJECT=vertex-ai-agent-yzdlnjey
   AGENT_API_URL=https://vertex-ai-agent-1072222703018.us-central1.run.app
   DASHBOARD_USERNAME=admin
   DASHBOARD_PASSWORD=tkc_monitor_2025
   ```

3. **Start Development Server**:
   ```bash
   npm run dev
   ```

4. **Access Dashboard**:
   - URL: http://localhost:3001
   - Username: admin
   - Password: tkc_monitor_2025

## 📊 **Real Data Sources**

### **Connected Systems**
- **Agent API**: Live health checks and status from your deployed agent
- **Firestore**: Real conversation history and customer profiles
- **Redis**: Actual tenant data and session information
- **Pinecone**: Vector database statistics and operations
- **Google Cloud**: Infrastructure monitoring and metrics

### **Data Verification**
- **Agent Health**: Real response times from actual endpoints
- **Conversation Logs**: Authentic interaction history with timestamps
- **Tenant Metrics**: Actual customer usage and resource consumption
- **System Events**: Real-time monitoring of all platform activities

## 🔍 **Features**

### **Enhanced Monitoring**
- **Denver Timezone**: All timestamps in your local timezone
- **Interactive Drill-Down**: Click any event for detailed information
- **Real-Time Updates**: Auto-refresh every 30 seconds
- **Advanced Filtering**: Filter by event type, search activities
- **System Health**: Overall platform status monitoring

### **Admin Capabilities**
- **Agent Performance**: Response times, success rates, error tracking
- **Tenant Management**: Multi-tenant resource usage and isolation
- **Infrastructure Status**: Service health across all components
- **Activity Tracking**: Real conversation and system events

## 🔧 **Architecture**

### **Tech Stack**
- **Frontend**: Next.js 14 + React + TypeScript
- **Styling**: Tailwind CSS + Headless UI
- **Data Fetching**: SWR for real-time updates
- **Authentication**: Basic Auth (matches existing system)

### **API Routes**
- `/api/status` - Main dashboard data aggregation
- `/api/activity/[id]` - Detailed event drill-down
- `/api/agents/[id]` - Agent-specific metrics
- `/api/tenants/[id]` - Tenant-specific data

## 🎯 **Solving Critical Issues**

### **1. Mock Data → Real Data**
- **Before**: Simulated "Sales Development Agent qualified new lead"
- **After**: Actual agent interactions from your TKC_v5 system

### **2. Timestamp Accuracy**
- **Before**: Timestamps updated on refresh
- **After**: True event occurrence times from system logs

### **3. Multi-Tenant KPIs**
- **Before**: All zeros, no real data
- **After**: Actual tenant counts, conversations, vector storage

### **4. Data Source Integration**
- **Before**: No connection to real systems
- **After**: Direct integration with Firestore, Redis, Pinecone, Agent API

## 🔐 **Security**

- **Authentication**: Basic Auth matching your existing credentials
- **Environment Variables**: Secure configuration management
- **API Protection**: All endpoints require authentication
- **CORS Configuration**: Restricted to authorized origins

## 📈 **Monitoring Capabilities**

### **Real-Time Verification**
- ✅ Chat history storage and retrieval
- ✅ Message persistence across sessions
- ✅ Vector embeddings and search accuracy
- ✅ Email classification and filtering
- ✅ Tool usage and effectiveness
- ✅ Customer engagement metrics

### **Performance Tracking**
- ✅ Response time monitoring
- ✅ Success rate analysis
- ✅ Error detection and alerting
- ✅ Resource usage optimization

## 🚀 **Production Deployment**

### **Build for Production**
```bash
npm run build
npm start
```

### **Docker Deployment**
```bash
# Build image
docker build -t tkc-v5-admin-dashboard .

# Run container
docker run -p 3001:3001 tkc-v5-admin-dashboard
```

### **Integration with tkcgroup.co**
This dashboard can be integrated into your main site at `/admin` route for seamless access.

## 🎉 **Benefits**

- **Reliable Monitoring**: Real data instead of mock simulations
- **Accurate Timestamps**: True event timing for debugging
- **Multi-Tenant Insights**: Actual customer usage patterns
- **System Verification**: Confirm all components working correctly
- **Performance Optimization**: Identify bottlenecks and issues
- **Business Intelligence**: Real metrics for decision making

---

**This dashboard provides the authentic monitoring and verification capabilities you need to ensure your TKC_v5 platform is operating at peak efficiency with real, actionable data.**

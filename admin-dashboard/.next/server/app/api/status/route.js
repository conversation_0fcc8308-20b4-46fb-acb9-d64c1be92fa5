"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/status/route";
exports.ids = ["app/api/status/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatus%2Froute&page=%2Fapi%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatus%2Froute.ts&appDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatus%2Froute&page=%2Fapi%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatus%2Froute.ts&appDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_tylernelsonklug_Code_TKC_v5_admin_dashboard_app_api_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/status/route.ts */ \"(rsc)/./app/api/status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/status/route\",\n        pathname: \"/api/status\",\n        filename: \"route\",\n        bundlePath: \"app/api/status/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Code/TKC_v5/admin-dashboard/app/api/status/route.ts\",\n    nextConfigOutput,\n    userland: _Users_tylernelsonklug_Code_TKC_v5_admin_dashboard_app_api_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/status/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatus%2Froute&page=%2Fapi%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatus%2Froute.ts&appDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/status/route.ts":
/*!*********************************!*\
  !*** ./app/api/status/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_timezone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/timezone */ \"(rsc)/./lib/timezone.ts\");\n/* harmony import */ var _lib_data_clients__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/data-clients */ \"(rsc)/./lib/data-clients.ts\");\n// Real dashboard data API - connects to actual TKC_v5 system\n\n\n\n\nasync function GET(request) {\n    // Validate authentication\n    if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.validateAuth)(request)) {\n        return (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.createAuthResponse)();\n    }\n    try {\n        const agentClient = new _lib_data_clients__WEBPACK_IMPORTED_MODULE_3__.AgentAPIClient();\n        const firestoreClient = new _lib_data_clients__WEBPACK_IMPORTED_MODULE_3__.FirestoreClient();\n        const redisClient = new _lib_data_clients__WEBPACK_IMPORTED_MODULE_3__.RedisClient();\n        const pineconeClient = new _lib_data_clients__WEBPACK_IMPORTED_MODULE_3__.PineconeClient();\n        // Get real agent status\n        const agentStatus = await agentClient.getAgentHealth();\n        const agentInfo = await agentClient.getAgentInfo();\n        const agentTools = await agentClient.getAvailableTools();\n        // Get real conversation data\n        const conversations = await firestoreClient.getRecentConversations(10);\n        const conversationStats = await firestoreClient.getConversationStats();\n        // Get real tenant data\n        const tenantData = await redisClient.getTenantData();\n        const redisStats = await redisClient.getRedisStats();\n        // Get real vector database stats\n        const vectorStats = await pineconeClient.getVectorStats();\n        // Create real activity events from actual data\n        const activityEvents = [];\n        // Add agent health event\n        activityEvents.push({\n            id: `agent_health_${Date.now()}`,\n            timestamp: (0,_lib_timezone__WEBPACK_IMPORTED_MODULE_2__.formatDenverTime)(new Date()),\n            timestampRaw: new Date().toISOString(),\n            type: \"system_alert\",\n            severity: agentStatus.status === \"healthy\" ? \"success\" : \"error\",\n            message: `Agent health check: ${agentStatus.status} (${agentStatus.responseTime}ms)`,\n            source: \"agent_monitor\",\n            details: {\n                endpoint: agentStatus.endpoint,\n                responseTime: agentStatus.responseTime,\n                status: agentStatus.status,\n                lastCheck: agentStatus.lastHealthCheck\n            },\n            expandable: true\n        });\n        // Add agent info event if available\n        if (agentInfo) {\n            activityEvents.push({\n                id: `agent_info_${Date.now()}`,\n                timestamp: (0,_lib_timezone__WEBPACK_IMPORTED_MODULE_2__.formatDenverTime)(new Date()),\n                timestampRaw: new Date().toISOString(),\n                type: \"agent_interaction\",\n                severity: \"info\",\n                message: `Agent service: ${agentInfo.service || \"TKC_v5\"} - ${agentInfo.status || \"running\"}`,\n                source: \"agent_api\",\n                details: agentInfo,\n                expandable: true\n            });\n        }\n        // Add tools availability event\n        if (agentTools) {\n            const toolCount = Array.isArray(agentTools) ? agentTools.length : Object.keys(agentTools).length;\n            activityEvents.push({\n                id: `tools_check_${Date.now()}`,\n                timestamp: (0,_lib_timezone__WEBPACK_IMPORTED_MODULE_2__.formatDenverTime)(new Date()),\n                timestampRaw: new Date().toISOString(),\n                type: \"system_alert\",\n                severity: \"info\",\n                message: `Agent tools available: ${toolCount} tools loaded`,\n                source: \"tools_monitor\",\n                details: {\n                    toolCount,\n                    tools: agentTools\n                },\n                expandable: true\n            });\n        }\n        // Add conversation events from real data\n        conversations.forEach((conv, index)=>{\n            activityEvents.push({\n                id: conv.id,\n                timestamp: (0,_lib_timezone__WEBPACK_IMPORTED_MODULE_2__.formatDenverTime)(conv.timestamp),\n                timestampRaw: conv.timestampRaw,\n                type: \"agent_interaction\",\n                severity: conv.status === \"completed\" ? \"success\" : conv.status === \"failed\" ? \"error\" : \"info\",\n                message: `Conversation with ${conv.customerEmail}: ${conv.summary}`,\n                source: \"firestore\",\n                details: {\n                    customerEmail: conv.customerEmail,\n                    threadId: conv.threadId,\n                    messageCount: conv.messageCount,\n                    processingTime: conv.processingTime,\n                    agentUsed: conv.agentUsed,\n                    toolsUsed: conv.toolsUsed,\n                    status: conv.status\n                },\n                expandable: true\n            });\n        });\n        // Build dashboard data with real information\n        const dashboardData = {\n            timestamp: (0,_lib_timezone__WEBPACK_IMPORTED_MODULE_2__.formatDenverTime)(new Date()),\n            timestampRaw: new Date().toISOString(),\n            agents: [\n                agentStatus\n            ],\n            infrastructure: {\n                agent: {\n                    status: agentStatus.status,\n                    responseTime: agentStatus.responseTime,\n                    lastCheck: agentStatus.lastHealthCheck\n                },\n                firestore: {\n                    status: conversations.length >= 0 ? \"healthy\" : \"unknown\",\n                    connectionCount: 1,\n                    lastOperation: new Date().toISOString()\n                },\n                pinecone: {\n                    status: vectorStats.totalVectors >= 0 ? \"healthy\" : \"unknown\",\n                    indexHealth: \"operational\",\n                    lastSync: new Date().toISOString()\n                },\n                redis: {\n                    status: redisStats.keyCount >= 0 ? \"healthy\" : \"unknown\",\n                    memoryUsage: redisStats.memoryUsage,\n                    connectedClients: redisStats.connectedClients,\n                    keyCount: redisStats.keyCount\n                }\n            },\n            tenants: {\n                activeTenants: tenantData.length,\n                totalConversations: conversationStats.total,\n                totalVectors: vectorStats.totalVectors,\n                tenants: tenantData\n            },\n            activity: activityEvents.sort((a, b)=>new Date(b.timestampRaw).getTime() - new Date(a.timestampRaw).getTime()),\n            systemHealth: agentStatus.status === \"healthy\" ? \"healthy\" : \"degraded\"\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            data: dashboardData,\n            timestamp: (0,_lib_timezone__WEBPACK_IMPORTED_MODULE_2__.formatDenverTime)(new Date())\n        });\n    } catch (error) {\n        console.error(\"Dashboard API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Failed to fetch dashboard data\",\n            timestamp: (0,_lib_timezone__WEBPACK_IMPORTED_MODULE_2__.formatDenverTime)(new Date())\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/status/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthResponse: () => (/* binding */ createAuthResponse),\n/* harmony export */   validateAuth: () => (/* binding */ validateAuth)\n/* harmony export */ });\n// Simple authentication for admin dashboard\nfunction validateAuth(request) {\n    const authHeader = request.headers.get(\"authorization\");\n    if (!authHeader || !authHeader.startsWith(\"Basic \")) {\n        return false;\n    }\n    const base64Credentials = authHeader.slice(6);\n    const credentials = Buffer.from(base64Credentials, \"base64\").toString(\"ascii\");\n    const [username, password] = credentials.split(\":\");\n    const validUsername = \"admin\" || 0;\n    const validPassword = \"tkc_monitor_2025\" || 0;\n    return username === validUsername && password === validPassword;\n}\nfunction createAuthResponse() {\n    return new Response(\"Unauthorized\", {\n        status: 401,\n        headers: {\n            \"WWW-Authenticate\": 'Basic realm=\"TKC_v5 Admin Dashboard\"'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/data-clients.ts":
/*!*****************************!*\
  !*** ./lib/data-clients.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentAPIClient: () => (/* binding */ AgentAPIClient),\n/* harmony export */   FirestoreClient: () => (/* binding */ FirestoreClient),\n/* harmony export */   PineconeClient: () => (/* binding */ PineconeClient),\n/* harmony export */   RedisClient: () => (/* binding */ RedisClient)\n/* harmony export */ });\n// Real data clients for TKC_v5 system integration\n// Agent API Client - connects to real TKC_v5 agent\nclass AgentAPIClient {\n    constructor(){\n        this.baseUrl = \"https://vertex-ai-agent-1072222703018.us-central1.run.app\" || 0;\n    }\n    async getAgentHealth() {\n        try {\n            const startTime = Date.now();\n            const response = await fetch(`${this.baseUrl}/health`, {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                // Add timeout\n                signal: AbortSignal.timeout(10000)\n            });\n            const responseTime = Date.now() - startTime;\n            if (response.ok) {\n                const data = await response.json();\n                return {\n                    id: \"executive_agent\",\n                    name: \"TKC_v5 Executive Agent\",\n                    status: data.status === \"healthy\" ? \"healthy\" : \"unhealthy\",\n                    uptime: 0,\n                    lastHealthCheck: new Date().toISOString(),\n                    responseTime,\n                    errorRate: 0,\n                    totalRequests: 0,\n                    successfulRequests: 0,\n                    endpoint: `${this.baseUrl}/health`\n                };\n            } else {\n                throw new Error(`Health check failed: ${response.status}`);\n            }\n        } catch (error) {\n            console.error(\"Agent health check failed:\", error);\n            return {\n                id: \"executive_agent\",\n                name: \"TKC_v5 Executive Agent\",\n                status: \"unhealthy\",\n                uptime: 0,\n                lastHealthCheck: new Date().toISOString(),\n                responseTime: 0,\n                errorRate: 1,\n                totalRequests: 0,\n                successfulRequests: 0,\n                endpoint: `${this.baseUrl}/health`\n            };\n        }\n    }\n    async getAgentInfo() {\n        try {\n            const response = await fetch(`${this.baseUrl}/`, {\n                method: \"GET\",\n                signal: AbortSignal.timeout(5000)\n            });\n            if (response.ok) {\n                return await response.json();\n            }\n            throw new Error(`Agent info failed: ${response.status}`);\n        } catch (error) {\n            console.error(\"Agent info failed:\", error);\n            return null;\n        }\n    }\n    async getAvailableTools() {\n        try {\n            const response = await fetch(`${this.baseUrl}/tools`, {\n                method: \"GET\",\n                signal: AbortSignal.timeout(5000)\n            });\n            if (response.ok) {\n                return await response.json();\n            }\n            throw new Error(`Tools endpoint failed: ${response.status}`);\n        } catch (error) {\n            console.error(\"Tools endpoint failed:\", error);\n            return null;\n        }\n    }\n}\n// Firestore Client - connects to real conversation data\nclass FirestoreClient {\n    constructor(){\n        this.projectId = \"vertex-ai-agent-yzdlnjey\" || 0;\n    }\n    async getRecentConversations(limit = 20) {\n        try {\n            // This would require Firebase Admin SDK setup\n            // For now, return empty array - will implement in next iteration\n            console.log(\"Firestore client not yet implemented - would query email_conversations collection\");\n            return [];\n        } catch (error) {\n            console.error(\"Firestore query failed:\", error);\n            return [];\n        }\n    }\n    async getConversationStats() {\n        try {\n            // Would query Firestore for conversation counts\n            console.log(\"Firestore stats not yet implemented\");\n            return {\n                total: 0,\n                today: 0,\n                thisWeek: 0\n            };\n        } catch (error) {\n            console.error(\"Firestore stats failed:\", error);\n            return {\n                total: 0,\n                today: 0,\n                thisWeek: 0\n            };\n        }\n    }\n}\n// Redis Client - connects to real tenant and session data\nclass RedisClient {\n    constructor(){\n        this.connectionString = process.env.REDIS_URL || \"redis://localhost:6379\";\n    }\n    async getTenantData() {\n        try {\n            // This would require Redis connection setup\n            // For now, return empty array - will implement in next iteration\n            console.log(\"Redis client not yet implemented - would scan for tkc_*:* keys\");\n            return [];\n        } catch (error) {\n            console.error(\"Redis query failed:\", error);\n            return [];\n        }\n    }\n    async getRedisStats() {\n        try {\n            // Would query Redis INFO command\n            console.log(\"Redis stats not yet implemented\");\n            return {\n                keyCount: 0,\n                memoryUsage: 0,\n                connectedClients: 0\n            };\n        } catch (error) {\n            console.error(\"Redis stats failed:\", error);\n            return {\n                keyCount: 0,\n                memoryUsage: 0,\n                connectedClients: 0\n            };\n        }\n    }\n}\n// Pinecone Client - connects to real vector database\nclass PineconeClient {\n    constructor(){\n        this.apiKey = process.env.PINECONE_API_KEY || \"\";\n        this.environment = process.env.PINECONE_ENVIRONMENT || \"\";\n    }\n    async getVectorStats() {\n        try {\n            // This would require Pinecone client setup\n            // For now, return empty stats - will implement in next iteration\n            console.log(\"Pinecone client not yet implemented - would query index stats\");\n            return {\n                totalVectors: 0,\n                namespaces: [],\n                recentOperations: [],\n                indexStats: {\n                    dimension: 0,\n                    totalVectorCount: 0,\n                    namespaceCount: 0\n                }\n            };\n        } catch (error) {\n            console.error(\"Pinecone query failed:\", error);\n            return {\n                totalVectors: 0,\n                namespaces: [],\n                recentOperations: [],\n                indexStats: {\n                    dimension: 0,\n                    totalVectorCount: 0,\n                    namespaceCount: 0\n                }\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/data-clients.ts\n");

/***/ }),

/***/ "(rsc)/./lib/timezone.ts":
/*!*************************!*\
  !*** ./lib/timezone.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDenverTime: () => (/* binding */ formatDenverTime),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   getCurrentDenverTime: () => (/* binding */ getCurrentDenverTime),\n/* harmony export */   isDenverBusinessHours: () => (/* binding */ isDenverBusinessHours)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(rsc)/./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(rsc)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var date_fns_tz__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! date-fns-tz */ \"(rsc)/./node_modules/date-fns-tz/esm/index.js\");\n// Denver timezone utilities\n\n\nconst DENVER_TIMEZONE = \"America/Denver\";\nfunction formatDenverTime(timestamp) {\n    try {\n        const date = typeof timestamp === \"string\" ? (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(timestamp) : timestamp;\n        const denverTime = (0,date_fns_tz__WEBPACK_IMPORTED_MODULE_0__.utcToZonedTime)(date, DENVER_TIMEZONE);\n        return (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(denverTime, \"yyyy-MM-dd HH:mm:ss zzz\");\n    } catch (error) {\n        console.error(\"Error formatting Denver time:\", error);\n        return \"Invalid Date\";\n    }\n}\nfunction formatRelativeTime(timestamp) {\n    try {\n        const date = typeof timestamp === \"string\" ? (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(timestamp) : timestamp;\n        const now = new Date();\n        const diffMs = now.getTime() - date.getTime();\n        const diffMinutes = Math.floor(diffMs / (1000 * 60));\n        const diffHours = Math.floor(diffMinutes / 60);\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffMinutes < 1) return \"Just now\";\n        if (diffMinutes < 60) return `${diffMinutes}m ago`;\n        if (diffHours < 24) return `${diffHours}h ago`;\n        if (diffDays < 7) return `${diffDays}d ago`;\n        return formatDenverTime(date);\n    } catch (error) {\n        console.error(\"Error formatting relative time:\", error);\n        return \"Unknown\";\n    }\n}\nfunction getCurrentDenverTime() {\n    return formatDenverTime(new Date());\n}\nfunction isDenverBusinessHours() {\n    const denverTime = (0,date_fns_tz__WEBPACK_IMPORTED_MODULE_0__.utcToZonedTime)(new Date(), DENVER_TIMEZONE);\n    const hour = denverTime.getHours();\n    const day = denverTime.getDay();\n    // Monday-Friday, 8 AM - 6 PM Denver time\n    return day >= 1 && day <= 5 && hour >= 8 && hour < 18;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/timezone.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/date-fns","vendor-chunks/date-fns-tz","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstatus%2Froute&page=%2Fapi%2Fstatus%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstatus%2Froute.ts&appDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ftylernelsonklug%2FCode%2FTKC_v5%2Fadmin-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
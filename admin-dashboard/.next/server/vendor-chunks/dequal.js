"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dequal";
exports.ids = ["vendor-chunks/dequal"];
exports.modules = {

/***/ "(ssr)/./node_modules/dequal/lite/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/dequal/lite/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dequal: () => (/* binding */ dequal)\n/* harmony export */ });\nvar has = Object.prototype.hasOwnProperty;\nfunction dequal(foo, bar) {\n    var ctor, len;\n    if (foo === bar) return true;\n    if (foo && bar && (ctor = foo.constructor) === bar.constructor) {\n        if (ctor === Date) return foo.getTime() === bar.getTime();\n        if (ctor === RegExp) return foo.toString() === bar.toString();\n        if (ctor === Array) {\n            if ((len = foo.length) === bar.length) {\n                while(len-- && dequal(foo[len], bar[len]));\n            }\n            return len === -1;\n        }\n        if (!ctor || typeof foo === \"object\") {\n            len = 0;\n            for(ctor in foo){\n                if (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n                if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n            }\n            return Object.keys(bar).length === len;\n        }\n    }\n    return foo !== foo && bar !== bar;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dequal/lite/index.mjs\n");

/***/ })

};
;
'use client';

import { useState } from 'react';
import { ActivityEvent } from '@/types/dashboard';
import { formatRelativeTime } from '@/lib/timezone';
import { 
  MessageSquare, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle,
  ChevronDown,
  ChevronRight,
  Filter,
  Search
} from 'lucide-react';

interface ActivityFeedProps {
  activities: ActivityEvent[];
}

export default function ActivityFeed({ activities }: ActivityFeedProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'agent_interaction':
        return <MessageSquare className="h-4 w-4" />;
      case 'system_alert':
        return <Info className="h-4 w-4" />;
      case 'error':
        return <XCircle className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  const getSeverityClass = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'activity-type-error';
      case 'warning':
        return 'activity-type-warning';
      case 'success':
        return 'activity-type-success';
      default:
        return 'activity-type-info';
    }
  };

  const filteredActivities = activities.filter(activity => {
    const matchesFilter = filter === 'all' || activity.type === filter;
    const matchesSearch = !searchTerm || 
      activity.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.type.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-primary-600" />
          Real-Time Activity Feed
        </h3>
        <span className="text-sm text-gray-500">
          {filteredActivities.length} of {activities.length} events
        </span>
      </div>

      {/* Filters */}
      <div className="filter-controls mb-6">
        <div className="filter-group">
          <Filter className="h-4 w-4 text-gray-400" />
          <label className="filter-label">Type:</label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Types</option>
            <option value="agent_interaction">Agent Interactions</option>
            <option value="system_alert">System Alerts</option>
            <option value="error">Errors</option>
          </select>
        </div>

        <div className="filter-group">
          <Search className="h-4 w-4 text-gray-400" />
          <label className="filter-label">Search:</label>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search activities..."
            className="filter-input"
          />
        </div>
      </div>

      {/* Activity List */}
      <div className="space-y-0 max-h-96 overflow-y-auto">
        {filteredActivities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No activities found</p>
            <p className="text-sm">
              {activities.length === 0 
                ? 'Waiting for real system events...' 
                : 'Try adjusting your filters'
              }
            </p>
          </div>
        ) : (
          filteredActivities.map((activity) => (
            <div key={activity.id} className="activity-item">
              <div className="activity-header">
                <div className="flex items-center gap-3">
                  <div className={`activity-type ${getSeverityClass(activity.severity)} flex items-center gap-1`}>
                    {getTypeIcon(activity.type)}
                    {activity.type.replace(/_/g, ' ')}
                  </div>
                  <span className="activity-time">
                    {formatRelativeTime(activity.timestampRaw)}
                  </span>
                </div>
                
                {activity.expandable && (
                  <button
                    onClick={() => toggleExpanded(activity.id)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {expandedItems.has(activity.id) ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </button>
                )}
              </div>

              <div className="activity-message">
                {activity.message}
              </div>

              <div className="text-xs text-gray-500 mt-1">
                Source: {activity.source} • {activity.timestamp}
              </div>

              {activity.expandable && expandedItems.has(activity.id) && (
                <div className="activity-details slide-up">
                  <h4 className="font-medium text-gray-900 mb-3">Event Details</h4>
                  
                  <div className="detail-grid">
                    {Object.entries(activity.details).map(([key, value]) => (
                      <div key={key} className="detail-item">
                        <div className="detail-label">{key.replace(/_/g, ' ')}</div>
                        <div className="detail-value">
                          {typeof value === 'object' 
                            ? JSON.stringify(value, null, 2)
                            : String(value)
                          }
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="text-sm text-blue-700">
                      <strong>Real Data Source:</strong> This event was captured from the actual TKC_v5 system
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}

'use client';

import { formatDenverTime, getCurrentDenverTime } from '@/lib/timezone';
import { RefreshCw, Clock, Shield } from 'lucide-react';
import { useState, useEffect } from 'react';

interface DashboardHeaderProps {
  lastUpdated?: string;
  onRefresh?: () => void;
  isLoading?: boolean;
}

export default function DashboardHeader({ lastUpdated, onRefresh, isLoading }: DashboardHeaderProps) {
  const [currentTime, setCurrentTime] = useState<string>('');

  useEffect(() => {
    const updateTime = () => {
      setCurrentTime(getCurrentDenverTime());
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Shield className="h-8 w-8 text-primary-600" />
              TKC_v5 Admin Dashboard
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Real-time monitoring with actual system data integration
            </p>
          </div>
          
          <div className="flex items-center gap-6">
            <div className="text-right">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span className="font-mono">{currentTime}</span>
              </div>
              {lastUpdated && (
                <div className="text-xs text-gray-500 mt-1">
                  Last updated: {formatDenverTime(lastUpdated)}
                </div>
              )}
            </div>
            
            <button
              onClick={onRefresh}
              disabled={isLoading}
              className={`btn btn-secondary flex items-center gap-2 ${
                isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

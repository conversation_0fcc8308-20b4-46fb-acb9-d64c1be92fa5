'use client';

import { AgentStatus } from '@/types/dashboard';
import { formatRelativeTime } from '@/lib/timezone';
import { Activity, Clock, AlertCircle, CheckCircle, XCircle } from 'lucide-react';

interface AgentStatusCardProps {
  agents: AgentStatus[];
}

export default function AgentStatusCard({ agents }: AgentStatusCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'unhealthy':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'status-healthy';
      case 'unhealthy':
        return 'status-unhealthy';
      default:
        return 'status-unknown';
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title flex items-center gap-2">
          <Activity className="h-5 w-5 text-primary-600" />
          Agent Status
        </h3>
        <span className="text-sm text-gray-500">
          {agents.length} agent{agents.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="space-y-4">
        {agents.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Activity className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No agent data available</p>
            <p className="text-sm">Check agent connectivity</p>
          </div>
        ) : (
          agents.map((agent) => (
            <div key={agent.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(agent.status)}
                  <div>
                    <h4 className="font-medium text-gray-900">{agent.name}</h4>
                    <p className="text-sm text-gray-500">{agent.endpoint}</p>
                  </div>
                </div>
                <span className={`status-indicator ${getStatusClass(agent.status)}`}>
                  {agent.status}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Response Time:</span>
                  <span className="ml-2 font-medium">
                    {agent.responseTime > 0 ? `${agent.responseTime}ms` : 'N/A'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Last Check:</span>
                  <span className="ml-2 font-medium">
                    {formatRelativeTime(agent.lastHealthCheck)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Success Rate:</span>
                  <span className="ml-2 font-medium">
                    {agent.totalRequests > 0 
                      ? `${Math.round((agent.successfulRequests / agent.totalRequests) * 100)}%`
                      : 'N/A'
                    }
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Total Requests:</span>
                  <span className="ml-2 font-medium">{agent.totalRequests}</span>
                </div>
              </div>

              {agent.status === 'unhealthy' && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm text-red-700 font-medium">
                      Agent is not responding properly
                    </span>
                  </div>
                  <p className="text-sm text-red-600 mt-1">
                    Check agent logs and connectivity to {agent.endpoint}
                  </p>
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
}

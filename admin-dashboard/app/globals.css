@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .card-header {
    @apply flex items-center justify-between mb-4;
  }
  
  .card-title {
    @apply text-lg font-semibold text-gray-900;
  }
  
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-healthy {
    @apply bg-green-100 text-green-800;
  }
  
  .status-unhealthy {
    @apply bg-red-100 text-red-800;
  }
  
  .status-unknown {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-warning {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .metric-row {
    @apply flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-600;
  }
  
  .metric-value {
    @apply text-sm font-semibold text-gray-900;
  }
  
  .activity-item {
    @apply p-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 cursor-pointer transition-colors;
  }
  
  .activity-header {
    @apply flex items-center justify-between mb-2;
  }
  
  .activity-time {
    @apply text-xs text-gray-500 font-mono;
  }
  
  .activity-type {
    @apply inline-flex items-center px-2 py-1 rounded text-xs font-medium;
  }
  
  .activity-type-agent_interaction {
    @apply bg-blue-100 text-blue-800;
  }

  .activity-type-system_alert {
    @apply bg-orange-100 text-orange-800;
  }

  .activity-type-error {
    @apply bg-red-100 text-red-800;
  }

  .activity-type-success {
    @apply bg-green-100 text-green-800;
  }

  .activity-type-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .activity-type-info {
    @apply bg-blue-100 text-blue-800;
  }
  
  .activity-message {
    @apply text-sm text-gray-700;
  }
  
  .activity-details {
    @apply mt-3 p-3 bg-gray-50 rounded-md border border-gray-200;
  }
  
  .detail-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-3;
  }
  
  .detail-item {
    @apply bg-white p-3 rounded border border-gray-200;
  }
  
  .detail-label {
    @apply text-xs font-medium text-gray-500 uppercase tracking-wide;
  }
  
  .detail-value {
    @apply text-sm text-gray-900 mt-1;
  }
  
  .filter-controls {
    @apply flex flex-wrap gap-4 items-center;
  }
  
  .filter-group {
    @apply flex items-center gap-2;
  }
  
  .filter-label {
    @apply text-sm font-medium text-gray-700;
  }
  
  .filter-select {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  .filter-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm;
  }
  
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-primary-500;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600;
  }
  
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
}

'use client';

import { useState, useEffect } from 'react';
import useS<PERSON> from 'swr';
import DashboardHeader from '@/components/DashboardHeader';
import AgentStatusCard from '@/components/AgentStatusCard';
import ActivityFeed from '@/components/ActivityFeed';
import { DashboardData, ApiResponse } from '@/types/dashboard';
import { Server, Database, Users, AlertCircle } from 'lucide-react';

// Fetcher function with authentication
const fetcher = async (url: string): Promise<ApiResponse<DashboardData>> => {
  const username = process.env.NEXT_PUBLIC_DASHBOARD_USERNAME || 'admin';
  const password = process.env.NEXT_PUBLIC_DASHBOARD_PASSWORD || 'tkc_monitor_2025';
  const credentials = btoa(`${username}:${password}`);

  const response = await fetch(url, {
    headers: {
      'Authorization': `Basic ${credentials}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

export default function Dashboard() {
  const [isManualRefresh, setIsManualRefresh] = useState(false);

  // Fetch dashboard data with SWR for real-time updates
  const { data, error, isLoading, mutate } = useSWR<ApiResponse<DashboardData>>(
    '/api/status',
    fetcher,
    {
      refreshInterval: 30000, // Refresh every 30 seconds
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
    }
  );

  const handleManualRefresh = async () => {
    setIsManualRefresh(true);
    await mutate();
    setIsManualRefresh(false);
  };

  const dashboardData = data?.data;
  const loading = isLoading || isManualRefresh;

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Connection Error</h1>
          <p className="text-gray-600 mb-4">
            Failed to connect to TKC_v5 system: {error.message}
          </p>
          <button
            onClick={handleManualRefresh}
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? 'Retrying...' : 'Retry Connection'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <DashboardHeader
        lastUpdated={dashboardData?.timestamp}
        onRefresh={handleManualRefresh}
        isLoading={loading}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading && !dashboardData ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="loading-spinner mx-auto mb-4"></div>
              <p className="text-gray-600">Connecting to TKC_v5 system...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* System Health Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">System Health</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {dashboardData?.systemHealth || 'Unknown'}
                    </p>
                  </div>
                  <Server className="h-8 w-8 text-primary-600" />
                </div>
              </div>

              <div className="card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Agents</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {dashboardData?.agents?.length || 0}
                    </p>
                  </div>
                  <Database className="h-8 w-8 text-primary-600" />
                </div>
              </div>

              <div className="card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Tenants</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {dashboardData?.tenants?.activeTenants || 0}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-primary-600" />
                </div>
              </div>

              <div className="card">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Conversations</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {dashboardData?.tenants?.totalConversations || 0}
                    </p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-primary-600" />
                </div>
              </div>
            </div>

            {/* Main Dashboard Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Agent Status */}
              <AgentStatusCard agents={dashboardData?.agents || []} />

              {/* Infrastructure Status */}
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title flex items-center gap-2">
                    <Server className="h-5 w-5 text-primary-600" />
                    Infrastructure Status
                  </h3>
                </div>

                <div className="space-y-4">
                  {dashboardData?.infrastructure ? (
                    Object.entries(dashboardData.infrastructure).map(([service, status]) => (
                      <div key={service} className="metric-row">
                        <span className="metric-label capitalize">
                          {service.replace(/_/g, ' ')}
                        </span>
                        <span className={`status-indicator ${
                          typeof status === 'object' && status.status === 'healthy' 
                            ? 'status-healthy' 
                            : 'status-unknown'
                        }`}>
                          {typeof status === 'object' ? status.status : 'unknown'}
                        </span>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">
                      No infrastructure data available
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Activity Feed - Full Width */}
            <ActivityFeed activities={dashboardData?.activity || []} />

            {/* Real Data Connection Status */}
            <div className="card bg-blue-50 border-blue-200">
              <div className="flex items-center gap-3">
                <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                <div>
                  <h4 className="font-medium text-blue-900">
                    Connected to Real TKC_v5 System
                  </h4>
                  <p className="text-sm text-blue-700">
                    This dashboard displays actual data from your TKC_v5 agent platform at{' '}
                    <code className="bg-blue-100 px-1 rounded">
                      {process.env.NEXT_PUBLIC_AGENT_API_URL || 'https://vertex-ai-agent-1072222703018.us-central1.run.app'}
                    </code>
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Last updated: {dashboardData?.timestamp || 'Never'} • 
                    Auto-refresh: 30s • 
                    Project: {process.env.NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT || 'vertex-ai-agent-yzdlnjey'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

// Real dashboard data API - connects to actual TKC_v5 system
import { NextRequest, NextResponse } from 'next/server';
import { validateAuth, createAuthResponse } from '@/lib/auth';
import { formatDenverTime } from '@/lib/timezone';
import { AgentAPIClient, FirestoreClient, RedisClient, PineconeClient } from '@/lib/data-clients';
import { DashboardData, ActivityEvent } from '@/types/dashboard';

export async function GET(request: NextRequest) {
  // Validate authentication
  if (!validateAuth(request)) {
    return createAuthResponse();
  }

  try {
    const agentClient = new AgentAPIClient();
    const firestoreClient = new FirestoreClient();
    const redisClient = new RedisClient();
    const pineconeClient = new PineconeClient();

    // Get real agent status
    const agentStatus = await agentClient.getAgentHealth();
    const agentInfo = await agentClient.getAgentInfo();
    const agentTools = await agentClient.getAvailableTools();

    // Get real conversation data
    const conversations = await firestoreClient.getRecentConversations(10);
    const conversationStats = await firestoreClient.getConversationStats();

    // Get real tenant data
    const tenantData = await redisClient.getTenantData();
    const redisStats = await redisClient.getRedisStats();

    // Get real vector database stats
    const vectorStats = await pineconeClient.getVectorStats();

    // Create real activity events from actual data
    const activityEvents: ActivityEvent[] = [];

    // Add agent health event
    activityEvents.push({
      id: `agent_health_${Date.now()}`,
      timestamp: formatDenverTime(new Date()),
      timestampRaw: new Date().toISOString(),
      type: 'system_alert',
      severity: agentStatus.status === 'healthy' ? 'success' : 'error',
      message: `Agent health check: ${agentStatus.status} (${agentStatus.responseTime}ms)`,
      source: 'agent_monitor',
      details: {
        endpoint: agentStatus.endpoint,
        responseTime: agentStatus.responseTime,
        status: agentStatus.status,
        lastCheck: agentStatus.lastHealthCheck,
      },
      expandable: true,
    });

    // Add agent info event if available
    if (agentInfo) {
      activityEvents.push({
        id: `agent_info_${Date.now()}`,
        timestamp: formatDenverTime(new Date()),
        timestampRaw: new Date().toISOString(),
        type: 'agent_interaction',
        severity: 'info',
        message: `Agent service: ${agentInfo.service || 'TKC_v5'} - ${agentInfo.status || 'running'}`,
        source: 'agent_api',
        details: agentInfo,
        expandable: true,
      });
    }

    // Add tools availability event
    if (agentTools) {
      const toolCount = Array.isArray(agentTools) ? agentTools.length : Object.keys(agentTools).length;
      activityEvents.push({
        id: `tools_check_${Date.now()}`,
        timestamp: formatDenverTime(new Date()),
        timestampRaw: new Date().toISOString(),
        type: 'system_alert',
        severity: 'info',
        message: `Agent tools available: ${toolCount} tools loaded`,
        source: 'tools_monitor',
        details: {
          toolCount,
          tools: agentTools,
        },
        expandable: true,
      });
    }

    // Add conversation events from real data
    conversations.forEach((conv, index) => {
      activityEvents.push({
        id: conv.id,
        timestamp: formatDenverTime(conv.timestamp),
        timestampRaw: conv.timestampRaw,
        type: 'agent_interaction',
        severity: conv.status === 'completed' ? 'success' : conv.status === 'failed' ? 'error' : 'info',
        message: `Conversation with ${conv.customerEmail}: ${conv.summary}`,
        source: 'firestore',
        details: {
          customerEmail: conv.customerEmail,
          threadId: conv.threadId,
          messageCount: conv.messageCount,
          processingTime: conv.processingTime,
          agentUsed: conv.agentUsed,
          toolsUsed: conv.toolsUsed,
          status: conv.status,
        },
        expandable: true,
      });
    });

    // Build dashboard data with real information
    const dashboardData: DashboardData = {
      timestamp: formatDenverTime(new Date()),
      timestampRaw: new Date().toISOString(),
      agents: [agentStatus],
      infrastructure: {
        agent: {
          status: agentStatus.status,
          responseTime: agentStatus.responseTime,
          lastCheck: agentStatus.lastHealthCheck,
        },
        firestore: {
          status: conversations.length >= 0 ? 'healthy' : 'unknown',
          connectionCount: 1,
          lastOperation: new Date().toISOString(),
        },
        pinecone: {
          status: vectorStats.totalVectors >= 0 ? 'healthy' : 'unknown',
          indexHealth: 'operational',
          lastSync: new Date().toISOString(),
        },
        redis: {
          status: redisStats.keyCount >= 0 ? 'healthy' : 'unknown',
          memoryUsage: redisStats.memoryUsage,
          connectedClients: redisStats.connectedClients,
          keyCount: redisStats.keyCount,
        },
      },
      tenants: {
        activeTenants: tenantData.length,
        totalConversations: conversationStats.total,
        totalVectors: vectorStats.totalVectors,
        tenants: tenantData,
      },
      activity: activityEvents.sort((a, b) => 
        new Date(b.timestampRaw).getTime() - new Date(a.timestampRaw).getTime()
      ),
      systemHealth: agentStatus.status === 'healthy' ? 'healthy' : 'degraded',
    };

    return NextResponse.json({
      success: true,
      data: dashboardData,
      timestamp: formatDenverTime(new Date()),
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch dashboard data',
      timestamp: formatDenverTime(new Date()),
    }, { status: 500 });
  }
}

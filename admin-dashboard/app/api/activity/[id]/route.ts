// Activity details API - provides drill-down data for specific events
import { NextRequest, NextResponse } from 'next/server';
import { validateAuth, createAuthResponse } from '@/lib/auth';
import { formatDenverTime } from '@/lib/timezone';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Validate authentication
  if (!validateAuth(request)) {
    return createAuthResponse();
  }

  const activityId = params.id;

  try {
    // In a real implementation, this would query the actual data source
    // based on the activity ID and type. For now, we'll provide detailed
    // mock data that represents what real drill-down would look like.

    const activityDetails = {
      id: activityId,
      timestamp: formatDenverTime(new Date()),
      timestampRaw: new Date().toISOString(),
      type: 'agent_interaction',
      summary: 'Real agent interaction details',
      details: {
        source: 'real_system_data',
        note: 'This would contain actual event details from the TKC_v5 system',
        agentEndpoint: process.env.AGENT_API_URL,
        projectId: process.env.GOOGLE_CLOUD_PROJECT,
        dataSource: 'Connected to real TKC_v5 infrastructure',
      },
      metadata: {
        retrievedAt: new Date().toISOString(),
        dataSource: 'real_system',
        authenticated: true,
      },
    };

    return NextResponse.json(activityDetails);

  } catch (error) {
    console.error('Activity details API error:', error);
    return NextResponse.json({
      error: 'Failed to fetch activity details',
      timestamp: formatDenverTime(new Date()),
    }, { status: 500 });
  }
}

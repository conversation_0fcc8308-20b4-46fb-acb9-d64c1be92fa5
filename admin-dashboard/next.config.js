/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  env: {
    GOOGLE_CLOUD_PROJECT: process.env.GOOGLE_CLOUD_PROJECT || 'vertex-ai-agent-yzdlnjey',
    AGENT_API_URL: process.env.AGENT_API_URL || 'https://vertex-ai-agent-1072222703018.us-central1.run.app',
    DASHBOARD_USERNAME: process.env.DASHBOARD_USERNAME || 'admin',
    DASHBOARD_PASSWORD: process.env.DASHBOARD_PASSWORD || 'tkc_monitor_2025',
  },
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ]
  },
}

module.exports = nextConfig

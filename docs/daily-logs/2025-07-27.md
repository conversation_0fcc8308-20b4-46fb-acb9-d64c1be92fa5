### NOTES THOUGHTS AND LOGS FOR JULY 27th, 2025


DONE
latest: digest: sha256:88a4cf8106dcf8a1f886915f66aa0996fe9554c65a2545c55c05f2784427c16b size: 2415
9b4446a6ff10: Pushed
b48ae8756d71: Pushed
7cc7fe68eff6: Layer already exists
0a00f6ce5fb7: Layer already exists
d22cc68b10d7: Layer already exists
943faa7467a0: Layer already exists
d62e20d5c356: Pushed
e637eb055b4a: Pushed
2ef43da4e572: Pushed
c739d1d7e675: Pushed
b48ae8756d71: Waiting
7cc7fe68eff6: Waiting
0a00f6ce5fb7: Waiting
943faa7467a0: Waiting
d22cc68b10d7: Waiting
7cc7fe68eff6: Preparing
0a00f6ce5fb7: Preparing
943faa7467a0: Preparing
d22cc68b10d7: Preparing
b48ae8756d71: Preparing
d62e20d5c356: Preparing
c739d1d7e675: Preparing
9b4446a6ff10: Preparing
2ef43da4e572: Preparing
e637eb055b4a: Preparing
The push refers to repository [gcr.io/vertex-ai-agent-yzdlnjey/vertex-ai-agent]
Pushing gcr.io/vertex-ai-agent-yzdlnjey/vertex-ai-agent
PUSH
Successfully tagged gcr.io/vertex-ai-agent-yzdlnjey/vertex-ai-agent:latest
Successfully built d48621a67bc6
 ---> d48621a67bc6
Removing intermediate container ef6d8a326b6b
 ---> Running in ef6d8a326b6b
Step 13/13 : CMD ["python", "src/main.py"]
 ---> b7f847508ef8
Removing intermediate container 7179044b5de3
 ---> Running in 7179044b5de3
Step 12/13 : HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3     CMD curl -f http://localhost:8080/health || exit 1
 ---> fe3da7ccfd6f
Removing intermediate container 9b1dbd360393
 ---> Running in 9b1dbd360393
Step 11/13 : ENV GOOGLE_CLOUD_PROJECT=vertex-ai-agent-yzdlnjey
 ---> a165d3999a11
Removing intermediate container 124e494ca968
 ---> Running in 124e494ca968
Step 10/13 : ENV PYTHONPATH=/app/src
 ---> 5acd970fd98c
Removing intermediate container 26381cd665b3
 ---> Running in 26381cd665b3
Step 9/13 : EXPOSE 8080
 ---> 3e37e440f54c
Removing intermediate container 5f5d9cdb830e
 ---> Running in 5f5d9cdb830e
Step 8/13 : USER app
 ---> 357e98f147f4
Removing intermediate container 035f04a9e7ac
 ---> Running in 035f04a9e7ac
Step 7/13 : RUN useradd --create-home --shell /bin/bash app     && chown -R app:app /app
 ---> d52498a06378
Step 6/13 : COPY src/ ./src/
 ---> ef04a1c8d451
Removing intermediate container 01bf99e5ab71
[notice] To update, run: pip install --upgrade pip
[notice] A new release of pip is available: 24.0 -> 25.1.1

WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
Successfully installed PyYAML-6.0.2 SQLAlchemy-2.0.41 annotated-types-0.7.0 anyio-4.9.0 black-25.1.0 bottleneck-1.5.0 cachetools-5.5.2 certifi-2025.7.14 charset_normalizer-3.4.2 click-8.2.1 docstring_parser-0.17.0 fastapi-0.116.1 google-api-core-2.25.1 google-api-python-client-2.177.0 google-auth-2.40.3 google-auth-httplib2-0.2.0 google-auth-oauthlib-1.2.2 google-cloud-aiplatform-1.105.0 google-cloud-bigquery-3.35.1 google-cloud-core-2.4.3 google-cloud-resource-manager-1.14.2 google-cloud-secret-manager-2.24.0 google-cloud-storage-2.19.0 google-crc32c-1.7.1 google-genai-1.27.0 google-resumable-media-2.7.2 googleapis-common-protos-1.70.0 greenlet-3.2.3 grpc-google-iam-v1-0.14.2 grpcio-1.74.0 grpcio-status-1.74.0 h11-0.16.0 httpcore-1.0.9 httplib2-0.22.0 httptools-0.6.4 httpx-0.28.1 httpx-sse-0.4.1 idna-3.10 iniconfig-2.1.0 isort-6.0.1 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.3.27 langchain-core-0.3.72 langchain-google-vertexai-2.0.27 langchain-text-splitters-0.3.9 langgraph-0.5.4 langgraph-checkpoint-2.1.1 langgraph-prebuilt-0.5.2 langgraph-sdk-0.1.74 langsmith-0.4.8 mypy-extensions-1.1.0 numexpr-2.11.0 numpy-2.3.2 oauthlib-3.3.1 orjson-3.11.1 ormsgpack-1.10.0 packaging-25.0 pathspec-0.12.1 platformdirs-4.3.8 pluggy-1.6.0 proto-plus-1.26.1 protobuf-6.31.1 pyarrow-19.0.1 pyasn1-0.6.1 pyasn1-modules-0.4.2 pydantic-2.11.7 pydantic-core-2.33.2 pygments-2.19.2 pyparsing-3.2.3 pytest-8.4.1 pytest-asyncio-1.1.0 python-dateutil-2.9.0.post0 python-dotenv-1.1.1 requests-2.32.4 requests-oauthlib-2.0.0 requests-toolbelt-1.0.0 rsa-4.9.1 shapely-2.1.1 six-1.17.0 sniffio-1.3.1 starlette-0.47.2 tenacity-8.5.0 typing-extensions-4.14.1 typing-inspection-0.4.1 uritemplate-4.2.0 urllib3-2.5.0 uvicorn-0.35.0 uvloop-0.21.0 validators-0.35.0 watchfiles-1.1.0 websockets-15.0.1 xxhash-3.5.0 zstandard-0.23.0
Installing collected packages: zstandard, xxhash, websockets, validators, uvloop, urllib3, uritemplate, typing-extensions, tenacity, sniffio, six, PyYAML, python-dotenv, pyparsing, pygments, pyasn1, pyarrow, protobuf, pluggy, platformdirs, pathspec, packaging, ormsgpack, orjson, oauthlib, numpy, mypy-extensions, jsonpointer, isort, iniconfig, idna, httpx-sse, httptools, h11, grpcio, greenlet, google-crc32c, docstring_parser, click, charset_normalizer, certifi, cachetools, annotated-types, uvicorn, typing-inspection, SQLAlchemy, shapely, rsa, requests, python-dateutil, pytest, pydantic-core, pyasn1-modules, proto-plus, numexpr, jsonpatch, httplib2, httpcore, googleapis-common-protos, google-resumable-media, bottleneck, black, anyio, watchfiles, starlette, requests-toolbelt, requests-oauthlib, pytest-asyncio, pydantic, httpx, grpcio-status, google-auth, langsmith, langgraph-sdk, grpc-google-iam-v1, google-genai, google-auth-oauthlib, google-auth-httplib2, google-api-core, fastapi, langchain-core, google-cloud-core, google-api-python-client, langgraph-checkpoint, langchain-text-splitters, google-cloud-storage, google-cloud-secret-manager, google-cloud-resource-manager, google-cloud-bigquery, langgraph-prebuilt, langchain, google-cloud-aiplatform, langgraph, langchain-google-vertexai
Downloading sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading six-1.17.0-py2.py3-none-any.whl (11 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 5.4/5.4 MB 223.9 MB/s eta 0:00:00
Downloading zstandard-0.23.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (5.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 54.5/54.5 kB 214.8 MB/s eta 0:00:00
Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 280.1 MB/s eta 0:00:00
Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 111.1/111.1 kB 93.4 MB/s eta 0:00:00
Downloading pyparsing-3.2.3-py3-none-any.whl (111 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 83.1/83.1 kB 239.2 MB/s eta 0:00:00
Downloading pyasn1-0.6.1-py3-none-any.whl (83 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 216.5/216.5 kB 222.5 MB/s eta 0:00:00
Downloading ormsgpack-1.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (216 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 130.9/130.9 kB 244.6 MB/s eta 0:00:00
Downloading orjson-3.11.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (130 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 160.1/160.1 kB 247.4 MB/s eta 0:00:00
Downloading oauthlib-3.3.1-py3-none-any.whl (160 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.9/16.9 MB 230.2 MB/s eta 0:00:00
Downloading numpy-2.3.2-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (16.9 MB)
Downloading jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)
Downloading grpcio_status-1.74.0-py3-none-any.whl (14 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 6.2/6.2 MB 234.3 MB/s eta 0:00:00
Downloading grpcio-1.74.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (6.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 585.5/585.5 kB 259.8 MB/s eta 0:00:00
Downloading greenlet-3.2.3-cp311-cp311-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl (585 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 294.5/294.5 kB 246.5 MB/s eta 0:00:00
Downloading googleapis_common_protos-1.70.0-py3-none-any.whl (294 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 81.3/81.3 kB 186.8 MB/s eta 0:00:00
Downloading google_resumable_media-2.7.2-py2.py3-none-any.whl (81 kB)
Downloading google_crc32c-1.7.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (32 kB)
Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl (29 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100.9/100.9 kB 227.2 MB/s eta 0:00:00
Downloading anyio-4.9.0-py3-none-any.whl (100 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 194.8/194.8 kB 246.3 MB/s eta 0:00:00
Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (194 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 182.3/182.3 kB 197.4 MB/s eta 0:00:00
Downloading websockets-15.0.1-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (182 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 453.1/453.1 kB 214.5 MB/s eta 0:00:00
Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (453 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 44.7/44.7 kB 203.3 MB/s eta 0:00:00
Downloading validators-0.35.0-py3-none-any.whl (44 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.0/4.0 MB 250.1 MB/s eta 0:00:00
Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 129.8/129.8 kB 242.3 MB/s eta 0:00:00
Downloading urllib3-2.5.0-py3-none-any.whl (129 kB)
Downloading uritemplate-4.2.0-py3-none-any.whl (11 kB)
Downloading typing_inspection-0.4.1-py3-none-any.whl (14 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.9/43.9 kB 183.2 MB/s eta 0:00:00
Downloading typing_extensions-4.14.1-py3-none-any.whl (43 kB)
Downloading tenacity-8.5.0-py3-none-any.whl (28 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.0/73.0 kB 243.0 MB/s eta 0:00:00
Downloading starlette-0.47.2-py3-none-any.whl (72 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.3/3.3 MB 194.1 MB/s eta 0:00:00
Downloading sqlalchemy-2.0.41-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.1/3.1 MB 207.3 MB/s eta 0:00:00
Downloading shapely-2.1.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)
Downloading rsa-4.9.1-py3-none-any.whl (34 kB)
Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl (24 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 763.0/763.0 kB 280.9 MB/s eta 0:00:00
Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (762 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.2/1.2 MB 255.1 MB/s eta 0:00:00
Downloading pygments-2.19.2-py3-none-any.whl (1.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 181.3/181.3 kB 219.6 MB/s eta 0:00:00
Downloading pyasn1_modules-0.4.2-py3-none-any.whl (181 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.1/42.1 MB 218.2 MB/s eta 0:00:00
Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl (42.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 321.1/321.1 kB 269.4 MB/s eta 0:00:00
Downloading protobuf-6.31.1-cp39-abi3-manylinux2014_x86_64.whl (321 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 50.2/50.2 kB 190.6 MB/s eta 0:00:00
Downloading proto_plus-1.26.1-py3-none-any.whl (50 kB)
Downloading pluggy-1.6.0-py3-none-any.whl (20 kB)
Downloading platformdirs-4.3.8-py3-none-any.whl (18 kB)
Downloading pathspec-0.12.1-py3-none-any.whl (31 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 66.5/66.5 kB 223.9 MB/s eta 0:00:00
Downloading packaging-25.0-py3-none-any.whl (66 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 401.7/401.7 kB 284.1 MB/s eta 0:00:00
Downloading numexpr-2.11.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (401 kB)
Downloading mypy_extensions-1.1.0-py3-none-any.whl (5.0 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 368.0/368.0 kB 290.0 MB/s eta 0:00:00
Downloading langsmith-0.4.8-py3-none-any.whl (367 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 50.3/50.3 kB 215.0 MB/s eta 0:00:00
Downloading langgraph_sdk-0.1.74-py3-none-any.whl (50 kB)
Downloading langgraph_prebuilt-0.5.2-py3-none-any.whl (23 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.9/43.9 kB 206.0 MB/s eta 0:00:00
Downloading langgraph_checkpoint-2.1.1-py3-none-any.whl (43 kB)
Downloading langchain_text_splitters-0.3.9-py3-none-any.whl (33 kB)
Downloading jsonpatch-1.33-py2.py3-none-any.whl (12 kB)
Downloading iniconfig-2.1.0-py3-none-any.whl (6.0 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 70.4/70.4 kB 80.1 MB/s eta 0:00:00
Downloading idna-3.10-py3-none-any.whl (70 kB)
Downloading httpx_sse-0.4.1-py3-none-any.whl (8.1 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 78.8/78.8 kB 221.8 MB/s eta 0:00:00
Downloading httpcore-1.0.9-py3-none-any.whl (78 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 73.5/73.5 kB 218.7 MB/s eta 0:00:00
Downloading httpx-0.28.1-py3-none-any.whl (73 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 459.8/459.8 kB 273.7 MB/s eta 0:00:00
Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (459 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 96.9/96.9 kB 247.8 MB/s eta 0:00:00
Downloading httplib2-0.22.0-py3-none-any.whl (96 kB)
Downloading h11-0.16.0-py3-none-any.whl (37 kB)
Downloading grpc_google_iam_v1-0.14.2-py3-none-any.whl (19 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 218.5/218.5 kB 264.2 MB/s eta 0:00:00
Downloading google_genai-1.27.0-py3-none-any.whl (218 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 131.8/131.8 kB 222.0 MB/s eta 0:00:00
Downloading google_cloud_storage-2.19.0-py2.py3-none-any.whl (131 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 394.3/394.3 kB 269.7 MB/s eta 0:00:00
Downloading google_cloud_resource_manager-1.14.2-py3-none-any.whl (394 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 256.9/256.9 kB 258.1 MB/s eta 0:00:00
Downloading google_cloud_bigquery-3.35.1-py3-none-any.whl (256 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 160.8/160.8 kB 250.4 MB/s eta 0:00:00
Downloading google_api_core-2.25.1-py3-none-any.whl (160 kB)
Downloading docstring_parser-0.17.0-py3-none-any.whl (36 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 102.2/102.2 kB 95.6 MB/s eta 0:00:00
Downloading click-8.2.1-py3-none-any.whl (102 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 147.3/147.3 kB 246.6 MB/s eta 0:00:00
Downloading charset_normalizer-3.4.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (147 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 162.7/162.7 kB 261.6 MB/s eta 0:00:00
Downloading certifi-2025.7.14-py3-none-any.whl (162 kB)
Downloading cachetools-5.5.2-py3-none-any.whl (10 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 361.2/361.2 kB 249.1 MB/s eta 0:00:00
Downloading bottleneck-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (361 kB)
Downloading annotated_types-0.7.0-py3-none-any.whl (13 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 94.2/94.2 kB 257.0 MB/s eta 0:00:00
Downloading isort-6.0.1-py3-none-any.whl (94 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.7/1.7 MB 185.8 MB/s eta 0:00:00
Downloading black-25.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl (1.7 MB)
Downloading pytest_asyncio-1.1.0-py3-none-any.whl (15 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 365.5/365.5 kB 251.3 MB/s eta 0:00:00
Downloading pytest-8.4.1-py3-none-any.whl (365 kB)
Downloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 217.6 MB/s eta 0:00:00
Downloading pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 444.8/444.8 kB 260.8 MB/s eta 0:00:00
Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 64.8/64.8 kB 239.1 MB/s eta 0:00:00
Downloading requests-2.32.4-py3-none-any.whl (64 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 66.4/66.4 kB 223.7 MB/s eta 0:00:00
Downloading uvicorn-0.35.0-py3-none-any.whl (66 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 95.6/95.6 kB 250.6 MB/s eta 0:00:00
Downloading fastapi-0.116.1-py3-none-any.whl (95 kB)
Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl (9.3 kB)
Downloading google_auth_oauthlib-1.2.2-py3-none-any.whl (19 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 13.7/13.7 MB 224.2 MB/s eta 0:00:00
Downloading google_api_python_client-2.177.0-py3-none-any.whl (13.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 216.1/216.1 kB 270.8 MB/s eta 0:00:00
Downloading google_auth-2.40.3-py2.py3-none-any.whl (216 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 218.1/218.1 kB 250.7 MB/s eta 0:00:00
Downloading google_cloud_secret_manager-2.24.0-py3-none-any.whl (218 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 7.9/7.9 MB 211.4 MB/s eta 0:00:00
Downloading google_cloud_aiplatform-1.105.0-py2.py3-none-any.whl (7.9 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 101.0/101.0 kB 227.4 MB/s eta 0:00:00
Downloading langchain_google_vertexai-2.0.27-py3-none-any.whl (101 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 442.8/442.8 kB 271.0 MB/s eta 0:00:00
Downloading langchain_core-0.3.72-py3-none-any.whl (442 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 1.0/1.0 MB 111.2 MB/s eta 0:00:00
Downloading langchain-0.3.27-py3-none-any.whl (1.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 143.9/143.9 kB 263.0 MB/s eta 0:00:00
Downloading langgraph-0.5.4-py3-none-any.whl (143 kB)
  Downloading six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting six>=1.5 (from python-dateutil<3.0.0,>=2.8.2->google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting sniffio>=1.1 (from anyio<5.0.0,>=4.8.0->google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading greenlet-3.2.3-cp311-cp311-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl.metadata (4.1 kB)
Collecting greenlet>=1 (from SQLAlchemy<3,>=1.4->langchain>=0.3.0->-r requirements.txt (line 3))
  Downloading oauthlib-3.3.1-py3-none-any.whl.metadata (7.9 kB)
Collecting oauthlib>=3.0.0 (from requests-oauthlib>=0.7.0->google-auth-oauthlib>=1.2.0->-r requirements.txt (line 14))
  Downloading pyasn1-0.6.1-py3-none-any.whl.metadata (8.4 kB)
Collecting pyasn1<0.7.0,>=0.6.1 (from pyasn1-modules>=0.2.1->google-auth>=2.30.0->-r requirements.txt (line 10))
  Downloading zstandard-0.23.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.0 kB)
Collecting zstandard<0.24.0,>=0.23.0 (from langsmith>=0.1.17->langchain>=0.3.0->-r requirements.txt (line 3))
  Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl.metadata (14 kB)
Collecting requests-toolbelt<2.0.0,>=1.0.0 (from langsmith>=0.1.17->langchain>=0.3.0->-r requirements.txt (line 3))
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 42.1/42.1 kB 188.9 MB/s eta 0:00:00
  Downloading orjson-3.11.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (42 kB)
Collecting orjson>=3.10.1 (from langgraph-sdk<0.2.0,>=0.1.42->langgraph>=0.2.0->-r requirements.txt (line 2))
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.7/43.7 kB 166.3 MB/s eta 0:00:00
  Downloading ormsgpack-1.10.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (43 kB)
Collecting ormsgpack>=1.10.0 (from langgraph-checkpoint<3.0.0,>=2.1.0->langgraph>=0.2.0->-r requirements.txt (line 2))
  Downloading jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)
Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core>=0.3.0->-r requirements.txt (line 4))
  Downloading httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting httpcore==1.* (from httpx<0.29.0,>=0.28.0->langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading pyparsing-3.2.3-py3-none-any.whl.metadata (5.0 kB)
Collecting pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 (from httplib2<1.0.0,>=0.19.0->google-api-python-client>=2.140.0->-r requirements.txt (line 13))
  Downloading tenacity-8.5.0-py3-none-any.whl.metadata (1.2 kB)
Collecting tenacity!=8.4.0,<10.0.0,>=8.1.0 (from langchain-core>=0.3.0->-r requirements.txt (line 4))
  Downloading anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting anyio<5.0.0,>=4.8.0 (from google-genai<2.0.0,>=1.0.0->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading google_crc32c-1.7.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.3 kB)
Collecting google-crc32c<2.0dev,>=1.0 (from google-cloud-storage<3.0.0,>=2.18.0->langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting python-dateutil<3.0.0,>=2.8.2 (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading google_resumable_media-2.7.2-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting google-resumable-media<3.0.0,>=2.0.0 (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading google_cloud_core-2.4.3-py2.py3-none-any.whl.metadata (2.7 kB)
Collecting google-cloud-core<3.0.0,>=2.4.1 (from google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading grpcio_status-1.74.0-py3-none-any.whl.metadata (1.1 kB)
Collecting grpcio-status<2.0.0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading grpcio-1.74.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.8 kB)
Collecting grpcio<2.0.0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading googleapis_common_protos-1.70.0-py3-none-any.whl.metadata (9.3 kB)
Collecting googleapis-common-protos<2.0.0,>=1.56.2 (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.1/62.1 kB 187.1 MB/s eta 0:00:00
  Downloading numpy-2.3.2-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (62 kB)
Collecting numpy (from bottleneck<2.0.0,>=1.4.2->langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading websockets-15.0.1-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Collecting websockets>=10.4 (from uvicorn[standard]>=0.30.0->-r requirements.txt (line 19))
  Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)
Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.30.0->-r requirements.txt (line 19))
  Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)
Collecting uvloop>=0.15.1 (from uvicorn[standard]>=0.30.0->-r requirements.txt (line 19))
  Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)
Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.30.0->-r requirements.txt (line 19))
  Downloading platformdirs-4.3.8-py3-none-any.whl.metadata (12 kB)
Collecting platformdirs>=2 (from black>=24.0.0->-r requirements.txt (line 29))
  Downloading pathspec-0.12.1-py3-none-any.whl.metadata (21 kB)
Collecting pathspec>=0.9.0 (from black>=24.0.0->-r requirements.txt (line 29))
  Downloading mypy_extensions-1.1.0-py3-none-any.whl.metadata (1.1 kB)
Collecting mypy-extensions>=0.4.3 (from black>=24.0.0->-r requirements.txt (line 29))
  Downloading pygments-2.19.2-py3-none-any.whl.metadata (2.5 kB)
Collecting pygments>=2.7.2 (from pytest>=8.0.0->-r requirements.txt (line 27))
  Downloading pluggy-1.6.0-py3-none-any.whl.metadata (4.8 kB)
Collecting pluggy<2,>=1.5 (from pytest>=8.0.0->-r requirements.txt (line 27))
  Downloading iniconfig-2.1.0-py3-none-any.whl.metadata (2.7 kB)
Collecting iniconfig>=1 (from pytest>=8.0.0->-r requirements.txt (line 27))
  Downloading typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Collecting typing-inspection>=0.4.0 (from pydantic>=2.8.0->-r requirements.txt (line 23))
  Downloading pydantic_core-2.33.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Collecting pydantic-core==2.33.2 (from pydantic>=2.8.0->-r requirements.txt (line 23))
  Downloading annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting annotated-types>=0.6.0 (from pydantic>=2.8.0->-r requirements.txt (line 23))
  Downloading certifi-2025.7.14-py3-none-any.whl.metadata (2.4 kB)
Collecting certifi>=2017.4.17 (from requests>=2.32.0->-r requirements.txt (line 22))
  Downloading urllib3-2.5.0-py3-none-any.whl.metadata (6.5 kB)
Collecting urllib3<3,>=1.21.1 (from requests>=2.32.0->-r requirements.txt (line 22))
  Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)
Collecting idna<4,>=2.5 (from requests>=2.32.0->-r requirements.txt (line 22))
  Downloading charset_normalizer-3.4.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (35 kB)
Collecting charset_normalizer<4,>=2 (from requests>=2.32.0->-r requirements.txt (line 22))
  Downloading h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting h11>=0.8 (from uvicorn>=0.30.0->uvicorn[standard]>=0.30.0->-r requirements.txt (line 19))
  Downloading click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting click>=7.0 (from uvicorn>=0.30.0->uvicorn[standard]>=0.30.0->-r requirements.txt (line 19))
  Downloading starlette-0.47.2-py3-none-any.whl.metadata (6.2 kB)
Collecting starlette<0.48.0,>=0.40.0 (from fastapi>=0.110.0->-r requirements.txt (line 18))
  Downloading requests_oauthlib-2.0.0-py2.py3-none-any.whl.metadata (11 kB)
Collecting requests-oauthlib>=0.7.0 (from google-auth-oauthlib>=1.2.0->-r requirements.txt (line 14))
  Downloading uritemplate-4.2.0-py3-none-any.whl.metadata (2.6 kB)
Collecting uritemplate<5,>=3.0.1 (from google-api-python-client>=2.140.0->-r requirements.txt (line 13))
  Downloading httplib2-0.22.0-py3-none-any.whl.metadata (2.6 kB)
Collecting httplib2<1.0.0,>=0.19.0 (from google-api-python-client>=2.140.0->-r requirements.txt (line 13))
  Downloading rsa-4.9.1-py3-none-any.whl.metadata (5.6 kB)
Collecting rsa<5,>=3.1.4 (from google-auth>=2.30.0->-r requirements.txt (line 10))
  Downloading pyasn1_modules-0.4.2-py3-none-any.whl.metadata (3.5 kB)
Collecting pyasn1-modules>=0.2.1 (from google-auth>=2.30.0->-r requirements.txt (line 10))
  Downloading cachetools-5.5.2-py3-none-any.whl.metadata (5.4 kB)
Collecting cachetools<6.0,>=2.0.0 (from google-auth>=2.30.0->-r requirements.txt (line 10))
  Downloading grpc_google_iam_v1-0.14.2-py3-none-any.whl.metadata (9.1 kB)
Collecting grpc-google-iam-v1<1.0.0,>=0.14.0 (from google-cloud-secret-manager>=2.20.0->-r requirements.txt (line 9))
  Downloading docstring_parser-0.17.0-py3-none-any.whl.metadata (3.5 kB)
Collecting docstring_parser<1 (from google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 43.1/43.1 kB 155.9 MB/s eta 0:00:00
  Downloading google_genai-1.27.0-py3-none-any.whl.metadata (43 kB)
Collecting google-genai<2.0.0,>=1.0.0 (from google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading shapely-2.1.1-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)
Collecting shapely<3.0.0 (from google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading google_cloud_resource_manager-1.14.2-py3-none-any.whl.metadata (9.6 kB)
Collecting google-cloud-resource-manager<3.0.0,>=1.3.3 (from google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading google_cloud_bigquery-3.35.1-py3-none-any.whl.metadata (8.0 kB)
Collecting google-cloud-bigquery!=3.20.0,<4.0.0,>=1.15.0 (from google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading protobuf-6.31.1-cp39-abi3-manylinux2014_x86_64.whl.metadata (593 bytes)
Collecting protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.20.2 (from google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading proto_plus-1.26.1-py3-none-any.whl.metadata (2.2 kB)
Collecting proto-plus<2.0.0,>=1.22.3 (from google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading google_api_core-2.25.1-py3-none-any.whl.metadata (3.0 kB)
Collecting google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,<3.0.0,>=1.34.1->google-cloud-aiplatform>=1.70.0->-r requirements.txt (line 8))
  Downloading validators-0.35.0-py3-none-any.whl.metadata (3.9 kB)
Collecting validators<1,>=0.22.0 (from langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading pyarrow-19.0.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (3.3 kB)
Collecting pyarrow<20.0.0,>=19.0.1 (from langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading numexpr-2.11.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (9.0 kB)
Collecting numexpr<3.0.0,>=2.8.6 (from langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading httpx_sse-0.4.1-py3-none-any.whl.metadata (9.4 kB)
Collecting httpx-sse<0.5.0,>=0.4.0 (from langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting httpx<0.29.0,>=0.28.0 (from langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading google_cloud_storage-2.19.0-py2.py3-none-any.whl.metadata (9.1 kB)
Collecting google-cloud-storage<3.0.0,>=2.18.0 (from langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading bottleneck-1.5.0-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (8.1 kB)
Collecting bottleneck<2.0.0,>=1.4.2 (from langchain-google-vertexai>=2.0.0->-r requirements.txt (line 5))
  Downloading packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
Collecting packaging>=23.2 (from langchain-core>=0.3.0->-r requirements.txt (line 4))
  Downloading typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)
Collecting typing-extensions>=4.7 (from langchain-core>=0.3.0->-r requirements.txt (line 4))
  Downloading jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)
Collecting jsonpatch<2.0,>=1.33 (from langchain-core>=0.3.0->-r requirements.txt (line 4))
  Downloading tenacity-9.1.2-py3-none-any.whl.metadata (1.2 kB)
Collecting tenacity!=8.4.0,<10.0.0,>=8.1.0 (from langchain-core>=0.3.0->-r requirements.txt (line 4))
  Downloading PyYAML-6.0.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (2.1 kB)
Collecting PyYAML>=5.3 (from langchain>=0.3.0->-r requirements.txt (line 3))
  Downloading sqlalchemy-2.0.41-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.6 kB)
Collecting SQLAlchemy<3,>=1.4 (from langchain>=0.3.0->-r requirements.txt (line 3))
  Downloading langsmith-0.4.8-py3-none-any.whl.metadata (15 kB)
Collecting langsmith>=0.1.17 (from langchain>=0.3.0->-r requirements.txt (line 3))
  Downloading langchain_text_splitters-0.3.9-py3-none-any.whl.metadata (1.9 kB)
Collecting langchain-text-splitters<1.0.0,>=0.3.9 (from langchain>=0.3.0->-r requirements.txt (line 3))
  Downloading xxhash-3.5.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
Collecting xxhash>=3.5.0 (from langgraph>=0.2.0->-r requirements.txt (line 2))
  Downloading langgraph_sdk-0.1.74-py3-none-any.whl.metadata (1.5 kB)
Collecting langgraph-sdk<0.2.0,>=0.1.42 (from langgraph>=0.2.0->-r requirements.txt (line 2))
  Downloading langgraph_prebuilt-0.5.2-py3-none-any.whl.metadata (4.5 kB)
Collecting langgraph-prebuilt<0.6.0,>=0.5.0 (from langgraph>=0.2.0->-r requirements.txt (line 2))
  Downloading langgraph_checkpoint-2.1.1-py3-none-any.whl.metadata (4.2 kB)
Collecting langgraph-checkpoint<3.0.0,>=2.1.0 (from langgraph>=0.2.0->-r requirements.txt (line 2))
  Downloading isort-6.0.1-py3-none-any.whl.metadata (11 kB)
Collecting isort>=5.13.0 (from -r requirements.txt (line 30))
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 81.3/81.3 kB 209.7 MB/s eta 0:00:00
  Downloading black-25.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl.metadata (81 kB)
Collecting black>=24.0.0 (from -r requirements.txt (line 29))
  Downloading pytest_asyncio-1.1.0-py3-none-any.whl.metadata (4.1 kB)
Collecting pytest-asyncio>=0.24.0 (from -r requirements.txt (line 28))
  Downloading pytest-8.4.1-py3-none-any.whl.metadata (7.7 kB)
Collecting pytest>=8.0.0 (from -r requirements.txt (line 27))
  Downloading python_dotenv-1.1.1-py3-none-any.whl.metadata (24 kB)
Collecting python-dotenv>=1.0.0 (from -r requirements.txt (line 24))
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 68.0/68.0 kB 203.4 MB/s eta 0:00:00
  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Collecting pydantic>=2.8.0 (from -r requirements.txt (line 23))
  Downloading requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)
Collecting requests>=2.32.0 (from -r requirements.txt (line 22))
  Downloading uvicorn-0.35.0-py3-none-any.whl.metadata (6.5 kB)
Collecting uvicorn>=0.30.0 (from uvicorn[standard]>=0.30.0->-r requirements.txt (line 19))
  Downloading fastapi-0.116.1-py3-none-any.whl.metadata (28 kB)
Collecting fastapi>=0.110.0 (from -r requirements.txt (line 18))
  Downloading google_auth_httplib2-0.2.0-py2.py3-none-any.whl.metadata (2.2 kB)
Collecting google-auth-httplib2>=0.2.0 (from -r requirements.txt (line 15))
  Downloading google_auth_oauthlib-1.2.2-py3-none-any.whl.metadata (2.7 kB)
Collecting google-auth-oauthlib>=1.2.0 (from -r requirements.txt (line 14))
  Downloading google_api_python_client-2.177.0-py3-none-any.whl.metadata (7.0 kB)
Collecting google-api-python-client>=2.140.0 (from -r requirements.txt (line 13))
  Downloading google_auth-2.40.3-py2.py3-none-any.whl.metadata (6.2 kB)
Collecting google-auth>=2.30.0 (from -r requirements.txt (line 10))
  Downloading google_cloud_secret_manager-2.24.0-py3-none-any.whl.metadata (9.7 kB)
Collecting google-cloud-secret-manager>=2.20.0 (from -r requirements.txt (line 9))
  Downloading google_cloud_aiplatform-1.105.0-py2.py3-none-any.whl.metadata (38 kB)
Collecting google-cloud-aiplatform>=1.70.0 (from -r requirements.txt (line 8))
  Downloading langchain_google_vertexai-2.0.27-py3-none-any.whl.metadata (4.8 kB)
Collecting langchain-google-vertexai>=2.0.0 (from -r requirements.txt (line 5))
  Downloading langchain_core-0.3.72-py3-none-any.whl.metadata (5.8 kB)
Collecting langchain-core>=0.3.0 (from -r requirements.txt (line 4))
  Downloading langchain-0.3.27-py3-none-any.whl.metadata (7.8 kB)
Collecting langchain>=0.3.0 (from -r requirements.txt (line 3))
  Downloading langgraph-0.5.4-py3-none-any.whl.metadata (6.8 kB)
Collecting langgraph>=0.2.0 (from -r requirements.txt (line 2))
 ---> Running in 01bf99e5ab71
Step 5/13 : RUN pip install --no-cache-dir -r requirements.txt
 ---> 39de1cd0c494
Step 4/13 : COPY requirements.txt .
 ---> a4919436d78f
Removing intermediate container 0a3b8d864267
Processing triggers for libc-bin (2.36-9+deb12u10) ...
Setting up curl (7.88.1-10+deb12u12) ...
Setting up libcurl4:amd64 (7.88.1-10+deb12u12) ...
Setting up libldap-2.5-0:amd64 (2.5.13+dfsg-5) ...
Setting up publicsuffix (20230209.2326-1) ...
Setting up libssh2-1:amd64 (1.10.0-3+b1) ...
Setting up libsasl2-2:amd64 (2.1.28+dfsg-10) ...
Setting up librtmp1:amd64 (2.4+20151223.gitfa8646d.1-2+b2) ...
Setting up libsasl2-modules-db:amd64 (2.1.28+dfsg-10) ...
Setting up libldap-common (2.5.13+dfsg-5) ...
Setting up libnghttp2-14:amd64 (1.52.0-1+deb12u2) ...
Setting up libsasl2-modules:amd64 (2.1.28+dfsg-10) ...
Setting up libbrotli1:amd64 (1.0.9-2+b6) ...
Setting up libpsl5:amd64 (0.21.2-1) ...
Unpacking publicsuffix (20230209.2326-1) ...
Preparing to unpack .../12-publicsuffix_20230209.2326-1_all.deb ...
Selecting previously unselected package publicsuffix.
Unpacking libsasl2-modules:amd64 (2.1.28+dfsg-10) ...
Preparing to unpack .../11-libsasl2-modules_2.1.28+dfsg-10_amd64.deb ...
Selecting previously unselected package libsasl2-modules:amd64.
Unpacking libldap-common (2.5.13+dfsg-5) ...
Preparing to unpack .../10-libldap-common_2.5.13+dfsg-5_all.deb ...
Selecting previously unselected package libldap-common.
Unpacking curl (7.88.1-10+deb12u12) ...
Preparing to unpack .../09-curl_7.88.1-10+deb12u12_amd64.deb ...
Selecting previously unselected package curl.
Unpacking libcurl4:amd64 (7.88.1-10+deb12u12) ...
Preparing to unpack .../08-libcurl4_7.88.1-10+deb12u12_amd64.deb ...
Selecting previously unselected package libcurl4:amd64.
Unpacking libssh2-1:amd64 (1.10.0-3+b1) ...
Preparing to unpack .../07-libssh2-1_1.10.0-3+b1_amd64.deb ...
Selecting previously unselected package libssh2-1:amd64.
Unpacking librtmp1:amd64 (2.4+20151223.gitfa8646d.1-2+b2) ...
Preparing to unpack .../06-librtmp1_2.4+20151223.gitfa8646d.1-2+b2_amd64.deb ...
Selecting previously unselected package librtmp1:amd64.
Unpacking libpsl5:amd64 (0.21.2-1) ...
Preparing to unpack .../05-libpsl5_0.21.2-1_amd64.deb ...
Selecting previously unselected package libpsl5:amd64.
Unpacking libnghttp2-14:amd64 (1.52.0-1+deb12u2) ...
Preparing to unpack .../04-libnghttp2-14_1.52.0-1+deb12u2_amd64.deb ...
Selecting previously unselected package libnghttp2-14:amd64.
Unpacking libldap-2.5-0:amd64 (2.5.13+dfsg-5) ...
Preparing to unpack .../03-libldap-2.5-0_2.5.13+dfsg-5_amd64.deb ...
Selecting previously unselected package libldap-2.5-0:amd64.
Unpacking libsasl2-2:amd64 (2.1.28+dfsg-10) ...
Preparing to unpack .../02-libsasl2-2_2.1.28+dfsg-10_amd64.deb ...
Selecting previously unselected package libsasl2-2:amd64.
Unpacking libsasl2-modules-db:amd64 (2.1.28+dfsg-10) ...
Preparing to unpack .../01-libsasl2-modules-db_2.1.28+dfsg-10_amd64.deb ...
Selecting previously unselected package libsasl2-modules-db:amd64.
Unpacking libbrotli1:amd64 (1.0.9-2+b6) ...
Preparing to unpack .../00-libbrotli1_1.0.9-2+b6_amd64.deb ...
(Reading database ... 6688 files and directories currently installed.)
(Reading database ... 100%
(Reading database ... 95%
(Reading database ... 90%
(Reading database ... 85%
(Reading database ... 80%
(Reading database ... 75%
(Reading database ... 70%
(Reading database ... 65%
(Reading database ... 60%
(Reading database ... 55%
(Reading database ... 50%
(Reading database ... 45%
(Reading database ... 40%
(Reading database ... 35%
(Reading database ... 30%
(Reading database ... 25%
(Reading database ... 20%
(Reading database ... 15%
(Reading database ... 10%
(Reading database ... 5%
(Reading database ... 
Selecting previously unselected package libbrotli1:amd64.
Fetched 1839 kB in 0s (40.9 MB/s)
debconf: delaying package configuration, since apt-utils is not installed
Get:13 http://deb.debian.org/debian bookworm/main amd64 publicsuffix all 20230209.2326-1 [126 kB]
Get:12 http://deb.debian.org/debian bookworm/main amd64 libsasl2-modules amd64 2.1.28+dfsg-10 [66.6 kB]
Get:11 http://deb.debian.org/debian bookworm/main amd64 libldap-common all 2.5.13+dfsg-5 [29.3 kB]
Get:10 http://deb.debian.org/debian bookworm/main amd64 curl amd64 7.88.1-10+deb12u12 [315 kB]
Get:9 http://deb.debian.org/debian bookworm/main amd64 libcurl4 amd64 7.88.1-10+deb12u12 [391 kB]
Get:8 http://deb.debian.org/debian bookworm/main amd64 libssh2-1 amd64 1.10.0-3+b1 [179 kB]
Get:7 http://deb.debian.org/debian bookworm/main amd64 librtmp1 amd64 2.4+20151223.gitfa8646d.1-2+b2 [60.8 kB]
Get:6 http://deb.debian.org/debian bookworm/main amd64 libpsl5 amd64 0.21.2-1 [58.7 kB]
Get:5 http://deb.debian.org/debian bookworm/main amd64 libnghttp2-14 amd64 1.52.0-1+deb12u2 [73.0 kB]
Get:4 http://deb.debian.org/debian bookworm/main amd64 libldap-2.5-0 amd64 2.5.13+dfsg-5 [183 kB]
Get:3 http://deb.debian.org/debian bookworm/main amd64 libsasl2-2 amd64 2.1.28+dfsg-10 [59.7 kB]
Get:2 http://deb.debian.org/debian bookworm/main amd64 libsasl2-modules-db amd64 2.1.28+dfsg-10 [20.3 kB]
Get:1 http://deb.debian.org/debian bookworm/main amd64 libbrotli1 amd64 1.0.9-2+b6 [275 kB]
After this operation, 4550 kB of additional disk space will be used.
Need to get 1839 kB of archives.
0 upgraded, 13 newly installed, 0 to remove and 0 not upgraded.
  publicsuffix
  librtmp1 libsasl2-2 libsasl2-modules libsasl2-modules-db libssh2-1
  curl libbrotli1 libcurl4 libldap-2.5-0 libldap-common libnghttp2-14 libpsl5
The following NEW packages will be installed:
  libsasl2-modules-ldap libsasl2-modules-otp libsasl2-modules-sql
  libsasl2-modules-gssapi-mit | libsasl2-modules-gssapi-heimdal
Suggested packages:
  publicsuffix
  librtmp1 libsasl2-2 libsasl2-modules libsasl2-modules-db libssh2-1
  libbrotli1 libcurl4 libldap-2.5-0 libldap-common libnghttp2-14 libpsl5
The following additional packages will be installed:
Reading state information...
Building dependency tree...
Reading package lists...
Reading package lists...
Fetched 9320 kB in 2s (5802 kB/s)
Get:6 http://deb.debian.org/debian-security bookworm-security/main amd64 Packages [272 kB]
Get:5 http://deb.debian.org/debian bookworm-updates/main amd64 Packages [756 B]
Get:4 http://deb.debian.org/debian bookworm/main amd64 Packages [8793 kB]
Get:3 http://deb.debian.org/debian-security bookworm-security InRelease [48.0 kB]
Get:2 http://deb.debian.org/debian bookworm-updates InRelease [55.4 kB]
Get:1 http://deb.debian.org/debian bookworm InRelease [151 kB]
 ---> Running in 0a3b8d864267
Step 3/13 : RUN apt-get update && apt-get install -y     curl     && rm -rf /var/lib/apt/lists/*
 ---> e7dd24552dd0
Removing intermediate container 69973abe284f
 ---> Running in 69973abe284f
Step 2/13 : WORKDIR /app
 ---> f3bfd8e9386c
Status: Downloaded newer image for python:3.11-slim
Digest: sha256:0ce77749ac83174a31d5e107ce0cfa6b28a2fd6b0615e029d9d84b39c48976ee
4085babbc570: Pull complete
b7b61708209a: Pull complete
abd846fa1cdb: Pull complete
59e22667830b: Pull complete
4085babbc570: Download complete
4085babbc570: Verifying Checksum
59e22667830b: Download complete
59e22667830b: Verifying Checksum
b7b61708209a: Download complete
b7b61708209a: Verifying Checksum
abd846fa1cdb: Download complete
abd846fa1cdb: Verifying Checksum
4085babbc570: Waiting
4085babbc570: Pulling fs layer
b7b61708209a: Pulling fs layer
abd846fa1cdb: Pulling fs layer
59e22667830b: Pulling fs layer
3.11-slim: Pulling from library/python
Step 1/13 : FROM python:3.11-slim

Sending build context to Docker daemon  627.2kB
Already have image (with digest): gcr.io/cloud-builders/docker
BUILD
Operation completed over 1 objects/148.8 KiB.                                    
/ [1 files][148.8 KiB/148.8 KiB]                                                
/ [0 files][    0.0 B/148.8 KiB]                                                
Copying gs://vertex-ai-agent-yzdlnjey_cloudbuild/source/1753555112.185935-9cd5a9c877bb42d89647c4be6980035d.tgz#1753555112762761...
Fetching storage object: gs://vertex-ai-agent-yzdlnjey_cloudbuild/source/1753555112.185935-9cd5a9c877bb42d89647c4be6980035d.tgz#1753555112762761
FETCHSOURCE

starting build "174711b1-53a9-48d2-992b-40527c90d0fd"
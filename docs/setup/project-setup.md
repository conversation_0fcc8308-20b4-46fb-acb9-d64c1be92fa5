# GCP Project Setup Guide

This document details the complete setup process for the Vertex AI Agent project following enterprise-grade practices.

## Project Information

- **Project ID**: `vertex-ai-agent-yzdlnjey`
- **Project Name**: Vertex AI Agent Project
- **Organization**: tkcgroup.co (************)
- **Billing Account**: 01823C-826E19-5D0F6A
- **Region**: us-central1 (primary)

## Prerequisites

1. **gcloud CLI** installed and authenticated
2. **Billing Account** access with appropriate permissions
3. **Organization** access for project creation
4. **IAM Permissions** for project and service account management

## Setup Steps Completed

### 1. Project Creation

```bash
# Automated via provision_project.sh
./provision_project.sh vertex-ai-agent 01823C-826E19-5D0F6A ************
```

**Manual equivalent:**
```bash
# Create project
gcloud projects create vertex-ai-agent-yzdlnjey \
  --name="Vertex AI Agent Project" \
  --organization=************

# Link billing
gcloud billing projects link vertex-ai-agent-yzdlnjey \
  --billing-account=01823C-826E19-5D0F6A

# Set as active project
gcloud config set project vertex-ai-agent-yzdlnjey
```

### 2. API Enablement

**Required APIs:**
- `cloudresourcemanager.googleapis.com` - Project management
- `iam.googleapis.com` - Identity and Access Management
- `iamcredentials.googleapis.com` - Service account credentials
- `aiplatform.googleapis.com` - Vertex AI platform
- `cloudbuild.googleapis.com` - Container builds
- `artifactregistry.googleapis.com` - Container registry
- `run.googleapis.com` - Cloud Run deployment
- `logging.googleapis.com` - Cloud Logging
- `monitoring.googleapis.com` - Cloud Monitoring
- `secretmanager.googleapis.com` - Secret management

```bash
# Enable all APIs
gcloud services enable \
  cloudresourcemanager.googleapis.com \
  iam.googleapis.com \
  iamcredentials.googleapis.com \
  aiplatform.googleapis.com \
  cloudbuild.googleapis.com \
  artifactregistry.googleapis.com \
  run.googleapis.com \
  logging.googleapis.com \
  monitoring.googleapis.com \
  secretmanager.googleapis.com
```

### 3. IAM Configuration

**Service Account Created:**
```bash
gcloud iam service-accounts create agent-executor-sa \
  --display-name="AI Agent Executor Service Account" \
  --description="Service account for running AI agents with minimal required permissions"
```

**Service Account Email:** `<EMAIL>`

**Roles Assigned to Service Account:**
- `roles/aiplatform.user` - Access to Vertex AI services
- `roles/secretmanager.secretAccessor` - Read access to secrets

### 4. Development Permissions

**Current User Roles** (for development):
- `roles/aiplatform.user` - Vertex AI access
- `roles/storage.objectAdmin` - Storage management
- `roles/artifactregistry.writer` - Container registry access
- `roles/logging.viewer` - Log viewing
- `roles/iam.serviceAccountAdmin` - Service account management
- `roles/iam.serviceAccountUser` - Service account impersonation
- `roles/secretmanager.admin` - Secret management

## Verification Commands

```bash
# Check project status
gcloud projects describe vertex-ai-agent-yzdlnjey

# Verify billing
gcloud billing projects describe vertex-ai-agent-yzdlnjey

# List enabled APIs
gcloud services list --enabled

# Check IAM policy
gcloud projects get-iam-policy vertex-ai-agent-yzdlnjey

# Verify service account
gcloud iam service-<NAME_EMAIL>
```

## Security Considerations

1. **No Service Account Keys**: Following best practices, no JSON keys were created
2. **Least Privilege**: Service account has minimal required permissions
3. **Proper Hierarchy**: Project created under organization for governance
4. **Audit Trail**: All changes logged and documented

## Next Steps

1. Set up local development environment
2. Implement LangGraph agent
3. Configure deployment pipeline
4. Set up monitoring and observability

## Troubleshooting

**Common Issues:**
- **Permission Denied**: Ensure proper organization and billing account access
- **API Not Enabled**: Run the API enablement commands
- **Authentication Issues**: Run `gcloud auth login` and `gcloud auth application-default login`

**Support Resources:**
- [GCP Project Management](https://cloud.google.com/resource-manager/docs/creating-managing-projects)
- [IAM Best Practices](https://cloud.google.com/iam/docs/using-iam-securely)
- [Service Account Security](https://cloud.google.com/iam/docs/service-accounts)

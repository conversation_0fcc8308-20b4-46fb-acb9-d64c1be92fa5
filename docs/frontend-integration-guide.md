# Frontend Integration Guide - Inbound Lead Processing

## Overview

This guide shows how to integrate your frontend forms with the TKC_v5 Inbound Lead Processing System. The integration enables automatic lead processing, CRM synchronization, and intelligent follow-up workflows.

## Quick Start

### 1. Basic Form Integration

```html
<!-- HTML Form Example -->
<form id="demo-request-form" class="lead-form">
  <input type="hidden" name="form_type" value="demo_request">
  
  <div class="form-group">
    <label for="email">Email Address *</label>
    <input type="email" id="email" name="email" required>
  </div>
  
  <div class="form-group">
    <label for="name">Full Name *</label>
    <input type="text" id="name" name="name" required>
  </div>
  
  <div class="form-group">
    <label for="company">Company</label>
    <input type="text" id="company" name="company">
  </div>
  
  <div class="form-group">
    <label for="phone">Phone Number</label>
    <input type="tel" id="phone" name="phone">
  </div>
  
  <div class="form-group">
    <label for="message">Tell us about your needs</label>
    <textarea id="message" name="message" rows="4"></textarea>
  </div>
  
  <button type="submit">Request Demo</button>
</form>
```

### 2. JavaScript Integration

```javascript
// Form submission handler
document.getElementById('demo-request-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // Collect form data
    const formData = new FormData(this);
    const submission = {
        form_type: formData.get('form_type'),
        email: formData.get('email'),
        name: formData.get('name'),
        company: formData.get('company') || '',
        phone: formData.get('phone') || '',
        message: formData.get('message') || '',
        source: 'website',
        utm_campaign: getUrlParameter('utm_campaign') || '',
        utm_source: getUrlParameter('utm_source') || '',
        utm_medium: getUrlParameter('utm_medium') || '',
        page_url: window.location.href,
        timestamp: new Date().toISOString(),
        metadata: {
            user_agent: navigator.userAgent,
            referrer: document.referrer,
            screen_resolution: `${screen.width}x${screen.height}`
        }
    };
    
    try {
        // Submit to TKC_v5 webhook
        const response = await fetch('https://your-agent-url/webhook/form', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(submission)
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            // Show success message
            showSuccessMessage('Thank you! We\'ll be in touch soon.');
            
            // Optional: Track conversion
            gtag('event', 'conversion', {
                'send_to': 'AW-XXXXXXXXX/XXXXXXX',
                'value': 1.0,
                'currency': 'USD'
            });
            
            // Redirect to thank you page
            window.location.href = '/thank-you';
        } else {
            throw new Error(result.error || 'Submission failed');
        }
    } catch (error) {
        console.error('Form submission error:', error);
        showErrorMessage('Something went wrong. Please try again.');
    }
});

// Utility function to get URL parameters
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// UI feedback functions
function showSuccessMessage(message) {
    // Implement your success message display
    alert(message); // Replace with your UI component
}

function showErrorMessage(message) {
    // Implement your error message display
    alert(message); // Replace with your UI component
}
```

## Form Types and Configuration

### 1. Demo Request Form
```javascript
const demoRequestData = {
    form_type: 'demo_request',
    // ... other fields
};
```
- **Use Case**: Product demonstration requests
- **Priority**: High (immediate response)
- **Follow-up**: Demo scheduling email within 1 hour

### 2. Consultation Form
```javascript
const consultationData = {
    form_type: 'consultation',
    // ... other fields
};
```
- **Use Case**: Strategy consultation requests
- **Priority**: High (quick response)
- **Follow-up**: Consultation booking email within 2 hours

### 3. Contact Form
```javascript
const contactData = {
    form_type: 'contact',
    // ... other fields
};
```
- **Use Case**: General inquiries and questions
- **Priority**: Medium (standard response)
- **Follow-up**: Response email within 24 hours

### 4. Lead Capture Form
```javascript
const leadCaptureData = {
    form_type: 'lead_capture',
    // ... other fields
};
```
- **Use Case**: Newsletter signups, content downloads
- **Priority**: Low-Medium (nurture sequence)
- **Follow-up**: Welcome email and nurture sequence

## Advanced Integration

### 1. React Component Example

```jsx
import React, { useState } from 'react';

const LeadForm = ({ formType = 'contact' }) => {
    const [formData, setFormData] = useState({
        email: '',
        name: '',
        company: '',
        phone: '',
        message: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState(null);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);
        
        const submission = {
            form_type: formType,
            ...formData,
            source: 'website',
            utm_campaign: new URLSearchParams(window.location.search).get('utm_campaign') || '',
            utm_source: new URLSearchParams(window.location.search).get('utm_source') || '',
            utm_medium: new URLSearchParams(window.location.search).get('utm_medium') || '',
            page_url: window.location.href,
            timestamp: new Date().toISOString(),
            metadata: {
                component: 'LeadForm',
                form_version: '1.0'
            }
        };

        try {
            const response = await fetch(process.env.REACT_APP_WEBHOOK_URL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(submission)
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                setSubmitStatus('success');
                // Track conversion event
                if (window.gtag) {
                    window.gtag('event', 'form_submit', {
                        form_type: formType,
                        lead_id: result.lead_id
                    });
                }
            } else {
                setSubmitStatus('error');
            }
        } catch (error) {
            console.error('Submission error:', error);
            setSubmitStatus('error');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
    };

    if (submitStatus === 'success') {
        return (
            <div className="success-message">
                <h3>Thank you!</h3>
                <p>We've received your {formType.replace('_', ' ')} and will be in touch soon.</p>
            </div>
        );
    }

    return (
        <form onSubmit={handleSubmit} className="lead-form">
            <div className="form-group">
                <input
                    type="email"
                    name="email"
                    placeholder="Email Address *"
                    value={formData.email}
                    onChange={handleChange}
                    required
                />
            </div>
            
            <div className="form-group">
                <input
                    type="text"
                    name="name"
                    placeholder="Full Name *"
                    value={formData.name}
                    onChange={handleChange}
                    required
                />
            </div>
            
            <div className="form-group">
                <input
                    type="text"
                    name="company"
                    placeholder="Company"
                    value={formData.company}
                    onChange={handleChange}
                />
            </div>
            
            <div className="form-group">
                <input
                    type="tel"
                    name="phone"
                    placeholder="Phone Number"
                    value={formData.phone}
                    onChange={handleChange}
                />
            </div>
            
            <div className="form-group">
                <textarea
                    name="message"
                    placeholder="Tell us about your needs"
                    value={formData.message}
                    onChange={handleChange}
                    rows="4"
                />
            </div>
            
            <button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit'}
            </button>
            
            {submitStatus === 'error' && (
                <div className="error-message">
                    Something went wrong. Please try again.
                </div>
            )}
        </form>
    );
};

export default LeadForm;
```

### 2. Vue.js Component Example

```vue
<template>
  <form @submit.prevent="submitForm" class="lead-form">
    <div class="form-group">
      <input
        v-model="formData.email"
        type="email"
        placeholder="Email Address *"
        required
      />
    </div>
    
    <div class="form-group">
      <input
        v-model="formData.name"
        type="text"
        placeholder="Full Name *"
        required
      />
    </div>
    
    <div class="form-group">
      <input
        v-model="formData.company"
        type="text"
        placeholder="Company"
      />
    </div>
    
    <div class="form-group">
      <textarea
        v-model="formData.message"
        placeholder="Tell us about your needs"
        rows="4"
      />
    </div>
    
    <button type="submit" :disabled="isSubmitting">
      {{ isSubmitting ? 'Submitting...' : 'Submit' }}
    </button>
    
    <div v-if="submitStatus === 'success'" class="success-message">
      Thank you! We'll be in touch soon.
    </div>
    
    <div v-if="submitStatus === 'error'" class="error-message">
      Something went wrong. Please try again.
    </div>
  </form>
</template>

<script>
export default {
  name: 'LeadForm',
  props: {
    formType: {
      type: String,
      default: 'contact'
    }
  },
  data() {
    return {
      formData: {
        email: '',
        name: '',
        company: '',
        phone: '',
        message: ''
      },
      isSubmitting: false,
      submitStatus: null
    };
  },
  methods: {
    async submitForm() {
      this.isSubmitting = true;
      
      const submission = {
        form_type: this.formType,
        ...this.formData,
        source: 'website',
        utm_campaign: this.$route.query.utm_campaign || '',
        utm_source: this.$route.query.utm_source || '',
        utm_medium: this.$route.query.utm_medium || '',
        page_url: window.location.href,
        timestamp: new Date().toISOString(),
        metadata: {
          component: 'LeadForm',
          framework: 'vue'
        }
      };

      try {
        const response = await fetch(process.env.VUE_APP_WEBHOOK_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(submission)
        });

        const result = await response.json();
        
        if (result.status === 'success') {
          this.submitStatus = 'success';
          this.$emit('form-submitted', result);
        } else {
          this.submitStatus = 'error';
        }
      } catch (error) {
        console.error('Submission error:', error);
        this.submitStatus = 'error';
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
</script>
```

## Analytics Integration

### Google Analytics 4
```javascript
// Track form submissions
gtag('event', 'form_submit', {
  form_type: 'demo_request',
  form_location: 'homepage',
  lead_score: result.lead_score || 0
});

// Track successful lead processing
gtag('event', 'lead_processed', {
  lead_id: result.lead_id,
  crm_contact_id: result.crm_contact_id,
  priority: result.priority || 'medium'
});
```

### Facebook Pixel
```javascript
// Track lead events
fbq('track', 'Lead', {
  content_name: formType,
  content_category: 'lead_generation',
  value: 1.00,
  currency: 'USD'
});
```

### Custom Analytics
```javascript
// Send to your analytics platform
analytics.track('Form Submitted', {
  formType: submission.form_type,
  leadId: result.lead_id,
  source: submission.source,
  timestamp: submission.timestamp
});
```

## Error Handling

### Client-Side Validation
```javascript
function validateForm(formData) {
    const errors = [];
    
    if (!formData.email || !isValidEmail(formData.email)) {
        errors.push('Valid email address is required');
    }
    
    if (!formData.name || formData.name.trim().length < 2) {
        errors.push('Full name is required');
    }
    
    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
```

### Retry Logic
```javascript
async function submitWithRetry(submission, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const response = await fetch(webhookUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(submission)
            });
            
            if (response.ok) {
                return await response.json();
            }
            
            if (attempt === maxRetries) {
                throw new Error(`Failed after ${maxRetries} attempts`);
            }
            
            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            
        } catch (error) {
            if (attempt === maxRetries) {
                throw error;
            }
        }
    }
}
```

## Testing

### Test Form Submission
```javascript
// Test data for development
const testSubmission = {
    form_type: 'demo_request',
    email: '<EMAIL>',
    name: 'Test User',
    company: 'Test Company',
    phone: '******-0123',
    message: 'This is a test submission',
    source: 'website',
    utm_campaign: 'test-campaign',
    page_url: window.location.href,
    timestamp: new Date().toISOString(),
    metadata: { test: true }
};

// Submit test data
fetch('/webhook/form', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(testSubmission)
})
.then(response => response.json())
.then(result => console.log('Test result:', result));
```

## Security Considerations

### CSRF Protection
```javascript
// Include CSRF token if required
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

fetch('/webhook/form', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
    },
    body: JSON.stringify(submission)
});
```

### Rate Limiting
```javascript
// Implement client-side rate limiting
const SUBMISSION_COOLDOWN = 60000; // 1 minute
let lastSubmissionTime = 0;

function canSubmit() {
    const now = Date.now();
    if (now - lastSubmissionTime < SUBMISSION_COOLDOWN) {
        return false;
    }
    lastSubmissionTime = now;
    return true;
}
```

## Environment Configuration

### Development
```javascript
const WEBHOOK_URL = 'http://localhost:8080/webhook/form';
```

### Staging
```javascript
const WEBHOOK_URL = 'https://tkc-v5-executive-agent-staging.run.app/webhook/form';
```

### Production
```javascript
const WEBHOOK_URL = 'https://tkc-v5-executive-agent.run.app/webhook/form';
```

## Troubleshooting

### Common Issues
1. **CORS Errors**: Ensure webhook endpoint allows your domain
2. **Validation Failures**: Check required fields and data types
3. **Network Timeouts**: Implement retry logic and user feedback
4. **Missing UTM Parameters**: Verify URL parameter extraction

### Debug Mode
```javascript
// Enable debug logging
const DEBUG = process.env.NODE_ENV === 'development';

if (DEBUG) {
    console.log('Form submission data:', submission);
    console.log('Webhook response:', result);
}
```

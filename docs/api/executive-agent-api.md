# TKC_v5 Executive Agent API Documentation

## Overview

The TKC_v5 Executive Agent provides a comprehensive API for business automation including Gmail management, calendar scheduling, CRM integration, email automation, and advanced AI intelligence capabilities with predictive analytics.

**Base URL**: `https://tkc-v5-executive-agent-1072222703018.us-central1.run.app`

## Authentication

All API endpoints require authentication. Use the following methods:

### API Key Authentication
```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     https://tkc-v5-executive-agent-1072222703018.us-central1.run.app/api/endpoint
```

### Customer ID Header
Include customer identification for multi-tenant operations:
```bash
curl -H "X-Customer-ID: your_customer_id" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     https://tkc-v5-executive-agent-1072222703018.us-central1.run.app/api/endpoint
```

## Core Endpoints

### Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-27T20:30:00Z",
  "version": "1.0.0",
  "services": {
    "gmail": "connected",
    "calendar": "connected", 
    "crm": "connected",
    "database": "connected"
  }
}
```

### Chat with Executive Agent
```http
POST /chat
```

**Request Body:**
```json
{
  "message": "Schedule a <NAME_EMAIL> for next Tuesday at 2 PM",
  "customer_id": "tyler_layne",
  "thread_id": "optional_thread_id",
  "context": {
    "timezone": "America/Denver",
    "user_email": "<EMAIL>"
  }
}
```

**Response:**
```json
{
  "response": "I'll schedule that meeting for you. Let me check availability...",
  "actions_taken": [
    {
      "type": "calendar_check",
      "status": "completed",
      "details": "Checked availability for Tuesday 2 PM"
    },
    {
      "type": "meeting_scheduled",
      "status": "completed", 
      "details": "Meeting <NAME_EMAIL>",
      "meeting_id": "cal_event_123"
    }
  ],
  "thread_id": "thread_abc123",
  "timestamp": "2025-01-27T20:30:00Z"
}
```

## Gmail Integration

### Create Email Draft
```http
POST /api/gmail/draft
```

**Request Body:**
```json
{
  "to": "<EMAIL>",
  "subject": "Meeting Follow-up",
  "body": "Thank you for our conversation...",
  "customer_id": "tyler_layne"
}
```

### Get Recent Emails
```http
GET /api/gmail/recent?limit=10&customer_id=tyler_layne
```

### Search Emails
```http
GET /api/gmail/search?q=from:<EMAIL>&customer_id=tyler_layne
```

## Calendar Management

### Check Availability
```http
POST /api/calendar/availability
```

**Request Body:**
```json
{
  "start_date": "2025-01-28",
  "end_date": "2025-01-28", 
  "start_time": "09:00",
  "end_time": "17:00",
  "timezone": "America/Denver",
  "customer_id": "tyler_layne"
}
```

### Schedule Meeting
```http
POST /api/calendar/meeting
```

**Request Body:**
```json
{
  "title": "Strategy Discussion",
  "start_time": "2025-01-28T14:00:00",
  "end_time": "2025-01-28T15:00:00",
  "attendees": ["<EMAIL>", "<EMAIL>"],
  "description": "Quarterly strategy review",
  "video_meeting": true,
  "customer_id": "tyler_layne"
}
```

### Get Upcoming Meetings
```http
GET /api/calendar/upcoming?days=7&customer_id=tyler_layne
```

## CRM Integration

### Create Lead
```http
POST /api/crm/lead
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "name": "John Prospect",
  "company": "Prospect Corp",
  "phone": "******-0123",
  "lead_source": "website",
  "notes": "Interested in AI automation",
  "customer_id": "tyler_layne"
}
```

### Create Deal
```http
POST /api/crm/deal
```

**Request Body:**
```json
{
  "deal_name": "Prospect Corp - AI Implementation",
  "contact_email": "<EMAIL>",
  "amount": 50000,
  "stage": "qualification",
  "close_date": "2025-03-15",
  "customer_id": "tyler_layne"
}
```

### Update Deal Stage
```http
PUT /api/crm/deal/{deal_id}/stage
```

**Request Body:**
```json
{
  "new_stage": "proposal",
  "notes": "Sent detailed proposal with pricing",
  "customer_id": "tyler_layne"
}
```

## Email Automation

### Create Email Sequence
```http
POST /api/email/sequence
```

**Request Body:**
```json
{
  "recipient_email": "<EMAIL>",
  "sequence_type": "welcome",
  "lead_info": {
    "name": "Lead Name",
    "company": "Lead Company",
    "topic": "AI automation"
  },
  "delay_days": [1, 3, 7],
  "customer_id": "tyler_layne"
}
```

### Schedule Follow-up
```http
POST /api/email/followup
```

**Request Body:**
```json
{
  "original_thread_id": "thread_123",
  "follow_up_type": "gentle_reminder",
  "days_delay": 3,
  "custom_message": "Just wanted to check in...",
  "customer_id": "tyler_layne"
}
```

## AI Intelligence & Analytics

### Analyze Conversation Intelligence
```http
POST /api/ai/conversation/analyze
```

**Request Body:**
```json
{
  "conversation_id": "conv_123",
  "customer_id": "tyler_layne",
  "include_predictions": true
}
```

**Response:**
```json
{
  "conversation_id": "conv_123",
  "context": {
    "topic_categories": ["business_strategy", "technical_discussion"],
    "sentiment_score": 0.7,
    "urgency_level": "high",
    "key_entities": [
      {"type": "email", "value": "<EMAIL>", "confidence": 0.9}
    ],
    "action_items": ["Schedule follow-up meeting", "Send proposal"],
    "follow_up_required": true
  },
  "predictions": {
    "response_time": "2.3 hours",
    "confidence": 0.85
  }
}
```

### Smart Query with RAG
```http
POST /api/ai/query/smart
```

**Request Body:**
```json
{
  "query": "What was discussed in our last meeting about the AI project?",
  "customer_id": "tyler_layne",
  "conversation_id": "conv_123",
  "include_predictions": false
}
```

### Generate Business Intelligence
```http
POST /api/ai/intelligence/generate
```

**Request Body:**
```json
{
  "customer_id": "tyler_layne",
  "analysis_type": "forecast",
  "time_period": "30_days"
}
```

### Predict Deal Outcome
```http
POST /api/ai/predict/deal
```

**Request Body:**
```json
{
  "deal_id": "deal_123",
  "customer_id": "tyler_layne"
}
```

**Response:**
```json
{
  "deal_id": "deal_123",
  "closure_probability": "73%",
  "confidence": 0.82,
  "contributing_factors": [
    {"factor": "deal_amount", "importance": 0.35, "impact": "high"},
    {"factor": "stage", "importance": 0.28, "impact": "medium"}
  ],
  "recommendations": [
    "High probability deal - focus on closing activities",
    "Prepare contract and next steps"
  ]
}
```

## Analytics & Monitoring

### Get Customer Analytics
```http
GET /api/analytics/customer/{customer_id}
```

**Response:**
```json
{
  "customer_id": "tyler_layne",
  "total_conversations": 45,
  "active_conversations": 8,
  "vector_count": 1247,
  "last_activity": "2025-01-27T20:30:00Z",
  "usage_stats": {
    "api_calls_today": 156,
    "emails_processed": 23,
    "meetings_scheduled": 5,
    "leads_created": 3
  }
}
```

### Get Pipeline Analysis
```http
GET /api/crm/pipeline/analysis?customer_id=tyler_layne&time_period=current_quarter
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Missing required field: customer_id",
    "details": {
      "field": "customer_id",
      "expected": "string"
    }
  },
  "timestamp": "2025-01-27T20:30:00Z",
  "request_id": "req_abc123"
}
```

### Common Error Codes

- `AUTHENTICATION_REQUIRED` - Missing or invalid API key
- `CUSTOMER_NOT_FOUND` - Invalid customer ID
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INVALID_REQUEST` - Malformed request body
- `SERVICE_UNAVAILABLE` - External service temporarily unavailable
- `PERMISSION_DENIED` - Insufficient permissions for operation

## Rate Limits

- **Standard Tier**: 100 requests/minute
- **Professional Tier**: 500 requests/minute  
- **Enterprise Tier**: 1000 requests/minute

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1643723400
```

## Webhooks

Configure webhooks to receive real-time notifications:

### Email Events
```json
{
  "event": "email.received",
  "customer_id": "tyler_layne",
  "data": {
    "thread_id": "thread_123",
    "from": "<EMAIL>",
    "subject": "Project Update",
    "timestamp": "2025-01-27T20:30:00Z"
  }
}
```

### Calendar Events
```json
{
  "event": "meeting.scheduled",
  "customer_id": "tyler_layne", 
  "data": {
    "meeting_id": "cal_event_123",
    "title": "Strategy Meeting",
    "start_time": "2025-01-28T14:00:00Z",
    "attendees": ["<EMAIL>"]
  }
}
```

## SDKs and Libraries

### JavaScript/Node.js
```bash
npm install @tkcgroup/executive-agent-sdk
```

```javascript
import { ExecutiveAgent } from '@tkcgroup/executive-agent-sdk';

const agent = new ExecutiveAgent({
  apiKey: 'your_api_key',
  customerId: 'tyler_layne',
  baseUrl: 'https://tkc-v5-executive-agent-1072222703018.us-central1.run.app'
});

// Schedule a meeting
const meeting = await agent.calendar.scheduleMeeting({
  title: 'Client Call',
  startTime: '2025-01-28T14:00:00',
  endTime: '2025-01-28T15:00:00',
  attendees: ['<EMAIL>']
});
```

### Python
```bash
pip install tkc-executive-agent
```

```python
from tkc_executive_agent import ExecutiveAgent

agent = ExecutiveAgent(
    api_key='your_api_key',
    customer_id='tyler_layne',
    base_url='https://tkc-v5-executive-agent-1072222703018.us-central1.run.app'
)

# Create a lead
lead = agent.crm.create_lead(
    email='<EMAIL>',
    name='John Prospect',
    company='Prospect Corp'
)
```

## Support

- **Documentation**: https://docs.tkcgroup.co/executive-agent
- **API Status**: https://status.tkcgroup.co
- **Support Email**: <EMAIL>
- **GitHub Issues**: https://github.com/TKCGroup/tkc_v5/issues

# Frontend Enablement Package - TKC_v5 Agent Platform

**Prepared for**: Frontend Development Team  
**Date**: 2025-07-26  
**Backend Architecture**: TKC_v5 Enhanced Production + Commercial Agents  
**Status**: Ready for Frontend Implementation

---

## Executive Summary

Thank you for the positive feedback on our backend architecture! I'm excited to provide this comprehensive Frontend Enablement Package that will bridge our robust backend services with an exceptional user experience. 

Our backend is production-ready with:
- ✅ **Enhanced Production Agent** with vector database and RAG
- ✅ **Sales Development Agent** with CRM integration
- ✅ **Marketing Content Agent** with content creation capabilities
- ✅ **Multi-tenant Architecture** for commercial deployment
- ✅ **Comprehensive API Layer** ready for frontend integration

This package provides everything needed for efficient frontend development while leveraging our advanced AI agent capabilities.

---

# Document 1: Frontend Architecture & Project Strategy

## Recommended Project Structure

### **Monorepo Architecture (Recommended)**
```
tkc-agent-platform/
├── apps/
│   ├── marketing/          # Marketing site (/agents, /agents/marketplace)
│   └── sandbox/            # Application (/sandbox, /agents/builder)
├── packages/
│   ├── ui/                 # Shared component library
│   ├── types/              # TypeScript definitions
│   ├── api-client/         # Backend API client
│   └── utils/              # Shared utilities
├── package.json            # Root package.json with workspaces
└── turbo.json             # Turborepo configuration
```

**Technology Stack Recommendation**:
- **Monorepo**: Turborepo with pnpm workspaces
- **Marketing Site**: Next.js 14 with static export (SEO-optimized)
- **Application**: Next.js 14 with App Router (dynamic SPA experience)
- **UI Library**: Tailwind CSS + Radix UI components
- **State Management**: Zustand for application state
- **API Client**: TanStack Query for server state management

### **Project Separation Strategy**

#### **Marketing Site (`apps/marketing`)**
**Routes**: `/agents`, `/agents/marketplace`
**Purpose**: Fast, SEO-friendly static site for discovery and conversion

**Key Features**:
- Static generation for optimal SEO and performance
- Agent template showcase and marketplace
- Pricing, documentation, and onboarding flows
- Lead capture and conversion optimization
- Blog and educational content

**Build Output**: Static files deployable to CDN

#### **Application (`apps/sandbox`)**
**Routes**: `/sandbox`, `/agents/builder` (merged experience)
**Purpose**: Dynamic, interactive agent building and chat environment

**Key Features**:
- Real-time chat interface with streaming responses
- Agent configuration and management
- Tool integration and authentication flows
- Conversation history and management
- Advanced debugging and inspection tools

**Build Output**: Dynamic Next.js application

## Routing Philosophy

### **Unified User Experience Design**

```
/agents                    → Marketing: Agent discovery and templates
/agents/marketplace        → Marketing: Browse pre-built agents
/sandbox                   → Application: Unified agent workspace
  ├── /chat               → Chat interface with agent selection
  ├── /agents             → Agent management dashboard
  ├── /agents/new         → Agent builder (merged with /agents/builder)
  ├── /agents/{id}/edit   → Agent configuration
  ├── /conversations      → Conversation history
  └── /settings           → User settings and integrations
```

### **Merged /sandbox and /agents/builder Experience**

**Rationale**: Instead of separate routes, we recommend a unified workspace where:
- Users can seamlessly switch between chatting and configuring agents
- Agent building is contextual and iterative
- The interface adapts based on user intent (chat vs. configure)

**Implementation**: Single-page application with contextual panels and modal overlays for configuration.

---

# Document 2: The Official Agent API Contract

## Authentication Flow

### **JWT-Based Authentication**
```typescript
// Authentication endpoint
POST /auth/login
Content-Type: application/json

Request:
{
  "provider": "google",
  "code": "google_oauth_code",
  "redirect_uri": "https://app.tkcgroup.co/auth/callback"
}

Response:
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "refresh_token_here",
  "expires_in": 3600,
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}
```

**All subsequent requests must include**:
```
Authorization: Bearer <access_token>
```

## Core API Endpoints

### **Chat & Conversations**

#### **POST /api/chat**
**Purpose**: Send message to agent with streaming response
```typescript
Request:
{
  "agent_id": "agent_123",
  "conversation_id": "conv_456", // Optional, creates new if omitted
  "message": "Help me qualify this lead: John Smith, CEO at TechCorp"
}

Response (Server-Sent Events):
data: {"type": "message_start", "conversation_id": "conv_456"}
data: {"type": "content_delta", "delta": "I'll help you qualify"}
data: {"type": "tool_use", "tool": "qualify_lead", "status": "running"}
data: {"type": "tool_result", "tool": "qualify_lead", "result": "..."}
data: {"type": "content_delta", "delta": " this lead for you."}
data: {"type": "message_end"}
```

#### **GET /api/conversations**
```typescript
Response:
{
  "conversations": [
    {
      "id": "conv_456",
      "title": "Lead Qualification Discussion",
      "agent_id": "agent_123",
      "agent_name": "Sales Development Agent",
      "last_message_at": "2025-07-26T10:30:00Z",
      "message_count": 12
    }
  ]
}
```

#### **GET /api/conversations/{id}**
```typescript
Response:
{
  "id": "conv_456",
  "agent_id": "agent_123",
  "messages": [
    {
      "id": "msg_789",
      "role": "user",
      "content": "Help me qualify this lead",
      "timestamp": "2025-07-26T10:30:00Z"
    },
    {
      "id": "msg_790",
      "role": "assistant",
      "content": "I'll help you qualify this lead...",
      "tool_calls": [
        {
          "id": "call_123",
          "tool": "qualify_lead",
          "arguments": {...},
          "result": {...}
        }
      ],
      "timestamp": "2025-07-26T10:30:15Z"
    }
  ]
}
```

### **Agent Management**

#### **GET /api/agents**
```typescript
Response:
{
  "agents": [
    {
      "id": "agent_123",
      "name": "Sales Development Agent",
      "description": "Specializes in lead qualification and outreach",
      "type": "sales_development",
      "status": "active",
      "tools_enabled": ["qualify_lead", "create_outreach_sequence"],
      "created_at": "2025-07-26T09:00:00Z",
      "last_used_at": "2025-07-26T10:30:00Z"
    }
  ]
}
```

#### **POST /api/agents**
```typescript
Request:
{
  "name": "My Custom Sales Agent",
  "description": "Custom agent for my sales process",
  "type": "sales_development",
  "system_prompt": "You are a sales expert specializing in...",
  "tools_enabled": ["qualify_lead", "create_outreach_sequence"],
  "configuration": {
    "crm_integration": "hubspot",
    "qualification_criteria": {...}
  }
}

Response:
{
  "id": "agent_124",
  "name": "My Custom Sales Agent",
  "status": "active",
  "created_at": "2025-07-26T11:00:00Z"
}
```

#### **GET /api/agents/{id}**
```typescript
Response:
{
  "id": "agent_123",
  "name": "Sales Development Agent",
  "description": "Specializes in lead qualification and outreach",
  "type": "sales_development",
  "system_prompt": "You are a professional Sales Development Representative...",
  "tools_enabled": ["qualify_lead", "create_outreach_sequence"],
  "configuration": {
    "crm_integration": "hubspot",
    "qualification_criteria": {...}
  },
  "statistics": {
    "total_conversations": 45,
    "total_messages": 234,
    "avg_response_time": 2.3
  }
}
```

#### **PUT /api/agents/{id}**
```typescript
Request:
{
  "name": "Updated Agent Name",
  "system_prompt": "Updated system prompt...",
  "tools_enabled": ["qualify_lead", "create_outreach_sequence", "update_deal_stage"]
}

Response:
{
  "id": "agent_123",
  "updated_at": "2025-07-26T11:15:00Z"
}
```

### **Tools & Integrations**

#### **GET /api/tools**
```typescript
Response:
{
  "tools": [
    {
      "id": "qualify_lead",
      "name": "Lead Qualification",
      "description": "Analyze and score leads based on multiple criteria",
      "category": "sales",
      "requires_auth": false,
      "parameters": [
        {
          "name": "contact_info",
          "type": "object",
          "required": true,
          "description": "Contact information for qualification"
        }
      ]
    },
    {
      "id": "gmail_integration",
      "name": "Gmail Integration",
      "description": "Send and receive emails through Gmail",
      "category": "communication",
      "requires_auth": true,
      "auth_provider": "google",
      "scopes": ["https://www.googleapis.com/auth/gmail.modify"]
    }
  ]
}
```

## Error Handling

### **Standard HTTP Status Codes**
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `429`: Rate Limited
- `500`: Internal Server Error

### **Error Response Schema**
```typescript
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid agent configuration",
    "details": {
      "field": "system_prompt",
      "issue": "System prompt cannot be empty"
    }
  }
}
```

---

# Document 3: User Authentication & Data Scoping Flow

## Authentication Flow Diagram

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Frontend  │    │   Backend   │    │   Google    │    │  Database   │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       │ 1. Click "Login"  │                   │                   │
       ├──────────────────►│                   │                   │
       │                   │ 2. Redirect to    │                   │
       │                   │    Google OAuth   │                   │
       │                   ├──────────────────►│                   │
       │                   │                   │ 3. User approves  │
       │ 4. Redirect with  │                   │    and returns    │
       │    auth code      │                   │    with code      │
       │◄──────────────────┼───────────────────┤                   │
       │                   │                   │                   │
       │ 5. Send code to   │                   │                   │
       │    backend        │                   │                   │
       ├──────────────────►│                   │                   │
       │                   │ 6. Validate code  │                   │
       │                   │    with Google    │                   │
       │                   ├──────────────────►│                   │
       │                   │ 7. User info      │                   │
       │                   │◄──────────────────┤                   │
       │                   │ 8. Create/update  │                   │
       │                   │    user record    │                   │
       │                   ├──────────────────────────────────────►│
       │                   │ 9. Generate JWT   │                   │
       │ 10. Return JWT    │    with user_id   │                   │
       │    and user info  │                   │                   │
       │◄──────────────────┤                   │                   │
       │                   │                   │                   │
```

## Data Scoping Guarantee

### **Automatic User Scoping**
Every API request is automatically scoped to the authenticated user through JWT validation:

1. **JWT Validation**: Every request validates the JWT and extracts `user_id`
2. **Database Queries**: All database queries automatically include `WHERE user_id = {authenticated_user_id}`
3. **Multi-tenant Isolation**: Each user gets isolated vector database namespace and Redis keys
4. **Zero Cross-User Access**: Impossible for users to access each other's data

### **Security Implementation**
```typescript
// Example of automatic user scoping in backend
async function getAgents(request) {
  const user_id = validateJWT(request.headers.authorization);
  
  // All queries automatically scoped to user
  return database.query(
    'SELECT * FROM agents WHERE user_id = ?', 
    [user_id]
  );
}
```

### **Data Isolation Layers**
1. **Application Layer**: JWT-based user identification
2. **Database Layer**: User-scoped queries with foreign key constraints
3. **Vector Database**: Customer-specific namespaces (e.g., `user_123_*`)
4. **Redis Cache**: User-prefixed keys (e.g., `user:123:conversation:*`)

**Guarantee**: Users can only ever access their own agents, conversations, and data. Cross-user data access is architecturally impossible.

---

# Document 4: UI/UX Component Guide & Wireframes

## Chat Interface Components

### **Main Chat Container**
```
┌─────────────────────────────────────────────────────────────┐
│ [Agent Selector ▼] [Sales Development Agent]    [⚙️ Config] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  💬 User: Help me qualify this lead: John Smith, CEO       │
│                                                             │
│  🤖 Agent: I'll help you qualify this lead. Let me         │
│     analyze the information...                              │
│                                                             │
│     🔧 Using tool: qualify_lead                            │
│     ⏳ Analyzing contact information...                     │
│                                                             │
│     ✅ Lead Qualification Analysis:                         │
│     Contact: John Smith                                     │
│     Company: TechCorp Inc                                   │
│     Score: 8.5/10 🔥 HOT LEAD                             │
│                                                             │
│  💬 User: Create an outreach sequence                      │
│                                                             │
│  🤖 Agent: [Streaming response...]                         │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ [Type your message...                    ] [Send] [🎤] [📎] │
└─────────────────────────────────────────────────────────────┘
```

### **Streaming Response Handling**
- **Typing Indicator**: Show "Agent is thinking..." with animated dots
- **Tool Status**: Display "🔧 Using tool: qualify_lead" with progress indicator
- **Incremental Text**: Stream text character by character for natural feel
- **Markdown Rendering**: Support for **bold**, *italic*, `code`, and lists
- **Code Blocks**: Syntax highlighting for code snippets

### **Message Status Indicators**
- ⏳ **Processing**: "Agent is analyzing..."
- 🔧 **Tool Use**: "Using tool: create_outreach_sequence"
- ✅ **Complete**: Message fully rendered
- ❌ **Error**: "Something went wrong. Please try again."

## Agent Builder/Configuration Panel

### **Agent Configuration Modal**
```
┌─────────────────────────────────────────────────────────────┐
│ ✕ Configure Agent                                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Agent Name: [Sales Development Agent            ]           │
│                                                             │
│ Description:                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Specializes in lead qualification, outreach, and       │ │
│ │ pipeline management with CRM integration.              │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ System Prompt:                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ You are a professional Sales Development Representative │ │
│ │ for TKC Group. Your primary role is to help with...    │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Available Tools:                                            │
│ ☑️ Lead Qualification        ☑️ Outreach Sequences         │
│ ☑️ Pipeline Management       ☐ Email Integration          │
│ ☐ Calendar Scheduling        ☐ Analytics Tracking         │
│                                                             │
│                                    [Cancel] [Save Changes] │
└─────────────────────────────────────────────────────────────┘
```

### **Tool Configuration Flow**
When user enables a tool requiring authentication:

1. **Tool Selection**: User checks "Gmail Integration"
2. **Auth Modal**: Modal appears with "Connect to Gmail" button
3. **OAuth Flow**: Button opens Google OAuth in popup
4. **Success Callback**: Popup closes, tool shows "✅ Connected"
5. **Configuration**: Additional tool-specific settings appear

## Inspector/Run Log View

### **Agent Thought Process Panel**
```
┌─────────────────────────────────────────────────────────────┐
│ 🔍 Agent Inspector                                    [📋] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 💭 Thought: I need to qualify this lead based on the       │
│    provided information about John Smith.                  │
│                                                             │
│ 🔧 Action: qualify_lead                                     │
│    Input: {                                                 │
│      "contact_info": {                                      │
│        "name": "John Smith",                               │
│        "title": "CEO",                                     │
│        "company": "TechCorp Inc"                           │
│      }                                                      │
│    }                                                        │
│                                                             │
│ 📊 Observation: Lead qualification completed.              │
│    Score: 8.5/10 (HOT LEAD)                               │
│    Reasoning: CEO title, enterprise company, high potential │
│                                                             │
│ 💭 Thought: Based on the high qualification score, I       │
│    should provide detailed analysis and next steps.        │
│                                                             │
│ ✅ Final Response: Generated comprehensive lead analysis    │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**Key Features**:
- **Real-time Updates**: Shows agent's reasoning as it happens
- **Expandable Sections**: Click to expand tool inputs/outputs
- **Copy Functionality**: Copy individual steps or entire log
- **Export Options**: Download log for debugging or sharing

## Agent Management Dashboard

### **Agent Grid View**
```
┌─────────────────────────────────────────────────────────────┐
│ My Agents                                    [+ New Agent] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │ 📊 Sales Dev    │ │ 📝 Marketing    │ │ 📧 Email        │ │
│ │ Agent           │ │ Content Agent   │ │ Assistant       │ │
│ │                 │ │                 │ │                 │ │
│ │ 45 conversations│ │ 23 conversations│ │ 12 conversations│ │
│ │ Last used: 2h   │ │ Last used: 1d   │ │ Last used: 3d   │ │
│ │                 │ │                 │ │                 │ │
│ │ [Chat] [Edit]   │ │ [Chat] [Edit]   │ │ [Chat] [Edit]   │ │
│ │        [Delete] │ │        [Delete] │ │        [Delete] │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **Agent Actions**
- **Chat**: Opens agent in chat interface
- **Edit**: Opens configuration modal
- **Delete**: Confirmation dialog with warning about conversation history
- **Duplicate**: Create copy of agent with same configuration

## Responsive Design Considerations

### **Mobile Layout**
- **Collapsible Sidebar**: Agent list and inspector panel collapse on mobile
- **Bottom Sheet**: Configuration panels slide up from bottom
- **Touch Optimized**: Larger touch targets for mobile interaction
- **Swipe Gestures**: Swipe to switch between agents or conversations

### **Desktop Layout**
- **Three-Panel Layout**: Agent list | Chat | Inspector
- **Resizable Panels**: Users can adjust panel widths
- **Keyboard Shortcuts**: Quick actions and navigation
- **Multi-tab Support**: Open multiple conversations simultaneously

---

## Implementation Priority

### **Phase 1: Core Chat Experience**
1. Authentication flow and user management
2. Basic chat interface with streaming responses
3. Agent selection and switching
4. Conversation history

### **Phase 2: Agent Management**
1. Agent configuration and creation
2. Tool selection and authentication
3. Agent dashboard and management

### **Phase 3: Advanced Features**
1. Inspector/debugging panel
2. Advanced tool configurations
3. Analytics and usage tracking
4. Export and sharing capabilities

This Frontend Enablement Package provides everything needed to build an exceptional user experience on top of our robust backend architecture. The API contracts are production-ready, and the UI components are designed to showcase the full power of our AI agent platform.

Ready to build the future of AI agent interaction! 🚀

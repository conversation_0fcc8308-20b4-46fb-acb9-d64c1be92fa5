# TKC_v5 Production Secrets Setup Guide

**Project**: vertex-ai-agent-yzdlnjey  
**Date**: 2025-07-27  
**Status**: Ready for secret configuration

## 🔐 **Required Secrets for Production Deployment**

Based on our service connection testing, these are the secrets that need actual values:

### **✅ Already Configured (Working)**
- `gmail-config` - Gmail API configuration ✅
- `gmail-service-account-key` - Gmail service account JSON ✅
- `redis-config` - Redis Memorystore configuration ✅
- `agent-config` - Agent configuration ✅
- `env-variables` - Environment variables ✅

### **🔧 Need Configuration**
- `pinecone-config` - Pinecone API key and configuration
- `calendar-credentials` - Google Calendar API credentials
- `hubspot-api-key` - HubSpot CRM integration
- `jwt-secret-key` - JWT token signing key
- `encryption-key` - Data encryption key

## 📋 **Step-by-Step Secret Configuration**

### **1. Pinecone Configuration**
```bash
# Create pinecone-config.json file with your Pinecone API key
cat > pinecone-config.json << EOF
{
  "api_key": "YOUR_PINECONE_API_KEY",
  "environment": "us-east-1-aws",
  "index_name": "tkc-conversations",
  "dimension": 384,
  "metric": "cosine"
}
EOF

# Add to Secret Manager
gcloud secrets versions add pinecone-config --data-file=pinecone-config.json --project=vertex-ai-agent-yzdlnjey

# Clean up local file
rm pinecone-config.json
```

### **2. Calendar Credentials**
```bash
# Create calendar-credentials.json with Google Calendar API credentials
cat > calendar-credentials.json << EOF
{
  "type": "service_account",
  "project_id": "vertex-ai-agent-yzdlnjey",
  "private_key_id": "YOUR_PRIVATE_KEY_ID",
  "private_key": "YOUR_PRIVATE_KEY",
  "client_email": "YOUR_SERVICE_ACCOUNT_EMAIL",
  "client_id": "YOUR_CLIENT_ID",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
}
EOF

# Add to Secret Manager
gcloud secrets versions add calendar-credentials --data-file=calendar-credentials.json --project=vertex-ai-agent-yzdlnjey

# Clean up local file
rm calendar-credentials.json
```

### **3. HubSpot API Key**
```bash
# Add HubSpot API key
echo "YOUR_HUBSPOT_API_KEY" | gcloud secrets versions add hubspot-api-key --data-file=- --project=vertex-ai-agent-yzdlnjey
```

### **4. JWT Secret Key**
```bash
# Generate and add JWT secret key
python3 -c "import secrets; print(secrets.token_urlsafe(32))" | gcloud secrets versions add jwt-secret-key --data-file=- --project=vertex-ai-agent-yzdlnjey
```

### **5. Encryption Key**
```bash
# Generate and add encryption key
python3 -c "import secrets; print(secrets.token_urlsafe(32))" | gcloud secrets versions add encryption-key --data-file=- --project=vertex-ai-agent-yzdlnjey
```

## 🔍 **Verify Secret Configuration**

After adding secrets, verify they're properly configured:

```bash
# List all secrets
gcloud secrets list --project=vertex-ai-agent-yzdlnjey

# Test secret access (example with pinecone-config)
gcloud secrets versions access latest --secret="pinecone-config" --project=vertex-ai-agent-yzdlnjey
```

## 📝 **Secret Values You Need to Provide**

### **Pinecone API Key**
- **Where to get**: Pinecone Console → API Keys
- **Format**: String (e.g., "********-1234-1234-1234-********9abc")

### **Calendar Service Account**
- **Where to get**: Google Cloud Console → IAM & Admin → Service Accounts
- **Format**: JSON service account key file
- **Scopes needed**: Calendar API access

### **HubSpot API Key**
- **Where to get**: HubSpot → Settings → Integrations → API Key
- **Format**: String (e.g., "pat-na1-********-1234-1234-1234-********9abc")

## 🚀 **Quick Setup Commands**

If you have the values ready, here are the quick commands:

```bash
# Set project
gcloud config set project vertex-ai-agent-yzdlnjey

# Pinecone (replace YOUR_API_KEY)
echo '{"api_key":"YOUR_PINECONE_API_KEY","environment":"us-east-1-aws","index_name":"tkc-conversations","dimension":384,"metric":"cosine"}' | gcloud secrets versions add pinecone-config --data-file=-

# HubSpot (replace YOUR_API_KEY)
echo "YOUR_HUBSPOT_API_KEY" | gcloud secrets versions add hubspot-api-key --data-file=-

# Generate JWT and encryption keys automatically
python3 -c "import secrets; print(secrets.token_urlsafe(32))" | gcloud secrets versions add jwt-secret-key --data-file=-
python3 -c "import secrets; print(secrets.token_urlsafe(32))" | gcloud secrets versions add encryption-key --data-file=-
```

## ✅ **Verification Commands**

```bash
# Check all secrets exist
gcloud secrets list --filter="name:pinecone-config OR name:hubspot-api-key OR name:jwt-secret-key OR name:encryption-key" --project=vertex-ai-agent-yzdlnjey

# Test secret access
gcloud secrets versions access latest --secret="pinecone-config" --project=vertex-ai-agent-yzdlnjey | jq .
```

## 🔒 **Security Notes**

1. **Never commit secrets to git**
2. **Use temporary files that are immediately deleted**
3. **Verify secret access permissions for service accounts**
4. **Rotate secrets regularly in production**
5. **Monitor secret access in Cloud Logging**

## 📞 **Ready for Your Input**

I'm ready to help you add these secrets. You can either:

1. **Provide the secret values** and I'll give you the exact commands
2. **Run the commands above** with your actual values
3. **Ask me to generate** the JWT and encryption keys for you

**Next Step**: Provide your Pinecone API key and HubSpot API key, and I'll give you the exact commands to run.

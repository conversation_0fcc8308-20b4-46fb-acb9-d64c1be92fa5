# Inbound Lead Processing System - TKC_v5

## Overview

The Inbound Lead Processing System is a comprehensive, event-driven architecture that automatically processes form submissions from your frontend through the TKC_v5 Executive Agent infrastructure. It provides intelligent lead enrichment, CRM integration, and automated follow-up workflows.

## System Architecture

```mermaid
graph TD
    A[Frontend Forms] --> B[Form Webhook Handler]
    B --> C[Lead Processor]
    C --> D[Lead Enrichment Service]
    C --> E[CRM Integration]
    C --> F[Executive Agent Tools]
    C --> G[Pub/Sub Notifications]
    
    D --> H[External APIs]
    E --> I[HubSpot CRM]
    F --> J[Email Automation]
    F --> K[Calendar Scheduling]
    G --> L[Executive Agent]
    G --> M[Sales Dev Agent]
    
    N[Redis Storage] --> C
    O[Monitoring Service] --> C
```

## Key Components

### 1. Form Webhook Handler (`/webhook/form`)
- **Location**: `src/webhooks/form_handler.py`
- **Purpose**: Receives and processes form submissions from frontend
- **Features**:
  - Form data validation and normalization
  - Lead scoring and prioritization
  - Comprehensive error handling
  - Real-time processing status

### 2. Inbound Lead Processor
- **Class**: `InboundLeadProcessor`
- **Purpose**: Orchestrates the complete lead processing workflow
- **Workflow Steps**:
  1. Data validation and normalization
  2. Existing contact lookup
  3. Lead data enrichment
  4. CRM record creation/update
  5. Automated follow-up scheduling
  6. Agent notifications

### 3. Lead Enrichment Service
- **Location**: `src/services/lead_enrichment_service.py`
- **Purpose**: Enhances lead data using multiple sources
- **Features**:
  - Company information lookup
  - Contact verification
  - Technology stack detection
  - AI-powered analysis
  - Intent signal analysis

### 4. Notification Service
- **Location**: `src/services/lead_notification_service.py`
- **Purpose**: Manages real-time notifications via Pub/Sub
- **Features**:
  - Event publishing to multiple topics
  - Agent notification queues
  - Real-time event streaming
  - Scalable message processing

## API Endpoints

### Form Submission Webhook
```http
POST /webhook/form
Content-Type: application/json

{
  "form_type": "demo_request",
  "email": "<EMAIL>",
  "name": "John Prospect",
  "company": "Prospect Corp",
  "phone": "******-0123",
  "message": "Interested in a demo of your platform",
  "source": "website",
  "utm_campaign": "q4-demo-campaign",
  "utm_source": "google",
  "utm_medium": "cpc",
  "page_url": "https://tkcgroup.co/demo",
  "metadata": {}
}
```

**Response:**
```json
{
  "status": "success",
  "lead_id": "lead_prospect@company.com_1703123456",
  "crm_contact_id": "12345",
  "crm_deal_id": "67890",
  "follow_up_scheduled": true,
  "message": "Form submission processed successfully"
}
```

### Lead Notification Webhook
```http
POST /webhook/lead-notification
Content-Type: application/json

{
  "source": "external_platform",
  "lead_data": {...},
  "notification_type": "new_lead"
}
```

## Form Types and Processing

### 1. Demo Request (`demo_request`)
- **Priority**: High
- **Lead Score Bonus**: +50 points
- **Follow-up**: Immediate (1 hour)
- **CRM Action**: Creates deal in "qualification" stage
- **Agent Notification**: Both Executive and Sales Dev agents

### 2. Consultation Request (`consultation`)
- **Priority**: High
- **Lead Score Bonus**: +45 points
- **Follow-up**: Quick response (2 hours)
- **CRM Action**: Creates deal with consultation flag
- **Agent Notification**: Executive agent priority

### 3. Contact Form (`contact`)
- **Priority**: Medium
- **Lead Score Bonus**: +30 points
- **Follow-up**: Standard (24 hours)
- **CRM Action**: Creates contact record
- **Agent Notification**: Sales Dev agent

### 4. Lead Capture (`lead_capture`)
- **Priority**: Low-Medium
- **Lead Score Bonus**: +25 points
- **Follow-up**: Nurture sequence (48 hours)
- **CRM Action**: Creates contact with lead status
- **Agent Notification**: Automated nurture sequence

## Lead Scoring Algorithm

### Base Scoring Factors
- **Form Type**: 20-50 points
- **Company Provided**: +20 points
- **Phone Number**: +15 points
- **Message Length**: +10-15 points
- **UTM Campaign**: +10 points
- **Existing Contact**: +25 points

### Enrichment Scoring
- **ICP Fit Score**: 0-100 points (weighted 30%)
- **Intent Signals**: +5 points per signal
- **Company Size**: Bonus for target range
- **Technology Stack**: Bonus for relevant tech

### Priority Levels
- **High Priority**: Score ≥ 70 or demo/consultation forms
- **Medium Priority**: Score 50-69
- **Low Priority**: Score < 50

## CRM Integration

### HubSpot Integration
- **Contact Creation**: Automatic for all leads
- **Deal Creation**: High-priority leads only
- **Property Mapping**:
  - Lead source tracking
  - UTM parameter storage
  - Lead score assignment
  - Form type classification
  - Priority level setting

### Data Synchronization
- **Real-time Updates**: Immediate CRM sync
- **Bidirectional Sync**: Updates flow both ways
- **Conflict Resolution**: Timestamp-based priority
- **Error Handling**: Retry logic with exponential backoff

## Automated Follow-up

### Email Sequences
- **Immediate Response**: Welcome and next steps
- **Value-Add Content**: Industry insights and resources
- **Meeting Scheduling**: Calendar link and availability
- **Proposal Follow-up**: Custom proposals and quotes

### Sequence Types
1. **Immediate Response**: Demo requests and consultations
2. **Consultation Booking**: Calendar scheduling workflow
3. **General Inquiry**: Standard response sequence
4. **Nurture Sequence**: Long-term relationship building

## Agent Notifications

### Executive Agent Notifications
- **High-priority leads**: Immediate notification
- **Deal creation events**: Real-time updates
- **Follow-up scheduling**: Confirmation messages
- **CRM sync status**: Success/failure alerts

### Sales Development Agent Notifications
- **All new leads**: Complete lead information
- **Lead scoring updates**: Score changes and reasons
- **Follow-up reminders**: Scheduled action items
- **Pipeline updates**: Deal stage changes

## Monitoring and Analytics

### Key Metrics
- **Form Conversion Rate**: Submissions to qualified leads
- **Processing Time**: End-to-end workflow duration
- **CRM Sync Success**: Integration reliability
- **Follow-up Response Rate**: Email engagement metrics
- **Lead Quality Score**: Average lead scores by source

### Error Tracking
- **Processing Failures**: Failed lead processing attempts
- **CRM Sync Errors**: Integration issues and retries
- **Enrichment Failures**: External API failures
- **Notification Failures**: Pub/Sub delivery issues

## Configuration

### Environment Variables
```bash
# CRM Configuration
HUBSPOT_API_KEY=your_hubspot_api_key
HUBSPOT_PORTAL_ID=your_portal_id

# Enrichment APIs
CLEARBIT_API_KEY=your_clearbit_key
HUNTER_API_KEY=your_hunter_key
BUILTWITH_API_KEY=your_builtwith_key

# Pub/Sub Configuration
GOOGLE_CLOUD_PROJECT=your_project_id
PUBSUB_TOPIC_PREFIX=tkc-v5

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_DB=0
```

### Lead Scoring Configuration
```python
# Customizable scoring weights
FORM_TYPE_SCORES = {
    'demo_request': 50,
    'consultation': 45,
    'contact': 30,
    'lead_capture': 25
}

ENRICHMENT_WEIGHTS = {
    'icp_fit': 0.3,
    'intent_signals': 5,
    'company_size_bonus': 20,
    'technology_bonus': 15
}
```

## Deployment

### Prerequisites
- TKC_v5 Executive Agent deployed
- HubSpot CRM access configured
- Google Cloud Pub/Sub enabled
- Redis instance available

### Deployment Steps
1. Deploy updated Executive Agent with webhook endpoints
2. Configure environment variables
3. Set up Pub/Sub topics and subscriptions
4. Test webhook endpoints
5. Configure frontend form submissions
6. Monitor processing metrics

## Testing

### Unit Tests
```bash
# Run lead processor tests
pytest tests/test_lead_processor.py

# Run enrichment service tests
pytest tests/test_enrichment_service.py

# Run notification service tests
pytest tests/test_notification_service.py
```

### Integration Tests
```bash
# Test complete workflow
curl -X POST https://your-agent-url/webhook/form \
  -H "Content-Type: application/json" \
  -d @test_form_submission.json
```

### Load Testing
```bash
# Test webhook performance
ab -n 1000 -c 10 -T application/json \
  -p test_form_submission.json \
  https://your-agent-url/webhook/form
```

## Troubleshooting

### Common Issues
1. **CRM Sync Failures**: Check API credentials and rate limits
2. **Enrichment Timeouts**: Verify external API availability
3. **Notification Delays**: Check Pub/Sub topic configuration
4. **Lead Scoring Inconsistencies**: Review scoring algorithm weights

### Debug Mode
```python
# Enable debug logging
import logging
logging.getLogger('src.webhooks.form_handler').setLevel(logging.DEBUG)
```

## Future Enhancements

### Planned Features
- **Machine Learning Scoring**: AI-powered lead scoring models
- **Advanced Enrichment**: Social media and news monitoring
- **Multi-CRM Support**: Salesforce, Pipedrive integration
- **Real-time Dashboard**: Live lead processing metrics
- **A/B Testing**: Follow-up sequence optimization

### Integration Roadmap
- **Marketing Automation**: Marketo, Pardot integration
- **Analytics Platforms**: Google Analytics, Mixpanel
- **Communication Tools**: Slack, Microsoft Teams notifications
- **Calendar Systems**: Calendly, Acuity Scheduling integration

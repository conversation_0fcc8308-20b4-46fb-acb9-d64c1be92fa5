openapi: 3.0.3
info:
  title: TKC_v5 Agent Platform API
  description: Official API specification for the TKC_v5 AI Agent Platform
  version: 1.0.0
  contact:
    name: TKC Group
    email: <EMAIL>
servers:
  - url: https://vertex-ai-agent-1072222703018.us-central1.run.app/api
    description: Production server
  - url: http://localhost:8080/api
    description: Development server

security:
  - BearerAuth: []

paths:
  /auth/login:
    post:
      summary: Authenticate user and obtain JWT token
      tags: [Authentication]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [provider, code, redirect_uri]
              properties:
                provider:
                  type: string
                  enum: [google]
                  description: OAuth provider
                code:
                  type: string
                  description: OAuth authorization code
                redirect_uri:
                  type: string
                  description: OAuth redirect URI
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /chat:
    post:
      summary: Send message to agent with streaming response
      tags: [Chat]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
      responses:
        '200':
          description: Streaming chat response
          content:
            text/event-stream:
              schema:
                type: string
                description: Server-Sent Events stream
              examples:
                streaming_response:
                  value: |
                    data: {"type": "message_start", "conversation_id": "conv_456"}
                    data: {"type": "content_delta", "delta": "I'll help you qualify"}
                    data: {"type": "tool_use", "tool": "qualify_lead", "status": "running"}
                    data: {"type": "tool_result", "tool": "qualify_lead", "result": "..."}
                    data: {"type": "content_delta", "delta": " this lead for you."}
                    data: {"type": "message_end"}
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /conversations:
    get:
      summary: List all conversations for authenticated user
      tags: [Conversations]
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
            maximum: 100
        - name: offset
          in: query
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: List of conversations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationList'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /conversations/{id}:
    get:
      summary: Get conversation details and message history
      tags: [Conversations]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Conversation details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /agents:
    get:
      summary: List all agents for authenticated user
      tags: [Agents]
      responses:
        '200':
          description: List of agents
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentList'
        '401':
          $ref: '#/components/responses/Unauthorized'
    
    post:
      summary: Create a new agent
      tags: [Agents]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAgentRequest'
      responses:
        '201':
          description: Agent created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /agents/{id}:
    get:
      summary: Get agent details and configuration
      tags: [Agents]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Agent details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentDetails'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
    
    put:
      summary: Update agent configuration
      tags: [Agents]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAgentRequest'
      responses:
        '200':
          description: Agent updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Agent'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
    
    delete:
      summary: Delete an agent
      tags: [Agents]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Agent deleted successfully
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /tools:
    get:
      summary: Get list of available tools
      tags: [Tools]
      responses:
        '200':
          description: List of available tools
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ToolList'
        '401':
          $ref: '#/components/responses/Unauthorized'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AuthResponse:
      type: object
      required: [access_token, refresh_token, expires_in, user]
      properties:
        access_token:
          type: string
          description: JWT access token
        refresh_token:
          type: string
          description: Refresh token for obtaining new access tokens
        expires_in:
          type: integer
          description: Token expiration time in seconds
        user:
          $ref: '#/components/schemas/User'

    User:
      type: object
      required: [id, email, name]
      properties:
        id:
          type: string
          description: Unique user identifier
        email:
          type: string
          format: email
        name:
          type: string

    ChatRequest:
      type: object
      required: [agent_id, message]
      properties:
        agent_id:
          type: string
          description: ID of the agent to chat with
        conversation_id:
          type: string
          description: Optional conversation ID (creates new if omitted)
        message:
          type: string
          description: User message content

    ConversationList:
      type: object
      required: [conversations]
      properties:
        conversations:
          type: array
          items:
            $ref: '#/components/schemas/ConversationSummary'

    ConversationSummary:
      type: object
      required: [id, title, agent_id, agent_name, last_message_at, message_count]
      properties:
        id:
          type: string
        title:
          type: string
        agent_id:
          type: string
        agent_name:
          type: string
        last_message_at:
          type: string
          format: date-time
        message_count:
          type: integer

    Conversation:
      type: object
      required: [id, agent_id, messages]
      properties:
        id:
          type: string
        agent_id:
          type: string
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'

    Message:
      type: object
      required: [id, role, content, timestamp]
      properties:
        id:
          type: string
        role:
          type: string
          enum: [user, assistant]
        content:
          type: string
        tool_calls:
          type: array
          items:
            $ref: '#/components/schemas/ToolCall'
        timestamp:
          type: string
          format: date-time

    ToolCall:
      type: object
      required: [id, tool, arguments]
      properties:
        id:
          type: string
        tool:
          type: string
        arguments:
          type: object
        result:
          type: object

    AgentList:
      type: object
      required: [agents]
      properties:
        agents:
          type: array
          items:
            $ref: '#/components/schemas/Agent'

    Agent:
      type: object
      required: [id, name, description, type, status, tools_enabled, created_at]
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        type:
          type: string
          enum: [sales_development, marketing_content, custom]
        status:
          type: string
          enum: [active, inactive]
        tools_enabled:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time
        last_used_at:
          type: string
          format: date-time

    AgentDetails:
      allOf:
        - $ref: '#/components/schemas/Agent'
        - type: object
          required: [system_prompt, configuration, statistics]
          properties:
            system_prompt:
              type: string
            configuration:
              type: object
            statistics:
              $ref: '#/components/schemas/AgentStatistics'

    AgentStatistics:
      type: object
      required: [total_conversations, total_messages, avg_response_time]
      properties:
        total_conversations:
          type: integer
        total_messages:
          type: integer
        avg_response_time:
          type: number
          description: Average response time in seconds

    CreateAgentRequest:
      type: object
      required: [name, description, type]
      properties:
        name:
          type: string
        description:
          type: string
        type:
          type: string
          enum: [sales_development, marketing_content, custom]
        system_prompt:
          type: string
        tools_enabled:
          type: array
          items:
            type: string
        configuration:
          type: object

    UpdateAgentRequest:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        system_prompt:
          type: string
        tools_enabled:
          type: array
          items:
            type: string
        configuration:
          type: object

    ToolList:
      type: object
      required: [tools]
      properties:
        tools:
          type: array
          items:
            $ref: '#/components/schemas/Tool'

    Tool:
      type: object
      required: [id, name, description, category, requires_auth]
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        category:
          type: string
          enum: [sales, marketing, communication, analytics]
        requires_auth:
          type: boolean
        auth_provider:
          type: string
          enum: [google, microsoft, hubspot, salesforce]
        scopes:
          type: array
          items:
            type: string
        parameters:
          type: array
          items:
            $ref: '#/components/schemas/ToolParameter'

    ToolParameter:
      type: object
      required: [name, type, required, description]
      properties:
        name:
          type: string
        type:
          type: string
          enum: [string, number, boolean, object, array]
        required:
          type: boolean
        description:
          type: string

    Error:
      type: object
      required: [error]
      properties:
        error:
          type: object
          required: [code, message]
          properties:
            code:
              type: string
            message:
              type: string
            details:
              type: object

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

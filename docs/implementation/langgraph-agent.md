# LangGraph Agent Implementation

This document details the implementation of the production-grade AI agent using LangGraph and Gemini on Vertex AI.

## Architecture Overview

The agent follows the ReAct (Reason+Act) pattern implemented as a state machine using LangGraph. This provides explicit control over the agent's reasoning process and makes it observable and debuggable.

### Core Components

1. **Agent State** - Maintains conversation history and context
2. **LLM Node** - Calls Gemini model for reasoning
3. **Tool Node** - Executes external tools and APIs
4. **Conditional Edges** - Routes between reasoning and action

## Implementation Details

### File Structure

```
src/
├── agent/
│   ├── __init__.py          # Package initialization
│   └── core.py              # Core agent implementation
└── main.py                  # FastAPI web service
```

### Core Agent (`src/agent/core.py`)

The `VertexAIAgent` class implements the complete ReAct pattern:

```python
class VertexAIAgent:
    def __init__(self, project_id: str, location: str = "us-central1", model_name: str = "gemini-pro"):
        # Initialize with Application Default Credentials
        self.llm = ChatVertexAI(
            project=project_id,
            location=location,
            model_name=model_name,
            temperature=0.7,
        )
```

**Key Features:**
- **Application Default Credentials**: Seamless authentication in dev and production
- **Tool Integration**: Extensible tool system with function calling
- **State Management**: Persistent conversation state
- **Error Handling**: Robust error handling and recovery

### Available Tools

1. **Exchange Rate Tool** (`get_exchange_rate`)
   - Fetches real-time currency exchange rates
   - Uses public API for demonstration
   - Handles API errors gracefully

2. **Weather Tool** (`get_weather`)
   - Mock weather information service
   - Template for integrating real weather APIs

### Web Service (`src/main.py`)

FastAPI-based REST API providing:

- **POST /chat** - Main chat endpoint
- **GET /health** - Health check
- **GET /tools** - List available tools
- **POST /test** - Test agent functionality

## Authentication Strategy

Following the architecture document's guidance on Application Default Credentials (ADC):

### Local Development
```bash
gcloud auth application-default login
```

### Production Deployment
- Service account attached to compute resource
- No key files required
- Automatic credential management

## Configuration

### Environment Variables
- `GOOGLE_CLOUD_PROJECT` - GCP project ID
- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8080)

### Model Configuration
Currently configured for:
- **Model**: `Gemini-2.5-Flash` (exact name required)
- **Region**: `us-central1`
- **Temperature**: 0.7

## Current Status

### ✅ Completed
- Core agent implementation with ReAct pattern
- Tool integration framework
- FastAPI web service
- Docker containerization
- Dependency management
- Authentication setup

### 🔄 In Progress
- Vertex AI model access troubleshooting
- Model name and region verification

### ⏳ Pending
- Model access resolution
- Local testing completion
- Deployment to Cloud Run
- Monitoring and observability setup

## Known Issues

### Vertex AI Model Access
**Issue**: Getting 404 errors when accessing Gemini models
```
404 Publisher Model `projects/.../models/gemini-pro` was not found
```

**Potential Causes:**
1. Incorrect model name format
2. Regional availability restrictions
3. API access permissions
4. Model version deprecation

**Investigation Steps:**
1. Verify available model names and versions
2. Check regional model availability
3. Confirm API permissions and quotas
4. Test with different model names

## Testing

### Local Testing
```bash
# Set environment
export GOOGLE_CLOUD_PROJECT=vertex-ai-agent-yzdlnjey
export PYTHONPATH=/path/to/src

# Test core agent
python src/agent/core.py

# Test web service
python src/main.py
```

### API Testing
```bash
# Health check
curl http://localhost:8080/health

# Chat request
curl -X POST http://localhost:8080/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "What is the exchange rate from USD to EUR?"}'
```

## Deployment

### Docker Build
```bash
docker build -t vertex-ai-agent .
```

### Local Container Run
```bash
docker run -p 8080:8080 \
  -e GOOGLE_CLOUD_PROJECT=vertex-ai-agent-yzdlnjey \
  vertex-ai-agent
```

## Next Steps

1. **Resolve Model Access** - Fix Vertex AI model connectivity
2. **Complete Testing** - Verify all functionality works end-to-end
3. **Deploy to Cloud Run** - Production deployment
4. **Add Monitoring** - Implement observability
5. **Enhance Tools** - Add more sophisticated tool integrations

## Security Considerations

- No service account keys in code
- Least-privilege service account permissions
- Secure credential management via ADC
- Input validation and error handling
- Container security best practices

## Performance Considerations

- Async/await patterns for scalability
- Connection pooling for external APIs
- Caching strategies for repeated requests
- Resource limits in container deployment

# Agent Development Best Practices

## Overview
This document captures critical lessons learned from debugging and deploying TKC_v5 agents, particularly focusing on common pitfalls and best practices for robust agent development.

## Critical Syntax Issues

### F-String Backslash Limitations
**Problem**: Python f-strings cannot contain backslashes in the expression portion.

**❌ Incorrect:**
```python
f"Subject: {subject}\nBody: {body}"  # SyntaxError
f"Path: {path}\{filename}"           # SyntaxError
```

**✅ Correct Solutions:**
```python
# Option 1: Use chr() function
f"Subject: {subject}{chr(10)}Body: {body}"

# Option 2: Pre-define the character
newline = "\n"
f"Subject: {subject}{newline}Body: {body}"

# Option 3: Use string concatenation
f"Subject: {subject}" + "\n" + f"Body: {body}"

# Option 4: Use .format() or % formatting for complex cases
"Subject: {}\nBody: {}".format(subject, body)
```

**Detection**: Look for f-strings containing `\n`, `\t`, `\\`, or other escape sequences.

### Common F-String Patterns to Avoid
1. **Newlines in email templates**: `f"Dear {name}\nThank you..."` → Use `chr(10)`
2. **File paths**: `f"{directory}\{filename}"` → Use `os.path.join()` or `/`
3. **Tab formatting**: `f"{label}\t{value}"` → Use `chr(9)` or spaces
4. **Quotes**: `f"He said \"{quote}\""` → Use different quote types or escape outside f-string

## Dependency Management

### ML/AI Package Considerations
**Large packages that significantly increase build time:**
- `sentence-transformers` (includes PyTorch, ~2GB)
- `pandas` + `numpy` + `scikit-learn` (data science stack)
- `transformers` (Hugging Face models)

**Best Practices:**
1. **Pin versions** to avoid compatibility issues
2. **Group related dependencies** in requirements.txt with comments
3. **Consider alternatives** for production (e.g., lighter models)
4. **Use multi-stage Docker builds** for large ML dependencies
5. **Increase Cloud Run memory/CPU** for ML workloads (2Gi+ memory, 2+ CPU)

### Dependency Discovery Process
When encountering import errors:
1. **Trace the import chain**: Follow imports from failing module
2. **Check all imported services**: Look for hidden dependencies
3. **Scan for data science imports**: `pandas`, `numpy`, `sklearn`, `torch`
4. **Check Google Cloud services**: `monitoring_v3`, `logging`, `error_reporting`, `trace_v1`
5. **Look for specialized libraries**: `structlog`, `sentence-transformers`
6. **Add missing packages** with appropriate version constraints

### Common Hidden Dependencies
- **Monitoring Service**: `google-cloud-monitoring`, `google-cloud-logging`, `google-cloud-error-reporting`, `google-cloud-trace`, `structlog`
- **Data Science**: `pandas`, `numpy`, `scikit-learn`, `sentence-transformers`
- **AI/ML**: `torch`, `transformers`, `tensorflow` (if using TensorFlow models)

## Debugging Workflow

### 1. Import Validation Endpoint
Always implement a `/test-imports` endpoint for systematic validation:

```python
@app.get("/test-imports")
async def test_imports():
    modules_to_test = [
        "config.settings",
        "tools.email_automation", 
        "agent.state",
        "agent.core",
        "services.gmail_client",
        "services.pinecone_client",
        "services.firestore_client",
        "business_tools.email_automation"
    ]
    
    results = {}
    for module in modules_to_test:
        try:
            importlib.import_module(module)
            results[module] = "✅ Success"
        except Exception as e:
            results[module] = f"❌ Error: {str(e)}"
    
    return {"import_test_results": results, "status": "completed"}
```

### 2. Error Reporting Enhancement
Include detailed tracebacks in development:

```python
except Exception as e:
    import traceback
    results[module] = f"❌ Error: {str(e)}\nTraceback: {traceback.format_exc()}"
```

### 3. Systematic Debugging Process
1. **Deploy with basic dependencies** first
2. **Test import validation** endpoint
3. **Identify failing modules** and trace dependencies
4. **Add missing dependencies** incrementally
5. **Re-deploy and validate** until all modules pass

## Cloud Run Deployment

### Resource Allocation for AI Agents
```bash
gcloud run deploy [service-name] \
  --memory 2Gi \
  --cpu 2 \
  --timeout 3600 \
  --allow-unauthenticated
```

**Rationale:**
- **2Gi memory**: Required for ML models and sentence transformers
- **2 CPU**: Improves build and runtime performance
- **3600s timeout**: Allows for long-running agent operations
- **Unauthenticated**: Simplifies testing (secure in production)

### Build Time Expectations
- **Basic FastAPI + LangChain**: 2-3 minutes
- **+ sentence-transformers**: 8-12 minutes
- **+ Full ML stack**: 10-15 minutes

## Code Quality Practices

### 1. Import Organization
```python
# Standard library
import logging
import time
from typing import Dict, Any

# Third-party packages
from langchain_core.messages import HumanMessage
from fastapi import FastAPI

# Local imports
from src.config.settings import get_settings
from src.services.gmail_client import create_gmail_client
```

### 2. Error Handling
```python
try:
    result = risky_operation()
except SpecificException as e:
    logger.error(f"Specific error occurred: {e}")
    # Handle gracefully
except Exception as e:
    logger.error(f"Unexpected error: {e}", exc_info=True)
    # Fallback behavior
```

### 3. Configuration Management
- Use environment variables for all external dependencies
- Validate configuration at startup
- Provide clear error messages for missing config

## Testing Strategy

### 1. Local Testing
```bash
# Test imports locally
python -c "import src.agent.core; print('Core imports successful')"

# Test specific modules
python -m pytest tests/test_imports.py -v
```

### 2. Deployment Testing
```bash
# Test deployed service
curl https://[service-url]/test-imports

# Test specific endpoints
curl https://[service-url]/health
```

### 3. Continuous Validation
- Include import tests in CI/CD pipeline
- Monitor deployment health endpoints
- Set up alerts for import failures

## Common Pitfalls Summary

1. **F-string backslashes** → Use `chr()` or pre-defined variables
2. **Missing ML dependencies** → Trace import chains thoroughly
3. **Insufficient resources** → Use 2Gi+ memory for ML workloads
4. **Long build times** → Expect 10+ minutes for ML packages
5. **Import order issues** → Organize imports systematically
6. **Configuration errors** → Validate environment variables early

## Template Updates Required

Based on these lessons, the following template files should be updated:
1. **Agent template**: Include f-string best practices
2. **Requirements template**: Include common ML dependencies
3. **Dockerfile template**: Optimize for ML packages
4. **Testing template**: Include import validation endpoint
5. **Deployment scripts**: Include proper resource allocation

---

*This document should be updated as new patterns and issues are discovered during agent development.*

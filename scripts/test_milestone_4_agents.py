#!/usr/bin/env python3
"""
Test Script for Milestone 4 Agents - TKC_v5 Commercial Package

Tests the Sales Development Agent and Marketing Content Agent
to validate functionality before full deployment.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.sales_development.core import SalesDevAgent
from agents.marketing_content.core import MarketingContentAgent
from agent.state import TaskRequest, TaskType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_sales_development_agent():
    """Test the Sales Development Agent functionality."""
    logger.info("🔄 Testing Sales Development Agent...")
    
    try:
        # Initialize the agent
        sales_agent = SalesDevAgent()
        await sales_agent.initialize_services()
        
        # Test 1: Lead Qualification
        logger.info("📊 Test 1: Lead Qualification")
        qualification_request = TaskRequest(
            task_id="test_qualify_lead_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Qualify this lead: <PERSON>, CEO at TechCorp Inc, 500 employees, interested in automation solutions. Had a 20-minute conversation about their current challenges.",
                "customer_id": "test_customer",
                "metadata": {
                    "test_type": "lead_qualification"
                }
            }
        )
        
        result = await sales_agent.execute_task(qualification_request)
        logger.info(f"✅ Lead Qualification Result: {result.success}")
        if result.success:
            logger.info("📋 Qualification completed successfully")
        else:
            logger.error(f"❌ Qualification failed: {result.error}")
        
        # Test 2: Outreach Sequence Creation
        logger.info("📧 Test 2: Outreach Sequence Creation")
        outreach_request = TaskRequest(
            task_id="test_outreach_seq_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Create an initial outreach sequence for a qualified lead at a mid-size software company. The contact is a VP of Operations interested in process automation.",
                "customer_id": "test_customer",
                "metadata": {
                    "test_type": "outreach_sequence"
                }
            }
        )
        
        result = await sales_agent.execute_task(outreach_request)
        logger.info(f"✅ Outreach Sequence Result: {result.success}")
        if result.success:
            logger.info("📧 Outreach sequence created successfully")
        else:
            logger.error(f"❌ Outreach sequence failed: {result.error}")
        
        # Test 3: Pipeline Metrics
        logger.info("📈 Test 3: Pipeline Metrics Analysis")
        metrics_request = TaskRequest(
            task_id="test_pipeline_metrics_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Analyze our sales pipeline performance for the last 30 days and provide insights on deal progression and win rates.",
                "customer_id": "test_customer",
                "metadata": {
                    "test_type": "pipeline_metrics"
                }
            }
        )
        
        result = await sales_agent.execute_task(metrics_request)
        logger.info(f"✅ Pipeline Metrics Result: {result.success}")
        if result.success:
            logger.info("📊 Pipeline analysis completed successfully")
        else:
            logger.error(f"❌ Pipeline analysis failed: {result.error}")
        
        logger.info("🎉 Sales Development Agent testing completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Sales Development Agent test failed: {e}")
        return False


async def test_marketing_content_agent():
    """Test the Marketing Content Agent functionality."""
    logger.info("🔄 Testing Marketing Content Agent...")
    
    try:
        # Initialize the agent
        marketing_agent = MarketingContentAgent()
        await marketing_agent.initialize_services()
        
        # Test 1: Blog Post Creation
        logger.info("📝 Test 1: Blog Post Creation")
        blog_request = TaskRequest(
            task_id="test_blog_post_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Create a blog post about 'AI Automation for Small Businesses' targeting business owners, 800 words, professional tone, include SEO keywords: automation, AI, efficiency, small business.",
                "customer_id": "test_customer",
                "metadata": {
                    "test_type": "blog_creation"
                }
            }
        )
        
        result = await marketing_agent.execute_task(blog_request)
        logger.info(f"✅ Blog Post Result: {result.success}")
        if result.success:
            logger.info("📝 Blog post created successfully")
        else:
            logger.error(f"❌ Blog post failed: {result.error}")
        
        # Test 2: Social Media Campaign
        logger.info("📱 Test 2: Social Media Campaign Creation")
        social_request = TaskRequest(
            task_id="test_social_campaign_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Create a 7-day social media campaign about 'Digital Transformation' for LinkedIn and Twitter, posting daily with engaging content.",
                "customer_id": "test_customer",
                "metadata": {
                    "test_type": "social_campaign"
                }
            }
        )
        
        result = await marketing_agent.execute_task(social_request)
        logger.info(f"✅ Social Campaign Result: {result.success}")
        if result.success:
            logger.info("📱 Social media campaign created successfully")
        else:
            logger.error(f"❌ Social campaign failed: {result.error}")
        
        # Test 3: Email Campaign
        logger.info("📧 Test 3: Email Campaign Creation")
        email_request = TaskRequest(
            task_id="test_email_campaign_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Create a newsletter email campaign about our latest AI automation features, targeting existing customers with a professional tone.",
                "customer_id": "test_customer",
                "metadata": {
                    "test_type": "email_campaign"
                }
            }
        )
        
        result = await marketing_agent.execute_task(email_request)
        logger.info(f"✅ Email Campaign Result: {result.success}")
        if result.success:
            logger.info("📧 Email campaign created successfully")
        else:
            logger.error(f"❌ Email campaign failed: {result.error}")
        
        # Test 4: Campaign Performance Analysis
        logger.info("📊 Test 4: Campaign Performance Analysis")
        analytics_request = TaskRequest(
            task_id="test_campaign_analytics_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Analyze the performance of our recent marketing campaigns across email, social media, and content marketing. Provide optimization recommendations.",
                "customer_id": "test_customer",
                "metadata": {
                    "test_type": "campaign_analytics"
                }
            }
        )
        
        result = await marketing_agent.execute_task(analytics_request)
        logger.info(f"✅ Campaign Analytics Result: {result.success}")
        if result.success:
            logger.info("📊 Campaign analysis completed successfully")
        else:
            logger.error(f"❌ Campaign analysis failed: {result.error}")
        
        logger.info("🎉 Marketing Content Agent testing completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Marketing Content Agent test failed: {e}")
        return False


async def test_agent_integration():
    """Test integration between agents."""
    logger.info("🔄 Testing Agent Integration...")
    
    try:
        # Test scenario: Lead qualification → Content creation for nurturing
        logger.info("🎯 Integration Test: Lead Nurturing Workflow")
        
        # Step 1: Qualify a lead with Sales Agent
        sales_agent = SalesDevAgent()
        await sales_agent.initialize_services()
        
        qualification_request = TaskRequest(
            task_id="integration_test_001",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Qualify this lead: Sarah Johnson, Marketing Director at GrowthCorp, 200 employees, interested in marketing automation. Engaged in multiple conversations.",
                "customer_id": "integration_test",
                "metadata": {
                    "test_type": "integration_qualification"
                }
            }
        )
        
        qualification_result = await sales_agent.execute_task(qualification_request)
        logger.info(f"✅ Integration Step 1 - Lead Qualification: {qualification_result.success}")
        
        # Step 2: Create nurturing content with Marketing Agent
        marketing_agent = MarketingContentAgent()
        await marketing_agent.initialize_services()
        
        content_request = TaskRequest(
            task_id="integration_test_002",
            task_type=TaskType.EMAIL_ANALYSIS,
            input_data={
                "message": "Create a nurturing email campaign for a qualified marketing director interested in automation. Focus on educational content about marketing automation benefits.",
                "customer_id": "integration_test",
                "metadata": {
                    "test_type": "integration_content",
                    "lead_context": "Marketing Director, interested in automation"
                }
            }
        )
        
        content_result = await marketing_agent.execute_task(content_request)
        logger.info(f"✅ Integration Step 2 - Content Creation: {content_result.success}")
        
        if qualification_result.success and content_result.success:
            logger.info("🎉 Agent integration test successful!")
            return True
        else:
            logger.error("❌ Agent integration test failed")
            return False
        
    except Exception as e:
        logger.error(f"❌ Agent integration test failed: {e}")
        return False


async def run_comprehensive_test():
    """Run comprehensive test suite for Milestone 4 agents."""
    logger.info("🚀 Starting Milestone 4 Agent Testing")
    logger.info("=" * 60)
    
    test_results = []
    
    # Test Sales Development Agent
    logger.info("\n📊 SALES DEVELOPMENT AGENT TESTS")
    logger.info("-" * 40)
    sales_result = await test_sales_development_agent()
    test_results.append(("Sales Development Agent", sales_result))
    
    # Test Marketing Content Agent
    logger.info("\n📝 MARKETING CONTENT AGENT TESTS")
    logger.info("-" * 40)
    marketing_result = await test_marketing_content_agent()
    test_results.append(("Marketing Content Agent", marketing_result))
    
    # Test Agent Integration
    logger.info("\n🔗 AGENT INTEGRATION TESTS")
    logger.info("-" * 40)
    integration_result = await test_agent_integration()
    test_results.append(("Agent Integration", integration_result))
    
    # Summary
    logger.info("\n📋 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    logger.info(f"\nOverall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED - Milestone 4 agents ready for deployment!")
        return True
    else:
        logger.error(f"⚠️  {total_tests - passed_tests} tests failed - Review and fix issues before deployment")
        return False


async def main():
    """Main test execution function."""
    try:
        logger.info("🧪 TKC_v5 Milestone 4 Agent Testing")
        logger.info(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🎯 Testing: Sales Development Agent + Marketing Content Agent")
        
        # Run comprehensive tests
        success = await run_comprehensive_test()
        
        if success:
            logger.info("\n🎉 MILESTONE 4 TESTING COMPLETE - SUCCESS!")
            logger.info("✅ Both agents are functional and ready for commercial deployment")
            logger.info("📋 Next Steps:")
            logger.info("   1. Deploy agents to Cloud Run")
            logger.info("   2. Set up multi-tenant architecture")
            logger.info("   3. Configure customer onboarding")
            logger.info("   4. Begin pilot customer testing")
            return 0
        else:
            logger.error("\n❌ MILESTONE 4 TESTING FAILED")
            logger.error("🔧 Fix identified issues before proceeding with deployment")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return 1


if __name__ == "__main__":
    # Run the test suite
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

#!/usr/bin/env python3
"""
Test Script for Inbound Lead Processing Webhook

This script tests the deployed webhook endpoint with various form submission scenarios.
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, Any, List


class WebhookTester:
    """Test the inbound lead processing webhook."""
    
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
        self.test_results = []
    
    async def test_form_submission(self, submission_data: Dict[str, Any], test_name: str) -> Dict[str, Any]:
        """Test a single form submission."""
        print(f"\n🧪 Testing: {test_name}")
        print(f"📧 Email: {submission_data.get('email')}")
        print(f"📝 Form Type: {submission_data.get('form_type')}")
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=submission_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    response_data = await response.json()
                    duration = time.time() - start_time
                    
                    result = {
                        'test_name': test_name,
                        'status_code': response.status,
                        'response': response_data,
                        'duration': duration,
                        'success': response.status == 200 and response_data.get('status') == 'success'
                    }
                    
                    if result['success']:
                        print(f"✅ Success! Lead ID: {response_data.get('lead_id')}")
                        print(f"⏱️  Duration: {duration:.2f}s")
                        if response_data.get('crm_contact_id'):
                            print(f"👤 CRM Contact: {response_data.get('crm_contact_id')}")
                        if response_data.get('crm_deal_id'):
                            print(f"💼 CRM Deal: {response_data.get('crm_deal_id')}")
                    else:
                        print(f"❌ Failed! Status: {response.status}")
                        print(f"📄 Response: {response_data}")
                    
                    self.test_results.append(result)
                    return result
                    
        except Exception as e:
            duration = time.time() - start_time
            result = {
                'test_name': test_name,
                'status_code': 0,
                'response': {'error': str(e)},
                'duration': duration,
                'success': False
            }
            print(f"❌ Error: {e}")
            self.test_results.append(result)
            return result
    
    def generate_test_submissions(self) -> List[Dict[str, Any]]:
        """Generate test form submissions."""
        base_timestamp = datetime.now().isoformat()
        
        return [
            {
                'name': 'Demo Request - High Priority',
                'data': {
                    'form_type': 'demo_request',
                    'email': '<EMAIL>',
                    'name': 'Demo Test User',
                    'company': 'Test Corporation',
                    'phone': '******-0123',
                    'message': 'I would like to schedule a demo of your platform. We are evaluating solutions for our team of 50+ people.',
                    'source': 'website',
                    'utm_campaign': 'q4-demo-campaign',
                    'utm_source': 'google',
                    'utm_medium': 'cpc',
                    'page_url': 'https://tkcgroup.co/demo',
                    'timestamp': base_timestamp,
                    'metadata': {
                        'test': True,
                        'user_agent': 'WebhookTester/1.0',
                        'referrer': 'https://google.com'
                    }
                }
            },
            {
                'name': 'Consultation Request - High Priority',
                'data': {
                    'form_type': 'consultation',
                    'email': '<EMAIL>',
                    'name': 'Consultation Test User',
                    'company': 'Strategic Solutions Inc',
                    'phone': '******-0456',
                    'message': 'We need strategic consultation for our digital transformation initiative. Budget is $100k+.',
                    'source': 'website',
                    'utm_campaign': 'consultation-campaign',
                    'utm_source': 'linkedin',
                    'utm_medium': 'social',
                    'page_url': 'https://tkcgroup.co/consultation',
                    'timestamp': base_timestamp,
                    'metadata': {
                        'test': True,
                        'user_agent': 'WebhookTester/1.0'
                    }
                }
            },
            {
                'name': 'Contact Form - Medium Priority',
                'data': {
                    'form_type': 'contact',
                    'email': '<EMAIL>',
                    'name': 'Contact Test User',
                    'company': 'Medium Business LLC',
                    'phone': '',
                    'message': 'I have some questions about your services and pricing.',
                    'source': 'website',
                    'utm_campaign': '',
                    'utm_source': 'organic',
                    'utm_medium': '',
                    'page_url': 'https://tkcgroup.co/contact',
                    'timestamp': base_timestamp,
                    'metadata': {
                        'test': True,
                        'user_agent': 'WebhookTester/1.0'
                    }
                }
            },
            {
                'name': 'Lead Capture - Low Priority',
                'data': {
                    'form_type': 'lead_capture',
                    'email': '<EMAIL>',
                    'name': 'Lead Capture User',
                    'company': '',
                    'phone': '',
                    'message': '',
                    'source': 'website',
                    'utm_campaign': 'content-download',
                    'utm_source': 'email',
                    'utm_medium': 'newsletter',
                    'page_url': 'https://tkcgroup.co/resources/whitepaper',
                    'timestamp': base_timestamp,
                    'metadata': {
                        'test': True,
                        'user_agent': 'WebhookTester/1.0',
                        'content_type': 'whitepaper'
                    }
                }
            },
            {
                'name': 'Minimal Data Test',
                'data': {
                    'form_type': 'contact',
                    'email': '<EMAIL>',
                    'name': 'Min User',
                    'company': '',
                    'phone': '',
                    'message': '',
                    'source': 'website',
                    'timestamp': base_timestamp,
                    'metadata': {'test': True}
                }
            },
            {
                'name': 'Enterprise Lead Test',
                'data': {
                    'form_type': 'demo_request',
                    'email': '<EMAIL>',
                    'name': 'Enterprise Decision Maker',
                    'company': 'Big Enterprise Corp',
                    'phone': '******-0789',
                    'message': 'We are a Fortune 500 company looking for an enterprise solution. Need to schedule a demo ASAP for our executive team. Budget is not a concern.',
                    'source': 'website',
                    'utm_campaign': 'enterprise-outreach',
                    'utm_source': 'direct',
                    'utm_medium': 'referral',
                    'page_url': 'https://tkcgroup.co/enterprise',
                    'timestamp': base_timestamp,
                    'metadata': {
                        'test': True,
                        'user_agent': 'WebhookTester/1.0',
                        'company_size': 'enterprise',
                        'urgency': 'high'
                    }
                }
            }
        ]
    
    async def run_all_tests(self):
        """Run all test scenarios."""
        print("🚀 Starting Inbound Lead Processing Webhook Tests")
        print(f"🎯 Target URL: {self.webhook_url}")
        print("=" * 60)
        
        test_submissions = self.generate_test_submissions()
        
        for test_case in test_submissions:
            await self.test_form_submission(test_case['data'], test_case['name'])
            # Small delay between tests
            await asyncio.sleep(1)
        
        self.print_summary()
    
    def print_summary(self):
        """Print test results summary."""
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        if successful_tests > 0:
            avg_duration = sum(r['duration'] for r in self.test_results if r['success']) / successful_tests
            print(f"⏱️  Average Response Time: {avg_duration:.2f}s")
        
        print("\n📋 Detailed Results:")
        for result in self.test_results:
            status_icon = "✅" if result['success'] else "❌"
            print(f"{status_icon} {result['test_name']} - {result['duration']:.2f}s")
            
            if not result['success']:
                print(f"   Error: {result['response'].get('error', 'Unknown error')}")
        
        print("\n🔍 Response Analysis:")
        for result in self.test_results:
            if result['success']:
                response = result['response']
                print(f"\n{result['test_name']}:")
                print(f"  Lead ID: {response.get('lead_id', 'N/A')}")
                print(f"  CRM Contact: {response.get('crm_contact_id', 'N/A')}")
                print(f"  CRM Deal: {response.get('crm_deal_id', 'N/A')}")
                print(f"  Follow-up Scheduled: {response.get('follow_up_scheduled', 'N/A')}")
    
    async def test_error_scenarios(self):
        """Test error handling scenarios."""
        print("\n🧪 Testing Error Scenarios")
        print("-" * 40)
        
        error_tests = [
            {
                'name': 'Missing Required Fields',
                'data': {
                    'form_type': 'contact',
                    # Missing email and name
                    'company': 'Test Company'
                }
            },
            {
                'name': 'Invalid Email Format',
                'data': {
                    'form_type': 'contact',
                    'email': 'invalid-email-format',
                    'name': 'Test User'
                }
            },
            {
                'name': 'Invalid Form Type',
                'data': {
                    'form_type': 'invalid_form_type',
                    'email': '<EMAIL>',
                    'name': 'Test User'
                }
            }
        ]
        
        for test_case in error_tests:
            await self.test_form_submission(test_case['data'], test_case['name'])
            await asyncio.sleep(0.5)


async def main():
    """Main test execution."""
    import sys
    
    # Default to test environment
    webhook_url = "https://tkc-v5-executive-agent-test-1072222703018.us-central1.run.app/webhook/form"
    
    # Allow URL override from command line
    if len(sys.argv) > 1:
        webhook_url = sys.argv[1]
    
    print(f"🎯 Testing webhook at: {webhook_url}")
    
    tester = WebhookTester(webhook_url)
    
    # Run main tests
    await tester.run_all_tests()
    
    # Run error scenario tests
    await tester.test_error_scenarios()
    
    print("\n🏁 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())

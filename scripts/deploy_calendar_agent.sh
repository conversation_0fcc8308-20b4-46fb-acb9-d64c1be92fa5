#!/bin/bash
# Deploy Calendar Agent - TKC_v5 Template Example
# This script demonstrates the rapid deployment process for specialized agents

set -e

# Configuration
AGENT_NAME="calendar"
PROJECT_ID="vertex-ai-agent-yzdlnjey"
REGION="us-central1"
SERVICE_ACCOUNT="${AGENT_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com"

echo "🚀 Deploying Calendar Agent using TKC_v5 Template Framework..."
echo "=================================================="

# Phase 1: Foundation Setup (30 minutes)
echo "📋 Phase 1: Foundation Setup"

# Create service account
echo "Creating service account..."
gcloud iam service-accounts create ${AGENT_NAME}-sa \
  --display-name="Calendar Service Agent" \
  --description="Service account for TKC_v5 Calendar Agent" \
  --project=${PROJECT_ID} || echo "Service account already exists"

# Assign core permissions
echo "Assigning IAM permissions..."
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SERVICE_ACCOUNT}" \
  --role="roles/aiplatform.user"

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SERVICE_ACCOUNT}" \
  --role="roles/secretmanager.secretAccessor"

# Calendar-specific permissions
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SERVICE_ACCOUNT}" \
  --role="roles/calendar.admin" || echo "Calendar admin role not available, manual setup required"

# Create agent-specific configuration
echo "Creating agent configuration..."
cat > /tmp/${AGENT_NAME}-config.json << EOF
{
  "agent_type": "calendar",
  "model_name": "gemini-2.5-flash",
  "temperature": 0.1,
  "location": "us-central1",
  "calendar_settings": {
    "default_timezone": "America/Chicago",
    "default_duration_minutes": 30,
    "business_hours": {
      "start": "09:00",
      "end": "17:00"
    },
    "working_days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
  },
  "integration_settings": {
    "google_calendar_api_version": "v3",
    "max_attendees": 50,
    "default_reminder_minutes": 15
  }
}
EOF

# Store configuration in Secret Manager
gcloud secrets create ${AGENT_NAME}-config \
  --data-file=/tmp/${AGENT_NAME}-config.json \
  --project=${PROJECT_ID} || \
gcloud secrets versions add ${AGENT_NAME}-config \
  --data-file=/tmp/${AGENT_NAME}-config.json \
  --project=${PROJECT_ID}

# Clean up temporary file
rm /tmp/${AGENT_NAME}-config.json

echo "✅ Phase 1 Complete: Foundation Setup"

# Phase 2: Build and Deploy (30 minutes)
echo "📋 Phase 2: Build and Deploy"

# Create Dockerfile for Calendar Agent
echo "Creating Dockerfile..."
cat > Dockerfile.calendar << EOF
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional calendar-specific dependencies
RUN pip install --no-cache-dir \\
    google-api-python-client \\
    google-auth-httplib2 \\
    google-auth-oauthlib

# Copy source code
COPY src/ ./src/
COPY config/ ./config/

# Set environment variables
ENV PYTHONPATH=/app
ENV GOOGLE_CLOUD_PROJECT=${PROJECT_ID}
ENV AGENT_TYPE=calendar

# Expose port
EXPOSE 8080

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash calendar \\
    && chown -R calendar:calendar /app
USER calendar

# Start the Calendar Agent
CMD ["python", "-m", "src.agents.calendar.main"]
EOF

# Build container image
echo "Building container image..."
gcloud builds submit --tag gcr.io/${PROJECT_ID}/${AGENT_NAME}-agent \
  --dockerfile=Dockerfile.calendar \
  --project=${PROJECT_ID}

# Deploy to Cloud Run
echo "Deploying to Cloud Run..."
gcloud run deploy ${AGENT_NAME}-agent \\
  --image gcr.io/${PROJECT_ID}/${AGENT_NAME}-agent \\
  --platform managed \\
  --region ${REGION} \\
  --allow-unauthenticated \\
  --memory 2Gi \\
  --cpu 2 \\
  --timeout 3600 \\
  --max-instances 10 \\
  --min-instances 0 \\
  --service-account ${SERVICE_ACCOUNT} \\
  --set-env-vars GOOGLE_CLOUD_PROJECT=${PROJECT_ID},AGENT_TYPE=${AGENT_NAME} \\
  --project ${PROJECT_ID}

echo "✅ Phase 2 Complete: Build and Deploy"

# Phase 3: Validation and Testing (45 minutes)
echo "📋 Phase 3: Validation and Testing"

# Get service URL
SERVICE_URL=$(gcloud run services describe ${AGENT_NAME}-agent \\
  --region=${REGION} \\
  --format="value(status.url)" \\
  --project=${PROJECT_ID})

echo "Service deployed at: ${SERVICE_URL}"

# Health check
echo "Performing health check..."
curl -f "${SERVICE_URL}/health" || {
  echo "❌ Health check failed"
  exit 1
}

# Test calendar functionality
echo "Testing calendar functionality..."
curl -X POST "${SERVICE_URL}/chat" \\
  -H "Content-Type: application/json" \\
  -d '{
    "message": "Check my availability for tomorrow at 2 PM",
    "metadata": {
      "customer_id": "test_user",
      "agent_type": "calendar"
    }
  }' || {
  echo "❌ Calendar functionality test failed"
  exit 1
}

echo "✅ Phase 3 Complete: Validation and Testing"

# Phase 4: Monitoring Setup
echo "📋 Phase 4: Monitoring Setup"

# Create monitoring dashboard
echo "Setting up monitoring..."
cat > /tmp/calendar-agent-dashboard.json << EOF
{
  "displayName": "Calendar Agent Dashboard",
  "mosaicLayout": {
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Calendar Agent Response Time",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\\"cloud_run_revision\\" AND resource.labels.service_name=\\"${AGENT_NAME}-agent\\"",
                  "aggregation": {
                    "alignmentPeriod": "60s",
                    "perSeriesAligner": "ALIGN_MEAN"
                  }
                }
              }
            }]
          }
        }
      }
    ]
  }
}
EOF

# Note: Dashboard creation would require additional monitoring API setup
echo "📊 Monitoring dashboard configuration created"
echo "   Manual setup required in Cloud Console for full monitoring"

# Clean up temporary files
rm -f Dockerfile.calendar /tmp/calendar-agent-dashboard.json

echo "✅ Phase 4 Complete: Monitoring Setup"

# Summary
echo ""
echo "🎉 Calendar Agent Deployment Complete!"
echo "=================================================="
echo "📊 Deployment Summary:"
echo "   • Service Account: ${SERVICE_ACCOUNT}"
echo "   • Service URL: ${SERVICE_URL}"
echo "   • Configuration: ${AGENT_NAME}-config (Secret Manager)"
echo "   • Container: gcr.io/${PROJECT_ID}/${AGENT_NAME}-agent"
echo ""
echo "🔧 Next Steps:"
echo "   1. Configure Google Calendar API credentials"
echo "   2. Set up domain delegation for calendar access"
echo "   3. Test end-to-end calendar operations"
echo "   4. Configure monitoring alerts"
echo ""
echo "📋 Estimated Deployment Time: ~2 hours (vs 5 hours manual)"
echo "💰 Estimated Monthly Cost: ~$61 (including shared infrastructure)"
echo ""
echo "✅ Calendar Agent is ready for production use!"

# Test the deployment
echo "🧪 Running final deployment test..."
if curl -s "${SERVICE_URL}/health" | grep -q "healthy"; then
  echo "✅ Final test passed - Calendar Agent is operational!"
  exit 0
else
  echo "❌ Final test failed - Please check logs"
  exit 1
fi

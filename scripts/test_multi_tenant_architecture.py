#!/usr/bin/env python3
"""
Multi-Tenant Architecture Test Script - TKC_v5 Commercial Package

Tests the multi-tenant architecture to ensure proper customer isolation
and configuration management.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from services.multi_tenant_manager import get_tenant_manager
from services.tenant_vector_service import get_tenant_vector_service, get_tenant_rag_service
from services.tenant_redis_checkpointer import get_tenant_redis_checkpointer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_tenant_manager():
    """Test the tenant manager functionality."""
    logger.info("🔄 Testing Tenant Manager...")
    
    try:
        # Get tenant manager
        tenant_manager = await get_tenant_manager()
        
        # Test 1: Initialize test tenants
        logger.info("📋 Test 1: Initialize Test Tenants")
        
        test_tenants = [
            {
                'customer_id': 'test_customer_1',
                'config': {
                    'company_name': 'Test Company 1',
                    'industry': 'Technology',
                    'plan': 'starter',
                    'crm_config': {
                        'hubspot': {
                            'api_key': 'test_hubspot_key_1'
                        }
                    }
                }
            },
            {
                'customer_id': 'test_customer_2',
                'config': {
                    'company_name': 'Test Company 2',
                    'industry': 'Healthcare',
                    'plan': 'professional',
                    'crm_config': {
                        'hubspot': {
                            'api_key': 'test_hubspot_key_2'
                        }
                    }
                }
            }
        ]
        
        for tenant in test_tenants:
            success = await tenant_manager.initialize_tenant(
                tenant['customer_id'],
                tenant['config']
            )
            
            if success:
                logger.info(f"✅ Tenant {tenant['customer_id']} initialized successfully")
            else:
                logger.error(f"❌ Failed to initialize tenant {tenant['customer_id']}")
                return False
        
        # Test 2: Retrieve tenant configurations
        logger.info("📋 Test 2: Retrieve Tenant Configurations")
        
        for tenant in test_tenants:
            config = await tenant_manager.get_tenant_config(tenant['customer_id'])
            
            if config:
                logger.info(f"✅ Retrieved config for {tenant['customer_id']}")
                logger.info(f"   Company: {config['config']['company_name']}")
                logger.info(f"   Namespace: {config['namespace']}")
            else:
                logger.error(f"❌ Failed to retrieve config for {tenant['customer_id']}")
                return False
        
        # Test 3: Test namespace isolation
        logger.info("📋 Test 3: Test Namespace Isolation")
        
        namespace1 = tenant_manager.get_vector_namespace('test_customer_1')
        namespace2 = tenant_manager.get_vector_namespace('test_customer_2')
        
        if namespace1 != namespace2:
            logger.info(f"✅ Namespace isolation working: {namespace1} != {namespace2}")
        else:
            logger.error(f"❌ Namespace isolation failed: {namespace1} == {namespace2}")
            return False
        
        # Test 4: Test Redis key isolation
        logger.info("📋 Test 4: Test Redis Key Isolation")
        
        key1 = tenant_manager.get_redis_key('test_customer_1', 'test_key')
        key2 = tenant_manager.get_redis_key('test_customer_2', 'test_key')
        
        if key1 != key2 and 'test_customer_1' in key1 and 'test_customer_2' in key2:
            logger.info(f"✅ Redis key isolation working: {key1} != {key2}")
        else:
            logger.error(f"❌ Redis key isolation failed: {key1} == {key2}")
            return False
        
        logger.info("🎉 Tenant Manager testing completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tenant Manager test failed: {e}")
        return False


async def test_tenant_vector_service():
    """Test the tenant vector service functionality."""
    logger.info("🔄 Testing Tenant Vector Service...")
    
    try:
        # Test with two different tenants
        customer_ids = ['test_customer_1', 'test_customer_2']
        
        for customer_id in customer_ids:
            logger.info(f"📋 Testing Vector Service for {customer_id}")
            
            # Get tenant vector service
            vector_service = await get_tenant_vector_service(customer_id)
            
            # Test 1: Store conversation
            test_conversation = {
                'conversation_id': f'test_conv_{customer_id}',
                'messages': [
                    {'role': 'user', 'content': f'Hello from {customer_id}'},
                    {'role': 'assistant', 'content': f'Hello! I am helping {customer_id}'}
                ],
                'metadata': {
                    'test_data': True,
                    'customer_specific': customer_id
                }
            }
            
            success = await vector_service.store_conversation(
                test_conversation['conversation_id'],
                test_conversation['messages'],
                test_conversation['metadata']
            )
            
            if success:
                logger.info(f"✅ Conversation stored for {customer_id}")
            else:
                logger.error(f"❌ Failed to store conversation for {customer_id}")
                return False
            
            # Test 2: Search conversations (should only find own data)
            results = await vector_service.search_conversations(
                query=f"Hello from {customer_id}",
                limit=5
            )
            
            # Verify isolation - should only find own conversations
            for result in results:
                result_customer = result.get('metadata', {}).get('customer_id')
                if result_customer != customer_id:
                    logger.error(f"❌ Tenant isolation violation: found {result_customer} data in {customer_id} search")
                    return False
            
            logger.info(f"✅ Search isolation verified for {customer_id} ({len(results)} results)")
            
            # Test 3: Get tenant stats
            stats = await vector_service.get_tenant_stats()
            logger.info(f"✅ Stats retrieved for {customer_id}: {stats}")
        
        logger.info("🎉 Tenant Vector Service testing completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tenant Vector Service test failed: {e}")
        return False


async def test_tenant_redis_checkpointer():
    """Test the tenant Redis checkpointer functionality."""
    logger.info("🔄 Testing Tenant Redis Checkpointer...")
    
    try:
        # Test with two different tenants
        customer_ids = ['test_customer_1', 'test_customer_2']
        
        for customer_id in customer_ids:
            logger.info(f"📋 Testing Redis Checkpointer for {customer_id}")
            
            # Get tenant Redis checkpointer
            checkpointer = await get_tenant_redis_checkpointer(customer_id)
            
            # Test 1: Save checkpoint
            test_checkpoint = {
                'state': f'test_state_for_{customer_id}',
                'step': 1,
                'data': {'customer': customer_id, 'test': True}
            }
            
            test_metadata = {
                'created_by': customer_id,
                'test_metadata': True
            }
            
            thread_id = f'test_thread_{customer_id}'
            checkpoint_id = await checkpointer.save_checkpoint(
                thread_id,
                test_checkpoint,
                test_metadata
            )
            
            if checkpoint_id:
                logger.info(f"✅ Checkpoint saved for {customer_id}: {checkpoint_id}")
            else:
                logger.error(f"❌ Failed to save checkpoint for {customer_id}")
                return False
            
            # Test 2: Retrieve checkpoint
            retrieved = await checkpointer.get_checkpoint(thread_id, checkpoint_id)
            
            if retrieved:
                checkpoint, metadata = retrieved
                if checkpoint.get('data', {}).get('customer') == customer_id:
                    logger.info(f"✅ Checkpoint retrieved correctly for {customer_id}")
                else:
                    logger.error(f"❌ Checkpoint data mismatch for {customer_id}")
                    return False
            else:
                logger.error(f"❌ Failed to retrieve checkpoint for {customer_id}")
                return False
            
            # Test 3: List checkpoints
            checkpoints = await checkpointer.list_checkpoints(thread_id)
            
            if checkpoints:
                logger.info(f"✅ Listed {len(checkpoints)} checkpoints for {customer_id}")
            else:
                logger.error(f"❌ Failed to list checkpoints for {customer_id}")
                return False
            
            # Test 4: Get tenant stats
            stats = await checkpointer.get_tenant_stats()
            logger.info(f"✅ Checkpoint stats for {customer_id}: {stats}")
        
        logger.info("🎉 Tenant Redis Checkpointer testing completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tenant Redis Checkpointer test failed: {e}")
        return False


async def test_cross_tenant_isolation():
    """Test that tenants cannot access each other's data."""
    logger.info("🔄 Testing Cross-Tenant Isolation...")
    
    try:
        # Get services for both tenants
        vector_service_1 = await get_tenant_vector_service('test_customer_1')
        vector_service_2 = await get_tenant_vector_service('test_customer_2')
        
        checkpointer_1 = await get_tenant_redis_checkpointer('test_customer_1')
        checkpointer_2 = await get_tenant_redis_checkpointer('test_customer_2')
        
        # Test 1: Vector service isolation
        logger.info("📋 Test 1: Vector Service Cross-Tenant Isolation")
        
        # Customer 1 searches for Customer 2's data
        results_1 = await vector_service_1.search_conversations(
            query="Hello from test_customer_2",
            limit=10
        )
        
        # Should not find Customer 2's data
        for result in results_1:
            result_customer = result.get('metadata', {}).get('customer_id')
            if result_customer == 'test_customer_2':
                logger.error(f"❌ Cross-tenant data leak: Customer 1 found Customer 2's data")
                return False
        
        logger.info("✅ Vector service cross-tenant isolation verified")
        
        # Test 2: Redis checkpointer isolation
        logger.info("📋 Test 2: Redis Checkpointer Cross-Tenant Isolation")
        
        # Try to access Customer 2's thread from Customer 1's checkpointer
        customer_2_thread = 'test_thread_test_customer_2'
        result = await checkpointer_1.get_checkpoint(customer_2_thread)
        
        if result is None:
            logger.info("✅ Redis checkpointer cross-tenant isolation verified")
        else:
            logger.error(f"❌ Cross-tenant checkpoint access: Customer 1 accessed Customer 2's data")
            return False
        
        # Test 3: Namespace verification
        logger.info("📋 Test 3: Namespace Isolation Verification")
        
        namespace_1 = vector_service_1.namespace
        namespace_2 = vector_service_2.namespace
        
        if namespace_1 != namespace_2:
            logger.info(f"✅ Namespace isolation verified: {namespace_1} != {namespace_2}")
        else:
            logger.error(f"❌ Namespace collision: {namespace_1} == {namespace_2}")
            return False
        
        logger.info("🎉 Cross-Tenant Isolation testing completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Cross-Tenant Isolation test failed: {e}")
        return False


async def cleanup_test_data():
    """Clean up test data after testing."""
    logger.info("🧹 Cleaning up test data...")
    
    try:
        tenant_manager = await get_tenant_manager()
        
        # Deactivate test tenants
        test_customers = ['test_customer_1', 'test_customer_2']
        
        for customer_id in test_customers:
            success = await tenant_manager.deactivate_tenant(customer_id)
            if success:
                logger.info(f"✅ Test tenant {customer_id} deactivated")
            else:
                logger.warning(f"⚠️ Failed to deactivate test tenant {customer_id}")
        
        logger.info("🧹 Test data cleanup completed")
        
    except Exception as e:
        logger.warning(f"⚠️ Test data cleanup failed: {e}")


async def run_comprehensive_test():
    """Run comprehensive multi-tenant architecture test suite."""
    logger.info("🚀 Starting Multi-Tenant Architecture Testing")
    logger.info("=" * 60)
    
    test_results = []
    
    try:
        # Test 1: Tenant Manager
        logger.info("\n🏢 TENANT MANAGER TESTS")
        logger.info("-" * 40)
        tenant_manager_result = await test_tenant_manager()
        test_results.append(("Tenant Manager", tenant_manager_result))
        
        # Test 2: Tenant Vector Service
        logger.info("\n🔍 TENANT VECTOR SERVICE TESTS")
        logger.info("-" * 40)
        vector_service_result = await test_tenant_vector_service()
        test_results.append(("Tenant Vector Service", vector_service_result))
        
        # Test 3: Tenant Redis Checkpointer
        logger.info("\n💾 TENANT REDIS CHECKPOINTER TESTS")
        logger.info("-" * 40)
        redis_checkpointer_result = await test_tenant_redis_checkpointer()
        test_results.append(("Tenant Redis Checkpointer", redis_checkpointer_result))
        
        # Test 4: Cross-Tenant Isolation
        logger.info("\n🔒 CROSS-TENANT ISOLATION TESTS")
        logger.info("-" * 40)
        isolation_result = await test_cross_tenant_isolation()
        test_results.append(("Cross-Tenant Isolation", isolation_result))
        
    finally:
        # Always clean up test data
        await cleanup_test_data()
    
    # Summary
    logger.info("\n📋 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    logger.info(f"\nOverall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 ALL TESTS PASSED - Multi-tenant architecture ready for deployment!")
        return True
    else:
        logger.error(f"⚠️  {total_tests - passed_tests} tests failed - Review and fix issues before deployment")
        return False


async def main():
    """Main test execution function."""
    try:
        logger.info("🧪 TKC_v5 Multi-Tenant Architecture Testing")
        logger.info(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🎯 Testing: Customer isolation, vector namespacing, Redis tenant separation")
        
        # Run comprehensive tests
        success = await run_comprehensive_test()
        
        if success:
            logger.info("\n🎉 MULTI-TENANT ARCHITECTURE TESTING COMPLETE - SUCCESS!")
            logger.info("✅ Customer isolation working correctly")
            logger.info("✅ Vector database namespacing operational")
            logger.info("✅ Redis tenant separation functional")
            logger.info("✅ Cross-tenant data protection verified")
            logger.info("📋 Next Steps:")
            logger.info("   1. Deploy multi-tenant services to Cloud Run")
            logger.info("   2. Set up customer onboarding automation")
            logger.info("   3. Configure usage tracking and billing")
            logger.info("   4. Begin pilot customer testing")
            return 0
        else:
            logger.error("\n❌ MULTI-TENANT ARCHITECTURE TESTING FAILED")
            logger.error("🔧 Fix identified issues before proceeding with commercial deployment")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        return 1


if __name__ == "__main__":
    # Run the test suite
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

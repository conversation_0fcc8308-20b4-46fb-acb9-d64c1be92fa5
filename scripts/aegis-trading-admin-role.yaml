title: "Aegis Trading Agent Admin"
description: "Custom role for <PERSON>'s exclusive access to Aegis Trading Agent v3 resources"
stage: "GA"
includedPermissions:
  # Cloud Run permissions for trading agent service
  - run.services.get
  - run.services.list
  - run.revisions.get
  - run.revisions.list

  # Secret Manager permissions for trading secrets
  - secretmanager.secrets.get
  - secretmanager.versions.access
  - secretmanager.versions.get
  - secretmanager.versions.list

  # Firestore permissions for trading collections
  - datastore.entities.create
  - datastore.entities.delete
  - datastore.entities.get
  - datastore.entities.list
  - datastore.entities.update
  - datastore.indexes.get
  - datastore.indexes.list

  # Service Account permissions for impersonation (debugging)
  - iam.serviceAccounts.actAs
  - iam.serviceAccounts.get
  - iam.serviceAccounts.getIamPolicy

  # Basic project permissions
  - resourcemanager.projects.get

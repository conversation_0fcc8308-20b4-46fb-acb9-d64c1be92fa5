# AEGIS v2 Deployment Checklist

## Pre-Deployment Setup

### 1. 🔐 Authentication
- [ ] Run `gcloud auth login` to authenticate
- [ ] Run `gcloud auth application-default login` for app credentials
- [ ] Verify project: `gcloud config get-value project` should show `tkcgroup-v4`

### 2. 🔑 Environment Variables
Update `.env.local` with real API keys:
- [ ] `GOOGLE_API_KEY` - Your Gemini API key
- [ ] `COINGECKO_API_KEY` - CoinGecko API key (optional)
- [ ] `NEWSAPI_KEY` - News API key (optional)
- [ ] `POSTGRES_PASSWORD` - Database password (if using Cloud SQL)

### 3. 📋 API Enablement
Run these commands to enable required APIs:
```bash
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable redis.googleapis.com
```

## Deployment Process

### Step 1: Run Deployment Script
```bash
./deploy.sh
```

The script will:
- ✅ Enable required GCP APIs
- ✅ Create secrets in Secret Manager
- ✅ Deploy AEGIS to Cloud Run
- ✅ Set up Redis for session management
- ✅ Configure Pub/Sub for async tasks

### Step 2: Monitor Deployment
Watch for these success indicators:
- [ ] ✅ Cloud Run service created successfully
- [ ] ✅ Redis instance created
- [ ] ✅ Secrets created in Secret Manager
- [ ] ✅ Health check returns 200 OK

### Step 3: Test Deployment
```bash
# Replace <SERVICE_URL> with your actual Cloud Run URL
python test_deployment.py <SERVICE_URL>
```

## Expected Outputs

### Successful Deployment
```
🚀 Deploying AEGIS to Google Cloud Platform
Project: tkcgroup-v4
Region: us-west1

✅ API Service deployed successfully!
📍 API URL: https://aegis-agentic-intelligence-system-abc123-uw.a.run.app
✅ Redis Instance: aegis-redis
✅ Pub/Sub Topic: aegis-tasks
```

### Service URL Format
Your deployed service will be available at:
```
https://aegis-agentic-intelligence-system-[hash]-uw.a.run.app
```

## Post-Deployment Verification

### Health Check
```bash
curl https://your-service-url/
```
Expected response:
```json
{
  "status": "healthy",
  "agent": "AEGIS",
  "version": "2.0.0",
  "timestamp": "2024-07-18T...",
  "environment": "production"
}
```

### API Documentation
Visit these URLs after deployment:
- **Swagger UI**: `https://your-service-url/docs`
- **ReDoc**: `https://your-service-url/redoc`

### Test Endpoints
```bash
# Test crypto analysis
curl -X POST "https://your-service-url/v1/crypto/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "BTC",
    "analysis_type": "comprehensive",
    "timeframe": "7d"
  }'

# Test chat interface
curl -X POST "https://your-service-url/v1/crypto/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What is the current Bitcoin price?",
    "session_id": "test_session"
  }'
```

## Troubleshooting

### Common Issues

1. **Authentication Error**
   ```bash
   gcloud auth login
   gcloud auth application-default login
   ```

2. **API Not Enabled**
   ```bash
   gcloud services enable run.googleapis.com
   ```

3. **Secret Creation Failed**
   - Check your `.env.local` file has the correct values
   - Verify Secret Manager API is enabled

4. **Deployment Timeout**
   - Check Cloud Build logs: `gcloud builds log <BUILD_ID>`
   - Verify Docker build is successful

### Monitoring Commands

```bash
# View service logs
gcloud run services logs read aegis-agentic-intelligence-system --region=us-west1

# Check service details
gcloud run services describe aegis-agentic-intelligence-system --region=us-west1

# List all services
gcloud run services list --region=us-west1
```

## Success Criteria

- [ ] ✅ Cloud Run service is running
- [ ] ✅ Health check returns 200 OK
- [ ] ✅ All API endpoints respond correctly
- [ ] ✅ Redis session management works
- [ ] ✅ Secrets are properly mounted
- [ ] ✅ Logging is working correctly

## Next Steps After Deployment

1. **Set up monitoring alerts**
2. **Configure custom domain (optional)**
3. **Set up CI/CD pipeline**
4. **Monitor costs and usage**
5. **Test with real crypto data**

## Support

If you encounter issues:
1. Check the deployment logs
2. Verify all environment variables are set
3. Ensure APIs are enabled
4. Check service account permissions

Ready to deploy! 🚀
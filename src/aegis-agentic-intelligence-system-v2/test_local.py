#!/usr/bin/env python3
"""
Simple test to verify the basic imports work locally.
"""

try:
    import sys
    print(f"Python version: {sys.version}")
    
    # Test basic imports
    from fastapi import FastAPI
    print("✅ FastAPI imported successfully")
    
    from pydantic import BaseModel
    print("✅ Pydantic imported successfully")
    
    # Test our configuration
    from src.config import settings
    print("✅ Configuration imported successfully")
    print(f"Project: {settings.gcp_project_id}")
    print(f"Region: {settings.gcp_region}")
    
    # Test basic FastAPI app
    app = FastAPI(title="AEGIS Test")
    print("✅ FastAPI app created successfully")
    
    print("\n🎉 All basic imports work correctly!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
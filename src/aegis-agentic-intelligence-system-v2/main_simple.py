"""
Simple FastAPI entrypoint for AEGIS agentic intelligence system v2.
Minimal viable version for "B" quality deployment.
"""

import os
import logging
from fastapi import FastAPI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="AEGIS Agentic Intelligence System v2",
    description="Simplified crypto intelligence system",
    version="2.0.0"
)

@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "healthy", "service": "aegis-agentic-intelligence-system-v2", "version": "2.0.0"}

@app.get("/health")
async def health():
    """Detailed health check."""
    return {
        "status": "healthy",
        "service": "aegis-agentic-intelligence-system-v2", 
        "version": "2.0.0",
        "environment": os.getenv("ENVIRONMENT", "development")
    }

@app.post("/v1/analyze")
async def analyze(data: dict):
    """Simplified analysis endpoint."""
    return {
        "status": "success",
        "message": "Crypto analysis functionality will be restored in enhanced version",
        "data": data
    }

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8080))
    uvicorn.run(app, host="0.0.0.0", port=port)
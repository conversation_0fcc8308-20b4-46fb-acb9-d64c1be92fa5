#!/usr/bin/env python3
"""
Test script to validate AEGIS deployment.

This script tests the deployed AEGIS agent endpoints to ensure
the deployment was successful and the agent is working correctly.
"""

import requests
import json
import time
from datetime import datetime

def test_deployment(base_url):
    """Test the deployed AEGIS agent endpoints."""
    print(f"🧪 Testing AEGIS deployment at: {base_url}")
    print("=" * 60)
    
    # Test 1: Health Check
    print("\n1. 🔍 Testing health check...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"   ✅ Health check passed!")
            print(f"   📊 Agent: {health_data.get('agent', 'Unknown')}")
            print(f"   📊 Status: {health_data.get('status', 'Unknown')}")
            print(f"   📊 Environment: {health_data.get('environment', 'Unknown')}")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
        return False
    
    # Test 2: Status Endpoint
    print("\n2. 🔍 Testing status endpoint...")
    try:
        response = requests.get(f"{base_url}/v1/status", timeout=10)
        if response.status_code == 200:
            status_data = response.json()
            print(f"   ✅ Status endpoint working!")
            print(f"   📊 Agent: {status_data.get('agent', 'Unknown')}")
            capabilities = status_data.get('capabilities', {})
            if capabilities:
                print(f"   🎯 Capabilities: {', '.join(capabilities.keys())}")
        else:
            print(f"   ❌ Status endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Status endpoint error: {e}")
    
    # Test 3: Supported Symbols
    print("\n3. 🔍 Testing supported symbols endpoint...")
    try:
        response = requests.get(f"{base_url}/v1/crypto/supported-symbols", timeout=10)
        if response.status_code == 200:
            symbols_data = response.json()
            print(f"   ✅ Supported symbols endpoint working!")
            print(f"   📊 Total symbols: {symbols_data.get('total_count', 0)}")
            symbols = symbols_data.get('symbols', [])
            if symbols:
                print(f"   🪙 Sample symbols: {', '.join([s['symbol'] for s in symbols[:5]])}")
        else:
            print(f"   ❌ Supported symbols failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Supported symbols error: {e}")
    
    # Test 4: Crypto Analysis (Light Test)
    print("\n4. 🔍 Testing crypto analysis endpoint...")
    try:
        analysis_request = {
            "symbol": "BTC",
            "analysis_type": "comprehensive",
            "timeframe": "7d",
            "include_sentiment": True,
            "include_on_chain": True,
            "include_defi": False
        }
        
        response = requests.post(
            f"{base_url}/v1/crypto/analyze",
            json=analysis_request,
            timeout=30
        )
        
        if response.status_code == 200:
            analysis_data = response.json()
            print(f"   ✅ Crypto analysis endpoint working!")
            print(f"   📊 Symbol: {analysis_data.get('symbol', 'Unknown')}")
            print(f"   📊 Analysis type: {analysis_data.get('analysis_type', 'Unknown')}")
            print(f"   ⏱️ Processing time: {analysis_data.get('processing_time_ms', 0)}ms")
            
            # Check if response contains content
            if analysis_data.get('response'):
                print(f"   📝 Response preview: {analysis_data['response'][:100]}...")
        else:
            print(f"   ❌ Crypto analysis failed: {response.status_code}")
            if response.text:
                print(f"   📝 Error details: {response.text[:200]}...")
    except Exception as e:
        print(f"   ❌ Crypto analysis error: {e}")
    
    # Test 5: Chat Interface (Light Test)
    print("\n5. 🔍 Testing chat interface...")
    try:
        chat_request = {
            "message": "Hello! Can you tell me about Bitcoin?",
            "session_id": f"test_session_{int(time.time())}"
        }
        
        response = requests.post(
            f"{base_url}/v1/crypto/chat",
            json=chat_request,
            timeout=30
        )
        
        if response.status_code == 200:
            chat_data = response.json()
            print(f"   ✅ Chat interface working!")
            print(f"   📊 Session ID: {chat_data.get('session_id', 'Unknown')}")
            print(f"   📊 Message count: {chat_data.get('message_count', 0)}")
            
            # Check if response contains content
            if chat_data.get('response'):
                print(f"   📝 Response preview: {chat_data['response'][:100]}...")
        else:
            print(f"   ❌ Chat interface failed: {response.status_code}")
            if response.text:
                print(f"   📝 Error details: {response.text[:200]}...")
    except Exception as e:
        print(f"   ❌ Chat interface error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 AEGIS deployment test completed!")
    print(f"🔗 API Documentation: {base_url}/docs")
    print(f"🔗 OpenAPI Spec: {base_url}/redoc")
    
    return True

def main():
    """Main function to run deployment tests."""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python test_deployment.py <base_url>")
        print("Example: python test_deployment.py https://aegis-agentic-intelligence-system-abc123-uw.a.run.app")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    
    print(f"🚀 AEGIS Deployment Test Suite")
    print(f"📅 Test Time: {datetime.now().isoformat()}")
    print(f"🔗 Target URL: {base_url}")
    
    success = test_deployment(base_url)
    
    if success:
        print("\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Test script to validate the AEGIS migration.

This script tests the basic functionality of the migrated AEGIS agent
to ensure the migration from TypeScript to Python was successful.
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_basic_imports():
    """Test that all basic imports work correctly."""
    print("🔍 Testing basic imports...")
    
    try:
        from src.config import settings
        print("✅ Config module imported successfully")
        
        from src.agents.template_agent.tools import (
            get_on_chain_metrics,
            get_regulatory_news,
            analyze_market_sentiment,
            ALL_AEGIS_TOOLS
        )
        print("✅ AEGIS tools imported successfully")
        
        from src.agents.template_agent.aegis_graph import AegisAgent
        print("✅ AEGIS agent imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_config_validation():
    """Test that configuration is properly loaded."""
    print("\n🔍 Testing configuration validation...")
    
    try:
        from src.config import settings
        
        # Check required settings
        required_settings = [
            'gcp_project_id',
            'gcp_region',
            'gemini_model',
            'environment'
        ]
        
        for setting in required_settings:
            if not hasattr(settings, setting):
                print(f"❌ Missing required setting: {setting}")
                return False
            
        print(f"✅ Configuration validated - Environment: {settings.environment}")
        print(f"✅ GCP Project: {settings.gcp_project_id}")
        print(f"✅ Gemini Model: {settings.gemini_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

async def test_tools_functionality():
    """Test that AEGIS tools can be executed."""
    print("\n🔍 Testing AEGIS tools functionality...")
    
    try:
        from src.agents.template_agent.tools import (
            get_on_chain_metrics,
            get_regulatory_news,
            analyze_market_sentiment
        )
        
        # Test get_on_chain_metrics with BTC
        print("  Testing get_on_chain_metrics...")
        result = await get_on_chain_metrics.ainvoke({"symbol": "BTC"})
        
        # Parse the result to check if it's valid JSON
        parsed_result = json.loads(result)
        if "timestamp" in parsed_result:
            print("  ✅ get_on_chain_metrics working correctly")
        else:
            print("  ❌ get_on_chain_metrics returned invalid format")
            return False
        
        # Test get_regulatory_news
        print("  Testing get_regulatory_news...")
        result = await get_regulatory_news.ainvoke({"topic": "Bitcoin"})
        
        parsed_result = json.loads(result)
        if "timestamp" in parsed_result:
            print("  ✅ get_regulatory_news working correctly")
        else:
            print("  ❌ get_regulatory_news returned invalid format")
            return False
        
        # Test analyze_market_sentiment
        print("  Testing analyze_market_sentiment...")
        result = await analyze_market_sentiment.ainvoke({"symbol": "BTC", "timeframe": "7d"})
        
        parsed_result = json.loads(result)
        if "analyzed_at" in parsed_result:
            print("  ✅ analyze_market_sentiment working correctly")
        else:
            print("  ❌ analyze_market_sentiment returned invalid format")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Tools functionality test failed: {e}")
        return False

async def test_aegis_agent():
    """Test the AEGIS agent initialization and basic functionality."""
    print("\n🔍 Testing AEGIS agent initialization...")
    
    try:
        from src.agents.template_agent.aegis_graph import AegisAgent
        
        # Initialize the agent
        agent = AegisAgent()
        print("✅ AEGIS agent initialized successfully")
        
        # Test basic message processing (without actually calling models)
        print("  Testing agent structure...")
        
        # Check that the agent has required attributes
        required_attributes = ['model', 'tools', 'graph', 'checkpointer']
        for attr in required_attributes:
            if not hasattr(agent, attr):
                print(f"❌ Agent missing required attribute: {attr}")
                return False
        
        print("✅ AEGIS agent structure validated")
        
        # Test tools are properly configured
        if len(agent.tools) == 0:
            print("❌ No tools configured for agent")
            return False
        
        print(f"✅ Agent configured with {len(agent.tools)} tools")
        
        return True
        
    except Exception as e:
        print(f"❌ AEGIS agent test failed: {e}")
        return False

async def test_fastapi_imports():
    """Test that FastAPI application can be imported."""
    print("\n🔍 Testing FastAPI application imports...")
    
    try:
        from src.main import app
        print("✅ FastAPI application imported successfully")
        
        # Check that the app has the expected routes
        routes = [route.path for route in app.routes]
        expected_routes = [
            "/",
            "/v1/crypto/analyze",
            "/v1/crypto/chat",
            "/v1/crypto/supported-symbols"
        ]
        
        for route in expected_routes:
            if route not in routes:
                print(f"❌ Missing expected route: {route}")
                return False
        
        print("✅ All expected routes found")
        return True
        
    except Exception as e:
        print(f"❌ FastAPI import test failed: {e}")
        return False

async def run_all_tests():
    """Run all migration tests."""
    print("🚀 Starting AEGIS Migration Validation Tests")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Configuration Validation", test_config_validation),
        ("Tools Functionality", test_tools_functionality),
        ("AEGIS Agent", test_aegis_agent),
        ("FastAPI Imports", test_fastapi_imports)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! AEGIS migration is successful.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
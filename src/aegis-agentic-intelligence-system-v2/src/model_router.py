"""
Model Router for selecting the best Gemini model per task.

This module provides intelligent routing between different Gemini models
(Pro, Flash, Flash-Lite) based on task complexity and requirements.
"""

import logging
from enum import Enum
from typing import Optional, Dict, Any, Union
from pydantic import BaseModel
from langchain_google_genai import <PERSON>t<PERSON>oogleGenerativeAI
from langchain_google_vertexai import ChatVertexAI

from src.config import settings

logger = logging.getLogger(__name__)


class GeminiModel(str, Enum):
    """Available Gemini models with their characteristics."""
    
    # Gemini 2.0 Flash (Fastest, good balance)
    FLASH_2_0 = "gemini-2.0-flash-exp"
    
    # Gemini 1.5 Flash (Fast, efficient)
    FLASH_1_5 = "gemini-1.5-flash"
    
    # Gemini 1.5 Pro (Most capable, slower)
    PRO_1_5 = "gemini-1.5-pro"
    
    # Gemini 1.5 Flash-8B (Lightweight, fastest)
    FLASH_8B = "gemini-1.5-flash-8b"


class TaskComplexity(str, Enum):
    """Task complexity levels for model selection."""
    
    SIMPLE = "simple"           # Basic classification, simple Q&A
    MODERATE = "moderate"       # Content generation, analysis
    COMPLEX = "complex"         # Reasoning, multi-step tasks
    CRITICAL = "critical"       # High-stakes decisions, complex reasoning


class ModelSelectionCriteria(BaseModel):
    """Criteria for selecting the appropriate model."""
    
    task_complexity: TaskComplexity
    max_tokens: Optional[int] = None
    response_time_priority: bool = False  # True for speed over quality
    cost_optimization: bool = False       # True for cost over quality
    context_length: Optional[int] = None


class ModelRouter:
    """
    Intelligent router for selecting the best Gemini model for a given task.
    """
    
    # Model performance characteristics
    MODEL_CHARACTERISTICS = {
        GeminiModel.FLASH_2_0: {
            "speed": "fastest",
            "cost": "low",
            "capability": "high",
            "max_tokens": 8192,
            "context_window": 1048576,
            "best_for": ["general", "conversation", "classification"]
        },
        GeminiModel.FLASH_1_5: {
            "speed": "fast", 
            "cost": "low",
            "capability": "good",
            "max_tokens": 8192,
            "context_window": 1048576,
            "best_for": ["content_generation", "analysis", "coding"]
        },
        GeminiModel.PRO_1_5: {
            "speed": "moderate",
            "cost": "high", 
            "capability": "highest",
            "max_tokens": 8192,
            "context_window": 2097152,
            "best_for": ["reasoning", "complex_analysis", "research"]
        },
        GeminiModel.FLASH_8B: {
            "speed": "very_fast",
            "cost": "very_low",
            "capability": "moderate",
            "max_tokens": 8192,
            "context_window": 1048576,
            "best_for": ["simple_tasks", "classification", "lightweight"]
        }
    }
    
    def __init__(self):
        self._model_cache: Dict[str, Union[ChatGoogleGenerativeAI, ChatVertexAI]] = {}
    
    def select_model(self, criteria: ModelSelectionCriteria) -> GeminiModel:
        """
        Select the most appropriate model based on task criteria.
        
        Args:
            criteria: Selection criteria for the task
            
        Returns:
            The most appropriate Gemini model
        """
        logger.info(f"Selecting model for task complexity: {criteria.task_complexity}")
        
        # Speed-first selection
        if criteria.response_time_priority:
            if criteria.task_complexity in [TaskComplexity.SIMPLE]:
                return GeminiModel.FLASH_8B
            else:
                return GeminiModel.FLASH_2_0
        
        # Cost-first selection
        if criteria.cost_optimization:
            if criteria.task_complexity in [TaskComplexity.SIMPLE]:
                return GeminiModel.FLASH_8B
            elif criteria.task_complexity == TaskComplexity.MODERATE:
                return GeminiModel.FLASH_1_5
            else:
                return GeminiModel.FLASH_2_0  # Balance cost and capability
        
        # Quality-first selection (default)
        complexity_to_model = {
            TaskComplexity.SIMPLE: GeminiModel.FLASH_8B,
            TaskComplexity.MODERATE: GeminiModel.FLASH_2_0,
            TaskComplexity.COMPLEX: GeminiModel.FLASH_1_5,
            TaskComplexity.CRITICAL: GeminiModel.PRO_1_5
        }
        
        selected_model = complexity_to_model[criteria.task_complexity]
        
        # Override for high token requirements
        if criteria.max_tokens and criteria.max_tokens > 4000:
            if selected_model == GeminiModel.FLASH_8B:
                selected_model = GeminiModel.FLASH_2_0
        
        # Override for large context requirements
        if criteria.context_length and criteria.context_length > 500000:
            if selected_model in [GeminiModel.FLASH_8B, GeminiModel.FLASH_1_5]:
                selected_model = GeminiModel.PRO_1_5
        
        logger.info(f"Selected model: {selected_model}")
        return selected_model
    
    def get_model_instance(
        self, 
        model: GeminiModel,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        use_vertex: bool = False
    ) -> Union[ChatGoogleGenerativeAI, ChatVertexAI]:
        """
        Get a configured model instance.
        
        Args:
            model: The Gemini model to instantiate
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            use_vertex: Whether to use Vertex AI instead of direct API
            
        Returns:
            Configured model instance
        """
        cache_key = f"{model}_{temperature}_{max_tokens}_{use_vertex}"
        
        if cache_key in self._model_cache:
            return self._model_cache[cache_key]
        
        # Set max tokens based on model characteristics if not provided
        if max_tokens is None:
            max_tokens = min(
                self.MODEL_CHARACTERISTICS[model]["max_tokens"],
                settings.gemini_max_tokens
            )
        
        try:
            if use_vertex:
                # Use Vertex AI for enterprise deployments
                instance = ChatVertexAI(
                    model_name=model.value,
                    project=settings.gcp_project_id,
                    location=settings.gcp_region,
                    temperature=temperature,
                    max_output_tokens=max_tokens
                )
            else:
                # Use direct API for development and simpler deployments
                instance = ChatGoogleGenerativeAI(
                    model=model.value,
                    google_api_key=settings.google_api_key,
                    temperature=temperature,
                    max_output_tokens=max_tokens,
                    convert_system_message_to_human=True
                )
            
            self._model_cache[cache_key] = instance
            logger.info(f"Created model instance: {model} (use_vertex={use_vertex})")
            return instance
            
        except Exception as e:
            logger.error(f"Failed to create model instance for {model}: {e}")
            raise
    
    def get_model_for_task(
        self,
        task_type: str,
        input_length: Optional[int] = None,
        priority: str = "quality"  # "quality", "speed", "cost"
    ) -> Union[ChatGoogleGenerativeAI, ChatVertexAI]:
        """
        Convenience method to get a model instance for a specific task type.
        
        Args:
            task_type: Type of task (e.g., "classification", "generation", "reasoning")
            input_length: Length of input text (characters)
            priority: Priority for selection ("quality", "speed", "cost")
            
        Returns:
            Configured model instance
        """
        # Map task types to complexity
        task_complexity_map = {
            "classification": TaskComplexity.SIMPLE,
            "intent_detection": TaskComplexity.SIMPLE,
            "sentiment_analysis": TaskComplexity.SIMPLE,
            "content_generation": TaskComplexity.MODERATE,
            "email_reply": TaskComplexity.MODERATE,
            "summarization": TaskComplexity.MODERATE,
            "reasoning": TaskComplexity.COMPLEX,
            "research": TaskComplexity.COMPLEX,
            "analysis": TaskComplexity.COMPLEX,
            "critical_decision": TaskComplexity.CRITICAL,
            "complex_reasoning": TaskComplexity.CRITICAL
        }
        
        complexity = task_complexity_map.get(task_type, TaskComplexity.MODERATE)
        
        # Set selection criteria based on priority
        criteria = ModelSelectionCriteria(
            task_complexity=complexity,
            response_time_priority=(priority == "speed"),
            cost_optimization=(priority == "cost"),
            context_length=input_length
        )
        
        model = self.select_model(criteria)
        return self.get_model_instance(model)
    
    def clear_cache(self):
        """Clear the model instance cache."""
        self._model_cache.clear()
        logger.info("Model instance cache cleared")


# Global model router instance
model_router = ModelRouter()


# Convenience functions for common use cases
def get_fast_model() -> Union[ChatGoogleGenerativeAI, ChatVertexAI]:
    """Get the fastest available model for simple tasks."""
    return model_router.get_model_for_task("classification", priority="speed")


def get_quality_model() -> Union[ChatGoogleGenerativeAI, ChatVertexAI]:
    """Get the highest quality model for complex tasks."""
    return model_router.get_model_for_task("complex_reasoning", priority="quality")


def get_balanced_model() -> Union[ChatGoogleGenerativeAI, ChatVertexAI]:
    """Get a balanced model for general-purpose tasks."""
    return model_router.get_model_for_task("content_generation", priority="quality")
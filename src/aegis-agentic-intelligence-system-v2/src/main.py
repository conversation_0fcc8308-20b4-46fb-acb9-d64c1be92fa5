"""
FastAPI application entrypoint for the Agent Template.

This module handles HTTP requests and serves as the main API interface
for the agent, including webhooks, task processing, and AI service endpoints.
"""

import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import FastAPI, HTTPException, Request, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from src.config import settings, load_production_secrets
from src.model_router import model_router
from src.agents.template_agent.graph import agent_graph
from src.agents.template_agent.state import AgentState, TaskRequest, TaskResponse
from src.agents.template_agent.aegis_graph import aegis_agent, process_crypto_analysis, analyze_symbol
from src.services.database_client import DatabaseClient
from src.async_tasks.tasks import process_background_task

# Configure logging
logger = logging.getLogger(__name__)

# Load production secrets if in production environment
if settings.is_production:
    production_secrets = load_production_secrets()
    logger.info("Production secrets loaded from Google Secret Manager")

# --- FastAPI App Initialization ---
app = FastAPI(
    title="AEGIS - Agentic Intelligence System",
    description="Cryptocurrency analysis and regulatory monitoring AI agent with LangGraph workflows and GCP integration",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.is_development else ["https://your-domain.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database client
db_client = DatabaseClient()


# --- Request/Response Models ---

class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    agent: str
    version: str
    timestamp: str
    environment: str
    services: Dict[str, str]


class AIRequest(BaseModel):
    """General AI service request model."""
    prompt: str
    context: Optional[str] = None
    task_type: str = "general"
    priority: str = "quality"  # "quality", "speed", "cost"
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None


class AIResponse(BaseModel):
    """AI service response model."""
    response: str
    success: bool
    model_used: str
    processing_time_ms: int
    error: Optional[str] = None


class WebhookPayload(BaseModel):
    """Generic webhook payload model."""
    event_type: str
    data: Dict[str, Any]
    timestamp: Optional[str] = None


class CryptoAnalysisRequest(BaseModel):
    """Cryptocurrency analysis request model."""
    symbol: str
    analysis_type: str = "comprehensive"  # "comprehensive", "technical", "regulatory"
    timeframe: str = "7d"  # "1d", "7d", "30d"
    include_sentiment: bool = True
    include_on_chain: bool = True
    include_defi: bool = False


class CryptoAnalysisResponse(BaseModel):
    """Cryptocurrency analysis response model."""
    symbol: str
    analysis_type: str
    response: str
    analysis_results: Dict[str, Any]
    processing_time_ms: int
    timestamp: str
    session_id: Optional[str] = None
    error: Optional[str] = None


class ChatRequest(BaseModel):
    """Chat request model for conversational crypto analysis."""
    message: str
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """Chat response model."""
    response: str
    session_id: str
    analysis_results: Dict[str, Any]
    timestamp: str
    message_count: int
    error: Optional[str] = None


# --- Event Handlers ---

@app.on_event("startup")
async def startup_event():
    """Initialize services on application startup."""
    logger.info("Starting Agent Template application...")
    
    try:
        # Initialize database connection
        await db_client.initialize()
        logger.info("Database client initialized")
        
        # Warm up model router
        model_router.get_fast_model()
        logger.info("Model router warmed up")
        
        logger.info("Agent Template startup completed successfully")
        
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on application shutdown."""
    logger.info("Shutting down Agent Template application...")
    
    try:
        # Clean up database connections
        await db_client.close()
        
        # Clear model cache
        model_router.clear_cache()
        
        logger.info("Agent Template shutdown completed")
        
    except Exception as e:
        logger.error(f"Shutdown error: {e}")


# --- API Endpoints ---

@app.get("/", response_model=HealthResponse, summary="Health Check")
async def health_check():
    """Comprehensive health check endpoint."""
    try:
        # Check database connectivity
        db_status = await db_client.health_check()
        
        # Check model availability
        try:
            test_model = model_router.get_fast_model()
            model_status = "operational"
        except Exception:
            model_status = "degraded"
        
        services = {
            "database": "operational" if db_status else "degraded",
            "model_router": model_status,
            "agent_graph": "operational"
        }
        
        overall_status = "healthy" if all(s == "operational" for s in services.values()) else "degraded"
        
        return HealthResponse(
            status=overall_status,
            agent="AEGIS",
            version="2.0.0",
            timestamp=datetime.utcnow().isoformat(),
            environment=settings.environment,
            services=services
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            agent="AEGIS",
            version="2.0.0",
            timestamp=datetime.utcnow().isoformat(),
            environment=settings.environment,
            services={"error": str(e)}
        )


@app.post("/v1/webhook", status_code=status.HTTP_200_OK, summary="Generic Webhook Handler")
async def webhook_handler(
    payload: WebhookPayload,
    background_tasks: BackgroundTasks,
    request: Request
):
    """
    Generic webhook handler for external integrations.
    
    Processes incoming webhooks and queues background tasks as needed.
    """
    logger.info(f"Received webhook: {payload.event_type}")
    
    try:
        # Log webhook details
        logger.info(f"Webhook payload: {payload.dict()}")
        
        # Queue background processing if needed
        if payload.event_type in ["data_update", "external_trigger"]:
            background_tasks.add_task(
                process_background_task,
                payload.event_type,
                payload.data
            )
            logger.info(f"Queued background task for {payload.event_type}")
        
        return {"status": "received", "event_type": payload.event_type}
        
    except Exception as e:
        logger.error(f"Webhook processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Webhook processing failed: {str(e)}")


@app.post("/v1/process-task", response_model=TaskResponse, summary="Process Agent Task")
async def process_agent_task(task_request: TaskRequest):
    """
    Process a task using the LangGraph agent workflow.
    
    This endpoint demonstrates how to use the agent graph for task processing.
    """
    start_time = time.time()
    logger.info(f"Processing agent task: {task_request.task_type}")
    
    try:
        # Create initial agent state
        initial_state = AgentState(
            task_id=task_request.task_id,
            task_type=task_request.task_type,
            input_data=task_request.input_data,
            context=task_request.context or {}
        )
        
        # Run the agent graph
        result = await agent_graph.ainvoke(initial_state)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.info(f"Task {task_request.task_id} completed in {processing_time}ms")
        
        return TaskResponse(
            task_id=task_request.task_id,
            success=True,
            result=result,
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"Task processing failed: {e}")
        
        return TaskResponse(
            task_id=task_request.task_id,
            success=False,
            result={},
            processing_time_ms=processing_time,
            error=str(e)
        )


@app.post("/v1/ai/generate", response_model=AIResponse, summary="AI Generation Service")
async def ai_generation_service(request: AIRequest):
    """
    Centralized AI service endpoint with intelligent model routing.
    
    This endpoint provides AI capabilities with automatic model selection
    based on task type and performance requirements.
    """
    start_time = time.time()
    logger.info(f"AI generation request: {request.task_type}")
    
    try:
        # Get appropriate model for the task
        model = model_router.get_model_for_task(
            task_type=request.task_type,
            input_length=len(request.prompt) if request.prompt else 0,
            priority=request.priority
        )
        
        # Configure model parameters
        temperature = request.temperature or settings.gemini_temperature
        max_tokens = request.max_tokens or settings.gemini_max_tokens
        
        # Prepare prompt
        if request.context:
            full_prompt = f"Context: {request.context}\n\nPrompt: {request.prompt}"
        else:
            full_prompt = request.prompt
        
        # Generate response
        response = await model.ainvoke(full_prompt)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.info(f"AI generation completed in {processing_time}ms")
        
        return AIResponse(
            response=response.content,
            success=True,
            model_used=model.model_name if hasattr(model, 'model_name') else "gemini",
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"AI generation failed: {e}")
        
        return AIResponse(
            response="",
            success=False,
            model_used="error",
            processing_time_ms=processing_time,
            error=str(e)
        )


@app.post("/v1/crypto/analyze", response_model=CryptoAnalysisResponse, summary="Analyze Cryptocurrency")
async def analyze_cryptocurrency(request: CryptoAnalysisRequest):
    """
    Perform comprehensive cryptocurrency analysis using AEGIS agent.
    
    This endpoint provides detailed analysis including market data, sentiment,
    technical indicators, and regulatory insights.
    """
    start_time = time.time()
    logger.info(f"Analyzing cryptocurrency: {request.symbol} ({request.analysis_type})")
    
    try:
        # Use AEGIS agent for analysis
        result = await analyze_symbol(request.symbol, request.analysis_type)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.info(f"Cryptocurrency analysis completed in {processing_time}ms")
        
        return CryptoAnalysisResponse(
            symbol=request.symbol,
            analysis_type=request.analysis_type,
            response=result.get("response", ""),
            analysis_results=result.get("analysis_results", {}),
            processing_time_ms=processing_time,
            timestamp=datetime.utcnow().isoformat(),
            session_id=result.get("session_id"),
            error=result.get("error")
        )
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"Cryptocurrency analysis failed: {e}")
        
        return CryptoAnalysisResponse(
            symbol=request.symbol,
            analysis_type=request.analysis_type,
            response="",
            analysis_results={},
            processing_time_ms=processing_time,
            timestamp=datetime.utcnow().isoformat(),
            error=str(e)
        )


@app.post("/v1/crypto/chat", response_model=ChatResponse, summary="Crypto Analysis Chat")
async def crypto_analysis_chat(request: ChatRequest):
    """
    Conversational cryptocurrency analysis endpoint.
    
    This endpoint allows users to chat with the AEGIS agent about
    cryptocurrency analysis, market trends, and regulatory insights.
    """
    start_time = time.time()
    logger.info(f"Processing crypto chat message: {request.message[:100]}...")
    
    try:
        # Process message through AEGIS agent
        result = await process_crypto_analysis(request.message, request.session_id)
        
        processing_time = int((time.time() - start_time) * 1000)
        
        logger.info(f"Crypto chat completed in {processing_time}ms")
        
        return ChatResponse(
            response=result.get("response", ""),
            session_id=result.get("session_id", "default"),
            analysis_results=result.get("analysis_results", {}),
            timestamp=result.get("timestamp", datetime.utcnow().isoformat()),
            message_count=result.get("message_count", 0),
            error=result.get("error")
        )
        
    except Exception as e:
        processing_time = int((time.time() - start_time) * 1000)
        logger.error(f"Crypto chat failed: {e}")
        
        return ChatResponse(
            response=f"I encountered an error: {str(e)}",
            session_id=request.session_id or "default",
            analysis_results={},
            timestamp=datetime.utcnow().isoformat(),
            message_count=0,
            error=str(e)
        )


@app.get("/v1/crypto/supported-symbols", summary="Get Supported Symbols")
async def get_supported_symbols():
    """
    Get list of supported cryptocurrency symbols.
    
    Returns a list of cryptocurrency symbols that can be analyzed.
    """
    try:
        # In production, this would be dynamically generated
        supported_symbols = [
            {"symbol": "BTC", "name": "Bitcoin", "category": "cryptocurrency"},
            {"symbol": "ETH", "name": "Ethereum", "category": "cryptocurrency"},
            {"symbol": "SOL", "name": "Solana", "category": "cryptocurrency"},
            {"symbol": "ADA", "name": "Cardano", "category": "cryptocurrency"},
            {"symbol": "MATIC", "name": "Polygon", "category": "cryptocurrency"},
            {"symbol": "DOT", "name": "Polkadot", "category": "cryptocurrency"},
            {"symbol": "AVAX", "name": "Avalanche", "category": "cryptocurrency"},
            {"symbol": "LINK", "name": "Chainlink", "category": "cryptocurrency"},
            {"symbol": "UNI", "name": "Uniswap", "category": "defi"},
            {"symbol": "AAVE", "name": "Aave", "category": "defi"},
        ]
        
        return {
            "symbols": supported_symbols,
            "total_count": len(supported_symbols),
            "categories": ["cryptocurrency", "defi"],
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting supported symbols: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting supported symbols: {str(e)}")


@app.get("/v1/status", summary="Detailed Status")
async def get_detailed_status():
    """Get detailed agent status and metrics."""
    try:
        # Get model router statistics
        model_cache_size = len(model_router._model_cache)
        
        # Get database statistics
        db_stats = await db_client.get_statistics()
        
        return {
            "agent": "AEGIS",
            "status": "operational",
            "environment": settings.environment,
            "model_cache_size": model_cache_size,
            "database_stats": db_stats,
            "configuration": {
                "gcp_project": settings.gcp_project_id,
                "gcp_region": settings.gcp_region,
                "default_model": settings.gemini_model
            },
            "capabilities": {
                "crypto_analysis": True,
                "regulatory_monitoring": True,
                "technical_analysis": True,
                "sentiment_analysis": True,
                "defi_metrics": True,
                "portfolio_risk": True
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return {
            "agent": "AEGIS",
            "status": "degraded",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


# --- Error Handlers ---

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with detailed logging."""
    logger.error(f"HTTP {exc.status_code}: {exc.detail} - {request.url}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions with detailed logging."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "status_code": 500}
    )


# --- Application Entry Point ---
if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8080,
        reload=settings.is_development,
        log_level=settings.log_level.lower()
    )
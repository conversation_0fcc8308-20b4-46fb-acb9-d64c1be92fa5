"""
Database client for handling connections to Cloud SQL (PostgreSQL).

This module provides a thread-safe, async-compatible database client
with connection pooling and automatic retry logic.
"""

import logging
import asyncio
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
import asyncpg
from asyncpg import Pool
from google.cloud.sql.python_connector import connector

from src.config import settings

logger = logging.getLogger(__name__)


class DatabaseClient:
    """
    Async PostgreSQL client with Cloud SQL connector support.
    
    Provides connection pooling, automatic retries, and health monitoring.
    """
    
    def __init__(self):
        self._pool: Optional[Pool] = None
        self._connector_instance = None
        
    async def initialize(self):
        """Initialize the database connection pool."""
        try:
            if settings.is_production:
                # Use Cloud SQL connector for production
                await self._init_cloud_sql_pool()
            else:
                # Use direct connection for development
                await self._init_direct_pool()
                
            logger.info("Database client initialized successfully")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise
    
    async def _init_cloud_sql_pool(self):
        """Initialize connection pool using Cloud SQL connector."""
        try:
            # Initialize Cloud SQL connector
            self._connector_instance = connector.Connector()
            
            # Create connection function for Cloud SQL
            async def getconn() -> asyncpg.Connection:
                conn = await self._connector_instance.connect_async(
                    instance_connection_string=settings.postgres_host,
                    driver="asyncpg",
                    user=settings.postgres_user,
                    password=settings.postgres_password,
                    db=settings.postgres_db
                )
                return conn
            
            # Create connection pool
            self._pool = await asyncpg.create_pool(
                min_size=1,
                max_size=10,
                connection_class=asyncpg.Connection,
                init=getconn
            )
            
            logger.info("Cloud SQL connection pool created")
            
        except Exception as e:
            logger.error(f"Cloud SQL pool initialization failed: {e}")
            raise
    
    async def _init_direct_pool(self):
        """Initialize connection pool using direct connection."""
        try:
            # Create connection pool for local development
            self._pool = await asyncpg.create_pool(
                host=settings.postgres_host,
                port=settings.postgres_port,
                user=settings.postgres_user,
                password=settings.postgres_password,
                database=settings.postgres_db,
                min_size=1,
                max_size=5,
                command_timeout=60
            )
            
            logger.info("Direct connection pool created")
            
        except Exception as e:
            logger.error(f"Direct pool initialization failed: {e}")
            raise
    
    @asynccontextmanager
    async def get_connection(self):
        """
        Get a database connection from the pool.
        
        Usage:
            async with db_client.get_connection() as conn:
                result = await conn.fetch("SELECT * FROM table")
        """
        if not self._pool:
            raise RuntimeError("Database client not initialized")
        
        conn = None
        try:
            conn = await self._pool.acquire()
            yield conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                await self._pool.release(conn)
    
    async def execute_query(
        self,
        query: str,
        *args,
        fetch: str = "all"  # "all", "one", "none"
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Execute a query and return results.
        
        Args:
            query: SQL query to execute
            *args: Query parameters
            fetch: Type of fetch operation ("all", "one", "none")
            
        Returns:
            Query results or None
        """
        async with self.get_connection() as conn:
            try:
                if fetch == "all":
                    rows = await conn.fetch(query, *args)
                    return [dict(row) for row in rows]
                elif fetch == "one":
                    row = await conn.fetchrow(query, *args)
                    return dict(row) if row else None
                elif fetch == "none":
                    await conn.execute(query, *args)
                    return None
                else:
                    raise ValueError(f"Invalid fetch type: {fetch}")
                    
            except Exception as e:
                logger.error(f"Query execution failed: {e}")
                raise
    
    async def execute_transaction(self, queries: List[tuple]) -> bool:
        """
        Execute multiple queries in a transaction.
        
        Args:
            queries: List of (query, args) tuples
            
        Returns:
            True if transaction succeeded, False otherwise
        """
        async with self.get_connection() as conn:
            try:
                async with conn.transaction():
                    for query, args in queries:
                        await conn.execute(query, *args)
                
                logger.info(f"Transaction completed successfully ({len(queries)} queries)")
                return True
                
            except Exception as e:
                logger.error(f"Transaction failed: {e}")
                return False
    
    async def health_check(self) -> bool:
        """
        Check database connectivity and health.
        
        Returns:
            True if database is healthy, False otherwise
        """
        try:
            result = await self.execute_query("SELECT 1 as health_check", fetch="one")
            return result is not None and result.get("health_check") == 1
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        Get database connection pool statistics.
        
        Returns:
            Dictionary with pool statistics
        """
        if not self._pool:
            return {"status": "not_initialized"}
        
        try:
            return {
                "pool_size": self._pool.get_size(),
                "idle_connections": self._pool.get_idle_size(),
                "max_size": self._pool.get_max_size(),
                "min_size": self._pool.get_min_size(),
                "status": "operational"
            }
            
        except Exception as e:
            logger.error(f"Failed to get database statistics: {e}")
            return {"status": "error", "error": str(e)}
    
    async def close(self):
        """Close the database connection pool."""
        try:
            if self._pool:
                await self._pool.close()
                self._pool = None
                logger.info("Database connection pool closed")
            
            if self._connector_instance:
                await self._connector_instance.close_async()
                self._connector_instance = None
                logger.info("Cloud SQL connector closed")
                
        except Exception as e:
            logger.error(f"Database cleanup error: {e}")


# Convenience functions for common database operations

async def create_table_if_not_exists(db_client: DatabaseClient, table_name: str, schema: str):
    """
    Create a table if it doesn't exist.
    
    Args:
        db_client: Database client instance
        table_name: Name of the table
        schema: Table schema definition
    """
    query = f"""
    CREATE TABLE IF NOT EXISTS {table_name} (
        {schema}
    );
    """
    
    await db_client.execute_query(query, fetch="none")
    logger.info(f"Ensured table exists: {table_name}")


async def insert_record(
    db_client: DatabaseClient,
    table_name: str,
    data: Dict[str, Any]
) -> bool:
    """
    Insert a record into a table.
    
    Args:
        db_client: Database client instance
        table_name: Name of the table
        data: Dictionary of column-value pairs
        
    Returns:
        True if insert succeeded, False otherwise
    """
    if not data:
        return False
    
    columns = list(data.keys())
    values = list(data.values())
    placeholders = [f"${i+1}" for i in range(len(values))]
    
    query = f"""
    INSERT INTO {table_name} ({', '.join(columns)})
    VALUES ({', '.join(placeholders)})
    """
    
    try:
        await db_client.execute_query(query, *values, fetch="none")
        return True
    except Exception as e:
        logger.error(f"Insert failed for table {table_name}: {e}")
        return False


async def update_record(
    db_client: DatabaseClient,
    table_name: str,
    data: Dict[str, Any],
    where_clause: str,
    where_params: List[Any]
) -> bool:
    """
    Update records in a table.
    
    Args:
        db_client: Database client instance
        table_name: Name of the table
        data: Dictionary of column-value pairs to update
        where_clause: WHERE clause (without WHERE keyword)
        where_params: Parameters for WHERE clause
        
    Returns:
        True if update succeeded, False otherwise
    """
    if not data:
        return False
    
    # Build SET clause
    set_clauses = []
    values = []
    param_index = 1
    
    for column, value in data.items():
        set_clauses.append(f"{column} = ${param_index}")
        values.append(value)
        param_index += 1
    
    # Add WHERE parameters
    values.extend(where_params)
    
    query = f"""
    UPDATE {table_name}
    SET {', '.join(set_clauses)}
    WHERE {where_clause}
    """
    
    try:
        await db_client.execute_query(query, *values, fetch="none")
        return True
    except Exception as e:
        logger.error(f"Update failed for table {table_name}: {e}")
        return False
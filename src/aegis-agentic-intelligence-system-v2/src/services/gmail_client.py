"""
Gmail API client for email operations.

This module provides a thread-safe Gmail client with authentication,
email reading, sending, and management capabilities.
"""

import logging
import pickle
import os
from typing import Optional, List, Dict, Any
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import InstalledAppFlow
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import base64
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from src.config import settings

logger = logging.getLogger(__name__)


class GmailClient:
    """
    Gmail API client with authentication and email operations.
    
    Supports both OAuth2 and service account authentication modes.
    """
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify'
    ]
    
    def __init__(self, credentials_file: str = "credentials.json", token_file: str = "token.json"):
        self.credentials_file = credentials_file
        self.token_file = token_file
        self.service = None
        self._authenticated = False
    
    async def authenticate(self) -> bool:
        """
        Authenticate with Gmail API using OAuth2 flow.
        
        Returns:
            True if authentication succeeded, False otherwise
        """
        try:
            creds = None
            
            # Load existing token
            if os.path.exists(self.token_file):
                with open(self.token_file, 'rb') as token:
                    creds = pickle.load(token)
            
            # If there are no (valid) credentials available, run OAuth flow
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_file):
                        logger.error(f"Credentials file not found: {self.credentials_file}")
                        return False
                    
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, self.SCOPES
                    )
                    creds = flow.run_local_server(port=0)
                
                # Save the credentials for the next run
                with open(self.token_file, 'wb') as token:
                    pickle.dump(creds, token)
            
            # Build the Gmail service
            self.service = build('gmail', 'v1', credentials=creds)
            self._authenticated = True
            
            logger.info("Gmail authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Gmail authentication failed: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if the client is authenticated."""
        return self._authenticated and self.service is not None
    
    async def get_messages(
        self,
        query: str = "",
        max_results: int = 10,
        user_id: str = "me"
    ) -> List[Dict[str, Any]]:
        """
        Get Gmail messages based on search query.
        
        Args:
            query: Gmail search query (e.g., "is:unread", "from:<EMAIL>")
            max_results: Maximum number of messages to return
            user_id: User ID (default: "me")
            
        Returns:
            List of message dictionaries
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            # Get message list
            results = self.service.users().messages().list(
                userId=user_id,
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            
            # Get full message details
            detailed_messages = []
            for message in messages:
                msg_detail = await self.get_message(message['id'], user_id)
                if msg_detail:
                    detailed_messages.append(msg_detail)
            
            logger.info(f"Retrieved {len(detailed_messages)} messages")
            return detailed_messages
            
        except HttpError as e:
            logger.error(f"Gmail API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to get messages: {e}")
            return []
    
    async def get_message(self, message_id: str, user_id: str = "me") -> Optional[Dict[str, Any]]:
        """
        Get a specific Gmail message by ID.
        
        Args:
            message_id: Gmail message ID
            user_id: User ID (default: "me")
            
        Returns:
            Message dictionary or None
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            message = self.service.users().messages().get(
                userId=user_id,
                id=message_id,
                format='full'
            ).execute()
            
            # Parse message details
            parsed_message = self._parse_message(message)
            logger.info(f"Retrieved message: {message_id}")
            
            return parsed_message
            
        except HttpError as e:
            logger.error(f"Gmail API error for message {message_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to get message {message_id}: {e}")
            return None
    
    def _parse_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse Gmail message into a standardized format.
        
        Args:
            message: Raw Gmail message object
            
        Returns:
            Parsed message dictionary
        """
        try:
            headers = message['payload'].get('headers', [])
            header_dict = {header['name'].lower(): header['value'] for header in headers}
            
            # Extract message body
            body = self._extract_message_body(message['payload'])
            
            return {
                'id': message['id'],
                'thread_id': message['threadId'],
                'label_ids': message.get('labelIds', []),
                'snippet': message.get('snippet', ''),
                'history_id': message.get('historyId'),
                'internal_date': message.get('internalDate'),
                'subject': header_dict.get('subject', ''),
                'from': header_dict.get('from', ''),
                'to': header_dict.get('to', ''),
                'cc': header_dict.get('cc', ''),
                'bcc': header_dict.get('bcc', ''),
                'date': header_dict.get('date', ''),
                'message_id': header_dict.get('message-id', ''),
                'body': body,
                'headers': header_dict
            }
            
        except Exception as e:
            logger.error(f"Failed to parse message: {e}")
            return {}
    
    def _extract_message_body(self, payload: Dict[str, Any]) -> str:
        """
        Extract message body from Gmail payload.
        
        Args:
            payload: Gmail message payload
            
        Returns:
            Message body as string
        """
        try:
            # Check if message has parts (multipart)
            if 'parts' in payload:
                for part in payload['parts']:
                    if part['mimeType'] == 'text/plain':
                        data = part['body'].get('data')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8')
                    elif part['mimeType'] == 'text/html':
                        # Fallback to HTML if no plain text
                        data = part['body'].get('data')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8')
            else:
                # Single part message
                if payload['mimeType'] in ['text/plain', 'text/html']:
                    data = payload['body'].get('data')
                    if data:
                        return base64.urlsafe_b64decode(data).decode('utf-8')
            
            return ""
            
        except Exception as e:
            logger.error(f"Failed to extract message body: {e}")
            return ""
    
    async def send_message(
        self,
        to: str,
        subject: str,
        body: str,
        from_email: Optional[str] = None,
        reply_to: Optional[str] = None,
        thread_id: Optional[str] = None,
        user_id: str = "me"
    ) -> Optional[str]:
        """
        Send an email message.
        
        Args:
            to: Recipient email address
            subject: Email subject
            body: Email body
            from_email: Sender email (optional)
            reply_to: Reply-to address (optional)
            thread_id: Thread ID for reply (optional)
            user_id: User ID (default: "me")
            
        Returns:
            Message ID if successful, None otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            # Create message
            message = MIMEText(body)
            message['to'] = to
            message['subject'] = subject
            
            if from_email:
                message['from'] = from_email
            if reply_to:
                message['reply-to'] = reply_to
            
            # Encode message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            # Prepare send request
            send_request = {'raw': raw_message}
            if thread_id:
                send_request['threadId'] = thread_id
            
            # Send message
            result = self.service.users().messages().send(
                userId=user_id,
                body=send_request
            ).execute()
            
            message_id = result.get('id')
            logger.info(f"Message sent successfully: {message_id}")
            
            return message_id
            
        except HttpError as e:
            logger.error(f"Gmail API error sending message: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return None
    
    async def mark_as_read(self, message_id: str, user_id: str = "me") -> bool:
        """
        Mark a message as read.
        
        Args:
            message_id: Gmail message ID
            user_id: User ID (default: "me")
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            self.service.users().messages().modify(
                userId=user_id,
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            
            logger.info(f"Message marked as read: {message_id}")
            return True
            
        except HttpError as e:
            logger.error(f"Gmail API error marking message as read: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to mark message as read: {e}")
            return False
    
    async def add_label(self, message_id: str, label_id: str, user_id: str = "me") -> bool:
        """
        Add a label to a message.
        
        Args:
            message_id: Gmail message ID
            label_id: Label ID to add
            user_id: User ID (default: "me")
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            self.service.users().messages().modify(
                userId=user_id,
                id=message_id,
                body={'addLabelIds': [label_id]}
            ).execute()
            
            logger.info(f"Label {label_id} added to message: {message_id}")
            return True
            
        except HttpError as e:
            logger.error(f"Gmail API error adding label: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to add label: {e}")
            return False
    
    async def get_labels(self, user_id: str = "me") -> List[Dict[str, Any]]:
        """
        Get all Gmail labels.
        
        Args:
            user_id: User ID (default: "me")
            
        Returns:
            List of label dictionaries
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            results = self.service.users().labels().list(userId=user_id).execute()
            labels = results.get('labels', [])
            
            logger.info(f"Retrieved {len(labels)} labels")
            return labels
            
        except HttpError as e:
            logger.error(f"Gmail API error getting labels: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to get labels: {e}")
            return []


# Convenience functions for common Gmail operations

async def create_gmail_client() -> GmailClient:
    """
    Create and authenticate a Gmail client.
    
    Returns:
        Authenticated Gmail client
    """
    client = GmailClient()
    success = await client.authenticate()
    
    if not success:
        raise RuntimeError("Failed to authenticate Gmail client")
    
    return client


async def send_simple_email(to: str, subject: str, body: str) -> bool:
    """
    Send a simple email using Gmail API.
    
    Args:
        to: Recipient email address
        subject: Email subject
        body: Email body
        
    Returns:
        True if successful, False otherwise
    """
    try:
        client = await create_gmail_client()
        message_id = await client.send_message(to, subject, body)
        return message_id is not None
        
    except Exception as e:
        logger.error(f"Failed to send simple email: {e}")
        return False
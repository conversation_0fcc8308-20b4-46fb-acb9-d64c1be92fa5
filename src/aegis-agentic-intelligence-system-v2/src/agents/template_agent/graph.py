"""
LangGraph workflow definition for the template agent.

This module defines the complete agent workflow using LangGraph's
StateGraph with conditional routing and error handling.
"""

import logging
from typing import Literal
from langgraph.graph import StateGraph, END

from src.agents.template_agent.state import AgentState, TaskStatus
from src.agents.template_agent.nodes import (
    initialize_task,
    classify_intent,
    analyze_content,
    generate_response,
    finalize_task
)
from src.checkpointer.redis import redis_checkpointer

logger = logging.getLogger(__name__)


def should_analyze_content(state: AgentState) -> Literal["analyze_content", "generate_response"]:
    """
    Determine whether to perform detailed content analysis.
    
    Routes to analysis if the intent classification suggests it's needed,
    or if the task type requires analysis.
    """
    
    # Always analyze if it's an analysis task
    if state.task_type.value == "analysis":
        logger.info("Routing to content analysis (analysis task type)")
        return "analyze_content"
    
    # Check intent classification result
    if state.intent_classification:
        if state.intent_classification.requires_action:
            logger.info("Routing to content analysis (intent requires action)")
            return "analyze_content"
        
        # Analyze if confidence is low (need more understanding)
        if state.intent_classification.confidence < 0.7:
            logger.info("Routing to content analysis (low confidence)")
            return "analyze_content"
    
    # For conversation tasks, always do some analysis
    if state.task_type.value == "conversation":
        logger.info("Routing to content analysis (conversation task)")
        return "analyze_content"
    
    # Default to direct response generation
    logger.info("Routing directly to response generation")
    return "generate_response"


def should_continue_processing(state: AgentState) -> Literal["finalize_task", "classify_intent"]:
    """
    Determine whether to continue processing or finalize the task.
    
    Handles retry logic and error conditions.
    """
    
    # If there are critical errors, finalize
    if state.status == TaskStatus.FAILED:
        logger.info("Finalizing due to failed status")
        return "finalize_task"
    
    # If processing is marked as complete, finalize
    if not state.should_continue:
        logger.info("Finalizing - processing marked complete")
        return "finalize_task"
    
    # If we have a final result, finalize
    if state.final_result is not None:
        logger.info("Finalizing - final result available")
        return "finalize_task"
    
    # Check if we should retry on errors
    if state.errors and state.should_retry():
        logger.warning(f"Retrying task {state.task_id} (attempt {state.retry_count + 1})")
        state.increment_retry()
        state.errors.clear()  # Clear errors for retry
        return "classify_intent"
    
    # If we have too many retries, finalize with failure
    if state.retry_count >= state.max_retries:
        logger.error(f"Max retries exceeded for task {state.task_id}")
        state.update_status(TaskStatus.FAILED)
        return "finalize_task"
    
    # Default to finalization
    logger.info("Finalizing task processing")
    return "finalize_task"


def handle_task_failure(state: AgentState) -> Literal["finalize_task"]:
    """
    Handle task failure and route to finalization.
    
    This function is called when the task encounters an unrecoverable error.
    """
    logger.error(f"Task {state.task_id} failed - routing to finalization")
    state.update_status(TaskStatus.FAILED)
    return "finalize_task"


def create_agent_graph() -> StateGraph:
    """
    Create and configure the complete agent workflow graph.
    
    Returns:
        Compiled StateGraph ready for execution
    """
    logger.info("Creating agent workflow graph")
    
    # Create the workflow graph
    workflow = StateGraph(AgentState)
    
    # Add all the processing nodes
    workflow.add_node("initialize_task", initialize_task)
    workflow.add_node("classify_intent", classify_intent)
    workflow.add_node("analyze_content", analyze_content)
    workflow.add_node("generate_response", generate_response)
    workflow.add_node("finalize_task", finalize_task)
    
    # Set the entry point
    workflow.set_entry_point("initialize_task")
    
    # Add conditional edges for routing logic
    
    # From initialization, always go to intent classification
    workflow.add_edge("initialize_task", "classify_intent")
    
    # From intent classification, decide whether to analyze content or generate response
    workflow.add_conditional_edges(
        "classify_intent",
        should_analyze_content,
        {
            "analyze_content": "analyze_content",
            "generate_response": "generate_response"
        }
    )
    
    # From content analysis, always go to response generation
    workflow.add_edge("analyze_content", "generate_response")
    
    # From response generation, always go to finalization
    workflow.add_edge("generate_response", "finalize_task")
    
    # From finalization, end the workflow
    workflow.add_edge("finalize_task", END)
    
    # Compile the workflow
    try:
        # Try to use Redis checkpointer if available
        if redis_checkpointer._initialized:
            compiled_graph = workflow.compile(checkpointer=redis_checkpointer.get_saver())
            logger.info("Agent graph compiled with Redis checkpointer")
        else:
            compiled_graph = workflow.compile()
            logger.info("Agent graph compiled without checkpointer")
    except Exception as e:
        logger.warning(f"Failed to compile with checkpointer, falling back to basic compilation: {e}")
        compiled_graph = workflow.compile()
        logger.info("Agent graph compiled without checkpointer (fallback)")
    
    return compiled_graph


def create_conversational_agent_graph() -> StateGraph:
    """
    Create a specialized graph for conversational agents.
    
    This variant includes additional conversation-specific processing
    and memory management.
    
    Returns:
        Compiled StateGraph optimized for conversations
    """
    logger.info("Creating conversational agent workflow graph")
    
    # Create the workflow graph
    workflow = StateGraph(AgentState)
    
    # Add processing nodes (same as regular agent)
    workflow.add_node("initialize_task", initialize_task)
    workflow.add_node("classify_intent", classify_intent)
    workflow.add_node("analyze_content", analyze_content)
    workflow.add_node("generate_response", generate_response)
    workflow.add_node("finalize_task", finalize_task)
    
    # Set the entry point
    workflow.set_entry_point("initialize_task")
    
    # Conversational routing - always analyze content for better understanding
    workflow.add_edge("initialize_task", "classify_intent")
    workflow.add_edge("classify_intent", "analyze_content")
    workflow.add_edge("analyze_content", "generate_response")
    workflow.add_edge("generate_response", "finalize_task")
    workflow.add_edge("finalize_task", END)
    
    # Compile with checkpointer for conversation memory
    try:
        if redis_checkpointer._initialized:
            compiled_graph = workflow.compile(checkpointer=redis_checkpointer.get_saver())
            logger.info("Conversational agent graph compiled with Redis checkpointer")
        else:
            compiled_graph = workflow.compile()
            logger.info("Conversational agent graph compiled without checkpointer")
    except Exception as e:
        logger.warning(f"Failed to compile conversational graph with checkpointer: {e}")
        compiled_graph = workflow.compile()
    
    return compiled_graph


def create_analysis_agent_graph() -> StateGraph:
    """
    Create a specialized graph for analysis-focused agents.
    
    This variant emphasizes detailed analysis and structured outputs.
    
    Returns:
        Compiled StateGraph optimized for analysis tasks
    """
    logger.info("Creating analysis agent workflow graph")
    
    # Create the workflow graph
    workflow = StateGraph(AgentState)
    
    # Add processing nodes
    workflow.add_node("initialize_task", initialize_task)
    workflow.add_node("classify_intent", classify_intent)
    workflow.add_node("analyze_content", analyze_content)
    workflow.add_node("generate_response", generate_response)
    workflow.add_node("finalize_task", finalize_task)
    
    # Set the entry point
    workflow.set_entry_point("initialize_task")
    
    # Analysis routing - always do deep analysis
    workflow.add_edge("initialize_task", "classify_intent")
    workflow.add_edge("classify_intent", "analyze_content")
    workflow.add_edge("analyze_content", "generate_response")
    workflow.add_edge("generate_response", "finalize_task")
    workflow.add_edge("finalize_task", END)
    
    # Compile the workflow
    compiled_graph = workflow.compile()
    logger.info("Analysis agent graph compiled successfully")
    
    return compiled_graph


# Create the default agent graph instance
agent_graph = create_agent_graph()

# Create specialized graph instances
conversational_agent_graph = create_conversational_agent_graph()
analysis_agent_graph = create_analysis_agent_graph()


def get_agent_graph_for_task_type(task_type: str) -> StateGraph:
    """
    Get the appropriate agent graph for a specific task type.
    
    Args:
        task_type: Type of task to process
        
    Returns:
        Appropriate StateGraph for the task type
    """
    if task_type in ["conversation", "chat", "dialogue"]:
        logger.info(f"Using conversational agent graph for task type: {task_type}")
        return conversational_agent_graph
    elif task_type in ["analysis", "analyze", "research"]:
        logger.info(f"Using analysis agent graph for task type: {task_type}")
        return analysis_agent_graph
    else:
        logger.info(f"Using default agent graph for task type: {task_type}")
        return agent_graph


async def execute_agent_workflow(
    task_request,
    graph: StateGraph = None,
    config: dict = None
) -> dict:
    """
    Execute the agent workflow with proper error handling.
    
    Args:
        task_request: Task request object
        graph: Optional specific graph to use
        config: Optional LangGraph configuration
        
    Returns:
        Workflow execution result
    """
    from src.agents.template_agent.state import create_initial_state, state_to_response
    
    # Create initial state
    initial_state = create_initial_state(task_request)
    
    # Select appropriate graph
    if graph is None:
        graph = get_agent_graph_for_task_type(task_request.task_type)
    
    # Default configuration
    if config is None:
        config = {
            "configurable": {
                "thread_id": task_request.task_id
            }
        }
    
    try:
        logger.info(f"Executing workflow for task: {task_request.task_id}")
        
        # Execute the workflow
        result = await graph.ainvoke(initial_state, config=config)
        
        # Convert state to response
        response = state_to_response(result)
        
        logger.info(f"Workflow completed for task: {task_request.task_id}")
        return response.dict()
        
    except Exception as e:
        logger.error(f"Workflow execution failed for task {task_request.task_id}: {e}")
        
        # Create error response
        error_response = {
            "task_id": task_request.task_id,
            "success": False,
            "result": {},
            "processing_time_ms": 0,
            "error": f"Workflow execution failed: {str(e)}"
        }
        
        return error_response


# Workflow visualization and debugging helpers

def get_graph_structure(graph: StateGraph = None) -> dict:
    """
    Get the structure of the agent graph for visualization.
    
    Args:
        graph: Optional specific graph to analyze
        
    Returns:
        Dictionary describing the graph structure
    """
    if graph is None:
        graph = agent_graph
    
    try:
        # Get graph nodes and edges
        nodes = list(graph.nodes.keys())
        edges = []
        
        # Extract edge information (simplified)
        for node_name, node_data in graph.nodes.items():
            # This would extract actual edge information in a real implementation
            pass
        
        return {
            "nodes": nodes,
            "edges": edges,
            "entry_point": "initialize_task",
            "end_points": ["finalize_task"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get graph structure: {e}")
        return {"error": str(e)}


def validate_graph_configuration() -> bool:
    """
    Validate that the agent graph is properly configured.
    
    Returns:
        True if graph is valid, False otherwise
    """
    try:
        # Basic validation checks
        if not hasattr(agent_graph, 'nodes'):
            logger.error("Agent graph missing nodes")
            return False
        
        required_nodes = ["initialize_task", "classify_intent", "generate_response", "finalize_task"]
        for node in required_nodes:
            if node not in agent_graph.nodes:
                logger.error(f"Missing required node: {node}")
                return False
        
        logger.info("Agent graph configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Graph validation failed: {e}")
        return False
"""
AEGIS Agent LangGraph workflow for cryptocurrency analysis.

This module implements the AEGIS (Agentic Intelligence System) for crypto analysis,
migrated from the original TypeScript implementation.
"""

import logging
from typing import Dict, List, Optional, Any, Annotated
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, ToolMessage
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langchain_core.tools import tool
from langchain_core.messages import ToolMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.checkpoint.redis import RedisSaver
from redis.asyncio import Redis
import asyncio
import json
import os
from datetime import datetime
from ...core.config import settings
from ...core.logger import logger
from .tools import (
    get_on_chain_metrics,
    get_regulatory_news,
    analyze_market_sentiment,
    get_defi_metrics,
    calculate_portfolio_risk,
    analyze_trading_signals,
    generate_crypto_report,
    ALL_AEGIS_TOOLS
)

# Define the state for AEGIS agent
class AegisAgentState:
    """State for the AEGIS cryptocurrency analysis agent."""
    
    def __init__(self):
        self.messages: List[BaseMessage] = []
        self.analysis_results: Dict[str, Any] = {}
        self.errors: List[str] = []
        self.metadata: Dict[str, Any] = {}


class AegisAgent:
    """
    AEGIS (Agentic Intelligence System) for cryptocurrency analysis.
    
    Migrated from TypeScript to Python, maintaining the same LangGraph
    workflow structure but with enhanced crypto analysis capabilities.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the AEGIS Agent.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.logger = logger
        self.model = self._init_model()
        self.tools = ALL_AEGIS_TOOLS
        self.tool_node = ToolNode(self.tools)
        self.checkpointer = self._init_checkpointer()
        self.graph = self._build_graph()
        
        self.logger.info("AEGIS Agent initialized successfully")
    
    def _init_model(self) -> ChatVertexAI:
        """Initialize the ChatVertexAI model with tools."""
        model = ChatVertexAI(
            model=settings.GEMINI_MODEL_NAME,
            temperature=0,
            project=settings.GOOGLE_CLOUD_PROJECT,
            location=settings.GOOGLE_CLOUD_LOCATION,
        )
        
        # Bind tools to the model
        model_with_tools = model.bind_tools(self.tools)
        
        self.logger.info(f"Model initialized: {settings.GEMINI_MODEL_NAME}")
        return model_with_tools
    
    def _init_checkpointer(self):
        """Initialize Redis checkpointer for conversation persistence."""
        try:
            if hasattr(settings, 'REDIS_URL') and settings.REDIS_URL:
                redis_client = Redis.from_url(settings.REDIS_URL)
                checkpointer = RedisSaver(redis_client)
                self.logger.info("Redis checkpointer initialized")
                return checkpointer
        except Exception as e:
            self.logger.warning(f"Redis checkpointer initialization failed: {e}")
        
        # Fallback to memory checkpointer
        self.logger.info("Using memory checkpointer")
        return MemorySaver()
    
    def _build_graph(self) -> StateGraph:
        """Build the AEGIS agent LangGraph workflow."""
        
        # Create state graph with message handling
        workflow = StateGraph(AegisAgentState)
        
        # Add nodes
        workflow.add_node("agent", self._agent_node)
        workflow.add_node("tools", self._tool_node)
        
        # Set entry point
        workflow.set_entry_point("agent")
        
        # Add conditional edges
        workflow.add_conditional_edges(
            "agent",
            self._should_continue,
            {
                "tools": "tools",
                "end": END,
            }
        )
        
        # Add edge from tools back to agent
        workflow.add_edge("tools", "agent")
        
        # Compile the graph
        compiled_graph = workflow.compile(checkpointer=self.checkpointer)
        
        self.logger.info("AEGIS agent graph compiled successfully")
        return compiled_graph
    
    async def _agent_node(self, state: AegisAgentState) -> Dict[str, Any]:
        """
        The main agent node that processes messages and decides on actions.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated state with agent response
        """
        try:
            self.logger.info(f"Agent node processing {len(state.messages)} messages")
            
            # If no messages, create a default system message
            if not state.messages:
                system_message = HumanMessage(content="Hello! I'm AEGIS, your cryptocurrency analysis assistant. How can I help you today?")
                state.messages.append(system_message)
            
            # Invoke the model with the current messages
            response = await self.model.ainvoke(state.messages)
            
            # Add the response to messages
            state.messages.append(response)
            
            self.logger.info(f"Agent generated response: {response.content[:100] if response.content else 'Tool calls only'}...")
            
            return {"messages": state.messages}
            
        except Exception as e:
            self.logger.error(f"Error in agent node: {str(e)}")
            error_message = AIMessage(content=f"I encountered an error: {str(e)}")
            state.messages.append(error_message)
            state.errors.append(str(e))
            return {"messages": state.messages}
    
    async def _tool_node(self, state: AegisAgentState) -> Dict[str, Any]:
        """
        The tool execution node that runs the tools selected by the agent.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated state with tool results
        """
        try:
            last_message = state.messages[-1]
            
            if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
                raise ValueError("No tool calls found in the last message")
            
            self.logger.info(f"Executing {len(last_message.tool_calls)} tool calls")
            
            # Execute all tool calls
            tool_messages = []
            for tool_call in last_message.tool_calls:
                try:
                    self.logger.info(f"Executing tool: {tool_call['name']}")
                    
                    # Execute the tool using the tool node
                    tool_result = await self.tool_node.ainvoke(
                        {"messages": [last_message]}
                    )
                    
                    # Extract the actual result
                    if hasattr(tool_result, 'content'):
                        result_content = tool_result.content
                    else:
                        result_content = str(tool_result)
                    
                    # Store result in analysis_results for later reference
                    tool_name = tool_call["name"]
                    if tool_name not in state.analysis_results:
                        state.analysis_results[tool_name] = []
                    state.analysis_results[tool_name].append({
                        "input": tool_call["args"],
                        "output": tool_result,
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    
                    tool_messages.append(
                        ToolMessage(
                            tool_call_id=tool_call["id"],
                            content=str(tool_result),
                        )
                    )
                    
                except Exception as e:
                    self.logger.error(f"Error executing tool {tool_call['name']}: {str(e)}")
                    state.errors.append(f"Tool {tool_call['name']} failed: {str(e)}")
                    
                    tool_messages.append(
                        ToolMessage(
                            tool_call_id=tool_call["id"],
                            content=f"Error: {str(e)}",
                        )
                    )
            
            # Add tool messages to state
            state.messages.extend(tool_messages)
            
            return {"messages": state.messages}
            
        except Exception as e:
            self.logger.error(f"Error in tool node: {str(e)}")
            state.errors.append(f"Tool execution failed: {str(e)}")
            return {"messages": state.messages}
    
    def _should_continue(self, state: AegisAgentState) -> str:
        """
        Determine whether to continue with tool execution or end the conversation.
        
        Args:
            state: Current agent state
            
        Returns:
            "tools" to continue with tool execution, "end" to finish
        """
        if not state.messages:
            return "end"
        
        last_message = state.messages[-1]
        
        if isinstance(last_message, AIMessage) and last_message.tool_calls:
            self.logger.info(f"Continuing to tools node with {len(last_message.tool_calls)} tool calls")
            return "tools"
        
        self.logger.info("Ending conversation")
        return "end"
    
    async def process_message(self, message: str, session_id: str = None) -> Dict[str, Any]:
        """
        Process a single message through the AEGIS agent workflow.
        
        Args:
            message: User message to process
            session_id: Optional session ID for conversation continuity
            
        Returns:
            Dictionary containing the agent's response and analysis
        """
        try:
            self.logger.info(f"Processing message: {message[:100]}...")
            
            # Create initial state
            initial_state = AegisAgentState()
            initial_state.messages = [HumanMessage(content=message)]
            initial_state.metadata = {
                "session_id": session_id or "default",
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Configure for session continuity
            config = {
                "configurable": {
                    "thread_id": session_id or "default_thread"
                }
            }
            
            # Execute the workflow
            result = await self.graph.ainvoke(initial_state, config=config)
            
            # Extract the final response
            final_response = ""
            if result.messages:
                for msg in reversed(result.messages):
                    if isinstance(msg, AIMessage) and msg.content:
                        final_response = msg.content
                        break
            
            response_data = {
                "response": final_response,
                "analysis_results": result.analysis_results,
                "session_id": session_id or "default",
                "timestamp": datetime.utcnow().isoformat(),
                "message_count": len(result.messages)
            }
            
            if result.errors:
                response_data["errors"] = result.errors
            
            self.logger.info("Message processed successfully")
            return response_data
            
        except Exception as e:
            self.logger.error(f"Error processing message: {str(e)}")
            return {
                "response": f"I encountered an error processing your request: {str(e)}",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def analyze_cryptocurrency(self, symbol: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Perform comprehensive cryptocurrency analysis.
        
        Args:
            symbol: Cryptocurrency symbol (e.g., 'BTC', 'ETH')
            analysis_type: Type of analysis ('comprehensive', 'technical', 'regulatory')
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            self.logger.info(f"Analyzing cryptocurrency: {symbol} (type: {analysis_type})")
            
            # Create analysis prompt
            prompt = f"""
            Please provide a {analysis_type} analysis of {symbol} cryptocurrency. 
            Use the available tools to gather relevant data including:
            - On-chain metrics
            - Market sentiment
            - Regulatory news
            - Technical indicators
            - Risk assessment
            
            Please provide a detailed analysis with actionable insights.
            """
            
            # Process through the agent
            result = await self.process_message(prompt, session_id=f"analysis_{symbol}")
            
            self.logger.info(f"Cryptocurrency analysis completed for {symbol}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing cryptocurrency {symbol}: {str(e)}")
            return {
                "error": f"Analysis failed: {str(e)}",
                "symbol": symbol,
                "timestamp": datetime.utcnow().isoformat()
            }


# Create the default AEGIS agent instance
aegis_agent = AegisAgent()


async def process_crypto_analysis(message: str, session_id: str = None) -> Dict[str, Any]:
    """
    Convenience function to process crypto analysis requests.
    
    Args:
        message: Analysis request message
        session_id: Optional session ID
        
    Returns:
        Analysis results
    """
    return await aegis_agent.process_message(message, session_id)


async def analyze_symbol(symbol: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
    """
    Convenience function to analyze a specific cryptocurrency.
    
    Args:
        symbol: Cryptocurrency symbol
        analysis_type: Type of analysis
        
    Returns:
        Analysis results
    """
    return await aegis_agent.analyze_cryptocurrency(symbol, analysis_type)
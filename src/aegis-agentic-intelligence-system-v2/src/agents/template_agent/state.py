"""
Agent state definitions for the template agent.

This module defines the Pydantic models that represent the agent's state
throughout the LangGraph workflow execution.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class TaskType(str, Enum):
    """Supported task types for the template agent."""
    
    CLASSIFICATION = "classification"
    GENERATION = "generation"
    ANALYSIS = "analysis"
    RESEARCH = "research"
    CONVERSATION = "conversation"
    CUSTOM = "custom"


class TaskStatus(str, Enum):
    """Task execution status."""
    
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Priority(str, Enum):
    """Task priority levels."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TaskRequest(BaseModel):
    """Incoming task request model."""
    
    task_id: str
    task_type: TaskType
    input_data: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None
    priority: Priority = Priority.MEDIUM
    timeout_seconds: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class TaskResponse(BaseModel):
    """Task response model."""
    
    task_id: str
    success: bool
    result: Dict[str, Any]
    processing_time_ms: int
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class IntentClassification(BaseModel):
    """Intent classification result."""
    
    intent: str
    confidence: float = Field(ge=0.0, le=1.0)
    reasoning: str
    requires_action: bool = False
    suggested_actions: List[str] = []


class AnalysisResult(BaseModel):
    """Analysis result model."""
    
    summary: str
    key_points: List[str]
    sentiment: Optional[str] = None
    entities: List[Dict[str, Any]] = []
    confidence: float = Field(ge=0.0, le=1.0)
    metadata: Optional[Dict[str, Any]] = None


class GenerationRequest(BaseModel):
    """Content generation request."""
    
    prompt: str
    context: Optional[str] = None
    style: Optional[str] = None
    max_length: Optional[int] = None
    temperature: Optional[float] = None


class GenerationResult(BaseModel):
    """Content generation result."""
    
    generated_content: str
    confidence: float = Field(ge=0.0, le=1.0)
    model_used: str
    metadata: Optional[Dict[str, Any]] = None


class AgentState(BaseModel):
    """
    Main agent state that persists throughout the workflow.
    
    This state is passed between all nodes in the LangGraph workflow.
    """
    
    # Core task information
    task_id: str
    task_type: TaskType
    input_data: Dict[str, Any]
    context: Dict[str, Any] = {}
    
    # Processing state
    status: TaskStatus = TaskStatus.PENDING
    current_step: Optional[str] = None
    steps_completed: List[str] = []
    
    # Results and analysis
    intent_classification: Optional[IntentClassification] = None
    analysis_result: Optional[AnalysisResult] = None
    generation_result: Optional[GenerationResult] = None
    
    # Workflow control
    should_continue: bool = True
    next_action: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    # Metadata and tracking
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    processing_time_ms: int = 0
    
    # Error handling
    errors: List[str] = []
    warnings: List[str] = []
    
    # External service data
    external_data: Dict[str, Any] = {}
    
    # Final results
    final_result: Optional[Dict[str, Any]] = None
    final_status: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
    
    def add_error(self, error: str):
        """Add an error to the state."""
        self.errors.append(error)
        self.updated_at = datetime.utcnow()
    
    def add_warning(self, warning: str):
        """Add a warning to the state."""
        self.warnings.append(warning)
        self.updated_at = datetime.utcnow()
    
    def mark_step_completed(self, step: str):
        """Mark a step as completed."""
        if step not in self.steps_completed:
            self.steps_completed.append(step)
        self.current_step = step
        self.updated_at = datetime.utcnow()
    
    def update_status(self, status: TaskStatus):
        """Update the task status."""
        self.status = status
        self.updated_at = datetime.utcnow()
    
    def increment_retry(self):
        """Increment retry count."""
        self.retry_count += 1
        self.updated_at = datetime.utcnow()
    
    def should_retry(self) -> bool:
        """Check if task should be retried."""
        return self.retry_count < self.max_retries
    
    def get_context_for_key(self, key: str, default: Any = None) -> Any:
        """Get a value from context with default."""
        return self.context.get(key, default)
    
    def set_context(self, key: str, value: Any):
        """Set a value in context."""
        self.context[key] = value
        self.updated_at = datetime.utcnow()
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get a summary of processing status."""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "status": self.status,
            "steps_completed": self.steps_completed,
            "processing_time_ms": self.processing_time_ms,
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "retry_count": self.retry_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


class ConversationState(BaseModel):
    """Extended state for conversation-based agents."""
    
    conversation_id: str
    messages: List[Dict[str, Any]] = []
    participant_count: int = 0
    conversation_context: Dict[str, Any] = {}
    conversation_history: List[str] = []
    
    def add_message(self, message: Dict[str, Any]):
        """Add a message to the conversation."""
        self.messages.append({
            **message,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def get_recent_messages(self, count: int = 5) -> List[Dict[str, Any]]:
        """Get the most recent messages."""
        return self.messages[-count:] if self.messages else []
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the conversation."""
        if not self.messages:
            return "No messages in conversation"
        
        message_count = len(self.messages)
        participants = set()
        for msg in self.messages:
            if "sender" in msg:
                participants.add(msg["sender"])
        
        return f"Conversation with {len(participants)} participants, {message_count} messages"


# Utility functions for state management

def create_initial_state(task_request: TaskRequest) -> AgentState:
    """
    Create initial agent state from task request.
    
    Args:
        task_request: Incoming task request
        
    Returns:
        Initial agent state
    """
    return AgentState(
        task_id=task_request.task_id,
        task_type=task_request.task_type,
        input_data=task_request.input_data,
        context=task_request.context or {},
        status=TaskStatus.PENDING
    )


def state_to_response(state: AgentState) -> TaskResponse:
    """
    Convert agent state to task response.
    
    Args:
        state: Agent state
        
    Returns:
        Task response
    """
    return TaskResponse(
        task_id=state.task_id,
        success=(state.status == TaskStatus.COMPLETED and len(state.errors) == 0),
        result=state.final_result or {},
        processing_time_ms=state.processing_time_ms,
        error="; ".join(state.errors) if state.errors else None,
        metadata={
            "steps_completed": state.steps_completed,
            "warnings": state.warnings,
            "retry_count": state.retry_count
        }
    )
"""
LangGraph nodes for the template agent workflow.

This module defines the individual processing nodes that make up
the agent's workflow graph.
"""

import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

from src.agents.template_agent.state import (
    AgentState, 
    TaskStatus, 
    IntentClassification,
    AnalysisResult,
    GenerationResult
)
from src.agents.template_agent.tools import get_tools_for_task_type
from src.model_router import model_router, TaskComplexity, ModelSelectionCriteria

logger = logging.getLogger(__name__)


async def initialize_task(state: AgentState) -> Dict[str, Any]:
    """
    Initialize the task and prepare for processing.
    
    This is the entry point node that sets up the initial state
    and validates the input data.
    """
    logger.info(f"Initializing task: {state.task_id}")
    
    try:
        # Update state
        state.update_status(TaskStatus.IN_PROGRESS)
        state.mark_step_completed("initialize_task")
        
        # Validate input data
        if not state.input_data:
            state.add_error("No input data provided")
            state.update_status(TaskStatus.FAILED)
            return {"status": TaskStatus.FAILED}
        
        # Set up context based on task type
        if state.task_type.value == "conversation":
            state.set_context("conversation_mode", True)
        elif state.task_type.value == "analysis":
            state.set_context("analysis_mode", True)
        elif state.task_type.value == "generation":
            state.set_context("generation_mode", True)
        
        # Initialize processing time tracking
        state.set_context("start_time", time.time())
        
        logger.info(f"Task {state.task_id} initialized successfully")
        
        return {
            "status": TaskStatus.IN_PROGRESS,
            "next_action": "classify_intent"
        }
        
    except Exception as e:
        logger.error(f"Task initialization failed: {e}")
        state.add_error(f"Initialization failed: {str(e)}")
        state.update_status(TaskStatus.FAILED)
        
        return {"status": TaskStatus.FAILED}


async def classify_intent(state: AgentState) -> Dict[str, Any]:
    """
    Classify the intent of the incoming task.
    
    Determines what the user is trying to accomplish and
    how the agent should respond.
    """
    logger.info(f"Classifying intent for task: {state.task_id}")
    
    try:
        state.mark_step_completed("classify_intent")
        
        # Get input text for classification
        input_text = state.input_data.get("text", "")
        if not input_text:
            input_text = str(state.input_data)
        
        # Select appropriate model for classification
        criteria = ModelSelectionCriteria(
            task_complexity=TaskComplexity.SIMPLE,
            response_time_priority=True
        )
        model = model_router.get_model_instance(
            model_router.select_model(criteria)
        )
        
        # Create classification prompt
        prompt = f"""
Analyze the following input and classify the user's intent. Consider what they are trying to accomplish.

Input: {input_text}

Please provide:
1. Primary intent (one of: question, request, complaint, compliment, information_seeking, task_completion)
2. Confidence level (0.0 to 1.0)
3. Brief reasoning for your classification
4. Whether this requires immediate action (yes/no)
5. Suggested next steps

Classification:
        """.strip()
        
        response = await model.ainvoke(prompt)
        classification_text = response.content.strip()
        
        # Parse the response (simplified - in production you'd use structured output)
        lines = classification_text.split('\n')
        intent = "unknown"
        confidence = 0.5
        reasoning = "Unable to parse classification"
        requires_action = False
        
        for line in lines:
            line = line.strip()
            if line.startswith("1.") or "intent" in line.lower():
                intent = line.split(":")[-1].strip() if ":" in line else "unknown"
            elif line.startswith("2.") or "confidence" in line.lower():
                try:
                    confidence_str = line.split(":")[-1].strip()
                    confidence = float(confidence_str.replace("(", "").replace(")", ""))
                except:
                    confidence = 0.5
            elif line.startswith("3.") or "reasoning" in line.lower():
                reasoning = line.split(":", 1)[-1].strip() if ":" in line else line
            elif line.startswith("4.") or "action" in line.lower():
                requires_action = "yes" in line.lower()
        
        # Create intent classification result
        intent_result = IntentClassification(
            intent=intent,
            confidence=confidence,
            reasoning=reasoning,
            requires_action=requires_action,
            suggested_actions=["analyze_content"] if requires_action else ["generate_response"]
        )
        
        state.intent_classification = intent_result
        
        logger.info(f"Intent classified as: {intent} (confidence: {confidence})")
        
        return {
            "intent_classification": intent_result,
            "next_action": "analyze_content" if requires_action else "generate_response"
        }
        
    except Exception as e:
        logger.error(f"Intent classification failed: {e}")
        state.add_error(f"Intent classification failed: {str(e)}")
        
        # Create fallback classification
        fallback_intent = IntentClassification(
            intent="unknown",
            confidence=0.0,
            reasoning=f"Classification failed: {str(e)}",
            requires_action=True,
            suggested_actions=["analyze_content"]
        )
        
        state.intent_classification = fallback_intent
        
        return {
            "intent_classification": fallback_intent,
            "next_action": "analyze_content"
        }


async def analyze_content(state: AgentState) -> Dict[str, Any]:
    """
    Perform detailed analysis of the input content.
    
    This node handles deep analysis including sentiment analysis,
    entity extraction, and content understanding.
    """
    logger.info(f"Analyzing content for task: {state.task_id}")
    
    try:
        state.mark_step_completed("analyze_content")
        
        # Get input for analysis
        input_text = state.input_data.get("text", str(state.input_data))
        
        # Select appropriate model for analysis
        criteria = ModelSelectionCriteria(
            task_complexity=TaskComplexity.MODERATE,
            context_length=len(input_text)
        )
        model = model_router.get_model_instance(
            model_router.select_model(criteria)
        )
        
        # Comprehensive analysis prompt
        prompt = f"""
Perform a comprehensive analysis of the following content:

Content: {input_text}

Please provide:
1. Summary (2-3 sentences)
2. Key points (bullet list)
3. Sentiment (Positive/Negative/Neutral with confidence)
4. Important entities (people, organizations, locations, dates)
5. Overall confidence in your analysis (0.0 to 1.0)

Analysis:
        """.strip()
        
        response = await model.ainvoke(prompt)
        analysis_text = response.content.strip()
        
        # Parse analysis (simplified parsing)
        lines = analysis_text.split('\n')
        summary = ""
        key_points = []
        sentiment = "neutral"
        entities = []
        confidence = 0.8
        
        current_section = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if "summary" in line.lower() and ":" in line:
                summary = line.split(":", 1)[-1].strip()
                current_section = "summary"
            elif "key points" in line.lower():
                current_section = "key_points"
            elif "sentiment" in line.lower() and ":" in line:
                sentiment = line.split(":")[-1].strip().lower()
                current_section = "sentiment"
            elif "entities" in line.lower():
                current_section = "entities"
            elif "confidence" in line.lower() and ":" in line:
                try:
                    conf_str = line.split(":")[-1].strip()
                    confidence = float(conf_str.replace("(", "").replace(")", ""))
                except:
                    confidence = 0.8
            elif line.startswith("-") or line.startswith("•"):
                if current_section == "key_points":
                    key_points.append(line[1:].strip())
                elif current_section == "entities":
                    entities.append({"text": line[1:].strip(), "type": "unknown"})
        
        # Create analysis result
        analysis_result = AnalysisResult(
            summary=summary or "Content analysis completed",
            key_points=key_points,
            sentiment=sentiment,
            entities=entities,
            confidence=confidence,
            metadata={
                "analyzed_at": datetime.utcnow().isoformat(),
                "input_length": len(input_text)
            }
        )
        
        state.analysis_result = analysis_result
        
        logger.info(f"Content analysis completed with confidence: {confidence}")
        
        return {
            "analysis_result": analysis_result,
            "next_action": "generate_response"
        }
        
    except Exception as e:
        logger.error(f"Content analysis failed: {e}")
        state.add_error(f"Content analysis failed: {str(e)}")
        
        # Create fallback analysis
        fallback_analysis = AnalysisResult(
            summary="Analysis could not be completed due to an error",
            key_points=["Error occurred during analysis"],
            sentiment="neutral",
            entities=[],
            confidence=0.0,
            metadata={"error": str(e)}
        )
        
        state.analysis_result = fallback_analysis
        
        return {
            "analysis_result": fallback_analysis,
            "next_action": "generate_response"
        }


async def generate_response(state: AgentState) -> Dict[str, Any]:
    """
    Generate the final response based on the analysis.
    
    This node creates the output that will be returned to the user.
    """
    logger.info(f"Generating response for task: {state.task_id}")
    
    try:
        state.mark_step_completed("generate_response")
        
        # Determine response strategy based on task type and analysis
        if state.task_type.value == "generation":
            return await _generate_content_response(state)
        elif state.task_type.value == "analysis":
            return await _generate_analysis_response(state)
        elif state.task_type.value == "conversation":
            return await _generate_conversation_response(state)
        else:
            return await _generate_general_response(state)
            
    except Exception as e:
        logger.error(f"Response generation failed: {e}")
        state.add_error(f"Response generation failed: {str(e)}")
        
        # Generate fallback response
        fallback_response = GenerationResult(
            generated_content="I apologize, but I encountered an error while processing your request.",
            confidence=0.0,
            model_used="fallback",
            metadata={"error": str(e)}
        )
        
        state.generation_result = fallback_response
        
        return {
            "generation_result": fallback_response,
            "next_action": "finalize_task"
        }


async def _generate_content_response(state: AgentState) -> Dict[str, Any]:
    """Generate content-focused response."""
    
    # Get generation parameters
    prompt = state.input_data.get("prompt", "")
    style = state.input_data.get("style", "professional")
    max_length = state.input_data.get("max_length", 500)
    
    # Select model for generation
    model = model_router.get_model_for_task("content_generation", priority="quality")
    
    generation_prompt = f"""
Create content based on the following requirements:

Prompt: {prompt}
Style: {style}
Maximum length: {max_length} characters

Additional context from analysis: {state.analysis_result.summary if state.analysis_result else "None"}

Generated content:
    """.strip()
    
    response = await model.ainvoke(generation_prompt)
    generated_content = response.content.strip()
    
    # Truncate if necessary
    if len(generated_content) > max_length:
        generated_content = generated_content[:max_length-3] + "..."
    
    generation_result = GenerationResult(
        generated_content=generated_content,
        confidence=0.85,
        model_used=str(model.model_name if hasattr(model, 'model_name') else "gemini"),
        metadata={
            "style": style,
            "original_length": len(response.content),
            "truncated": len(response.content) > max_length
        }
    )
    
    state.generation_result = generation_result
    
    return {
        "generation_result": generation_result,
        "next_action": "finalize_task"
    }


async def _generate_analysis_response(state: AgentState) -> Dict[str, Any]:
    """Generate analysis-focused response."""
    
    analysis = state.analysis_result
    if not analysis:
        raise ValueError("No analysis result available")
    
    # Format analysis into readable response
    response_content = f"""
Analysis Results:

Summary: {analysis.summary}

Key Points:
{chr(10).join(f"• {point}" for point in analysis.key_points)}

Sentiment: {analysis.sentiment}

Confidence: {analysis.confidence:.2f}
    """.strip()
    
    generation_result = GenerationResult(
        generated_content=response_content,
        confidence=analysis.confidence,
        model_used="analysis_formatter",
        metadata={
            "analysis_type": "comprehensive",
            "entity_count": len(analysis.entities)
        }
    )
    
    state.generation_result = generation_result
    
    return {
        "generation_result": generation_result,
        "next_action": "finalize_task"
    }


async def _generate_conversation_response(state: AgentState) -> Dict[str, Any]:
    """Generate conversation-focused response."""
    
    intent = state.intent_classification
    analysis = state.analysis_result
    
    # Create conversational response based on intent
    model = model_router.get_model_for_task("conversation", priority="quality")
    
    conversation_prompt = f"""
Generate a helpful, conversational response based on the following:

User Intent: {intent.intent if intent else "unknown"}
Content Analysis: {analysis.summary if analysis else "No analysis available"}
Confidence: {intent.confidence if intent else 0.5}

Create a response that:
1. Acknowledges the user's intent
2. Provides helpful information or assistance
3. Uses a friendly, professional tone
4. Offers next steps if appropriate

Response:
    """.strip()
    
    response = await model.ainvoke(conversation_prompt)
    conversation_response = response.content.strip()
    
    generation_result = GenerationResult(
        generated_content=conversation_response,
        confidence=0.80,
        model_used=str(model.model_name if hasattr(model, 'model_name') else "gemini"),
        metadata={
            "conversation_type": "assistant",
            "intent": intent.intent if intent else "unknown"
        }
    )
    
    state.generation_result = generation_result
    
    return {
        "generation_result": generation_result,
        "next_action": "finalize_task"
    }


async def _generate_general_response(state: AgentState) -> Dict[str, Any]:
    """Generate general-purpose response."""
    
    # Create a general response based on available information
    input_data = state.input_data
    intent = state.intent_classification
    analysis = state.analysis_result
    
    response_parts = []
    
    if intent:
        response_parts.append(f"I understand you're looking for: {intent.intent}")
    
    if analysis:
        response_parts.append(f"Based on my analysis: {analysis.summary}")
        if analysis.key_points:
            response_parts.append("Key points identified:")
            response_parts.extend(f"• {point}" for point in analysis.key_points[:3])
    
    if not response_parts:
        response_parts.append("I've processed your request and am ready to assist you.")
    
    response_content = "\n\n".join(response_parts)
    
    generation_result = GenerationResult(
        generated_content=response_content,
        confidence=0.70,
        model_used="general_formatter",
        metadata={
            "response_type": "general",
            "components_used": len(response_parts)
        }
    )
    
    state.generation_result = generation_result
    
    return {
        "generation_result": generation_result,
        "next_action": "finalize_task"
    }


async def finalize_task(state: AgentState) -> Dict[str, Any]:
    """
    Finalize the task and prepare the final result.
    
    This is the final node that packages up all results
    and updates the final state.
    """
    logger.info(f"Finalizing task: {state.task_id}")
    
    try:
        state.mark_step_completed("finalize_task")
        
        # Calculate processing time
        start_time = state.get_context_for_key("start_time", time.time())
        processing_time = int((time.time() - start_time) * 1000)
        state.processing_time_ms = processing_time
        
        # Prepare final result
        final_result = {
            "task_id": state.task_id,
            "task_type": state.task_type,
            "processing_time_ms": processing_time,
            "steps_completed": state.steps_completed
        }
        
        # Add results based on what was generated
        if state.intent_classification:
            final_result["intent"] = {
                "classification": state.intent_classification.intent,
                "confidence": state.intent_classification.confidence,
                "reasoning": state.intent_classification.reasoning
            }
        
        if state.analysis_result:
            final_result["analysis"] = {
                "summary": state.analysis_result.summary,
                "key_points": state.analysis_result.key_points,
                "sentiment": state.analysis_result.sentiment,
                "confidence": state.analysis_result.confidence
            }
        
        if state.generation_result:
            final_result["response"] = {
                "content": state.generation_result.generated_content,
                "confidence": state.generation_result.confidence,
                "model_used": state.generation_result.model_used
            }
        
        # Add any errors or warnings
        if state.errors:
            final_result["errors"] = state.errors
        if state.warnings:
            final_result["warnings"] = state.warnings
        
        state.final_result = final_result
        state.final_status = "completed"
        
        # Update final status
        if state.errors:
            state.update_status(TaskStatus.FAILED)
        else:
            state.update_status(TaskStatus.COMPLETED)
        
        logger.info(f"Task {state.task_id} finalized with status: {state.status}")
        
        return {
            "final_result": final_result,
            "final_status": state.status,
            "processing_complete": True
        }
        
    except Exception as e:
        logger.error(f"Task finalization failed: {e}")
        state.add_error(f"Finalization failed: {str(e)}")
        state.update_status(TaskStatus.FAILED)
        
        # Create minimal final result
        minimal_result = {
            "task_id": state.task_id,
            "error": f"Task finalization failed: {str(e)}",
            "processing_time_ms": state.processing_time_ms
        }
        
        state.final_result = minimal_result
        state.final_status = "failed"
        
        return {
            "final_result": minimal_result,
            "final_status": TaskStatus.FAILED,
            "processing_complete": True
        }
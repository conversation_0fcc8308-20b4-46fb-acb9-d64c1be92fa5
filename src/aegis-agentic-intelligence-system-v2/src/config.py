"""
Centralized configuration loader for the Agent Template.

Loads configuration from environment variables or Google Secret Manager
depending on the deployment environment.
"""

import os
import logging
from typing import Optional, Dict, Any
from pydantic import BaseModel
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)


class DatabaseConfig(BaseModel):
    """Database connection configuration."""
    host: str
    user: str
    password: str
    database: str
    port: int = 5432


class RedisConfig(BaseModel):
    """Redis connection configuration."""
    host: str = "127.0.0.1"
    port: int = 6379
    db: int = 0


class PineconeConfig(BaseModel):
    """Pinecone vector database configuration."""
    api_key: str
    environment: str


class CryptoAPIConfig(BaseModel):
    """Cryptocurrency API configuration."""
    coingecko_api_key: Optional[str] = None
    dexscreener_api_key: Optional[str] = None
    santiment_api_key: Optional[str] = None
    newsapi_key: Optional[str] = None
    cryptopanic_api_key: Optional[str] = None


class Settings(BaseSettings):
    """
    Application settings with automatic environment variable loading.
    
    In production, sensitive values should be loaded from Google Secret Manager.
    """
    model_config = SettingsConfigDict(
        env_file=".env.local",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )

    # --- Application Settings ---
    environment: str = "development"
    log_level: str = "INFO"
    
    # --- GCP Configuration ---
    gcp_project_id: str
    gcp_region: str = "us-west1"
    
    # --- API Keys ---
    google_api_key: str
    hubspot_api_key: Optional[str] = None
    langchain_api_key: Optional[str] = None
    
    # --- Database Configuration ---
    postgres_host: str
    postgres_user: str
    postgres_password: str
    postgres_db: str
    postgres_port: int = 5432
    
    # --- Redis Configuration ---
    redis_host: str = "127.0.0.1"
    redis_port: int = 6379
    
    # --- Pinecone Configuration ---
    pinecone_api_key: Optional[str] = None
    pinecone_environment: Optional[str] = None
    
    # --- Celery Configuration ---
    celery_broker_url: str = "redis://localhost:6379/0"
    gcp_pubsub_celery_topic: str = "celery-tasks"
    
    # --- Supabase Configuration (TKC Standard) ---
    supabase_url: Optional[str] = None
    supabase_service_role_key: Optional[str] = None
    
    # --- LangChain Configuration ---
    langchain_tracing_v2: str = "false"
    
    # --- Gemini Model Configuration ---
    gemini_model: str = "gemini-2.0-flash-exp"
    gemini_max_tokens: int = 1000
    gemini_temperature: float = 0.7
    
    # --- AEGIS-Specific Configuration ---
    aegis_session_timeout: int = 3600  # 1 hour
    aegis_max_concurrent_analyses: int = 10
    aegis_enable_caching: bool = True
    aegis_cache_duration: int = 300  # 5 minutes
    
    # --- Crypto API Keys ---
    coingecko_api_key: Optional[str] = None
    dexscreener_api_key: Optional[str] = None
    santiment_api_key: Optional[str] = None
    newsapi_key: Optional[str] = None
    cryptopanic_api_key: Optional[str] = None
    
    # --- AEGIS Analysis Settings ---
    default_analysis_timeframe: str = "7d"
    enable_sentiment_analysis: bool = True
    enable_technical_analysis: bool = True
    enable_regulatory_monitoring: bool = True
    enable_defi_metrics: bool = True
    
    # --- Rate Limiting ---
    api_rate_limit_per_minute: int = 100
    analysis_rate_limit_per_hour: int = 1000

    def get_database_config(self) -> DatabaseConfig:
        """Get database configuration object."""
        return DatabaseConfig(
            host=self.postgres_host,
            user=self.postgres_user,
            password=self.postgres_password,
            database=self.postgres_db,
            port=self.postgres_port
        )

    def get_redis_config(self) -> RedisConfig:
        """Get Redis configuration object."""
        return RedisConfig(
            host=self.redis_host,
            port=self.redis_port
        )

    def get_pinecone_config(self) -> Optional[PineconeConfig]:
        """Get Pinecone configuration object if available."""
        if self.pinecone_api_key and self.pinecone_environment:
            return PineconeConfig(
                api_key=self.pinecone_api_key,
                environment=self.pinecone_environment
            )
        return None
    
    def get_crypto_api_config(self) -> CryptoAPIConfig:
        """Get cryptocurrency API configuration object."""
        return CryptoAPIConfig(
            coingecko_api_key=self.coingecko_api_key,
            dexscreener_api_key=self.dexscreener_api_key,
            santiment_api_key=self.santiment_api_key,
            newsapi_key=self.newsapi_key,
            cryptopanic_api_key=self.cryptopanic_api_key
        )

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() == "development"
    
    @property
    def redis_url(self) -> str:
        """Get Redis connection URL."""
        return f"redis://{self.redis_host}:{self.redis_port}/0"
    
    @property
    def database_url(self) -> str:
        """Get database connection URL."""
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@{self.postgres_host}:{self.postgres_port}/{self.postgres_db}"
    
    @property
    def gemini_model_name(self) -> str:
        """Get Gemini model name for backwards compatibility."""
        return self.gemini_model
    
    @property
    def google_cloud_project(self) -> str:
        """Get Google Cloud project ID."""
        return self.gcp_project_id
    
    @property
    def google_cloud_location(self) -> str:
        """Get Google Cloud location."""
        return self.gcp_region


# Global settings instance
settings = Settings()


class SecretManager:
    """
    Google Secret Manager integration for production deployments.
    
    In production, sensitive configuration should be loaded from Secret Manager
    instead of environment variables.
    """
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self._client = None
    
    @property
    def client(self):
        """Lazy initialization of Secret Manager client."""
        if self._client is None:
            try:
                from google.cloud import secretmanager
                self._client = secretmanager.SecretManagerServiceClient()
            except ImportError:
                logger.error("google-cloud-secret-manager not installed")
                raise
        return self._client
    
    def get_secret(self, secret_id: str, version: str = "latest") -> str:
        """
        Retrieve a secret from Google Secret Manager.
        
        Args:
            secret_id: The ID of the secret
            version: The version of the secret (default: "latest")
            
        Returns:
            The secret value as a string
        """
        try:
            name = f"projects/{self.project_id}/secrets/{secret_id}/versions/{version}"
            response = self.client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to retrieve secret {secret_id}: {e}")
            raise
    
    def load_secrets_to_settings(self, secret_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        Load multiple secrets and return as dictionary.
        
        Args:
            secret_mapping: Dict mapping setting names to secret IDs
            
        Returns:
            Dictionary of setting names to secret values
        """
        secrets = {}
        for setting_name, secret_id in secret_mapping.items():
            try:
                secrets[setting_name] = self.get_secret(secret_id)
                logger.info(f"Loaded secret for {setting_name}")
            except Exception as e:
                logger.error(f"Failed to load secret for {setting_name}: {e}")
        return secrets


def load_production_secrets() -> Dict[str, Any]:
    """
    Load secrets from Google Secret Manager for production deployment.
    
    Returns:
        Dictionary of configuration values loaded from Secret Manager
    """
    if not settings.is_production:
        return {}
    
    secret_manager = SecretManager(settings.gcp_project_id)
    
    # Define mapping of setting names to secret IDs
    secret_mapping = {
        "google_api_key": "GOOGLE_API_KEY",
        "hubspot_api_key": "HUBSPOT_API_KEY",
        "postgres_password": "POSTGRES_PASSWORD",
        "pinecone_api_key": "PINECONE_API_KEY",
        "supabase_service_role_key": "SUPABASE_SERVICE_ROLE_KEY",
        "langchain_api_key": "LANGCHAIN_API_KEY",
        "coingecko_api_key": "COINGECKO_API_KEY",
        "dexscreener_api_key": "DEXSCREENER_API_KEY",
        "santiment_api_key": "SANTIMENT_API_KEY",
        "newsapi_key": "NEWSAPI_KEY",
        "cryptopanic_api_key": "CRYPTOPANIC_API_KEY"
    }
    
    return secret_manager.load_secrets_to_settings(secret_mapping)


# Initialize logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
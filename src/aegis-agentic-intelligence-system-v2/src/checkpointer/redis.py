"""
Redis checkpointer for LangGraph conversational memory.

This module provides Redis-based checkpointing for LangGraph agents,
enabling conversation history and state persistence.
"""

import logging
from typing import Optional, Dict, Any
import redis.asyncio as redis
from langchain_community.checkpoint import RedisSaver

from src.config import settings

logger = logging.getLogger(__name__)


class RedisCheckpointer:
    """
    Redis-based checkpointer for LangGraph state management.
    
    Provides conversation history and state persistence across agent interactions.
    """
    
    def __init__(self):
        self.redis_config = settings.get_redis_config()
        self._redis_client: Optional[redis.Redis] = None
        self._saver: Optional[RedisSaver] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize Redis connection and checkpointer."""
        try:
            # Create Redis client
            self._redis_client = redis.Redis(
                host=self.redis_config.host,
                port=self.redis_config.port,
                db=self.redis_config.db,
                decode_responses=True,
                health_check_interval=30
            )
            
            # Test connection
            await self._redis_client.ping()
            
            # Initialize RedisSaver
            self._saver = RedisSaver(self._redis_client)
            
            self._initialized = True
            logger.info("Redis checkpointer initialized successfully")
            
        except Exception as e:
            logger.error(f"Redis checkpointer initialization failed: {e}")
            raise
    
    async def close(self):
        """Close Redis connection."""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
            self._saver = None
            self._initialized = False
            logger.info("Redis checkpointer closed")
    
    def get_saver(self) -> RedisSaver:
        """
        Get the Redis saver instance for LangGraph.
        
        Returns:
            RedisSaver instance for use with LangGraph
        """
        if not self._initialized or not self._saver:
            raise RuntimeError("Redis checkpointer not initialized")
        
        return self._saver
    
    async def save_checkpoint(
        self,
        thread_id: str,
        checkpoint_data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Save a checkpoint for a specific thread.
        
        Args:
            thread_id: Unique thread identifier
            checkpoint_data: Checkpoint data to save
            metadata: Optional metadata
            
        Returns:
            True if successful, False otherwise
        """
        if not self._redis_client:
            raise RuntimeError("Redis checkpointer not initialized")
        
        try:
            # Create checkpoint key
            checkpoint_key = f"checkpoint:{thread_id}"
            
            # Save checkpoint data
            await self._redis_client.hset(
                checkpoint_key,
                mapping={
                    "data": str(checkpoint_data),
                    "metadata": str(metadata or {})
                }
            )
            
            # Set expiration (optional, adjust as needed)
            await self._redis_client.expire(checkpoint_key, 86400)  # 24 hours
            
            logger.info(f"Checkpoint saved for thread: {thread_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save checkpoint for thread {thread_id}: {e}")
            return False
    
    async def load_checkpoint(
        self,
        thread_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Load a checkpoint for a specific thread.
        
        Args:
            thread_id: Unique thread identifier
            
        Returns:
            Checkpoint data or None if not found
        """
        if not self._redis_client:
            raise RuntimeError("Redis checkpointer not initialized")
        
        try:
            checkpoint_key = f"checkpoint:{thread_id}"
            
            # Load checkpoint data
            checkpoint = await self._redis_client.hgetall(checkpoint_key)
            
            if not checkpoint:
                logger.info(f"No checkpoint found for thread: {thread_id}")
                return None
            
            # Parse checkpoint data (simplified - you may need more sophisticated parsing)
            try:
                import ast
                data = ast.literal_eval(checkpoint.get("data", "{}"))
                metadata = ast.literal_eval(checkpoint.get("metadata", "{}"))
                
                return {
                    "data": data,
                    "metadata": metadata
                }
            except (ValueError, SyntaxError) as e:
                logger.error(f"Failed to parse checkpoint data: {e}")
                return None
            
        except Exception as e:
            logger.error(f"Failed to load checkpoint for thread {thread_id}: {e}")
            return None
    
    async def delete_checkpoint(self, thread_id: str) -> bool:
        """
        Delete a checkpoint for a specific thread.
        
        Args:
            thread_id: Unique thread identifier
            
        Returns:
            True if successful, False otherwise
        """
        if not self._redis_client:
            raise RuntimeError("Redis checkpointer not initialized")
        
        try:
            checkpoint_key = f"checkpoint:{thread_id}"
            result = await self._redis_client.delete(checkpoint_key)
            
            if result:
                logger.info(f"Checkpoint deleted for thread: {thread_id}")
                return True
            else:
                logger.info(f"No checkpoint found to delete for thread: {thread_id}")
                return False
            
        except Exception as e:
            logger.error(f"Failed to delete checkpoint for thread {thread_id}: {e}")
            return False
    
    async def list_checkpoints(self, pattern: str = "checkpoint:*") -> List[str]:
        """
        List all checkpoint keys matching a pattern.
        
        Args:
            pattern: Redis key pattern
            
        Returns:
            List of checkpoint keys
        """
        if not self._redis_client:
            raise RuntimeError("Redis checkpointer not initialized")
        
        try:
            keys = await self._redis_client.keys(pattern)
            return [key.replace("checkpoint:", "") for key in keys]
            
        except Exception as e:
            logger.error(f"Failed to list checkpoints: {e}")
            return []
    
    async def cleanup_old_checkpoints(self, max_age_hours: int = 24) -> int:
        """
        Clean up old checkpoints based on age.
        
        Args:
            max_age_hours: Maximum age in hours for checkpoints
            
        Returns:
            Number of checkpoints cleaned up
        """
        if not self._redis_client:
            raise RuntimeError("Redis checkpointer not initialized")
        
        try:
            # Get all checkpoint keys
            checkpoint_keys = await self._redis_client.keys("checkpoint:*")
            
            cleaned_count = 0
            max_age_seconds = max_age_hours * 3600
            
            for key in checkpoint_keys:
                # Check TTL
                ttl = await self._redis_client.ttl(key)
                
                # If TTL is close to expiring or already expired, clean it up
                if ttl <= 0 or ttl < (86400 - max_age_seconds):
                    await self._redis_client.delete(key)
                    cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} old checkpoints")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old checkpoints: {e}")
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get Redis and checkpointer statistics.
        
        Returns:
            Dictionary with statistics
        """
        if not self._redis_client:
            return {"status": "not_initialized"}
        
        try:
            # Get Redis info
            info = await self._redis_client.info()
            
            # Count checkpoints
            checkpoint_count = len(await self._redis_client.keys("checkpoint:*"))
            
            return {
                "status": "operational",
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory": info.get("used_memory_human"),
                "checkpoint_count": checkpoint_count,
                "total_keys": info.get("keyspace", {}).get("db0", {}).get("keys", 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to get Redis stats: {e}")
            return {"status": "error", "error": str(e)}
    
    async def health_check(self) -> bool:
        """
        Check Redis connectivity and health.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            if not self._redis_client:
                return False
            
            # Simple ping test
            pong = await self._redis_client.ping()
            return pong is True
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False


# Global checkpointer instance
redis_checkpointer = RedisCheckpointer()


# Convenience functions

async def initialize_checkpointer():
    """Initialize the global Redis checkpointer."""
    await redis_checkpointer.initialize()


async def get_checkpointer_saver():
    """Get the Redis saver for LangGraph usage."""
    return redis_checkpointer.get_saver()


async def save_conversation_state(
    conversation_id: str,
    state_data: Dict[str, Any],
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Save conversation state to Redis.
    
    Args:
        conversation_id: Unique conversation identifier
        state_data: State data to save
        metadata: Optional metadata
        
    Returns:
        True if successful, False otherwise
    """
    return await redis_checkpointer.save_checkpoint(
        thread_id=conversation_id,
        checkpoint_data=state_data,
        metadata=metadata
    )


async def load_conversation_state(conversation_id: str) -> Optional[Dict[str, Any]]:
    """
    Load conversation state from Redis.
    
    Args:
        conversation_id: Unique conversation identifier
        
    Returns:
        State data or None if not found
    """
    return await redis_checkpointer.load_checkpoint(thread_id=conversation_id)
# Minimal AEGIS test deployment
FROM python:3.11-slim

WORKDIR /app

# Install minimal dependencies
RUN pip install fastapi uvicorn pydantic pydantic-settings

# Copy only essential files
COPY main.py .
COPY src/ src/

# Create a simple .env file
RUN echo "ENVIRONMENT=production" > .env.local && \
    echo "GCP_PROJECT_ID=tkcgroup-v4" >> .env.local && \
    echo "GCP_REGION=us-west1" >> .env.local && \
    echo "GOOGLE_API_KEY=test" >> .env.local && \
    echo "POSTGRES_HOST=localhost" >> .env.local && \
    echo "POSTGRES_USER=postgres" >> .env.local && \
    echo "POSTGRES_PASSWORD=test" >> .env.local && \
    echo "POSTGRES_DB=test" >> .env.local

EXPOSE 8080

CMD ["python", "main.py"]
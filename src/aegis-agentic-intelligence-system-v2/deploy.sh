#!/bin/bash

# Deployment script for AEGIS - Agentic Intelligence System
# Deploys cryptocurrency analysis agent to Google Cloud Platform

set -e

# Configuration
PROJECT_ID="tkcgroup-v4"
REGION="us-west1"
SERVICE_NAME="aegis-agentic-intelligence-system"
API_SERVICE="${SERVICE_NAME}"
WORKER_SERVICE="${SERVICE_NAME}-worker"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploying AEGIS to Google Cloud Platform${NC}"
echo -e "${BLUE}Project: ${PROJECT_ID}${NC}"
echo -e "${BLUE}Region: ${REGION}${NC}"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK.${NC}"
    exit 1
fi

# Check if logged in to gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not logged in to gcloud. Please run 'gcloud auth login'${NC}"
    exit 1
fi

# Set the project
echo -e "${YELLOW}📋 Setting project to ${PROJECT_ID}${NC}"
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required Google Cloud APIs${NC}"
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable sql-component.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable pubsub.googleapis.com

# Create secrets if they don't exist
echo -e "${YELLOW}🔐 Checking and creating secrets${NC}"

create_secret_if_not_exists() {
    local secret_name=$1
    local secret_value=$2
    
    if ! gcloud secrets describe ${secret_name} >/dev/null 2>&1; then
        echo "Creating secret: ${secret_name}"
        echo -n "${secret_value}" | gcloud secrets create ${secret_name} --data-file=-
    else
        echo "Secret ${secret_name} already exists"
    fi
}

# Read from .env.local if it exists, otherwise prompt for values
if [ -f ".env.local" ]; then
    echo -e "${GREEN}📄 Reading configuration from .env.local${NC}"
    source .env.local
else
    echo -e "${YELLOW}⚠️  .env.local not found. You'll need to create secrets manually.${NC}"
fi

# Check if we have the required environment variables
if [ ! -z "${GOOGLE_API_KEY}" ]; then
    create_secret_if_not_exists "GOOGLE_API_KEY" "${GOOGLE_API_KEY}"
fi

if [ ! -z "${HUBSPOT_API_KEY}" ]; then
    create_secret_if_not_exists "HUBSPOT_API_KEY" "${HUBSPOT_API_KEY}"
fi

if [ ! -z "${POSTGRES_PASSWORD}" ]; then
    create_secret_if_not_exists "POSTGRES_PASSWORD" "${POSTGRES_PASSWORD}"
fi

if [ ! -z "${PINECONE_API_KEY}" ]; then
    create_secret_if_not_exists "PINECONE_API_KEY" "${PINECONE_API_KEY}"
fi

if [ ! -z "${SUPABASE_SERVICE_ROLE_KEY}" ]; then
    create_secret_if_not_exists "SUPABASE_SERVICE_ROLE_KEY" "${SUPABASE_SERVICE_ROLE_KEY}"
fi

if [ ! -z "${LANGCHAIN_API_KEY}" ]; then
    create_secret_if_not_exists "LANGCHAIN_API_KEY" "${LANGCHAIN_API_KEY}"
fi

# AEGIS-specific secrets (using existing AEGIS_ prefixed secrets)
echo -e "${GREEN}📋 AEGIS secrets already exist in Secret Manager:${NC}"
echo "  - AEGIS_COINGECKO_API_KEY"
echo "  - AEGIS_CRYPTOPANIC_API_KEY"
echo "  - AEGIS_SANTIMENT_API_KEY"
echo "  - AEGIS_NEWSDATA_API_KEY"
echo "  - AEGIS_ALCHEMY_API_KEY"
echo "  - AEGIS_LANGCHAIN_API_KEY"
echo "  - AEGIS_REDIS_URL"
echo "  - AEGIS_SUPABASE_SERVICE_ROLE_KEY"

# Create/update standard secrets that Cloud Run will use
if [ ! -z "${COINGECKO_API_KEY}" ]; then
    create_secret_if_not_exists "COINGECKO_API_KEY" "${COINGECKO_API_KEY}"
fi

if [ ! -z "${CRYPTOPANIC_API_KEY}" ]; then
    create_secret_if_not_exists "CRYPTOPANIC_API_KEY" "${CRYPTOPANIC_API_KEY}"
fi

if [ ! -z "${SANTIMENT_API_KEY}" ]; then
    create_secret_if_not_exists "SANTIMENT_API_KEY" "${SANTIMENT_API_KEY}"
fi

if [ ! -z "${NEWSDATA_API_KEY}" ]; then
    create_secret_if_not_exists "NEWSDATA_API_KEY" "${NEWSDATA_API_KEY}"
fi

if [ ! -z "${ALCHEMY_API_KEY}" ]; then
    create_secret_if_not_exists "ALCHEMY_API_KEY" "${ALCHEMY_API_KEY}"
fi

# Build and deploy API service
echo -e "${YELLOW}🏗️  Building and deploying API service${NC}"
gcloud run deploy ${API_SERVICE} \
    --source . \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --max-instances 10 \
    --min-instances 0 \
    --port 8080 \
    --set-env-vars="GCP_PROJECT_ID=${PROJECT_ID},GCP_REGION=${REGION},ENVIRONMENT=production" \
    --set-secrets="GOOGLE_API_KEY=GOOGLE_API_KEY:latest,LANGCHAIN_API_KEY=LANGCHAIN_API_KEY:latest,COINGECKO_API_KEY=COINGECKO_API_KEY:latest,CRYPTOPANIC_API_KEY=CRYPTOPANIC_API_KEY:latest,SANTIMENT_API_KEY=SANTIMENT_API_KEY:latest,NEWSDATA_API_KEY=NEWSDATA_API_KEY:latest,ALCHEMY_API_KEY=ALCHEMY_API_KEY:latest,SUPABASE_SERVICE_ROLE_KEY=SUPABASE_SERVICE_ROLE_KEY:latest"

# Get the API service URL
API_URL=$(gcloud run services describe ${API_SERVICE} --region=${REGION} --format="value(status.url)")
echo -e "${GREEN}✅ API Service deployed successfully!${NC}"
echo -e "${GREEN}📍 API URL: ${API_URL}${NC}"

# AEGIS-specific setup
echo -e "${YELLOW}🔧 Setting up AEGIS-specific resources${NC}"

# Create Redis instance for session management and caching
echo -e "${YELLOW}📦 Setting up Redis for session management${NC}"
if ! gcloud redis instances describe aegis-redis --region=${REGION} >/dev/null 2>&1; then
    gcloud redis instances create aegis-redis \
        --size=1 \
        --region=${REGION} \
        --redis-version=redis_6_x \
        --tier=basic
    echo "Created Redis instance: aegis-redis"
else
    echo "Redis instance aegis-redis already exists"
fi

# Create Pub/Sub topic for async tasks if needed
echo -e "${YELLOW}📡 Setting up Pub/Sub for async tasks${NC}"
if ! gcloud pubsub topics describe aegis-tasks >/dev/null 2>&1; then
    gcloud pubsub topics create aegis-tasks
    echo "Created Pub/Sub topic: aegis-tasks"
else
    echo "Pub/Sub topic aegis-tasks already exists"
fi

# Optional: Deploy worker to GKE (commented out by default)
# echo -e "${YELLOW}🐛 Deploying worker service to GKE${NC}"
# 
# # Check if GKE cluster exists
# if ! gcloud container clusters describe agent-workers --region=${REGION} >/dev/null 2>&1; then
#     echo "Creating GKE cluster for workers..."
#     gcloud container clusters create agent-workers \
#         --region=${REGION} \
#         --num-nodes=1 \
#         --machine-type=e2-standard-2 \
#         --enable-autorepair \
#         --enable-autoupgrade \
#         --enable-autoscaling \
#         --min-nodes=0 \
#         --max-nodes=5
# fi
# 
# # Build and push worker image
# gcloud builds submit --tag gcr.io/${PROJECT_ID}/${WORKER_SERVICE} --dockerfile Dockerfile.worker
# 
# # Deploy to GKE (you would need Kubernetes manifests for this)
# echo "Worker image built and pushed to gcr.io/${PROJECT_ID}/${WORKER_SERVICE}"

# Test the deployment
echo -e "${YELLOW}🧪 Testing deployment${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" ${API_URL}/)

if [ ${HTTP_STATUS} -eq 200 ]; then
    echo -e "${GREEN}✅ Health check passed! API is responding.${NC}"
else
    echo -e "${RED}❌ Health check failed! HTTP status: ${HTTP_STATUS}${NC}"
fi

# Display summary
echo -e "${BLUE}🎉 AEGIS Deployment Summary${NC}"
echo -e "${GREEN}✅ AEGIS API Service: ${API_URL}${NC}"
echo -e "${GREEN}✅ Crypto Analysis Endpoints: ${API_URL}/v1/crypto/{analyze,chat}${NC}"
echo -e "${GREEN}✅ API Documentation: ${API_URL}/docs${NC}"
echo -e "${GREEN}✅ Pub/Sub Topic: aegis-tasks${NC}"
echo -e "${GREEN}✅ Redis Instance: aegis-redis${NC}"
echo -e "${GREEN}✅ Region: ${REGION}${NC}"
echo -e "${GREEN}✅ Project: ${PROJECT_ID}${NC}"

echo -e "${BLUE}📚 Useful Commands:${NC}"
echo -e "${YELLOW}View logs:${NC} gcloud run services logs read ${API_SERVICE} --region=${REGION}"
echo -e "${YELLOW}Update service:${NC} gcloud run services update ${API_SERVICE} --region=${REGION}"
echo -e "${YELLOW}Delete service:${NC} gcloud run services delete ${API_SERVICE} --region=${REGION}"

echo -e "${GREEN}🚀 Deployment completed successfully!${NC}"
# AEGIS v2 - Hybrid Crypto Analysis Agent
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy hybrid requirements
COPY requirements-hybrid.txt .
RUN pip install --no-cache-dir -r requirements-hybrid.txt

# Copy hybrid main file
COPY main_hybrid.py .

# Create health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose port
EXPOSE 8080

# Set environment variables
ENV PORT=8080
ENV PYTHONPATH=/app

# Run the application
CMD ["python", "main_hybrid.py"]
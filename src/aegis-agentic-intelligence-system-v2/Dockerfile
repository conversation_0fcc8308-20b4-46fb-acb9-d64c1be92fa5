# AEGIS - Agentic Intelligence System v2
FROM python:3.11-slim

WORKDIR /app

# Install essential dependencies
RUN pip install --no-cache-dir \
    fastapi \
    uvicorn \
    pydantic \
    pydantic-settings \
    langchain \
    langchain-google-vertexai \
    langchain-google-genai \
    langchain-core \
    langgraph \
    python-dotenv \
    httpx \
    redis \
    asyncpg \
    cloud-sql-python-connector[pg8000] \
    google-cloud-secret-manager \
    google-cloud-aiplatform

# Copy only essential files
COPY main.py .
COPY src/ src/

# Create a simple .env file
RUN echo "ENVIRONMENT=production" > .env.local && \
    echo "GCP_PROJECT_ID=tkcgroup-v4" >> .env.local && \
    echo "GCP_REGION=us-west1" >> .env.local && \
    echo "GOOGLE_API_KEY=test" >> .env.local && \
    echo "POSTGRES_HOST=localhost" >> .env.local && \
    echo "POSTGRES_USER=postgres" >> .env.local && \
    echo "POSTGRES_PASSWORD=test" >> .env.local && \
    echo "POSTGRES_DB=test" >> .env.local

EXPOSE 8080

CMD ["python", "main.py"]
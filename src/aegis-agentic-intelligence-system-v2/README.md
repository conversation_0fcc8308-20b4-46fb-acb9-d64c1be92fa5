# AEGIS - Agentic Intelligence System v2

**Advanced Cryptocurrency Analysis Agent with LangGraph and Google Cloud Platform**

AEGIS (Agentic Intelligence System) is a sophisticated cryptocurrency analysis agent that provides comprehensive market analysis, regulatory monitoring, and technical insights using AI-powered tools and real-time data sources.

## 🚀 Overview

This is the Python v2 migration of the original TypeScript AEGIS agent, built with:

- **LangGraph**: Advanced AI agent workflows
- **FastAPI**: High-performance API framework  
- **Google Cloud Platform**: Scalable cloud infrastructure
- **Gemini 2.0 Flash**: State-of-the-art language model
- **Redis**: Session management and caching
- **PostgreSQL**: Persistent data storage

## 🎯 Features

### Core Capabilities
- **Cryptocurrency Analysis**: Real-time market data and metrics
- **Regulatory Monitoring**: News and regulatory updates tracking
- **Technical Analysis**: Trading signals and technical indicators
- **Sentiment Analysis**: Market sentiment and social media monitoring
- **DeFi Metrics**: Decentralized finance protocol analytics
- **Portfolio Risk Assessment**: Risk calculation and recommendations

### Technical Features
- **Conversational Interface**: Natural language crypto analysis
- **Streaming Responses**: Real-time analysis updates
- **Session Management**: Persistent conversation contexts
- **Rate Limiting**: API usage controls
- **Error Handling**: Robust error recovery and logging
- **Monitoring**: Comprehensive observability

## 🏗️ Architecture

```
AEGIS v2 Architecture
├── FastAPI Application (src/main.py)
├── LangGraph Agent (src/agents/template_agent/aegis_graph.py)
├── Crypto Analysis Tools (src/agents/template_agent/tools.py)
├── Configuration Management (src/config.py)
├── Cloud Run Deployment (deploy.sh)
└── Redis Session Storage
```

## 📦 Installation

### Prerequisites
- Python 3.9+
- Google Cloud SDK
- Redis (for local development)
- PostgreSQL (optional)

### Local Development

1. **Clone and setup**:
```bash
cd apps/agents/aegis-agentic-intelligence-system-v2
cp .env.example .env.local
# Edit .env.local with your configuration
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Run the application**:
```bash
python main.py
```

4. **Test the migration**:
```bash
python test_migration.py
```

### Google Cloud Deployment

1. **Configure environment**:
```bash
# Set your GCP project
gcloud config set project tkcgroup-v4

# Ensure you have the required APIs enabled
gcloud services enable run.googleapis.com cloudbuild.googleapis.com secretmanager.googleapis.com
```

2. **Deploy to Cloud Run**:
```bash
./deploy.sh
```

## 🔧 API Endpoints

### Health Check
```
GET /
```

### Cryptocurrency Analysis
```
POST /v1/crypto/analyze
{
  "symbol": "BTC",
  "analysis_type": "comprehensive",
  "timeframe": "7d",
  "include_sentiment": true,
  "include_on_chain": true,
  "include_defi": false
}
```

### Conversational Analysis
```
POST /v1/crypto/chat
{
  "message": "What's the current market sentiment for Bitcoin?",
  "session_id": "user123"
}
```

### Supported Symbols
```
GET /v1/crypto/supported-symbols
```

## 📈 Frontend Trading Interface

AEGIS v2 includes a comprehensive, mobile-first trading interface built with Next.js and shadcn/ui.

**URL**: `/agents/trade`

### Features
- **Asset Selection**: A searchable dropdown to select from all supported cryptocurrencies.
- **Real-Time Data**: Live updates for price, 24h change, market cap, and volume.
- **Interactive Charting**: (In Development) Financial chart for technical analysis.
- **Tabbed Dashboard**: Organized sections for managing positions, orders, and trade history.
- **Responsive Design**: Fully functional on both desktop and mobile devices.

### Data Integration
- The interface fetches data directly from the AEGIS agent's API endpoints:
  - `GET /v1/crypto/supported-symbols` to populate the asset list.
  - `POST /v1/crypto/analyze` to retrieve detailed market data for the selected asset.

## 🧪 Testing

Run the validation tests:
```bash
python test_migration.py
```

Expected output:
```
🚀 Starting AEGIS Migration Validation Tests
==================================================
✅ Basic Imports: PASSED
✅ Configuration Validation: PASSED
✅ Tools Functionality: PASSED
✅ AEGIS Agent: PASSED
✅ FastAPI Imports: PASSED

🎯 Test Results: 5 passed, 0 failed
🎉 All tests passed! AEGIS migration is successful.
```

## 📊 Available Analysis Tools

### Core Tools
- `get_on_chain_metrics`: Retrieve blockchain metrics for cryptocurrencies
- `get_regulatory_news`: Fetch regulatory updates and news
- `analyze_market_sentiment`: Analyze social media and market sentiment
- `get_defi_metrics`: DeFi protocol analytics
- `calculate_portfolio_risk`: Risk assessment and recommendations
- `analyze_trading_signals`: Technical analysis and trading signals
- `generate_crypto_report`: Comprehensive analysis reports

### Integration APIs
- **CoinGecko**: Market data and cryptocurrency information
- **DEX Screener**: DEX trading data (planned)
- **Santiment**: Social sentiment analysis (planned)
- **NewsAPI**: Regulatory news monitoring (planned)
- **CryptoPanic**: Crypto news aggregation (planned)

## 🔐 Configuration

### Environment Variables
Key configuration options in `.env.local`:

```bash
# Required
GOOGLE_API_KEY=your_gemini_api_key
GCP_PROJECT_ID=tkcgroup-v4
GCP_REGION=us-west1

# Optional API Keys
COINGECKO_API_KEY=your_coingecko_key
NEWSAPI_KEY=your_newsapi_key
SANTIMENT_API_KEY=your_santiment_key

# Analysis Settings
DEFAULT_ANALYSIS_TIMEFRAME=7d
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_TECHNICAL_ANALYSIS=true
```

### Secret Management
In production, secrets are managed via Google Secret Manager:
- `GOOGLE_API_KEY`: Gemini API access
- `COINGECKO_API_KEY`: CoinGecko API access
- `NEWSAPI_KEY`: News API access
- `POSTGRES_PASSWORD`: Database password

## 🚀 Deployment

### Cloud Run Configuration
- **Service**: `aegis-agentic-intelligence-system`
- **Region**: `us-west1`
- **Memory**: 2Gi
- **CPU**: 2
- **Max Instances**: 10
- **Min Instances**: 0

### Supporting Services
- **Redis**: Session management and caching
- **Pub/Sub**: Async task processing
- **Secret Manager**: Secure credential storage
- **Cloud SQL**: Optional persistent storage

## 📈 Monitoring

### Health Checks
- **Endpoint**: `GET /`
- **Status**: `GET /v1/status`

### Logging
- Structured logging with correlation IDs
- Request/response tracking
- Error monitoring with Sentry integration

### Metrics
- API response times
- Analysis request rates
- Error rates and types
- Resource utilization

## 🔄 Migration Notes

### TypeScript to Python Migration
- **LangGraph**: Maintained workflow compatibility
- **Tools**: Converted to Python async functions
- **API**: Enhanced with new crypto-specific endpoints
- **Configuration**: Expanded for crypto API integrations
- **Deployment**: Updated for AEGIS-specific resources

### Key Improvements
- **Performance**: Async-first architecture
- **Scalability**: Cloud-native design
- **Monitoring**: Enhanced observability
- **Security**: Secret management integration
- **Flexibility**: Modular tool architecture

## 🛠️ Development

### Adding New Tools
1. Create tool function in `src/agents/template_agent/tools.py`
2. Add to `ALL_AEGIS_TOOLS` list
3. Update API documentation

### Testing
- Unit tests for individual tools
- Integration tests for workflows
- Load testing for performance validation

### Debugging
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# View Cloud Run logs
gcloud run services logs read aegis-agentic-intelligence-system --region=us-west1
```

## 📚 Documentation

- **API Docs**: Available at `/docs` when running
- **OpenAPI**: Available at `/redoc` when running
- **Architecture**: See `docs/architecture.md`
- **Deployment**: See `docs/deployment.md`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is part of the TKC Group internal tools and is proprietary software.

## 🆘 Support

For issues and support:
- Check the logs: `gcloud run services logs read aegis-agentic-intelligence-system --region=us-west1`
- Review the API documentation at `/docs`
- Contact the development team

---

**AEGIS v2**: Empowering intelligent cryptocurrency analysis with advanced AI capabilities.
"""
Configuration management for the Vertex AI Agent.

This module handles loading configuration from GCP Secret Manager
and environment variables, following production-grade practices.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from google.cloud import secretmanager
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class PersonaConfig(BaseModel):
    """BDR persona configuration."""
    name: str = "TKC Group BDR"
    role: str = "Business Development Representative"
    company: str = "TKC Group"
    tone: str = "professional and helpful"
    expertise_areas: list = ["AI automation", "business process optimization"]


class AgentConfig(BaseModel):
    """Agent configuration model."""
    model_name: str = "gemini-2.5-flash"
    location: str = "us-central1"
    temperature: float = 0.7
    max_tokens: int = 8192
    persona: PersonaConfig = Field(default_factory=PersonaConfig)


class GmailConfig(BaseModel):
    """Gmail configuration model."""
    subject_email: str = "<EMAIL>"
    scopes: list = [
        "https://www.googleapis.com/auth/gmail.readonly",
        "https://www.googleapis.com/auth/gmail.send",
        "https://www.googleapis.com/auth/gmail.modify",
        "https://www.googleapis.com/auth/gmail.compose"
    ]
    domain: str = "tkcgroup.co"


class Settings:
    """
    Application settings loaded from GCP Secret Manager and environment variables.
    """
    
    def __init__(self, project_id: Optional[str] = None):
        self.project_id = project_id or os.getenv("GOOGLE_CLOUD_PROJECT", "vertex-ai-agent-yzdlnjey")
        self.secret_client = None
        self._agent_config = None
        self._gmail_config = None
        self._env_vars = None
        
        # Initialize Secret Manager client
        try:
            self.secret_client = secretmanager.SecretManagerServiceClient()
            logger.info("Secret Manager client initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Secret Manager client: {e}")
    
    def _get_secret(self, secret_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a secret from GCP Secret Manager.
        
        Args:
            secret_name: Name of the secret
            
        Returns:
            Secret data as dictionary or None if not found
        """
        if not self.secret_client:
            logger.warning(f"Secret Manager client not available, cannot fetch {secret_name}")
            return None
        
        try:
            secret_path = f"projects/{self.project_id}/secrets/{secret_name}/versions/latest"
            response = self.secret_client.access_secret_version(request={"name": secret_path})
            secret_data = response.payload.data.decode("UTF-8")
            return json.loads(secret_data)
            
        except Exception as e:
            logger.warning(f"Failed to get secret {secret_name}: {e}")
            return None
    
    @property
    def agent_config(self) -> AgentConfig:
        """Get agent configuration."""
        if self._agent_config is None:
            secret_data = self._get_secret("agent-config")
            if secret_data:
                self._agent_config = AgentConfig(**secret_data)
            else:
                # Fallback to default configuration
                self._agent_config = AgentConfig()
                logger.info("Using default agent configuration")
        
        return self._agent_config
    
    @property
    def gmail_config(self) -> GmailConfig:
        """Get Gmail configuration."""
        if self._gmail_config is None:
            secret_data = self._get_secret("gmail-config")
            if secret_data:
                self._gmail_config = GmailConfig(**secret_data)
            else:
                # Fallback to default configuration
                self._gmail_config = GmailConfig()
                logger.info("Using default Gmail configuration")
        
        return self._gmail_config
    
    @property
    def env_vars(self) -> Dict[str, str]:
        """Get environment variables from secret or environment."""
        if self._env_vars is None:
            # Try to get from secret first
            secret_data = self._get_secret("env-variables")
            if secret_data:
                self._env_vars = secret_data
            else:
                # Fallback to actual environment variables
                self._env_vars = {
                    "GOOGLE_CLOUD_PROJECT": os.getenv("GOOGLE_CLOUD_PROJECT", self.project_id),
                    "VERTEX_AI_LOCATION": os.getenv("VERTEX_AI_LOCATION", "us-central1"),
                    "GMAIL_SUBJECT_EMAIL": os.getenv("GMAIL_SUBJECT_EMAIL", "<EMAIL>"),
                    "LOG_LEVEL": os.getenv("LOG_LEVEL", "INFO"),
                    "ENVIRONMENT": os.getenv("ENVIRONMENT", "development")
                }
                logger.info("Using environment variables as fallback")
        
        return self._env_vars
    
    def get_gmail_service_account_key(self) -> Optional[Dict[str, Any]]:
        """
        Get Gmail service account key from Secret Manager.
        
        Returns:
            Service account key as dictionary or None if not found
        """
        return self._get_secret("gmail-service-account-key")
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.env_vars.get("ENVIRONMENT", "development").lower() == "production"
    
    def get_log_level(self) -> str:
        """Get logging level."""
        return self.env_vars.get("LOG_LEVEL", "INFO")


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def configure_logging():
    """Configure logging based on settings."""
    log_level = getattr(logging, settings.get_log_level().upper(), logging.INFO)
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Set specific loggers
    if settings.is_production():
        # In production, reduce noise from external libraries
        logging.getLogger("google").setLevel(logging.WARNING)
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        logging.getLogger("requests").setLevel(logging.WARNING)
    
    logger.info(f"Logging configured for {settings.env_vars.get('ENVIRONMENT', 'development')} environment")


# Initialize logging when module is imported
configure_logging()

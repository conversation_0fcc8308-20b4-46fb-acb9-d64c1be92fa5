"""
Comprehensive Logging Configuration - TKC_v5 Executive Agent

Provides structured logging, log aggregation, and monitoring integration
for production-grade observability.
"""

import logging
import logging.config
import sys
import os
from typing import Dict, Any
from datetime import datetime

from google.cloud import logging as cloud_logging
import structlog

from config.settings import get_settings

settings = get_settings()


class CloudLoggingFormatter(logging.Formatter):
    """Custom formatter for Cloud Logging integration."""
    
    def format(self, record):
        # Add structured fields
        if not hasattr(record, 'customer_id'):
            record.customer_id = 'unknown'
        if not hasattr(record, 'environment'):
            record.environment = settings.environment
        if not hasattr(record, 'service'):
            record.service = 'tkc-v5-executive-agent'
        
        # Format timestamp
        record.timestamp = datetime.fromtimestamp(record.created).isoformat()
        
        return super().format(record)


class PerformanceFilter(logging.Filter):
    """Filter to add performance context to log records."""
    
    def filter(self, record):
        # Add performance tracking
        if not hasattr(record, 'request_id'):
            record.request_id = getattr(record, 'request_id', 'unknown')
        
        return True


def setup_logging():
    """Configure comprehensive logging for the application."""
    
    # Base logging configuration
    log_level = getattr(logging, settings.log_level.upper(), logging.INFO)
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    
    # Setup Cloud Logging if in production
    if settings.environment == 'production':
        try:
            client = cloud_logging.Client(project=settings.google_cloud_project)
            client.setup_logging(log_level=log_level)
        except Exception as e:
            logging.warning(f"Failed to setup Cloud Logging: {e}")
    
    # Configure structured logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.environment == 'production' 
            else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure specific loggers
    configure_application_loggers()
    
    # Log startup
    logger = structlog.get_logger()
    logger.info(
        "Logging configured",
        environment=settings.environment,
        log_level=settings.log_level,
        project_id=settings.google_cloud_project
    )


def configure_application_loggers():
    """Configure specific application loggers."""
    
    # Agent core logger
    agent_logger = logging.getLogger('src.agent')
    agent_logger.setLevel(logging.INFO)
    
    # Tools logger
    tools_logger = logging.getLogger('src.tools')
    tools_logger.setLevel(logging.INFO)
    
    # Services logger
    services_logger = logging.getLogger('src.services')
    services_logger.setLevel(logging.INFO)
    
    # Gmail integration logger
    gmail_logger = logging.getLogger('src.services.gmail_client')
    gmail_logger.setLevel(logging.DEBUG if settings.environment != 'production' else logging.INFO)
    
    # Calendar integration logger
    calendar_logger = logging.getLogger('src.services.calendar_client')
    calendar_logger.setLevel(logging.DEBUG if settings.environment != 'production' else logging.INFO)
    
    # CRM integration logger
    crm_logger = logging.getLogger('src.services.crm_client')
    crm_logger.setLevel(logging.DEBUG if settings.environment != 'production' else logging.INFO)
    
    # Database loggers
    redis_logger = logging.getLogger('src.services.redis_checkpointer')
    redis_logger.setLevel(logging.INFO)
    
    pinecone_logger = logging.getLogger('src.services.pinecone_tenant_manager')
    pinecone_logger.setLevel(logging.INFO)
    
    firestore_logger = logging.getLogger('src.services.data_persistence_service')
    firestore_logger.setLevel(logging.INFO)
    
    # External library loggers
    configure_external_loggers()


def configure_external_loggers():
    """Configure logging for external libraries."""
    
    # Reduce noise from external libraries
    logging.getLogger('google.auth').setLevel(logging.WARNING)
    logging.getLogger('google.cloud').setLevel(logging.WARNING)
    logging.getLogger('googleapiclient').setLevel(logging.WARNING)
    logging.getLogger('google_auth_httplib2').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    # LangChain/LangGraph loggers
    logging.getLogger('langchain').setLevel(logging.INFO)
    logging.getLogger('langgraph').setLevel(logging.INFO)
    logging.getLogger('langchain_google_vertexai').setLevel(logging.INFO)
    
    # Redis logger
    logging.getLogger('redis').setLevel(logging.WARNING)
    
    # Pinecone logger
    logging.getLogger('pinecone').setLevel(logging.INFO)
    
    # FastAPI/Uvicorn loggers
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('fastapi').setLevel(logging.INFO)


def get_request_logger(request_id: str, customer_id: str = None):
    """Get a logger bound to a specific request context."""
    logger = structlog.get_logger()
    
    context = {
        'request_id': request_id,
        'service': 'tkc-v5-executive-agent',
        'environment': settings.environment
    }
    
    if customer_id:
        context['customer_id'] = customer_id
    
    return logger.bind(**context)


def get_tool_logger(tool_name: str, customer_id: str = None):
    """Get a logger bound to a specific tool context."""
    logger = structlog.get_logger()
    
    context = {
        'tool_name': tool_name,
        'service': 'tkc-v5-executive-agent',
        'environment': settings.environment
    }
    
    if customer_id:
        context['customer_id'] = customer_id
    
    return logger.bind(**context)


def get_service_logger(service_name: str, customer_id: str = None):
    """Get a logger bound to a specific service context."""
    logger = structlog.get_logger()
    
    context = {
        'service_name': service_name,
        'service': 'tkc-v5-executive-agent',
        'environment': settings.environment
    }
    
    if customer_id:
        context['customer_id'] = customer_id
    
    return logger.bind(**context)


# Logging configuration dictionary for advanced setup
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        },
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(asctime)s %(name)s %(levelname)s %(message)s'
        },
        'cloud': {
            '()': CloudLoggingFormatter,
            'format': '%(timestamp)s [%(levelname)s] %(service)s:%(name)s - %(message)s'
        }
    },
    'filters': {
        'performance': {
            '()': PerformanceFilter,
        }
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'detailed',
            'filename': 'logs/tkc_v5_agent.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'detailed',
            'filename': 'logs/tkc_v5_errors.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        },
        'performance_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'json',
            'filename': 'logs/tkc_v5_performance.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'filters': ['performance']
        }
    },
    'loggers': {
        '': {  # root logger
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False
        },
        'src.agent': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False
        },
        'src.tools': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False
        },
        'src.services': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False
        },
        'performance': {
            'handlers': ['performance_file'],
            'level': 'INFO',
            'propagate': False
        },
        'errors': {
            'handlers': ['error_file', 'console'],
            'level': 'ERROR',
            'propagate': False
        }
    }
}


def setup_advanced_logging():
    """Setup advanced logging configuration."""
    # Ensure log directory exists
    os.makedirs('logs', exist_ok=True)
    
    # Apply logging configuration
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # Setup structured logging
    setup_logging()


# Performance logging utilities
class PerformanceLogger:
    """Utility class for performance logging."""
    
    def __init__(self, operation_name: str, customer_id: str = None):
        self.operation_name = operation_name
        self.customer_id = customer_id
        self.logger = get_service_logger('performance', customer_id)
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(
            f"Starting {self.operation_name}",
            operation=self.operation_name,
            start_time=self.start_time.isoformat()
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds() * 1000  # ms
        
        if exc_type is None:
            self.logger.info(
                f"Completed {self.operation_name}",
                operation=self.operation_name,
                duration_ms=duration,
                success=True
            )
        else:
            self.logger.error(
                f"Failed {self.operation_name}",
                operation=self.operation_name,
                duration_ms=duration,
                success=False,
                error_type=exc_type.__name__,
                error_message=str(exc_val)
            )


# Initialize logging on module import
if settings.environment == 'production':
    setup_advanced_logging()
else:
    setup_logging()

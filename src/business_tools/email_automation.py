"""
Email automation tools for business operations.
"""

import logging
from typing import Dict, Any, List, Optional
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
from typing import Type
from src.services.gmail_client import create_gmail_client
from src.services.firestore_client import get_firestore_client

logger = logging.getLogger(__name__)

class EmailAutomationInput(BaseModel):
    """Input schema for email automation tool."""
    action: str = Field(description="Action to perform: 'send', 'schedule', 'template', 'sequence'")
    recipient: Optional[str] = Field(default=None, description="Email recipient")
    subject: Optional[str] = Field(default=None, description="Email subject")
    body: Optional[str] = Field(default=None, description="Email body content")
    template_name: Optional[str] = Field(default=None, description="Template name to use")
    schedule_time: Optional[str] = Field(default=None, description="Schedule time (ISO format)")
    sequence_id: Optional[str] = Field(default=None, description="Email sequence ID")

class EmailAutomationTool(BaseTool):
    """Tool for automating email operations."""
    
    name: str = "email_automation"
    description: str = """
    Automate email operations including:
    - Send immediate emails
    - Schedule emails for later
    - Use email templates
    - Manage email sequences
    - Track email performance
    """
    args_schema: Type[BaseModel] = EmailAutomationInput
    
    def __init__(self):
        super().__init__()
        self.gmail_client = None
        self.firestore_client = None
    
    async def _initialize_clients(self):
        """Initialize email and database clients."""
        if not self.gmail_client:
            self.gmail_client = await create_gmail_client()
        if not self.firestore_client:
            self.firestore_client = await get_firestore_client()
    
    def _run(self, **kwargs) -> str:
        """Synchronous run method (not used)."""
        raise NotImplementedError("Use async arun method")
    
    async def _arun(
        self,
        action: str,
        recipient: Optional[str] = None,
        subject: Optional[str] = None,
        body: Optional[str] = None,
        template_name: Optional[str] = None,
        schedule_time: Optional[str] = None,
        sequence_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """Execute email automation action."""
        try:
            await self._initialize_clients()
            
            if action == "send":
                return await self._send_email(recipient, subject, body)
            elif action == "schedule":
                return await self._schedule_email(recipient, subject, body, schedule_time)
            elif action == "template":
                return await self._use_template(template_name, recipient)
            elif action == "sequence":
                return await self._manage_sequence(sequence_id, recipient)
            else:
                return f"Unknown action: {action}. Available actions: send, schedule, template, sequence"
                
        except Exception as e:
            logger.error(f"Email automation error: {e}")
            return f"Error in email automation: {str(e)}"
    
    async def _send_email(
        self, 
        recipient: str, 
        subject: str, 
        body: str
    ) -> str:
        """Send an immediate email."""
        try:
            # Create email message
            message = {
                'to': recipient,
                'subject': subject,
                'body': body,
                'html': True
            }
            
            # Send via Gmail client
            result = await self.gmail_client.send_email(message)
            
            # Log to Firestore
            await self.firestore_client.create_document(
                collection='email_logs',
                data={
                    'action': 'send',
                    'recipient': recipient,
                    'subject': subject,
                    'status': 'sent',
                    'timestamp': result.get('timestamp'),
                    'message_id': result.get('id')
                }
            )
            
            return f"Email sent successfully to {recipient}. Message ID: {result.get('id')}"
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return f"Failed to send email: {str(e)}"
    
    async def _schedule_email(
        self, 
        recipient: str, 
        subject: str, 
        body: str, 
        schedule_time: str
    ) -> str:
        """Schedule an email for later delivery."""
        try:
            # Store scheduled email in Firestore
            scheduled_email = {
                'recipient': recipient,
                'subject': subject,
                'body': body,
                'schedule_time': schedule_time,
                'status': 'scheduled',
                'created_at': 'now'
            }
            
            doc_id = await self.firestore_client.create_document(
                collection='scheduled_emails',
                data=scheduled_email
            )
            
            return f"Email scheduled for {schedule_time}. Schedule ID: {doc_id}"
            
        except Exception as e:
            logger.error(f"Failed to schedule email: {e}")
            return f"Failed to schedule email: {str(e)}"
    
    async def _use_template(
        self, 
        template_name: str, 
        recipient: str
    ) -> str:
        """Use an email template."""
        try:
            # Get template from Firestore
            template = await self.firestore_client.get_document(
                collection='email_templates',
                document_id=template_name
            )
            
            if not template:
                return f"Template '{template_name}' not found"
            
            # Process template variables
            subject = template.get('subject', '')
            body = template.get('body', '')
            
            # Send email using template
            return await self._send_email(recipient, subject, body)
            
        except Exception as e:
            logger.error(f"Failed to use template: {e}")
            return f"Failed to use template: {str(e)}"
    
    async def _manage_sequence(
        self, 
        sequence_id: str, 
        recipient: str
    ) -> str:
        """Manage email sequence."""
        try:
            # Get sequence from Firestore
            sequence = await self.firestore_client.get_document(
                collection='email_sequences',
                document_id=sequence_id
            )
            
            if not sequence:
                return f"Email sequence '{sequence_id}' not found"
            
            # Add recipient to sequence
            await self.firestore_client.create_document(
                collection='sequence_subscribers',
                data={
                    'sequence_id': sequence_id,
                    'recipient': recipient,
                    'status': 'active',
                    'current_step': 0,
                    'subscribed_at': 'now'
                }
            )
            
            return f"Added {recipient} to email sequence '{sequence_id}'"
            
        except Exception as e:
            logger.error(f"Failed to manage sequence: {e}")
            return f"Failed to manage sequence: {str(e)}"

# Factory function
def create_email_automation_tool() -> EmailAutomationTool:
    """Create an email automation tool instance."""
    return EmailAutomationTool()

"""
FastAPI Web Service for the Vertex AI Agent

This module provides a REST API interface for the LangGraph agent,
suitable for deployment on Cloud Run or other container platforms.
"""

import os
import logging
import time
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# Enable LangChain debugging
import langchain
langchain.debug = True

from agent.core import create_agent, VertexAIAgent, TaskRequest, TaskType
from config.settings import get_settings, configure_logging
from webhooks.form_handler import FormSubmission, InboundLeadProcessor, get_lead_processor

# Configure logging for production
configure_logging()
logger = logging.getLogger(__name__)
settings = get_settings()

# Initialize FastAPI app with production configuration
app = FastAPI(
    title="TKC_v5 Executive Agent",
    description="Production-grade AI agent for email automation and business processes using LangGraph and Gemini",
    version="1.0.0",
    docs_url="/docs" if not settings.is_production() else None,  # Disable docs in production
    redoc_url="/redoc" if not settings.is_production() else None
)

# Add CORS middleware with production settings
allowed_origins = ["*"] if not settings.is_production() else [
    "https://tkcgroup.co",
    "https://www.tkcgroup.co",
    "https://app.tkcgroup.co"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# Global agent instance
agent: VertexAIAgent = None

# Global variables for Gmail webhook deduplication
_processed_history_ids = set()
_last_webhook_time = 0
_processed_emails = set()  # Track processed email IDs to prevent duplicate drafts
_draft_created_for = set()  # Track emails with drafts already created


# Middleware for request logging and timing
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests with timing information."""
    start_time = time.time()

    # Log request
    logger.info(f"Request: {request.method} {request.url.path}")

    # Process request
    response = await call_next(request)

    # Calculate processing time
    process_time = time.time() - start_time

    # Log response
    logger.info(f"Response: {response.status_code} - {process_time:.3f}s")

    # Add timing header
    response.headers["X-Process-Time"] = str(process_time)

    return response


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Handle all unhandled exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)

    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    )


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    message: str
    task_type: Optional[str] = "general"
    stream: bool = False


class ChatResponse(BaseModel):
    """Response model for chat endpoint."""
    response: str
    status: str


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    project_id: str
    model: str


class ToolsResponse(BaseModel):
    """Available tools response model."""
    tools: list
    count: int = "success"


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str
    project_id: str
    model: str


@app.on_event("startup")
async def startup_event():
    """Initialize the agent on startup."""
    global agent
    
    try:
        agent = create_agent()
        logger.info(f"Executive Agent initialized successfully for project: {settings.project_id}")
        logger.info(f"Environment: {settings.env_vars.get('ENVIRONMENT', 'development')}")
        logger.info(f"Model: {settings.agent_config.model_name}")
        logger.info(f"Location: {settings.agent_config.location}")

    except Exception as e:
        logger.error(f"Failed to initialize Executive Agent: {e}")
        raise


@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with basic information."""
    return {
        "service": "Vertex AI Agent API",
        "status": "running",
        "version": "1.0.0"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Comprehensive health check endpoint."""
    if agent is None:
        raise HTTPException(status_code=503, detail="Executive Agent not initialized")

    # Test agent functionality
    agent_status = "healthy"
    # For now, just check if agent is initialized
    # In production, you might want to do a quick async test

    return HealthResponse(
        status="healthy" if agent_status != "unhealthy" else "unhealthy",
        project_id=agent.project_id,
        model=agent.model_name
    )


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Chat with the AI agent.
    
    Args:
        request: Chat request containing the user message
        
    Returns:
        Agent's response
    """
    if agent is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        logger.info(f"Processing chat request: {request.message[:100]}...")
        
        # Create a task request
        from agent.state import TaskRequest, TaskType
        import time

        task_request = TaskRequest(
            task_id=f"task_{int(time.time())}",
            task_type=TaskType.GENERAL,
            input_data={"message": request.message}
        )

        # Execute the task
        task_response = await agent.execute_task(task_request)

        # Extract the response content
        if task_response.success and task_response.results:
            # Get the final result from the task response
            final_result = task_response.results.get('final_result')
            if final_result:
                response = str(final_result)
            else:
                # Fallback to other possible response keys
                response = (task_response.results.get('response') or
                           task_response.results.get('output') or
                           str(task_response.results))
        else:
            response = f"Error: {task_response.error or 'Unknown error occurred'}"
        
        logger.info("Chat request processed successfully")
        
        return ChatResponse(
            response=response,
            status="success"
        )
        
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/tools")
async def list_tools():
    """List available tools."""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    tools_info = []
    for tool in agent.tools:
        tools_info.append({
            "name": tool.name,
            "description": tool.description,
            "args": tool.args
        })
    
    return {"tools": tools_info}


@app.post("/test")
async def test_agent():
    """Test endpoint to verify agent functionality."""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    test_message = "Hello! Can you tell me what tools you have available?"

    try:
        # Create a task request
        from agent.state import TaskRequest, TaskType
        import time

        task_request = TaskRequest(
            task_id=f"test_task_{int(time.time())}",
            task_type=TaskType.GENERAL,
            input_data={"message": test_message}
        )

        # Execute the task
        task_response = await agent.execute_task(task_request)

        # Extract the response content
        if task_response.success and task_response.results:
            # Get the final result from the task response
            final_result = task_response.results.get('final_result')
            if final_result:
                response = str(final_result)
            else:
                # Fallback to other possible response keys
                response = (task_response.results.get('response') or
                           task_response.results.get('output') or
                           str(task_response.results))
        else:
            response = f"Error: {task_response.error or 'Unknown error occurred'}"

        return {
            "test_message": test_message,
            "agent_response": response,
            "status": "success"
        }
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise HTTPException(status_code=500, detail=f"Test failed: {str(e)}")


@app.post("/webhook/gmail")
async def gmail_webhook(request: Request):
    """
    Webhook endpoint for Gmail push notifications via Pub/Sub.

    This endpoint receives Gmail push notifications when new emails arrive
    and automatically processes them.
    """
    if agent is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")

    try:
        # Parse Pub/Sub message
        import base64
        import json

        body = await request.body()
        envelope = json.loads(body.decode('utf-8'))

        # Extract the Pub/Sub message
        if 'message' not in envelope:
            logger.warning("No message in Pub/Sub envelope")
            return {"status": "no_message"}

        pubsub_message = envelope['message']

        # Decode the data if present
        if 'data' in pubsub_message:
            data = base64.b64decode(pubsub_message['data']).decode('utf-8')
            gmail_data = json.loads(data)

            # Extract email address and history ID
            email_address = gmail_data.get('emailAddress', '<EMAIL>')
            history_id = gmail_data.get('historyId')

            logger.info(f"Gmail webhook triggered for {email_address}, historyId: {history_id}")

            # Enhanced deduplication: Skip if we've already processed this history ID
            global _processed_history_ids, _last_webhook_time, _processed_emails, _draft_created_for
            if history_id in _processed_history_ids:
                logger.info(f"Skipping already processed historyId: {history_id}")
                return {"status": "duplicate_skipped"}

            # Rate limiting: Prevent processing if too recent (within 2 minutes)
            import time
            current_time = time.time()
            if current_time - _last_webhook_time < 120:  # Increased from 30 to 120 seconds
                logger.info(f"Rate limiting: Skipping webhook (too recent - within 2 minutes)")
                return {"status": "rate_limited"}

            # Add to processed set and update last time
            _processed_history_ids.add(history_id)
            _last_webhook_time = current_time

            # Clean up old tracking data (keep only recent entries)
            if len(_processed_history_ids) > 50:
                _processed_history_ids = set(list(_processed_history_ids)[-25:])

            if len(_processed_emails) > 100:
                _processed_emails = set(list(_processed_emails)[-50:])

            if len(_draft_created_for) > 100:
                _draft_created_for = set(list(_draft_created_for)[-50:])

            # Create a task request to check for new emails and respond
            from agent.state import TaskRequest, TaskType

            task_request = TaskRequest(
                task_id=f"gmail_auto_{int(current_time)}",
                task_type=TaskType.EMAIL_ANALYSIS,
                input_data={
                    "message": "Check for NEW unread emails in INBOX that need responses. IMPORTANT: Only create drafts for emails that don't already have drafts. Skip: newsletters, promotions, automated notifications, no-reply emails, and emails you've already processed. Focus on genuine business inquiries, questions, and requests that need professional replies representing TKC Group.",
                    "metadata": {
                        "email_address": email_address,
                        "history_id": history_id,
                        "source": "gmail_webhook",
                        "processed_emails": list(_processed_emails),
                        "draft_created_for": list(_draft_created_for)
                    }
                }
            )

            # Execute the task asynchronously
            task_response = await agent.execute_task(task_request)

            if task_response.success:
                logger.info(f"Gmail webhook processed successfully: {task_response.results}")
            else:
                logger.error(f"Gmail webhook processing failed: {task_response.error}")

            return {"status": "processed"}
        else:
            logger.warning("No data in Pub/Sub message")
            return {"status": "no_data"}

    except Exception as e:
        logger.error(f"Error processing Gmail webhook: {e}")
        return {"status": "error", "message": str(e)}


@app.post("/webhook/email")
async def email_webhook(request: ChatRequest):
    """
    Webhook endpoint for processing emails from Cloud Function.

    This endpoint receives email data from the Gmail processor Cloud Function
    and handles it with enhanced context about the email source.
    """
    if agent is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")

    try:
        logger.info(f"Email webhook request: {request.message[:100]}...")

        # Create a task request with email context
        from agent.state import TaskRequest, TaskType
        import time

        task_request = TaskRequest(
            task_id=f"email_task_{int(time.time())}",
            task_type=TaskType.EMAIL_ANALYSIS,  # Use email-specific task type
            input_data={
                "message": request.message,
                "metadata": getattr(request, 'metadata', {}),
                "source": "email_webhook"
            }
        )

        # Execute the task
        task_response = await agent.execute_task(task_request)

        # Extract the response content
        if task_response.success and task_response.results:
            # Get the final result from the task response
            final_result = task_response.results.get('final_result')
            if final_result:
                response = str(final_result)
            else:
                # Fallback to other possible response keys
                response = (task_response.results.get('response') or
                           task_response.results.get('output') or
                           str(task_response.results))
        else:
            response = f"Error: {task_response.error or 'Unknown error occurred'}"

        logger.info("Email webhook processed successfully")
        return ChatResponse(response=response, status="success")

    except Exception as e:
        logger.error(f"Error processing email webhook: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/webhook/form")
async def form_submission_webhook(submission: FormSubmission):
    """
    Webhook endpoint for processing form submissions from frontend.

    This endpoint receives form submissions and processes them through the
    Executive Agent for lead enrichment, CRM integration, and automated follow-up.
    """
    if agent is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")

    try:
        logger.info(f"Received form submission from {submission.email} ({submission.form_type})")

        # Get lead processor instance
        lead_processor = await get_lead_processor(agent)

        # Process the form submission
        result = await lead_processor.process_form_submission(submission)

        if result.success:
            logger.info(f"Successfully processed lead {result.lead_id}")
            return {
                "status": "success",
                "lead_id": result.lead_id,
                "crm_contact_id": result.crm_contact_id,
                "crm_deal_id": result.crm_deal_id,
                "follow_up_scheduled": result.follow_up_scheduled,
                "message": "Form submission processed successfully"
            }
        else:
            logger.error(f"Failed to process form submission: {result.error_message}")
            return {
                "status": "error",
                "error": result.error_message,
                "message": "Form submission processing failed"
            }

    except Exception as e:
        logger.error(f"Error processing form submission: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/webhook/lead-notification")
async def lead_notification_webhook(request: Request):
    """
    Webhook endpoint for external lead notifications (e.g., from marketing tools).

    This endpoint can receive lead notifications from external sources like
    marketing automation platforms, landing page builders, etc.
    """
    if agent is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")

    try:
        # Parse the incoming notification
        body = await request.body()
        notification_data = body.decode('utf-8')

        logger.info(f"Received lead notification: {notification_data[:200]}...")

        # TODO: Parse different notification formats based on source
        # For now, return success
        return {"status": "received", "message": "Lead notification processed"}

    except Exception as e:
        logger.error(f"Error processing lead notification: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    
    # Get configuration from environment
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8080"))
    
    # Set project ID if not already set
    if not os.getenv("GOOGLE_CLOUD_PROJECT"):
        os.environ["GOOGLE_CLOUD_PROJECT"] = "vertex-ai-agent-yzdlnjey"
    
    logger.info(f"Starting server on {host}:{port}")
    uvicorn.run(app, host=host, port=port)

# Aegis Trading Agent v3 - Phase 3: Import Resolution & API Integration
# Autonomous Cryptocurrency Research & Alert System

FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements-phase3.txt .

# Install dependencies in stages for better error handling
RUN pip install --no-cache-dir --upgrade pip

# Stage 1: Core dependencies (Phase 1 - tested)
RUN pip install --no-cache-dir \
    fastapi>=0.110.0 \
    uvicorn[standard]>=0.29.0 \
    pydantic>=2.7.0 \
    pydantic-settings>=2.2.0 \
    requests>=2.31.0 \
    python-dotenv>=1.0.0

# Stage 2: GCP dependencies (Phase 1 - tested)
RUN pip install --no-cache-dir \
    google-cloud-secret-manager>=2.20.0 \
    google-cloud-firestore>=2.16.0 \
    google-auth>=2.29.0 \
    redis>=5.0.0 \
    structlog>=24.1.0

# Stage 3: LangChain dependencies (Phase 2 - tested)
RUN pip install --no-cache-dir \
    langchain>=0.1.0 \
    langchain-core>=0.1.0 \
    langchain-community>=0.0.20 \
    langchain-google-vertexai>=1.0.0 \
    langgraph>=0.0.40

# Stage 4: Data analysis dependencies (Phase 2 - tested)
RUN pip install --no-cache-dir \
    numpy>=1.24.0 \
    pandas>=2.0.0 \
    scikit-learn>=1.3.0

# Stage 5: Email and scheduling (Phase 2 - tested)
RUN pip install --no-cache-dir \
    google-api-python-client>=2.100.0 \
    google-auth-oauthlib>=1.0.0 \
    google-auth-httplib2>=0.1.0 \
    apscheduler>=3.10.0

# Stage 6: Data processing (Phase 2 - tested)
RUN pip install --no-cache-dir \
    beautifulsoup4>=4.12.0 \
    feedparser>=6.0.0

# Stage 7: Phase 3 async and typing dependencies
RUN pip install --no-cache-dir \
    aiohttp>=3.9.0 \
    asyncio-throttle>=1.0.0 \
    typing-extensions>=4.8.0 \
    python-dateutil>=2.8.0 \
    pytz>=2023.3

# Stage 8: Phase 3 crypto analysis tools (adding back carefully)
RUN pip install --no-cache-dir \
    ccxt>=4.2.0 \
    yfinance>=0.2.0

# Copy application code
COPY main.py .
COPY src/ src/

# Set environment variables
ENV GCP_PROJECT_ID=vertex-ai-agent-yzdlnjey
ENV ENVIRONMENT=test
ENV PORT=8080
ENV PYTHONPATH=/app

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["python", "main.py"]

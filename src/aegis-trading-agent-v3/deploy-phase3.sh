#!/bin/bash

# Aegis Trading Agent v3 - Phase 3: Import Resolution & API Integration
# Autonomous Cryptocurrency Research & Alert System

set -e

echo "🚀 Starting Aegis Trading Agent v3 - Phase 3: Import Resolution & API Integration"
echo "Focus: Autonomous Cryptocurrency Research & Alert System"
echo "New Features: Crypto APIs, Technical Analysis, Email Alerts, LangGraph Workflows"
echo "Project: vertex-ai-agent-yzdlnjey"
echo "Service: aegis-trading-agent-v3-test"
echo "Region: us-west1"
echo ""

# Verify we're in the correct project
CURRENT_PROJECT=$(gcloud config get-value project)
if [ "$CURRENT_PROJECT" != "vertex-ai-agent-yzdlnjey" ]; then
    echo "❌ Error: Current project is $CURRENT_PROJECT, expected vertex-ai-agent-yzdlnjey"
    echo "Please run: gcloud config set project vertex-ai-agent-yzdlnjey"
    exit 1
fi

echo "✅ Project verified: $CURRENT_PROJECT"

# Verify Phase 2 service is running
echo "🔍 Verifying Phase 2 service status..."
SERVICE_URL=$(gcloud run services describe aegis-trading-agent-v3-test --region=us-west1 --format="value(status.url)" 2>/dev/null || echo "")

if [ -z "$SERVICE_URL" ]; then
    echo "❌ Error: Phase 2 service not found. Please complete Phase 2 first."
    exit 1
fi

echo "✅ Phase 2 service verified: $SERVICE_URL"

# Test Phase 2 service health
echo "🔍 Testing Phase 2 service health..."
HEALTH_RESPONSE=$(curl -s -H "Authorization: Bearer $(gcloud auth print-identity-token)" "$SERVICE_URL/health" || echo "")

if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
    echo "✅ Phase 2 service is healthy"
else
    echo "❌ Error: Phase 2 service is not responding correctly"
    echo "Response: $HEALTH_RESPONSE"
    exit 1
fi

# Test Phase 2 imports
echo "🔍 Testing Phase 2 imports..."
PHASE2_RESPONSE=$(curl -s -H "Authorization: Bearer $(gcloud auth print-identity-token)" "$SERVICE_URL/test-phase2-imports" || echo "")

if [[ "$PHASE2_RESPONSE" == *"100.0%"* ]]; then
    echo "✅ Phase 2 imports working (100% success rate)"
else
    echo "❌ Warning: Phase 2 imports may have issues"
    echo "Response: $PHASE2_RESPONSE"
fi

# Start Cloud Build for Phase 3
echo ""
echo "🏗️  Starting Cloud Build for Phase 3: Import Resolution & API Integration..."
echo "Build configuration: cloudbuild-phase3.yaml"
echo "New capabilities:"
echo "  - Crypto API integration (CoinGecko, DEX Screener, Santiment, NewsAPI, CryptoPanic)"
echo "  - Technical analysis engine with RSI, moving averages, Bollinger bands"
echo "  - Email alert system for autonomous notifications"
echo "  - LangGraph workflows for autonomous analysis cycles"
echo "  - Market intelligence reporting"
echo "Memory allocation: Increased to 2Gi for crypto analysis workloads"
echo "CPU allocation: Increased to 2 cores for concurrent API processing"

gcloud builds submit \
    --config=cloudbuild-phase3.yaml \
    ../..

echo ""
echo "🎉 Phase 3 deployment initiated successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Monitor build progress in Cloud Console (build may take 20-30 minutes)"
echo "2. Once deployed, test the new Phase 3 endpoints:"
echo "   - Phase 1 imports: GET /test-imports"
echo "   - Phase 2 imports: GET /test-phase2-imports"
echo "   - Phase 3 imports: GET /test-phase3-imports"
echo "   - Crypto APIs: GET /test-crypto-apis"
echo "   - Analysis engine: GET /test-analysis-engine"
echo "   - Email system: GET /test-email-system"
echo "   - Autonomous workflow: GET /test-autonomous-workflow"
echo "   - GCP connectivity: GET /test-gcp-connectivity"
echo "   - Service account: GET /test-service-account"
echo ""
echo "🔗 Service URL (same as previous phases): $SERVICE_URL"
echo ""
echo "✅ Phase 3: Import Resolution & API Integration deployment in progress..."
echo "🎯 Focus: Complete Autonomous Cryptocurrency Research & Alert System"
echo ""
echo "🤖 Autonomous Features Ready After Deployment:"
echo "  - 3-6 hour analysis cycles"
echo "  - Email alerts to Tyler with actionable recommendations"
echo "  - Technical analysis with confidence scoring"
echo "  - Multi-source crypto data aggregation"
echo "  - LangGraph-powered intelligent workflows"

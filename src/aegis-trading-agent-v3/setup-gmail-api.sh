#!/bin/bash

# Aegis Trading Agent v3 - Phase 4: Milestone 4.2: Gmail API Integration
# Configure Gmail API for autonomous email alerts to Tyler

set -e

echo "📧 Aegis Trading Agent v3 - Milestone 4.2: Gmail API Integration"
echo "Setting up Gmail API for autonomous email alerts"
echo "From: <EMAIL> → To: <EMAIL>"
echo "Project: vertex-ai-agent-yzdlnjey"
echo ""

# Verify we're in the correct project
CURRENT_PROJECT=$(gcloud config get-value project)
if [ "$CURRENT_PROJECT" != "vertex-ai-agent-yzdlnjey" ]; then
    echo "❌ Error: Current project is $CURRENT_PROJECT, expected vertex-ai-agent-yzdlnjey"
    echo "Please run: gcloud config set project vertex-ai-agent-yzdlnjey"
    exit 1
fi

echo "✅ Project verified: $CURRENT_PROJECT"
echo ""

# Check if Gmail API is enabled
echo "🔍 Checking Gmail API status..."
if gcloud services list --enabled --filter="name:gmail.googleapis.com" --format="value(name)" | grep -q gmail.googleapis.com; then
    echo "✅ Gmail API is already enabled"
else
    echo "🔧 Enabling Gmail API..."
    gcloud services enable gmail.googleapis.com
    echo "✅ Gmail API enabled successfully"
fi

echo ""

# Check service account exists
echo "🔍 Verifying Aegis service account..."
if gcloud iam service-<NAME_EMAIL> >/dev/null 2>&1; then
    echo "✅ Aegis service account exists: <EMAIL>"
else
    echo "❌ Error: Aegis service account not found"
    echo "Please ensure the service account is created first"
    exit 1
fi

echo ""

# Create Gmail API credentials secret
echo "🔑 Setting up Gmail API credentials..."
echo ""
echo "📋 Gmail API Setup Instructions:"
echo ""
echo "1. **Enable Domain-Wide Delegation** (if not already done):"
echo "   - Go to: https://console.cloud.google.com/iam-admin/serviceaccounts"
echo "   - Find: <EMAIL>"
echo "   - Click 'Edit' → Check 'Enable Google Workspace Domain-wide Delegation'"
echo "   - Note the Client ID for step 3"
echo ""
echo "2. **Download Service Account Key** (if needed):"
echo "   - In the service account details, go to 'Keys' tab"
echo "   - Click 'Add Key' → 'Create new key' → JSON format"
echo "   - Download the JSON key file"
echo ""
echo "3. **Configure Google Workspace Admin** (Tyler needs to do this):"
echo "   - Go to: https://admin.google.com/ac/owl/domainwidedelegation"
echo "   - Add new API client with:"
echo "     - Client ID: [from step 1]"
echo "     - OAuth Scopes: https://www.googleapis.com/auth/gmail.send"
echo ""

# Check if Gmail credentials secret exists
if gcloud secrets describe "AEGIS_TRADING_GMAIL_CREDENTIALS" >/dev/null 2>&1; then
    echo "✅ Gmail credentials secret already exists"
    echo ""
    read -p "Do you want to update the Gmail credentials? (y/N): " UPDATE_CREDS
    if [[ "$UPDATE_CREDS" =~ ^[Yy]$ ]]; then
        echo "📝 Please paste the service account JSON key content (press Ctrl+D when done):"
        GMAIL_CREDS=$(cat)
        echo -n "$GMAIL_CREDS" | gcloud secrets versions add "AEGIS_TRADING_GMAIL_CREDENTIALS" --data-file=-
        echo "✅ Gmail credentials updated"
    else
        echo "⏭️  Keeping existing Gmail credentials"
    fi
else
    echo "🆕 Creating Gmail credentials secret..."
    echo ""
    read -p "Do you have the service account JSON key ready? (y/N): " HAS_KEY
    if [[ "$HAS_KEY" =~ ^[Yy]$ ]]; then
        echo "📝 Please paste the service account JSON key content (press Ctrl+D when done):"
        GMAIL_CREDS=$(cat)
        echo -n "$GMAIL_CREDS" | gcloud secrets create "AEGIS_TRADING_GMAIL_CREDENTIALS" --data-file=-
        echo "✅ Gmail credentials secret created"
    else
        echo "⏭️  Skipping Gmail credentials setup"
        echo "   You can add them later using:"
        echo "   gcloud secrets create AEGIS_TRADING_GMAIL_CREDENTIALS --data-file=path/to/key.json"
    fi
fi

echo ""

# Grant access to the Aegis service account
if gcloud secrets describe "AEGIS_TRADING_GMAIL_CREDENTIALS" >/dev/null 2>&1; then
    echo "🔐 Granting Gmail credentials access to Aegis service account..."
    gcloud secrets add-iam-policy-binding "AEGIS_TRADING_GMAIL_CREDENTIALS" \
        --member="serviceAccount:<EMAIL>" \
        --role="roles/secretmanager.secretAccessor"
    echo "✅ Gmail credentials access granted"
fi

echo ""

# Create email configuration secret
echo "📧 Setting up email configuration..."
EMAIL_CONFIG='{
  "from_email": "<EMAIL>",
  "from_name": "Aegis Trading Agent v3",
  "to_email": "<EMAIL>",
  "domain": "tkcgroup.co",
  "subject_prefix": "🤖 Aegis",
  "urgent_subject_prefix": "🚨 URGENT: Aegis"
}'

if gcloud secrets describe "AEGIS_TRADING_EMAIL_CONFIG" >/dev/null 2>&1; then
    echo "📝 Email configuration secret already exists, updating..."
    echo -n "$EMAIL_CONFIG" | gcloud secrets versions add "AEGIS_TRADING_EMAIL_CONFIG" --data-file=-
else
    echo "🆕 Creating email configuration secret..."
    echo -n "$EMAIL_CONFIG" | gcloud secrets create "AEGIS_TRADING_EMAIL_CONFIG" --data-file=-
fi

# Grant access to the Aegis service account
gcloud secrets add-iam-policy-binding "AEGIS_TRADING_EMAIL_CONFIG" \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/secretmanager.secretAccessor"

echo "✅ Email configuration secret created and access granted"
echo ""

# Summary
echo "📋 Gmail API Configuration Summary:"
echo ""

# Check Gmail API
if gcloud services list --enabled --filter="name:gmail.googleapis.com" --format="value(name)" | grep -q gmail.googleapis.com; then
    echo "✅ Gmail API: Enabled"
else
    echo "❌ Gmail API: Not enabled"
fi

# Check service account
if gcloud iam service-<NAME_EMAIL> >/dev/null 2>&1; then
    echo "✅ Service Account: <EMAIL>"
else
    echo "❌ Service Account: Not found"
fi

# Check Gmail credentials
if gcloud secrets describe "AEGIS_TRADING_GMAIL_CREDENTIALS" >/dev/null 2>&1; then
    echo "✅ Gmail Credentials: Configured in Secret Manager"
else
    echo "❌ Gmail Credentials: Not configured"
fi

# Check email config
if gcloud secrets describe "AEGIS_TRADING_EMAIL_CONFIG" >/dev/null 2>&1; then
    echo "✅ Email Configuration: Configured in Secret Manager"
else
    echo "❌ Email Configuration: Not configured"
fi

echo ""
echo "📧 Email Configuration:"
echo "   From: <EMAIL> (Aegis Trading Agent v3)"
echo "   To: <EMAIL>"
echo "   Authentication: Service Account with Domain-Wide Delegation"
echo ""

echo "🚀 Next Steps:"
echo "1. Ensure Tyler completes Google Workspace Admin configuration"
echo "2. Deploy updated service with Gmail API integration"
echo "3. Test email delivery with /send-test-email endpoint"
echo "4. Validate autonomous email cycles"
echo ""

echo "⚠️  Important: Tyler must configure domain-wide delegation in Google Workspace Admin"
echo "   This is required for the service account to send emails on behalf of tkcgroup.co"
echo ""

echo "✅ Milestone 4.2: Gmail API Integration setup complete!"

# Aegis Trading Agent v3 - Phase 2: Progressive Dependencies
# Autonomous Cryptocurrency Research & Alert System

steps:
  # Build the Docker image with Phase 2 dependencies
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'Dockerfile.phase2',
      '-t', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:phase2-${SHORT_SHA}',
      '-t', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:phase2-latest',
      '.'
    ]
    dir: 'src/aegis-trading-agent-v3'

  # Push the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:phase2-${SHORT_SHA}']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:phase2-latest']

  # Deploy to Cloud Run with updated dependencies
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'aegis-trading-agent-v3-test',
      '--image', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:phase2-${SHORT_SHA}',
      '--platform', 'managed',
      '--region', 'us-west1',
      '--service-account', 'aegis-trading-agent-sa@$PROJECT_ID.iam.gserviceaccount.com',
      '--no-allow-unauthenticated',
      '--set-env-vars', 'GCP_PROJECT_ID=$PROJECT_ID,ENVIRONMENT=test',
      '--memory', '1Gi',
      '--cpu', '1',
      '--concurrency', '10',
      '--timeout', '300',
      '--max-instances', '3'
    ]

# Store images in Container Registry
images:
  - 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:phase2-${SHORT_SHA}'
  - 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:phase2-latest'

# Build configuration
options:
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build (increased for larger dependencies)
timeout: '1800s'

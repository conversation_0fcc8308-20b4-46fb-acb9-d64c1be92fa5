# Aegis Trading Agent v3 - Phase 2: Progressive Dependencies
# Autonomous Cryptocurrency Research & Alert System

# Phase 1 dependencies (already tested)
fastapi>=0.110.0
uvicorn[standard]>=0.29.0
pydantic>=2.7.0
pydantic-settings>=2.2.0
requests>=2.31.0
python-dotenv>=1.0.0
google-cloud-secret-manager>=2.20.0
google-cloud-firestore>=2.16.0
google-auth>=2.29.0
redis>=5.0.0
structlog>=24.1.0

# Phase 2: LangChain Core Dependencies for Autonomous Workflows
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.20
langchain-google-vertexai>=1.0.0

# LangGraph for Autonomous Agent Workflows
langgraph>=0.0.40

# Additional AI/ML Dependencies
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Crypto Analysis Dependencies (will add in Phase 3)
# ccxt>=4.2.0  # Cryptocurrency exchange library
# ta>=0.10.2   # Technical analysis library
# yfinance>=0.2.0  # Financial data

# Email Integration (Gmail API from TKC_v5 patterns)
google-api-python-client>=2.100.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0

# Scheduling and Background Tasks
apscheduler>=3.10.0

# Data Processing and Analysis
beautifulsoup4>=4.12.0
feedparser>=6.0.0

# Aegis Trading Agent v3 - Minimal Test Deployment
# Phase 1: Test Infrastructure Cloud Build Configuration

steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-f', 'Dockerfile.minimal',
      '-t', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:minimal-${SHORT_SHA}',
      '-t', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:minimal-latest',
      '.'
    ]
    dir: 'src/aegis-trading-agent-v3'

  # Push the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:minimal-${SHORT_SHA}']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:minimal-latest']

  # Deploy to Cloud Run with Tyler-only access
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', 'aegis-trading-agent-v3-test',
      '--image', 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:minimal-${SHORT_SHA}',
      '--platform', 'managed',
      '--region', 'us-west1',
      '--service-account', 'aegis-trading-agent-sa@$PROJECT_ID.iam.gserviceaccount.com',
      '--no-allow-unauthenticated',
      '--set-env-vars', 'GCP_PROJECT_ID=$PROJECT_ID,ENVIRONMENT=test',
      '--memory', '512Mi',
      '--cpu', '1',
      '--concurrency', '10',
      '--timeout', '300',
      '--max-instances', '3'
    ]



# Store images in Container Registry
images:
  - 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:minimal-${SHORT_SHA}'
  - 'gcr.io/$PROJECT_ID/aegis-trading-agent-v3:minimal-latest'

# Build configuration
options:
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build
timeout: '1200s'

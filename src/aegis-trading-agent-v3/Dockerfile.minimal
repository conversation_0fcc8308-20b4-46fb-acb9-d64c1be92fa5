# Aegis Trading Agent v3 - Minimal Test Deployment
# Phase 1: Test Infrastructure following agent template methodology

FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements-minimal.txt .
RUN pip install --no-cache-dir -r requirements-minimal.txt

# Copy application code
COPY main.py .

# Set environment variables for GCP project
ENV GCP_PROJECT_ID=vertex-ai-agent-yzdlnjey
ENV ENVIRONMENT=test
ENV PORT=8080

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["python", "main.py"]

#!/bin/bash

# Aegis Trading Agent v3 - Phase 4: Production API Configuration
# Milestone 4.1: Set up cryptocurrency API keys in Secret Manager

set -e

echo "🔑 Aegis Trading Agent v3 - Milestone 4.1: Production API Configuration"
echo "Setting up cryptocurrency API keys in Secret Manager"
echo "Project: vertex-ai-agent-yzdlnjey"
echo ""

# Verify we're in the correct project
CURRENT_PROJECT=$(gcloud config get-value project)
if [ "$CURRENT_PROJECT" != "vertex-ai-agent-yzdlnjey" ]; then
    echo "❌ Error: Current project is $CURRENT_PROJECT, expected vertex-ai-agent-yzdlnjey"
    echo "Please run: gcloud config set project vertex-ai-agent-yzdlnjey"
    exit 1
fi

echo "✅ Project verified: $CURRENT_PROJECT"
echo ""

# Function to create secret if it doesn't exist
create_secret_if_not_exists() {
    local secret_name=$1
    local secret_value=$2
    
    if gcloud secrets describe "$secret_name" >/dev/null 2>&1; then
        echo "📝 Secret $secret_name already exists, updating..."
        echo -n "$secret_value" | gcloud secrets versions add "$secret_name" --data-file=-
    else
        echo "🆕 Creating new secret: $secret_name"
        echo -n "$secret_value" | gcloud secrets create "$secret_name" --data-file=-
    fi
    
    # Grant access to the Aegis service account
    gcloud secrets add-iam-policy-binding "$secret_name" \
        --member="serviceAccount:<EMAIL>" \
        --role="roles/secretmanager.secretAccessor"
    
    echo "✅ Secret $secret_name configured with Aegis service account access"
}

echo "🔑 Setting up cryptocurrency API keys..."
echo ""

# CoinGecko API Key (Free Tier: 30 calls/minute)
echo "1. CoinGecko API Configuration"
echo "   - Free tier: 30 calls/minute"
echo "   - Sign up at: https://www.coingecko.com/en/api"
echo "   - Required for: Market data, price history, market cap data"
echo ""
read -p "Enter your CoinGecko API key (or press Enter to skip): " COINGECKO_KEY

if [ -n "$COINGECKO_KEY" ]; then
    create_secret_if_not_exists "AEGIS_TRADING_COINGECKO_API_KEY" "$COINGECKO_KEY"
    echo ""
else
    echo "⏭️  Skipping CoinGecko API key (can be added later)"
    echo ""
fi

# NewsAPI Key (Free Tier: 1000 requests/day)
echo "2. NewsAPI Configuration"
echo "   - Free tier: 1000 requests/day"
echo "   - Sign up at: https://newsapi.org/"
echo "   - Required for: Cryptocurrency news and sentiment analysis"
echo ""
read -p "Enter your NewsAPI key (or press Enter to skip): " NEWSAPI_KEY

if [ -n "$NEWSAPI_KEY" ]; then
    create_secret_if_not_exists "AEGIS_TRADING_NEWSAPI_KEY" "$NEWSAPI_KEY"
    echo ""
else
    echo "⏭️  Skipping NewsAPI key (can be added later)"
    echo ""
fi

# CryptoPanic API Key (Free Tier: 200 requests/day)
echo "3. CryptoPanic API Configuration"
echo "   - Free tier: 200 requests/day"
echo "   - Sign up at: https://cryptopanic.com/developers/api/"
echo "   - Required for: Crypto news aggregation and sentiment"
echo ""
read -p "Enter your CryptoPanic API key (or press Enter to skip): " CRYPTOPANIC_KEY

if [ -n "$CRYPTOPANIC_KEY" ]; then
    create_secret_if_not_exists "AEGIS_TRADING_CRYPTOPANIC_KEY" "$CRYPTOPANIC_KEY"
    echo ""
else
    echo "⏭️  Skipping CryptoPanic API key (can be added later)"
    echo ""
fi

# Santiment API (Optional - Paid Tier)
echo "4. Santiment API Configuration (Optional)"
echo "   - Paid tier required for whale movement data"
echo "   - Sign up at: https://santiment.net/"
echo "   - Required for: On-chain analytics and whale movements"
echo ""
read -p "Enter your Santiment API key (or press Enter to skip): " SANTIMENT_KEY

if [ -n "$SANTIMENT_KEY" ]; then
    create_secret_if_not_exists "AEGIS_TRADING_SANTIMENT_KEY" "$SANTIMENT_KEY"
    echo ""
else
    echo "⏭️  Skipping Santiment API key (whale movement data will be limited)"
    echo ""
fi

# DEX Screener (No API key required)
echo "5. DEX Screener Configuration"
echo "   - No API key required (public API)"
echo "   - Rate limit: 300 requests/minute"
echo "   - Provides: DEX trading pairs and trending tokens"
echo "✅ DEX Screener ready (no configuration needed)"
echo ""

# Summary
echo "📋 API Configuration Summary:"
echo ""

# Check which secrets exist
if gcloud secrets describe "AEGIS_TRADING_COINGECKO_API_KEY" >/dev/null 2>&1; then
    echo "✅ CoinGecko API: Configured"
else
    echo "❌ CoinGecko API: Not configured"
fi

if gcloud secrets describe "AEGIS_TRADING_NEWSAPI_KEY" >/dev/null 2>&1; then
    echo "✅ NewsAPI: Configured"
else
    echo "❌ NewsAPI: Not configured"
fi

if gcloud secrets describe "AEGIS_TRADING_CRYPTOPANIC_KEY" >/dev/null 2>&1; then
    echo "✅ CryptoPanic API: Configured"
else
    echo "❌ CryptoPanic API: Not configured"
fi

if gcloud secrets describe "AEGIS_TRADING_SANTIMENT_KEY" >/dev/null 2>&1; then
    echo "✅ Santiment API: Configured"
else
    echo "❌ Santiment API: Not configured (optional)"
fi

echo "✅ DEX Screener: Ready (no key required)"
echo ""

echo "🚀 Next Steps:"
echo "1. Deploy updated service with API key integration"
echo "2. Test live data retrieval from configured APIs"
echo "3. Validate rate limiting and error handling"
echo "4. Proceed to Milestone 4.2: Gmail API Integration"
echo ""

echo "📝 To add missing API keys later, run this script again or use:"
echo "   gcloud secrets create AEGIS_TRADING_[API_NAME]_KEY --data-file=-"
echo ""

echo "✅ Milestone 4.1: Production API Configuration setup complete!"

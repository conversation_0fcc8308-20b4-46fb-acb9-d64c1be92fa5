# Aegis Trading Agent v3 - Phase 3: Import Resolution & API Integration
# Autonomous Cryptocurrency Research & Alert System

# Phase 1 & 2 dependencies (tested and working)
fastapi>=0.110.0
uvicorn[standard]>=0.29.0
pydantic>=2.7.0
pydantic-settings>=2.2.0
requests>=2.31.0
python-dotenv>=1.0.0
google-cloud-secret-manager>=2.20.0
google-cloud-firestore>=2.16.0
google-auth>=2.29.0
redis>=5.0.0
structlog>=24.1.0

# LangChain/LangGraph for Autonomous Workflows (Phase 2 - working)
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.20
langchain-google-vertexai>=1.0.0
langgraph>=0.0.40

# Data Analysis (Phase 2 - working)
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Email Integration (Phase 2 - working)
google-api-python-client>=2.100.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0

# Scheduling (Phase 2 - working)
apscheduler>=3.10.0

# Data Processing (Phase 2 - working)
beautifulsoup4>=4.12.0
feedparser>=6.0.0

# Phase 3: Additional dependencies for crypto APIs and async operations
aiohttp>=3.9.0
asyncio-throttle>=1.0.0
typing-extensions>=4.8.0

# Phase 3: Crypto analysis tools (adding back incrementally)
# Note: These were removed in Phase 2 due to build issues, adding back for Phase 3
ccxt>=4.2.0
yfinance>=0.2.0

# Phase 3: Enhanced data processing
python-dateutil>=2.8.0
pytz>=2023.3

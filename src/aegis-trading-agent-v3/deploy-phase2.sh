#!/bin/bash

# Aegis Trading Agent v3 - Phase 2: Progressive Dependencies Deployment
# Autonomous Cryptocurrency Research & Alert System

set -e

echo "🚀 Starting Aegis Trading Agent v3 - Phase 2: Progressive Dependencies"
echo "Focus: Autonomous Cryptocurrency Research & Alert System"
echo "Project: vertex-ai-agent-yzdlnjey"
echo "Service: aegis-trading-agent-v3-test"
echo "Region: us-west1"
echo ""

# Verify we're in the correct project
CURRENT_PROJECT=$(gcloud config get-value project)
if [ "$CURRENT_PROJECT" != "vertex-ai-agent-yzdlnjey" ]; then
    echo "❌ Error: Current project is $CURRENT_PROJECT, expected vertex-ai-agent-yzdlnjey"
    echo "Please run: gcloud config set project vertex-ai-agent-yzdlnjey"
    exit 1
fi

echo "✅ Project verified: $CURRENT_PROJECT"

# Verify Phase 1 service is running
echo "🔍 Verifying Phase 1 service status..."
SERVICE_URL=$(gcloud run services describe aegis-trading-agent-v3-test --region=us-west1 --format="value(status.url)" 2>/dev/null || echo "")

if [ -z "$SERVICE_URL" ]; then
    echo "❌ Error: Phase 1 service not found. Please complete Phase 1 first."
    exit 1
fi

echo "✅ Phase 1 service verified: $SERVICE_URL"

# Test Phase 1 service health
echo "🔍 Testing Phase 1 service health..."
HEALTH_RESPONSE=$(curl -s -H "Authorization: Bearer $(gcloud auth print-identity-token)" "$SERVICE_URL/health" || echo "")

if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
    echo "✅ Phase 1 service is healthy"
else
    echo "❌ Error: Phase 1 service is not responding correctly"
    echo "Response: $HEALTH_RESPONSE"
    exit 1
fi

# Start Cloud Build for Phase 2
echo ""
echo "🏗️  Starting Cloud Build for Phase 2: Progressive Dependencies..."
echo "Build configuration: cloudbuild-phase2.yaml"
echo "New dependencies: LangChain, LangGraph, Crypto Analysis Tools"
echo "Memory allocation: Increased to 1Gi for larger dependencies"

gcloud builds submit \
    --config=cloudbuild-phase2.yaml \
    ../..

echo ""
echo "🎉 Phase 2 deployment initiated successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Monitor build progress in Cloud Console (build may take 15-20 minutes)"
echo "2. Once deployed, test the new Phase 2 endpoints:"
echo "   - Phase 1 imports: GET /test-imports"
echo "   - Phase 2 imports: GET /test-phase2-imports"
echo "   - GCP connectivity: GET /test-gcp-connectivity"
echo "   - Service account: GET /test-service-account"
echo ""
echo "🔗 Service URL (same as Phase 1): $SERVICE_URL"
echo ""
echo "✅ Phase 2: Progressive Dependencies deployment in progress..."
echo "🎯 Focus: Autonomous Cryptocurrency Research & Alert System"

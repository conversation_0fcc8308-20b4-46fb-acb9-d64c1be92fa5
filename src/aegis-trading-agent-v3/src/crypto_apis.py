# Aegis Trading Agent v3 - Cryptocurrency API Management
# Autonomous Cryptocurrency Research & Alert System

import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import structlog

from .config import config

logger = structlog.get_logger(__name__)

@dataclass
class MarketData:
    """Market data structure for cryptocurrency information."""
    symbol: str
    price: float
    volume_24h: float
    price_change_24h: float
    market_cap: Optional[float] = None
    timestamp: datetime = None
    source: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

@dataclass
class WhaleMovement:
    """Whale movement data structure."""
    transaction_hash: str
    from_address: str
    to_address: str
    amount: float
    token_symbol: str
    usd_value: float
    timestamp: datetime
    exchange_involved: bool = False

@dataclass
class NewsItem:
    """News item data structure."""
    title: str
    content: str
    source: str
    url: str
    published_at: datetime
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None

class RateLimiter:
    """Rate limiter for API calls."""
    
    def __init__(self, max_calls: int, time_window: int = 60):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    async def acquire(self):
        """Acquire permission to make an API call."""
        now = time.time()
        # Remove old calls outside the time window
        self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
        
        if len(self.calls) >= self.max_calls:
            sleep_time = self.time_window - (now - self.calls[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                return await self.acquire()
        
        self.calls.append(now)

class CoinGeckoAPI:
    """CoinGecko API client for market data."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.coingecko.com/api/v3"
        self.rate_limiter = RateLimiter(50, 60)  # 50 calls per minute
    
    async def get_market_data(self, symbols: List[str]) -> List[MarketData]:
        """Get current market data for specified symbols."""
        await self.rate_limiter.acquire()
        
        try:
            symbol_ids = ",".join(symbols)
            url = f"{self.base_url}/simple/price"
            params = {
                "ids": symbol_ids,
                "vs_currencies": "usd",
                "include_market_cap": "true",
                "include_24hr_vol": "true",
                "include_24hr_change": "true"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return [
                            MarketData(
                                symbol=symbol,
                                price=info["usd"],
                                volume_24h=info.get("usd_24h_vol", 0),
                                price_change_24h=info.get("usd_24h_change", 0),
                                market_cap=info.get("usd_market_cap"),
                                source="coingecko"
                            )
                            for symbol, info in data.items()
                        ]
                    else:
                        logger.error(f"CoinGecko API error: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"CoinGecko API request failed: {str(e)}")
            return []

class DEXScreenerAPI:
    """DEX Screener API client for DEX trading data."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.dexscreener.com/latest"
        self.rate_limiter = RateLimiter(300, 60)  # 300 calls per minute
    
    async def get_trending_pairs(self) -> List[Dict[str, Any]]:
        """Get trending trading pairs from DEX platforms."""
        await self.rate_limiter.acquire()
        
        try:
            url = f"{self.base_url}/dex/tokens/trending"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("pairs", [])
                    else:
                        logger.error(f"DEX Screener API error: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"DEX Screener API request failed: {str(e)}")
            return []

class SantimentAPI:
    """Santiment API client for on-chain and social data."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.santiment.net/graphql"
        self.rate_limiter = RateLimiter(100, 60)  # 100 calls per minute
    
    async def get_whale_movements(self, symbol: str, hours: int = 24) -> List[WhaleMovement]:
        """Get whale movements for a specific cryptocurrency."""
        await self.rate_limiter.acquire()
        
        # GraphQL query for whale transactions
        query = """
        query($slug: String!, $from: DateTime!, $to: DateTime!) {
            getMetric(metric: "whale_transaction_count") {
                timeseriesData(
                    slug: $slug
                    from: $from
                    to: $to
                    interval: "1h"
                ) {
                    datetime
                    value
                }
            }
        }
        """
        
        variables = {
            "slug": symbol.lower(),
            "from": (datetime.utcnow() - timedelta(hours=hours)).isoformat(),
            "to": datetime.utcnow().isoformat()
        }
        
        try:
            headers = {"Authorization": f"Apikey {self.api_key}"}
            payload = {"query": query, "variables": variables}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        # Process whale movement data
                        movements = []
                        # Note: This is a simplified implementation
                        # Real implementation would parse actual transaction data
                        return movements
                    else:
                        logger.error(f"Santiment API error: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"Santiment API request failed: {str(e)}")
            return []

class NewsAPIClient:
    """NewsAPI client for cryptocurrency news."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://newsapi.org/v2"
        self.rate_limiter = RateLimiter(1000, 86400)  # 1000 calls per day
    
    async def get_crypto_news(self, keywords: List[str], hours: int = 24) -> List[NewsItem]:
        """Get recent cryptocurrency news."""
        await self.rate_limiter.acquire()
        
        try:
            query = " OR ".join(keywords)
            from_date = (datetime.utcnow() - timedelta(hours=hours)).isoformat()
            
            url = f"{self.base_url}/everything"
            params = {
                "q": query,
                "from": from_date,
                "sortBy": "relevancy",
                "apiKey": self.api_key,
                "language": "en",
                "pageSize": 50
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return [
                            NewsItem(
                                title=article["title"],
                                content=article.get("content", ""),
                                source=article["source"]["name"],
                                url=article["url"],
                                published_at=datetime.fromisoformat(
                                    article["publishedAt"].replace("Z", "+00:00")
                                )
                            )
                            for article in data.get("articles", [])
                        ]
                    else:
                        logger.error(f"NewsAPI error: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"NewsAPI request failed: {str(e)}")
            return []

class CryptoPanicAPI:
    """CryptoPanic API client for crypto news aggregation."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://cryptopanic.com/api/v1"
        self.rate_limiter = RateLimiter(1000, 86400)  # 1000 calls per day
    
    async def get_news_feed(self, currencies: List[str] = None) -> List[NewsItem]:
        """Get cryptocurrency news feed."""
        await self.rate_limiter.acquire()
        
        try:
            url = f"{self.base_url}/posts/"
            params = {
                "auth_token": self.api_key,
                "public": "true",
                "kind": "news"
            }
            
            if currencies:
                params["currencies"] = ",".join(currencies)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        return [
                            NewsItem(
                                title=post["title"],
                                content="",  # CryptoPanic doesn't provide full content
                                source=post["source"]["title"],
                                url=post["url"],
                                published_at=datetime.fromisoformat(
                                    post["published_at"].replace("Z", "+00:00")
                                )
                            )
                            for post in data.get("results", [])
                        ]
                    else:
                        logger.error(f"CryptoPanic API error: {response.status}")
                        return []
                        
        except Exception as e:
            logger.error(f"CryptoPanic API request failed: {str(e)}")
            return []

class CryptoAPIManager:
    """Unified manager for all cryptocurrency APIs."""
    
    def __init__(self):
        api_config = config.get_api_config()
        
        # Initialize API clients
        self.coingecko = CoinGeckoAPI(api_config["coingecko"]["api_key"]) if api_config["coingecko"]["api_key"] else None
        self.dex_screener = DEXScreenerAPI(api_config["dex_screener"]["api_key"]) if api_config["dex_screener"]["api_key"] else None
        self.santiment = SantimentAPI(api_config["santiment"]["api_key"]) if api_config["santiment"]["api_key"] else None
        self.newsapi = NewsAPIClient(api_config["newsapi"]["api_key"]) if api_config["newsapi"]["api_key"] else None
        self.cryptopanic = CryptoPanicAPI(api_config["cryptopanic"]["api_key"]) if api_config["cryptopanic"]["api_key"] else None
        
        logger.info("CryptoAPIManager initialized with available APIs")
    
    async def get_comprehensive_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Get comprehensive market data from all available sources."""
        results = {
            "market_data": [],
            "trending_pairs": [],
            "whale_movements": [],
            "news": [],
            "timestamp": datetime.utcnow()
        }
        
        # Gather data from all sources concurrently
        tasks = []
        
        if self.coingecko:
            tasks.append(self.coingecko.get_market_data(symbols))
        
        if self.dex_screener:
            tasks.append(self.dex_screener.get_trending_pairs())
        
        if self.newsapi:
            tasks.append(self.newsapi.get_crypto_news(symbols))
        
        if self.cryptopanic:
            tasks.append(self.cryptopanic.get_news_feed(symbols))
        
        try:
            # Execute all API calls concurrently
            api_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            result_index = 0
            if self.coingecko:
                if not isinstance(api_results[result_index], Exception):
                    results["market_data"] = api_results[result_index]
                result_index += 1
            
            if self.dex_screener:
                if not isinstance(api_results[result_index], Exception):
                    results["trending_pairs"] = api_results[result_index]
                result_index += 1
            
            if self.newsapi:
                if not isinstance(api_results[result_index], Exception):
                    results["news"].extend(api_results[result_index])
                result_index += 1
            
            if self.cryptopanic:
                if not isinstance(api_results[result_index], Exception):
                    results["news"].extend(api_results[result_index])
                result_index += 1
            
            logger.info(f"Gathered comprehensive market data for {len(symbols)} symbols")
            return results
            
        except Exception as e:
            logger.error(f"Error gathering comprehensive market data: {str(e)}")
            return results
    
    def get_available_apis(self) -> List[str]:
        """Get list of available API clients."""
        available = []
        if self.coingecko: available.append("coingecko")
        if self.dex_screener: available.append("dex_screener")
        if self.santiment: available.append("santiment")
        if self.newsapi: available.append("newsapi")
        if self.cryptopanic: available.append("cryptopanic")
        return available

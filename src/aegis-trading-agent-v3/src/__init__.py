# Aegis Trading Agent v3 - Autonomous Cryptocurrency Research & Alert System
# Tyler-Only Access - Complete Isolation from Business Agents

__version__ = "3.0.0"
__author__ = "<PERSON>"
__description__ = "Autonomous Cryptocurrency Research & Alert System"

# Core module exports
from .config import AegisConfig
from .crypto_apis import CryptoAPIManager
from .analysis import TechnicalAnalyzer, MarketIntelligence
from .alerts import EmailAlertManager
from .workflows import AutonomousResearchWorkflow

__all__ = [
    "AegisConfig",
    "CryptoAPIManager", 
    "TechnicalAnalyzer",
    "MarketIntelligence",
    "EmailAlertManager",
    "AutonomousResearchWorkflow"
]

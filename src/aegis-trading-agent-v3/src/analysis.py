# Aegis Trading Agent v3 - Technical Analysis & Market Intelligence
# Autonomous Cryptocurrency Research & Alert System

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import structlog

from .crypto_apis import MarketData, WhaleMovement, NewsItem

logger = structlog.get_logger(__name__)

class SignalType(Enum):
    """Types of trading signals."""
    BUY = "buy"
    SELL = "sell"
    WATCH = "watch"
    HOLD = "hold"

class ConfidenceLevel(Enum):
    """Confidence levels for signals."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"

@dataclass
class TradingSignal:
    """Trading signal data structure."""
    symbol: str
    signal_type: SignalType
    confidence: ConfidenceLevel
    confidence_score: float  # 0.0 to 1.0
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    reasoning: str = ""
    technical_indicators: Dict[str, Any] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.technical_indicators is None:
            self.technical_indicators = {}

@dataclass
class MarketIntelligenceReport:
    """Market intelligence report structure."""
    title: str
    summary: str
    key_findings: List[str]
    signals: List[TradingSignal]
    whale_activity: List[WhaleMovement]
    news_sentiment: Dict[str, Any]
    market_overview: Dict[str, Any]
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

class TechnicalAnalyzer:
    """Technical analysis engine for cryptocurrency data."""
    
    def __init__(self):
        self.indicators = {}
        logger.info("TechnicalAnalyzer initialized")
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Calculate Relative Strength Index."""
        if len(prices) < period + 1:
            return 50.0  # Neutral RSI if insufficient data
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_moving_averages(self, prices: List[float]) -> Dict[str, float]:
        """Calculate various moving averages."""
        if len(prices) < 50:
            return {"sma_20": prices[-1] if prices else 0, "sma_50": prices[-1] if prices else 0}
        
        sma_20 = np.mean(prices[-20:])
        sma_50 = np.mean(prices[-50:])
        
        return {
            "sma_20": sma_20,
            "sma_50": sma_50,
            "price_vs_sma_20": (prices[-1] / sma_20 - 1) * 100 if sma_20 > 0 else 0,
            "price_vs_sma_50": (prices[-1] / sma_50 - 1) * 100 if sma_50 > 0 else 0
        }
    
    def calculate_bollinger_bands(self, prices: List[float], period: int = 20, std_dev: float = 2) -> Dict[str, float]:
        """Calculate Bollinger Bands."""
        if len(prices) < period:
            current_price = prices[-1] if prices else 0
            return {
                "upper_band": current_price * 1.1,
                "lower_band": current_price * 0.9,
                "middle_band": current_price,
                "band_position": 0.5
            }
        
        sma = np.mean(prices[-period:])
        std = np.std(prices[-period:])
        
        upper_band = sma + (std_dev * std)
        lower_band = sma - (std_dev * std)
        
        # Calculate position within bands (0 = lower band, 1 = upper band)
        current_price = prices[-1]
        band_position = (current_price - lower_band) / (upper_band - lower_band) if upper_band != lower_band else 0.5
        
        return {
            "upper_band": upper_band,
            "lower_band": lower_band,
            "middle_band": sma,
            "band_position": band_position
        }
    
    def detect_volume_anomalies(self, volumes: List[float], prices: List[float]) -> Dict[str, Any]:
        """Detect volume anomalies that might indicate whale activity."""
        if len(volumes) < 20:
            return {"volume_anomaly": False, "volume_spike": 1.0}
        
        avg_volume = np.mean(volumes[-20:])
        current_volume = volumes[-1]
        volume_spike = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Detect unusual volume patterns
        volume_anomaly = volume_spike > 3.0  # 3x average volume
        
        return {
            "volume_anomaly": volume_anomaly,
            "volume_spike": volume_spike,
            "avg_volume_20": avg_volume,
            "current_volume": current_volume
        }
    
    def analyze_price_action(self, market_data: List[MarketData]) -> Dict[str, Any]:
        """Analyze price action patterns."""
        if len(market_data) < 10:
            return {"trend": "insufficient_data", "momentum": "neutral"}
        
        prices = [data.price for data in market_data]
        volumes = [data.volume_24h for data in market_data]
        
        # Calculate technical indicators
        rsi = self.calculate_rsi(prices)
        ma_data = self.calculate_moving_averages(prices)
        bb_data = self.calculate_bollinger_bands(prices)
        volume_data = self.detect_volume_anomalies(volumes, prices)
        
        # Determine trend
        if ma_data["price_vs_sma_20"] > 5 and ma_data["price_vs_sma_50"] > 5:
            trend = "strong_uptrend"
        elif ma_data["price_vs_sma_20"] > 0 and ma_data["price_vs_sma_50"] > 0:
            trend = "uptrend"
        elif ma_data["price_vs_sma_20"] < -5 and ma_data["price_vs_sma_50"] < -5:
            trend = "strong_downtrend"
        elif ma_data["price_vs_sma_20"] < 0 and ma_data["price_vs_sma_50"] < 0:
            trend = "downtrend"
        else:
            trend = "sideways"
        
        # Determine momentum
        if rsi > 70:
            momentum = "overbought"
        elif rsi < 30:
            momentum = "oversold"
        elif 45 <= rsi <= 55:
            momentum = "neutral"
        elif rsi > 55:
            momentum = "bullish"
        else:
            momentum = "bearish"
        
        return {
            "trend": trend,
            "momentum": momentum,
            "rsi": rsi,
            "moving_averages": ma_data,
            "bollinger_bands": bb_data,
            "volume_analysis": volume_data,
            "current_price": prices[-1],
            "price_change_24h": market_data[-1].price_change_24h
        }
    
    def generate_trading_signal(self, symbol: str, analysis: Dict[str, Any]) -> Optional[TradingSignal]:
        """Generate trading signal based on technical analysis."""
        try:
            current_price = analysis["current_price"]
            rsi = analysis["rsi"]
            trend = analysis["trend"]
            momentum = analysis["momentum"]
            bb_data = analysis["bollinger_bands"]
            volume_data = analysis["volume_analysis"]
            
            # Signal generation logic
            signal_type = SignalType.HOLD
            confidence_score = 0.5
            reasoning_parts = []
            
            # Buy signal conditions
            if (rsi < 35 and trend in ["uptrend", "sideways"] and 
                bb_data["band_position"] < 0.2 and volume_data["volume_spike"] > 1.5):
                signal_type = SignalType.BUY
                confidence_score = 0.8
                reasoning_parts.append("Oversold RSI with volume confirmation")
            
            elif (trend == "strong_uptrend" and rsi < 60 and 
                  volume_data["volume_anomaly"]):
                signal_type = SignalType.BUY
                confidence_score = 0.75
                reasoning_parts.append("Strong uptrend with volume breakout")
            
            # Sell signal conditions
            elif (rsi > 75 and trend in ["downtrend", "sideways"] and 
                  bb_data["band_position"] > 0.8):
                signal_type = SignalType.SELL
                confidence_score = 0.8
                reasoning_parts.append("Overbought RSI at resistance")
            
            elif (trend == "strong_downtrend" and rsi > 50):
                signal_type = SignalType.SELL
                confidence_score = 0.7
                reasoning_parts.append("Strong downtrend continuation")
            
            # Watch signal conditions
            elif (volume_data["volume_spike"] > 2.0 and 
                  abs(bb_data["band_position"] - 0.5) > 0.3):
                signal_type = SignalType.WATCH
                confidence_score = 0.6
                reasoning_parts.append("High volume with price at band extremes")
            
            # Determine confidence level
            if confidence_score >= 0.8:
                confidence = ConfidenceLevel.VERY_HIGH
            elif confidence_score >= 0.7:
                confidence = ConfidenceLevel.HIGH
            elif confidence_score >= 0.6:
                confidence = ConfidenceLevel.MEDIUM
            else:
                confidence = ConfidenceLevel.LOW
            
            # Calculate target and stop loss for buy signals
            target_price = None
            stop_loss = None
            
            if signal_type == SignalType.BUY:
                target_price = current_price * 1.15  # 15% target
                stop_loss = current_price * 0.92     # 8% stop loss
            elif signal_type == SignalType.SELL:
                target_price = current_price * 0.85  # 15% downside target
                stop_loss = current_price * 1.08     # 8% stop loss
            
            reasoning = f"Technical Analysis: {', '.join(reasoning_parts)}. " \
                       f"RSI: {rsi:.1f}, Trend: {trend}, Volume Spike: {volume_data['volume_spike']:.1f}x"
            
            return TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                confidence_score=confidence_score,
                entry_price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                reasoning=reasoning,
                technical_indicators=analysis
            )
            
        except Exception as e:
            logger.error(f"Error generating trading signal for {symbol}: {str(e)}")
            return None

class MarketIntelligence:
    """Market intelligence engine for comprehensive analysis."""
    
    def __init__(self):
        self.analyzer = TechnicalAnalyzer()
        logger.info("MarketIntelligence initialized")
    
    def analyze_news_sentiment(self, news_items: List[NewsItem]) -> Dict[str, Any]:
        """Analyze sentiment from news items."""
        if not news_items:
            return {"overall_sentiment": "neutral", "sentiment_score": 0.0, "news_count": 0}
        
        # Simple sentiment analysis based on keywords
        positive_keywords = ["bullish", "surge", "rally", "breakout", "adoption", "partnership", "upgrade"]
        negative_keywords = ["bearish", "crash", "dump", "hack", "regulation", "ban", "concern"]
        
        sentiment_scores = []
        
        for item in news_items:
            text = (item.title + " " + item.content).lower()
            positive_count = sum(1 for keyword in positive_keywords if keyword in text)
            negative_count = sum(1 for keyword in negative_keywords if keyword in text)
            
            if positive_count > negative_count:
                sentiment_scores.append(1)
            elif negative_count > positive_count:
                sentiment_scores.append(-1)
            else:
                sentiment_scores.append(0)
        
        avg_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0
        
        if avg_sentiment > 0.3:
            overall_sentiment = "positive"
        elif avg_sentiment < -0.3:
            overall_sentiment = "negative"
        else:
            overall_sentiment = "neutral"
        
        return {
            "overall_sentiment": overall_sentiment,
            "sentiment_score": avg_sentiment,
            "news_count": len(news_items),
            "positive_news": sum(1 for score in sentiment_scores if score > 0),
            "negative_news": sum(1 for score in sentiment_scores if score < 0)
        }
    
    def generate_market_report(self, market_data: Dict[str, Any]) -> MarketIntelligenceReport:
        """Generate comprehensive market intelligence report."""
        try:
            signals = []
            key_findings = []
            
            # Analyze each cryptocurrency
            for market_item in market_data.get("market_data", []):
                # Create historical data simulation (in real implementation, this would come from database)
                historical_data = [market_item] * 50  # Simplified for demo
                
                analysis = self.analyzer.analyze_price_action(historical_data)
                signal = self.analyzer.generate_trading_signal(market_item.symbol, analysis)
                
                if signal and signal.confidence_score >= 0.6:
                    signals.append(signal)
                    
                    if signal.signal_type in [SignalType.BUY, SignalType.SELL]:
                        key_findings.append(
                            f"{signal.symbol}: {signal.signal_type.value.upper()} signal "
                            f"({signal.confidence.value} confidence) - {signal.reasoning}"
                        )
            
            # Analyze news sentiment
            news_sentiment = self.analyze_news_sentiment(market_data.get("news", []))
            
            # Generate market overview
            market_overview = {
                "total_cryptocurrencies_analyzed": len(market_data.get("market_data", [])),
                "signals_generated": len(signals),
                "high_confidence_signals": len([s for s in signals if s.confidence_score >= 0.7]),
                "trending_pairs_count": len(market_data.get("trending_pairs", [])),
                "news_sentiment": news_sentiment["overall_sentiment"],
                "analysis_timestamp": datetime.utcnow()
            }
            
            # Generate summary
            summary = f"Analyzed {market_overview['total_cryptocurrencies_analyzed']} cryptocurrencies. " \
                     f"Generated {market_overview['signals_generated']} signals " \
                     f"({market_overview['high_confidence_signals']} high confidence). " \
                     f"Market sentiment: {news_sentiment['overall_sentiment']}."
            
            return MarketIntelligenceReport(
                title=f"Autonomous Crypto Analysis - {datetime.utcnow().strftime('%Y-%m-%d %H:%M UTC')}",
                summary=summary,
                key_findings=key_findings,
                signals=signals,
                whale_activity=market_data.get("whale_movements", []),
                news_sentiment=news_sentiment,
                market_overview=market_overview
            )
            
        except Exception as e:
            logger.error(f"Error generating market report: {str(e)}")
            return MarketIntelligenceReport(
                title="Error in Analysis",
                summary=f"Analysis failed: {str(e)}",
                key_findings=[],
                signals=[],
                whale_activity=[],
                news_sentiment={"overall_sentiment": "neutral", "sentiment_score": 0.0},
                market_overview={}
            )

# Aegis Trading Agent v3 - Configuration Management
# Autonomous Cryptocurrency Research & Alert System

import os
from typing import Optional, Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings
from google.cloud import secretmanager
import structlog

logger = structlog.get_logger(__name__)

class AegisConfig(BaseSettings):
    """
    Configuration management for Aegis Trading Agent v3.
    Handles Tyler-only secrets and autonomous system settings.
    """
    
    # GCP Configuration
    project_id: str = Field(default="vertex-ai-agent-yzdlnjey")
    environment: str = Field(default="test")
    region: str = Field(default="us-west1")
    
    # Service Configuration
    service_name: str = Field(default="aegis-trading-agent-v3")
    version: str = Field(default="3.0.0")
    
    # Autonomous Analysis Configuration
    analysis_interval_hours: int = Field(default=4, description="Hours between autonomous analysis cycles")
    confidence_threshold: float = Field(default=0.75, description="Minimum confidence for alerts")
    max_alerts_per_cycle: int = Field(default=10, description="Maximum alerts per analysis cycle")
    
    # Email Configuration
    tyler_email: str = Field(default="<EMAIL>")
    alert_from_email: str = Field(default="<EMAIL>")  # Use Tyler's email as sender with domain delegation
    
    # Crypto API Keys (loaded from Secret Manager)
    coingecko_api_key: Optional[str] = None
    dex_screener_api_key: Optional[str] = None
    santiment_api_key: Optional[str] = None
    newsapi_key: Optional[str] = None
    cryptopanic_api_key: Optional[str] = None
    
    # Redis Configuration
    redis_host: str = Field(default="localhost")
    redis_port: int = Field(default=6379)
    redis_db: int = Field(default=1, description="Isolated Redis DB for trading agent")
    
    # Firestore Configuration
    firestore_collection_prefix: str = Field(default="aegis_trading_")
    
    # Pinecone Configuration (for vector storage of market intelligence)
    pinecone_namespace: str = Field(default="aegis-trading")
    
    class Config:
        env_prefix = "AEGIS_"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._load_secrets()
    
    def _load_secrets(self) -> None:
        """Load Tyler-only trading secrets from Secret Manager."""
        try:
            client = secretmanager.SecretManagerServiceClient()
            
            # Define secret mappings
            secret_mappings = {
                "AEGIS_TRADING_COINGECKO_API_KEY": "coingecko_api_key",
                "AEGIS_TRADING_DEX_SCREENER_API_KEY": "dex_screener_api_key", 
                "AEGIS_TRADING_SANTIMENT_API_KEY": "santiment_api_key",
                "AEGIS_TRADING_NEWSAPI_KEY": "newsapi_key",
                "AEGIS_TRADING_CRYPTOPANIC_API_KEY": "cryptopanic_api_key"
            }
            
            for secret_name, attr_name in secret_mappings.items():
                try:
                    secret_path = f"projects/{self.project_id}/secrets/{secret_name}/versions/latest"
                    response = client.access_secret_version(request={"name": secret_path})
                    secret_value = response.payload.data.decode("UTF-8")
                    setattr(self, attr_name, secret_value)
                    logger.info(f"Loaded secret: {secret_name}")
                except Exception as e:
                    logger.warning(f"Failed to load secret {secret_name}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Failed to initialize Secret Manager client: {str(e)}")
    
    def get_api_config(self) -> Dict[str, Any]:
        """Get API configuration for crypto data sources."""
        return {
            "coingecko": {
                "api_key": self.coingecko_api_key,
                "base_url": "https://api.coingecko.com/api/v3",
                "rate_limit": 50  # requests per minute
            },
            "dex_screener": {
                "api_key": self.dex_screener_api_key,
                "base_url": "https://api.dexscreener.com/latest",
                "rate_limit": 300  # requests per minute
            },
            "santiment": {
                "api_key": self.santiment_api_key,
                "base_url": "https://api.santiment.net/graphql",
                "rate_limit": 100  # requests per minute
            },
            "newsapi": {
                "api_key": self.newsapi_key,
                "base_url": "https://newsapi.org/v2",
                "rate_limit": 1000  # requests per day
            },
            "cryptopanic": {
                "api_key": self.cryptopanic_api_key,
                "base_url": "https://cryptopanic.com/api/v1",
                "rate_limit": 1000  # requests per day
            }
        }
    
    def get_redis_url(self) -> str:
        """Get Redis connection URL."""
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    def get_firestore_collections(self) -> Dict[str, str]:
        """Get Firestore collection names with trading prefix."""
        return {
            "analysis_results": f"{self.firestore_collection_prefix}analysis_results",
            "market_intelligence": f"{self.firestore_collection_prefix}market_intelligence", 
            "alert_history": f"{self.firestore_collection_prefix}alert_history",
            "trading_signals": f"{self.firestore_collection_prefix}trading_signals",
            "whale_movements": f"{self.firestore_collection_prefix}whale_movements"
        }
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"
    
    def get_vertex_ai_config(self) -> Dict[str, Any]:
        """Get Vertex AI configuration for Gemini-2.5-Flash."""
        return {
            "project_id": self.project_id,
            "location": self.region,
            "model_name": "gemini-2.5-flash",
            "temperature": 0.1,  # Low temperature for consistent analysis
            "max_output_tokens": 8192,
            "top_p": 0.8,
            "top_k": 40
        }

# Global configuration instance
config = AegisConfig()

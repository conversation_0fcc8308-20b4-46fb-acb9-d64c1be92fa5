# Aegis Trading Agent v3 - Autonomous Workflows
# Autonomous Cryptocurrency Research & Alert System

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import structlog

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import Annotated, TypedDict

from .config import config
from .crypto_apis import CryptoAPIManager
from .analysis import MarketIntelligence, TradingSignal, ConfidenceLevel
from .alerts import EmailAlertManager

logger = structlog.get_logger(__name__)

class AnalysisState(TypedDict):
    """State for the autonomous analysis workflow."""
    messages: Annotated[list, add_messages]
    market_data: Dict[str, Any]
    analysis_results: Dict[str, Any]
    signals: List[TradingSignal]
    report: Optional[Any]
    error: Optional[str]
    timestamp: datetime

@dataclass
class WorkflowConfig:
    """Configuration for autonomous workflows."""
    target_cryptocurrencies: List[str]
    analysis_interval_hours: int
    confidence_threshold: float
    max_alerts_per_cycle: int
    enable_urgent_alerts: bool = True
    enable_email_alerts: bool = True

class AutonomousResearchWorkflow:
    """Autonomous cryptocurrency research workflow using LangGraph."""
    
    def __init__(self):
        self.crypto_api = CryptoAPIManager()
        self.market_intelligence = MarketIntelligence()
        self.email_manager = EmailAlertManager()
        self.llm = self._initialize_llm()
        self.workflow = self._create_workflow()
        
        # Default configuration
        self.config = WorkflowConfig(
            target_cryptocurrencies=["bitcoin", "ethereum", "solana", "cardano", "polkadot", 
                                   "chainlink", "avalanche-2", "polygon", "cosmos", "near"],
            analysis_interval_hours=config.analysis_interval_hours,
            confidence_threshold=config.confidence_threshold,
            max_alerts_per_cycle=config.max_alerts_per_cycle
        )
        
        logger.info("AutonomousResearchWorkflow initialized")
    
    def _initialize_llm(self) -> ChatVertexAI:
        """Initialize Vertex AI LLM for analysis."""
        vertex_config = config.get_vertex_ai_config()
        
        return ChatVertexAI(
            project=vertex_config["project_id"],
            location=vertex_config["location"],
            model_name=vertex_config["model_name"],
            temperature=vertex_config["temperature"],
            max_output_tokens=vertex_config["max_output_tokens"]
        )
    
    def _create_workflow(self) -> StateGraph:
        """Create the autonomous analysis workflow using LangGraph."""
        workflow = StateGraph(AnalysisState)
        
        # Add nodes
        workflow.add_node("gather_data", self._gather_market_data)
        workflow.add_node("analyze_data", self._analyze_market_data)
        workflow.add_node("generate_signals", self._generate_trading_signals)
        workflow.add_node("create_report", self._create_intelligence_report)
        workflow.add_node("send_alerts", self._send_alerts)
        
        # Add edges
        workflow.add_edge("gather_data", "analyze_data")
        workflow.add_edge("analyze_data", "generate_signals")
        workflow.add_edge("generate_signals", "create_report")
        workflow.add_edge("create_report", "send_alerts")
        workflow.add_edge("send_alerts", END)
        
        # Set entry point
        workflow.set_entry_point("gather_data")
        
        return workflow.compile()
    
    async def _gather_market_data(self, state: AnalysisState) -> AnalysisState:
        """Gather comprehensive market data from all sources."""
        try:
            logger.info("Gathering market data from crypto APIs")
            
            # Get comprehensive market data
            market_data = await self.crypto_api.get_comprehensive_market_data(
                self.config.target_cryptocurrencies
            )
            
            state["market_data"] = market_data
            state["timestamp"] = datetime.utcnow()
            
            # Add system message about data gathering
            state["messages"].append(
                SystemMessage(content=f"Gathered market data for {len(self.config.target_cryptocurrencies)} cryptocurrencies. "
                                    f"Sources: {', '.join(self.crypto_api.get_available_apis())}")
            )
            
            logger.info(f"Market data gathered successfully", 
                       cryptocurrencies=len(market_data.get("market_data", [])),
                       news_items=len(market_data.get("news", [])),
                       trending_pairs=len(market_data.get("trending_pairs", [])))
            
        except Exception as e:
            logger.error(f"Error gathering market data: {str(e)}")
            state["error"] = f"Data gathering failed: {str(e)}"
        
        return state
    
    async def _analyze_market_data(self, state: AnalysisState) -> AnalysisState:
        """Analyze market data using AI and technical analysis."""
        try:
            if state.get("error"):
                return state
            
            logger.info("Analyzing market data with AI assistance")
            
            market_data = state["market_data"]
            
            # Create analysis prompt for the LLM
            analysis_prompt = f"""
            You are Aegis, an expert cryptocurrency trading analyst. Analyze the following market data and provide insights:

            Market Data Summary:
            - Cryptocurrencies analyzed: {len(market_data.get('market_data', []))}
            - News articles: {len(market_data.get('news', []))}
            - Trending DEX pairs: {len(market_data.get('trending_pairs', []))}

            Recent News Headlines:
            {self._format_news_for_analysis(market_data.get('news', [])[:5])}

            Trending Pairs:
            {self._format_trending_pairs(market_data.get('trending_pairs', [])[:5])}

            Provide analysis focusing on:
            1. Overall market sentiment and trends
            2. Key opportunities and risks
            3. Notable patterns in the data
            4. Recommendations for further investigation

            Be concise but insightful. Focus on actionable intelligence.
            """
            
            # Get AI analysis
            messages = [
                SystemMessage(content="You are Aegis, an expert cryptocurrency analyst providing autonomous market intelligence."),
                HumanMessage(content=analysis_prompt)
            ]
            
            ai_response = await self.llm.ainvoke(messages)
            
            state["analysis_results"] = {
                "ai_insights": ai_response.content,
                "market_summary": {
                    "total_cryptos": len(market_data.get("market_data", [])),
                    "news_count": len(market_data.get("news", [])),
                    "trending_pairs": len(market_data.get("trending_pairs", []))
                },
                "timestamp": datetime.utcnow()
            }
            
            state["messages"].append(
                SystemMessage(content=f"AI analysis completed. Generated insights for market intelligence.")
            )
            
            logger.info("Market data analysis completed with AI insights")
            
        except Exception as e:
            logger.error(f"Error analyzing market data: {str(e)}")
            state["error"] = f"Analysis failed: {str(e)}"
        
        return state
    
    async def _generate_trading_signals(self, state: AnalysisState) -> AnalysisState:
        """Generate trading signals using technical analysis."""
        try:
            if state.get("error"):
                return state
            
            logger.info("Generating trading signals")
            
            market_data = state["market_data"]
            signals = []
            
            # Generate signals for each cryptocurrency
            for crypto_data in market_data.get("market_data", []):
                # Create historical data simulation (in production, this would come from database)
                historical_data = [crypto_data] * 50  # Simplified for demo
                
                # Perform technical analysis
                analysis = self.market_intelligence.analyzer.analyze_price_action(historical_data)
                
                # Generate trading signal
                signal = self.market_intelligence.analyzer.generate_trading_signal(
                    crypto_data.symbol, analysis
                )
                
                if signal and signal.confidence_score >= self.config.confidence_threshold:
                    signals.append(signal)
            
            # Filter and sort signals
            signals = sorted(signals, key=lambda x: x.confidence_score, reverse=True)
            signals = signals[:self.config.max_alerts_per_cycle]
            
            state["signals"] = signals
            
            state["messages"].append(
                SystemMessage(content=f"Generated {len(signals)} trading signals above confidence threshold.")
            )
            
            logger.info(f"Generated {len(signals)} trading signals", 
                       high_confidence=len([s for s in signals if s.confidence_score >= 0.8]))
            
        except Exception as e:
            logger.error(f"Error generating trading signals: {str(e)}")
            state["error"] = f"Signal generation failed: {str(e)}"
        
        return state
    
    async def _create_intelligence_report(self, state: AnalysisState) -> AnalysisState:
        """Create comprehensive market intelligence report."""
        try:
            if state.get("error"):
                return state
            
            logger.info("Creating market intelligence report")
            
            # Generate comprehensive report
            report = self.market_intelligence.generate_market_report(state["market_data"])
            
            # Add AI insights to the report
            if "analysis_results" in state and "ai_insights" in state["analysis_results"]:
                report.key_findings.insert(0, f"AI Analysis: {state['analysis_results']['ai_insights'][:200]}...")
            
            # Update report with generated signals
            report.signals = state["signals"]
            
            state["report"] = report
            
            state["messages"].append(
                SystemMessage(content=f"Market intelligence report created with {len(report.signals)} signals.")
            )
            
            logger.info("Market intelligence report created successfully",
                       signals_count=len(report.signals),
                       key_findings=len(report.key_findings))
            
        except Exception as e:
            logger.error(f"Error creating intelligence report: {str(e)}")
            state["error"] = f"Report creation failed: {str(e)}"
        
        return state
    
    async def _send_alerts(self, state: AnalysisState) -> AnalysisState:
        """Send email alerts and urgent notifications."""
        try:
            if state.get("error") or not state.get("report"):
                return state
            
            logger.info("Sending alerts and notifications")
            
            report = state["report"]
            
            # Send main market intelligence email
            if self.config.enable_email_alerts:
                email_sent = self.email_manager.send_market_alert(report)
                if email_sent:
                    logger.info("Market intelligence email sent to Tyler")
                else:
                    logger.warning("Failed to send market intelligence email")
            
            # Send urgent alerts for very high confidence signals
            if self.config.enable_urgent_alerts:
                urgent_signals = [s for s in report.signals if s.confidence == ConfidenceLevel.VERY_HIGH]
                for signal in urgent_signals:
                    urgent_sent = self.email_manager.send_urgent_alert(signal)
                    if urgent_sent:
                        logger.info(f"Urgent alert sent for {signal.symbol} {signal.signal_type.value}")
            
            state["messages"].append(
                SystemMessage(content=f"Alerts sent successfully. Analysis cycle complete.")
            )
            
            logger.info("Alert sending completed", 
                       total_signals=len(report.signals),
                       urgent_alerts=len([s for s in report.signals if s.confidence == ConfidenceLevel.VERY_HIGH]))
            
        except Exception as e:
            logger.error(f"Error sending alerts: {str(e)}")
            state["error"] = f"Alert sending failed: {str(e)}"
        
        return state
    
    def _format_news_for_analysis(self, news_items: List[Any]) -> str:
        """Format news items for AI analysis."""
        if not news_items:
            return "No recent news available."
        
        formatted = []
        for item in news_items[:5]:
            formatted.append(f"- {item.title} ({item.source})")
        
        return "\n".join(formatted)
    
    def _format_trending_pairs(self, trending_pairs: List[Dict[str, Any]]) -> str:
        """Format trending pairs for AI analysis."""
        if not trending_pairs:
            return "No trending pairs data available."
        
        formatted = []
        for pair in trending_pairs[:5]:
            formatted.append(f"- {pair.get('baseToken', {}).get('symbol', 'Unknown')}")
        
        return "\n".join(formatted)
    
    async def run_analysis_cycle(self) -> Dict[str, Any]:
        """Run a complete autonomous analysis cycle."""
        try:
            logger.info("Starting autonomous analysis cycle")
            
            # Initialize state
            initial_state = AnalysisState(
                messages=[SystemMessage(content="Starting autonomous cryptocurrency analysis cycle")],
                market_data={},
                analysis_results={},
                signals=[],
                report=None,
                error=None,
                timestamp=datetime.utcnow()
            )
            
            # Run the workflow
            final_state = await self.workflow.ainvoke(initial_state)
            
            # Return results
            result = {
                "status": "success" if not final_state.get("error") else "error",
                "timestamp": final_state["timestamp"],
                "signals_generated": len(final_state.get("signals", [])),
                "high_confidence_signals": len([s for s in final_state.get("signals", []) if s.confidence_score >= 0.8]),
                "error": final_state.get("error"),
                "report_created": final_state.get("report") is not None
            }
            
            if final_state.get("error"):
                logger.error(f"Analysis cycle completed with error: {final_state['error']}")
            else:
                logger.info("Analysis cycle completed successfully", **result)
            
            return result
            
        except Exception as e:
            logger.error(f"Analysis cycle failed: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow(),
                "signals_generated": 0,
                "high_confidence_signals": 0,
                "report_created": False
            }
    
    def update_configuration(self, new_config: Dict[str, Any]) -> None:
        """Update workflow configuration."""
        if "target_cryptocurrencies" in new_config:
            self.config.target_cryptocurrencies = new_config["target_cryptocurrencies"]
        
        if "analysis_interval_hours" in new_config:
            self.config.analysis_interval_hours = new_config["analysis_interval_hours"]
        
        if "confidence_threshold" in new_config:
            self.config.confidence_threshold = new_config["confidence_threshold"]
        
        if "max_alerts_per_cycle" in new_config:
            self.config.max_alerts_per_cycle = new_config["max_alerts_per_cycle"]
        
        logger.info("Workflow configuration updated", **new_config)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current workflow status and configuration."""
        return {
            "workflow_initialized": self.workflow is not None,
            "crypto_apis_available": self.crypto_api.get_available_apis(),
            "target_cryptocurrencies": self.config.target_cryptocurrencies,
            "analysis_interval_hours": self.config.analysis_interval_hours,
            "confidence_threshold": self.config.confidence_threshold,
            "max_alerts_per_cycle": self.config.max_alerts_per_cycle,
            "email_alerts_enabled": self.config.enable_email_alerts,
            "urgent_alerts_enabled": self.config.enable_urgent_alerts
        }

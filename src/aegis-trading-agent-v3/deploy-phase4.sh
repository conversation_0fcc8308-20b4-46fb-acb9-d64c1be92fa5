#!/bin/bash

# Aegis Trading Agent v3 - Phase 4: Full Agent Deployment
# Milestone 4.1: Production API Configuration with Live Data

set -e

echo "🚀 Aegis Trading Agent v3 - Phase 4: Full Agent Deployment"
echo "Milestone 4.1: Production API Configuration with Live Data"
echo "Project: vertex-ai-agent-yzdlnjey"
echo "Service: aegis-trading-agent-v3-test"
echo "Region: us-west1"
echo ""

# Verify we're in the correct project
CURRENT_PROJECT=$(gcloud config get-value project)
if [ "$CURRENT_PROJECT" != "vertex-ai-agent-yzdlnjey" ]; then
    echo "❌ Error: Current project is $CURRENT_PROJECT, expected vertex-ai-agent-yzdlnjey"
    echo "Please run: gcloud config set project vertex-ai-agent-yzdlnjey"
    exit 1
fi

echo "✅ Project verified: $CURRENT_PROJECT"

# Check API key configuration
echo "🔍 Verifying API key configuration..."
API_KEYS_CONFIGURED=0

if gcloud secrets describe "AEGIS_TRADING_COINGECKO_API_KEY" >/dev/null 2>&1; then
    echo "✅ CoinGecko API: Configured"
    API_KEYS_CONFIGURED=$((API_KEYS_CONFIGURED + 1))
else
    echo "❌ CoinGecko API: Not configured"
fi

if gcloud secrets describe "AEGIS_TRADING_NEWSAPI_KEY" >/dev/null 2>&1; then
    echo "✅ NewsAPI: Configured"
    API_KEYS_CONFIGURED=$((API_KEYS_CONFIGURED + 1))
else
    echo "❌ NewsAPI: Not configured"
fi

if gcloud secrets describe "AEGIS_TRADING_CRYPTOPANIC_KEY" >/dev/null 2>&1; then
    echo "✅ CryptoPanic API: Configured"
    API_KEYS_CONFIGURED=$((API_KEYS_CONFIGURED + 1))
else
    echo "❌ CryptoPanic API: Not configured"
fi

echo "✅ DEX Screener: Ready (no key required)"

if gcloud secrets describe "AEGIS_TRADING_SANTIMENT_KEY" >/dev/null 2>&1; then
    echo "✅ Santiment API: Configured (optional)"
    API_KEYS_CONFIGURED=$((API_KEYS_CONFIGURED + 1))
else
    echo "❌ Santiment API: Not configured (optional)"
fi

echo ""
echo "📊 API Configuration Summary: $API_KEYS_CONFIGURED/4 APIs configured"

if [ $API_KEYS_CONFIGURED -lt 2 ]; then
    echo "⚠️  Warning: Only $API_KEYS_CONFIGURED APIs configured. Consider adding more for better data coverage."
    echo "   Run ./setup-api-keys.sh to add missing API keys"
else
    echo "✅ Sufficient APIs configured for live data testing"
fi

echo ""

# Verify Phase 3 service is running
echo "🔍 Verifying Phase 3 service status..."
SERVICE_URL=$(gcloud run services describe aegis-trading-agent-v3-test --region=us-west1 --format="value(status.url)" 2>/dev/null || echo "")

if [ -z "$SERVICE_URL" ]; then
    echo "❌ Error: Phase 3 service not found. Please complete Phase 3 first."
    exit 1
fi

echo "✅ Phase 3 service verified: $SERVICE_URL"

# Start Cloud Build for Phase 4
echo ""
echo "🏗️  Starting Cloud Build for Phase 4: Full Agent Deployment..."
echo "Build configuration: cloudbuild-phase3.yaml (updated with Phase 4 features)"
echo "New capabilities:"
echo "  - Live cryptocurrency data retrieval from configured APIs"
echo "  - Production API key integration via Secret Manager"
echo "  - Enhanced error handling for API failures"
echo "  - New endpoint: /test-live-data for real market data testing"
echo "  - New endpoint: /run-analysis-cycle for autonomous operation"
echo "Memory allocation: 2Gi for crypto analysis workloads"
echo "CPU allocation: 2 cores for concurrent API processing"

gcloud builds submit \
    --config=cloudbuild-phase3.yaml \
    ../..

echo ""
echo "🎉 Phase 4 Milestone 4.1 deployment initiated successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Monitor build progress in Cloud Console"
echo "2. Once deployed, test the new Phase 4 endpoints:"
echo "   - Live data test: GET /test-live-data"
echo "   - Analysis cycle: GET /run-analysis-cycle"
echo "   - All Phase 1-3 endpoints remain available"
echo ""
echo "🔗 Service URL: $SERVICE_URL"
echo ""
echo "✅ Phase 4 Milestone 4.1: Production API Configuration in progress..."
echo "🎯 Next: Milestone 4.2: Gmail API Integration"
echo ""
echo "🤖 Live Data Features Ready After Deployment:"
echo "  - Real cryptocurrency market data from $API_KEYS_CONFIGURED APIs"
echo "  - Live news and sentiment analysis"
echo "  - DEX trading pair monitoring"
echo "  - Production-ready autonomous analysis cycles"

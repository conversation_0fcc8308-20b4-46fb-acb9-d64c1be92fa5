# Aegis Trading Agent v3 - Minimal Requirements for Phase 1 Testing
# Following proven agent template methodology

# Core FastAPI dependencies
fastapi>=0.110.0
uvicorn[standard]>=0.29.0
pydantic>=2.7.0
pydantic-settings>=2.2.0

# Basic utilities
requests>=2.31.0
python-dotenv>=1.0.0

# GCP dependencies for authentication and services
google-cloud-secret-manager>=2.20.0
google-cloud-firestore>=2.16.0
google-auth>=2.29.0

# Redis for caching (will use db=1 for trading isolation)
redis>=5.0.0

# Logging and monitoring
structlog>=24.1.0

#!/bin/bash

# Aegis Trading Agent v3 - Minimal Test Deployment Script
# Phase 1: Test Infrastructure

set -e

echo "🚀 Starting Aegis Trading Agent v3 - Phase 1: Test Infrastructure Deployment"
echo "Project: vertex-ai-agent-yzdlnjey"
echo "Service: aegis-trading-agent-v3-test"
echo "Region: us-west1"
echo ""

# Verify we're in the correct project
CURRENT_PROJECT=$(gcloud config get-value project)
if [ "$CURRENT_PROJECT" != "vertex-ai-agent-yzdlnjey" ]; then
    echo "❌ Error: Current project is $CURRENT_PROJECT, expected vertex-ai-agent-yzdlnjey"
    echo "Please run: gcloud config set project vertex-ai-agent-yzdlnjey"
    exit 1
fi

echo "✅ Project verified: $CURRENT_PROJECT"

# Verify service account exists
echo "🔍 Verifying service account..."
if gcloud iam service-<NAME_EMAIL> > /dev/null 2>&1; then
    echo "✅ Service account verified: <EMAIL>"
else
    echo "❌ Error: <NAME_EMAIL> not found"
    exit 1
fi

# Verify trading secrets exist
echo "🔍 Verifying trading secrets..."
TRADING_SECRETS=(
    "AEGIS_TRADING_COINGECKO_API_KEY"
    "AEGIS_TRADING_NEWSAPI_KEY"
    "AEGIS_TRADING_SANTIMENT_API_KEY"
    "AEGIS_TRADING_CRYPTOPANIC_API_KEY"
    "AEGIS_TRADING_DEX_SCREENER_API_KEY"
)

for secret in "${TRADING_SECRETS[@]}"; do
    if gcloud secrets describe $secret > /dev/null 2>&1; then
        echo "✅ Secret verified: $secret"
    else
        echo "❌ Error: Secret $secret not found"
        exit 1
    fi
done

# Start Cloud Build
echo ""
echo "🏗️  Starting Cloud Build for minimal test deployment..."
echo "Build configuration: cloudbuild-minimal.yaml"

gcloud builds submit \
    --config=cloudbuild-minimal.yaml \
    ../..

echo ""
echo "🎉 Deployment initiated successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Monitor build progress in Cloud Console"
echo "2. Once deployed, test the service endpoints:"
echo "   - Health check: GET /health"
echo "   - Import test: GET /test-imports"
echo "   - GCP connectivity: GET /test-gcp-connectivity"
echo "   - Service account: GET /test-service-account"
echo ""
echo "🔗 Service URL will be available after deployment completes"
echo "   Format: https://aegis-trading-agent-v3-test-[hash]-uw.a.run.app"
echo ""
echo "✅ Phase 1: Test Infrastructure deployment in progress..."

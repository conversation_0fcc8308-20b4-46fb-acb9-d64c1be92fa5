"""
Inbound Lead Processing System - Form Webhook Handler

Handles form submissions from frontend and processes them through the Executive Agent
for lead enrichment, CRM integration, and automated follow-up.
"""

import logging
import json
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPException
from pydantic import BaseModel, Field

from services.crm_client import get_crm_client
from services.redis_checkpointer import get_redis_checkpointer
from agent.core import ExecutiveAgent

logger = logging.getLogger(__name__)


class FormSubmission(BaseModel):
    """Model for form submission data."""
    form_type: str = Field(description="Type of form: contact, lead_capture, demo_request, consultation")
    email: str = Field(description="Submitter email address")
    name: str = Field(description="Submitter full name")
    company: str = Field(default="", description="Company name")
    phone: str = Field(default="", description="Phone number")
    message: str = Field(default="", description="Form message/inquiry")
    source: str = Field(default="website", description="Traffic source")
    utm_campaign: str = Field(default="", description="UTM campaign")
    utm_source: str = Field(default="", description="UTM source")
    utm_medium: str = Field(default="", description="UTM medium")
    page_url: str = Field(default="", description="Page where form was submitted")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional form data")


class LeadProcessingResult(BaseModel):
    """Result of lead processing."""
    success: bool
    lead_id: str
    crm_contact_id: Optional[str] = None
    crm_deal_id: Optional[str] = None
    enrichment_data: Dict[str, Any] = Field(default_factory=dict)
    follow_up_scheduled: bool = False
    error_message: Optional[str] = None


class InboundLeadProcessor:
    """
    Processes inbound form submissions through the Executive Agent ecosystem.
    
    Workflow:
    1. Receive form submission
    2. Validate and normalize data
    3. Enrich lead information using agent tools
    4. Create/update CRM records
    5. Schedule automated follow-up
    6. Notify relevant agents
    """
    
    def __init__(self, executive_agent: ExecutiveAgent):
        self.executive_agent = executive_agent
        self.redis_client = None
        self.crm_client = None
    
    async def initialize(self):
        """Initialize service connections."""
        try:
            self.redis_client = await get_redis_checkpointer()
            self.crm_client = await get_crm_client()
            logger.info("Inbound lead processor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize lead processor: {e}")
            raise
    
    async def process_form_submission(self, submission: FormSubmission) -> LeadProcessingResult:
        """
        Process a form submission through the complete lead workflow.
        
        Args:
            submission: Form submission data
            
        Returns:
            LeadProcessingResult with processing details
        """
        try:
            logger.info(f"Processing form submission from {submission.email} ({submission.form_type})")
            
            # Step 1: Validate and normalize submission data
            normalized_data = await self._normalize_submission_data(submission)
            
            # Step 2: Check for existing lead/contact
            existing_contact = await self._check_existing_contact(submission.email)
            
            # Step 3: Enrich lead data using agent tools
            enrichment_data = await self._enrich_lead_data(submission, existing_contact)
            
            # Step 4: Create or update CRM records
            crm_result = await self._sync_to_crm(submission, enrichment_data, existing_contact)
            
            # Step 5: Schedule automated follow-up
            follow_up_result = await self._schedule_follow_up(submission, crm_result)
            
            # Step 6: Store processing result
            lead_id = await self._store_lead_record(submission, enrichment_data, crm_result)
            
            # Step 7: Notify agents
            await self._notify_agents(submission, crm_result, lead_id)
            
            result = LeadProcessingResult(
                success=True,
                lead_id=lead_id,
                crm_contact_id=crm_result.get('contact_id'),
                crm_deal_id=crm_result.get('deal_id'),
                enrichment_data=enrichment_data,
                follow_up_scheduled=follow_up_result.get('scheduled', False)
            )
            
            logger.info(f"Successfully processed lead {lead_id} for {submission.email}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process form submission: {e}")
            return LeadProcessingResult(
                success=False,
                lead_id="",
                error_message=str(e)
            )
    
    async def _normalize_submission_data(self, submission: FormSubmission) -> Dict[str, Any]:
        """Normalize and validate form submission data."""
        # Extract first/last name
        name_parts = submission.name.strip().split()
        first_name = name_parts[0] if name_parts else ""
        last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
        
        # Determine lead source
        lead_source = submission.utm_source or submission.source or "website"
        
        # Build normalized data
        normalized = {
            'email': submission.email.lower().strip(),
            'first_name': first_name,
            'last_name': last_name,
            'full_name': submission.name.strip(),
            'company': submission.company.strip(),
            'phone': submission.phone.strip(),
            'message': submission.message.strip(),
            'lead_source': lead_source,
            'form_type': submission.form_type,
            'page_url': submission.page_url,
            'utm_data': {
                'campaign': submission.utm_campaign,
                'source': submission.utm_source,
                'medium': submission.utm_medium
            },
            'timestamp': submission.timestamp,
            'metadata': submission.metadata
        }
        
        return normalized
    
    async def _check_existing_contact(self, email: str) -> Optional[Dict[str, Any]]:
        """Check if contact already exists in CRM."""
        try:
            contact = await self.crm_client.get_contact_by_email(email)
            return contact
        except Exception as e:
            logger.debug(f"No existing contact found for {email}: {e}")
            return None
    
    async def _enrich_lead_data(self, submission: FormSubmission, existing_contact: Optional[Dict]) -> Dict[str, Any]:
        """Enrich lead data using Executive Agent tools."""
        enrichment_data = {}
        
        try:
            # Use agent's AI intelligence tools for lead enrichment
            if submission.company:
                # Company research using smart query
                company_query = f"Research company information for {submission.company}"
                company_research = await self.executive_agent.execute_tool(
                    "smart_query_with_rag",
                    query=company_query,
                    customer_id="lead_enrichment",
                    include_predictions=True
                )
                enrichment_data['company_research'] = company_research
            
            # Lead scoring based on form data and existing data
            lead_score = await self._calculate_lead_score(submission, existing_contact)
            enrichment_data['lead_score'] = lead_score
            
            # Determine lead priority
            priority = await self._determine_lead_priority(submission, lead_score)
            enrichment_data['priority'] = priority
            
            logger.info(f"Enriched lead data for {submission.email}: score={lead_score}, priority={priority}")
            
        except Exception as e:
            logger.warning(f"Lead enrichment failed for {submission.email}: {e}")
            enrichment_data['enrichment_error'] = str(e)
        
        return enrichment_data
    
    async def _calculate_lead_score(self, submission: FormSubmission, existing_contact: Optional[Dict]) -> int:
        """Calculate lead score based on submission data."""
        score = 0
        
        # Form type scoring
        form_scores = {
            'demo_request': 50,
            'consultation': 45,
            'contact': 30,
            'lead_capture': 25
        }
        score += form_scores.get(submission.form_type, 20)
        
        # Company presence
        if submission.company:
            score += 20
        
        # Phone number provided
        if submission.phone:
            score += 15
        
        # Message quality (longer messages indicate higher intent)
        if len(submission.message) > 100:
            score += 15
        elif len(submission.message) > 50:
            score += 10
        
        # UTM campaign data (indicates marketing qualified)
        if submission.utm_campaign:
            score += 10
        
        # Existing contact (returning visitor)
        if existing_contact:
            score += 25
        
        return min(score, 100)  # Cap at 100

    async def _determine_lead_priority(self, submission: FormSubmission, lead_score: int) -> str:
        """Determine lead priority based on score and form type."""
        if lead_score >= 70 or submission.form_type in ['demo_request', 'consultation']:
            return 'high'
        elif lead_score >= 50:
            return 'medium'
        else:
            return 'low'

    async def _sync_to_crm(self, submission: FormSubmission, enrichment_data: Dict, existing_contact: Optional[Dict]) -> Dict[str, Any]:
        """Create or update CRM records."""
        try:
            # Prepare contact data
            contact_data = {
                'email': submission.email,
                'firstname': submission.name.split()[0] if submission.name else '',
                'lastname': ' '.join(submission.name.split()[1:]) if len(submission.name.split()) > 1 else '',
                'company': submission.company,
                'phone': submission.phone,
                'lead_source': submission.source,
                'lead_score': enrichment_data.get('lead_score', 0),
                'priority': enrichment_data.get('priority', 'low'),
                'form_type': submission.form_type,
                'utm_campaign': submission.utm_campaign,
                'utm_source': submission.utm_source,
                'utm_medium': submission.utm_medium,
                'page_url': submission.page_url,
                'message': submission.message,
                'created_date': submission.timestamp
            }

            # Create or update contact
            if existing_contact:
                contact_id = existing_contact['id']
                await self.crm_client.update_contact(contact_id, contact_data)
                logger.info(f"Updated existing contact {contact_id}")
            else:
                contact_id = await self.crm_client.create_contact(contact_data)
                logger.info(f"Created new contact {contact_id}")

            # Create deal for high-priority leads
            deal_id = None
            if enrichment_data.get('priority') == 'high':
                deal_data = {
                    'name': f"{submission.form_type.title()} - {submission.company or submission.name}",
                    'contact_id': contact_id,
                    'amount': 0,  # TBD
                    'stage': 'qualification',
                    'description': f"Inbound {submission.form_type} from {submission.page_url}\n\nMessage: {submission.message}",
                    'source': submission.source
                }
                deal_id = await self.crm_client.create_deal(deal_data)
                logger.info(f"Created deal {deal_id} for high-priority lead")

            return {
                'contact_id': contact_id,
                'deal_id': deal_id,
                'updated_existing': existing_contact is not None
            }

        except Exception as e:
            logger.error(f"CRM sync failed: {e}")
            raise

    async def _schedule_follow_up(self, submission: FormSubmission, crm_result: Dict) -> Dict[str, Any]:
        """Schedule automated follow-up based on form type and priority."""
        try:
            # Determine follow-up strategy based on form type
            follow_up_strategies = {
                'demo_request': {'delay_hours': 1, 'type': 'immediate_response'},
                'consultation': {'delay_hours': 2, 'type': 'consultation_booking'},
                'contact': {'delay_hours': 24, 'type': 'general_inquiry'},
                'lead_capture': {'delay_hours': 48, 'type': 'nurture_sequence'}
            }

            strategy = follow_up_strategies.get(submission.form_type, {'delay_hours': 24, 'type': 'general_inquiry'})

            # Use Executive Agent to create email sequence
            sequence_result = await self.executive_agent.execute_tool(
                "create_email_sequence",
                recipient_email=submission.email,
                sequence_type=strategy['type'],
                lead_name=submission.name,
                company_name=submission.company,
                delay_days="0,1,3",  # Immediate, 1 day, 3 days
                custom_content=f"Thank you for your {submission.form_type} submission. Message: {submission.message[:200]}"
            )

            logger.info(f"Scheduled follow-up sequence for {submission.email}: {strategy['type']}")

            return {
                'scheduled': True,
                'strategy': strategy,
                'sequence_result': sequence_result
            }

        except Exception as e:
            logger.warning(f"Follow-up scheduling failed: {e}")
            return {'scheduled': False, 'error': str(e)}

    async def _store_lead_record(self, submission: FormSubmission, enrichment_data: Dict, crm_result: Dict) -> str:
        """Store lead processing record in Redis."""
        try:
            lead_id = f"lead_{submission.email}_{int(datetime.now().timestamp())}"

            lead_record = {
                'lead_id': lead_id,
                'submission_data': submission.dict(),
                'enrichment_data': enrichment_data,
                'crm_result': crm_result,
                'processed_at': datetime.now().isoformat(),
                'status': 'processed'
            }

            # Store in Redis with 30-day expiration
            await self.redis_client.set(
                f"lead_record:{lead_id}",
                json.dumps(lead_record),
                ex=30 * 24 * 60 * 60  # 30 days
            )

            # Add to processing queue for agent notifications
            await self.redis_client.lpush("lead_processing_queue", lead_id)

            return lead_id

        except Exception as e:
            logger.error(f"Failed to store lead record: {e}")
            raise

    async def _notify_agents(self, submission: FormSubmission, crm_result: Dict, lead_id: str):
        """Notify Executive Agent and Sales Development Agent of new lead."""
        try:
            # Create notification message
            notification = {
                'type': 'new_lead',
                'lead_id': lead_id,
                'priority': submission.metadata.get('priority', 'medium'),
                'form_type': submission.form_type,
                'contact_info': {
                    'email': submission.email,
                    'name': submission.name,
                    'company': submission.company
                },
                'crm_ids': {
                    'contact_id': crm_result.get('contact_id'),
                    'deal_id': crm_result.get('deal_id')
                },
                'timestamp': datetime.now().isoformat()
            }

            # Add to agent notification queues
            await self.redis_client.lpush("executive_agent_notifications", json.dumps(notification))
            await self.redis_client.lpush("sales_dev_agent_notifications", json.dumps(notification))

            logger.info(f"Notified agents of new lead {lead_id}")

        except Exception as e:
            logger.warning(f"Agent notification failed: {e}")


# Convenience function for getting processor instance
async def get_lead_processor(executive_agent: ExecutiveAgent) -> InboundLeadProcessor:
    """Get an initialized lead processor instance."""
    processor = InboundLeadProcessor(executive_agent)
    await processor.initialize()
    return processor

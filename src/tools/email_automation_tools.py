"""
Enhanced Email Automation Tools - TKC_v5 Executive Agent

Provides advanced email automation including sequences, follow-ups, 
and intelligent email management.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from langchain_core.tools import tool
from pydantic import BaseModel, Field

from services.gmail_client import get_gmail_client
from services.crm_client import get_crm_client
from services.redis_checkpointer import get_redis_checkpointer

logger = logging.getLogger(__name__)


# Removed old Pydantic models - now using simple parameters for Vertex AI compatibility


@tool
async def create_email_sequence(
    recipient_email: str,
    sequence_type: str,
    lead_name: str = "",
    company_name: str = "",
    delay_days: str = "1,3,7",
    custom_content: str = ""
) -> str:
    """
    Create an automated email sequence for lead nurturing.

    Args:
        recipient_email: Recipient email address
        sequence_type: Type of sequence: 'welcome', 'follow_up', 'nurture', 'sales'
        lead_name: Lead's name for personalization
        company_name: Lead's company name
        delay_days: Comma-separated days between emails (e.g., "1,3,7")
        custom_content: Custom content to include

    Generates a series of personalized emails to be sent over time
    for lead nurturing, onboarding, or sales follow-up.
    """
    try:
        gmail_client = await get_gmail_client()
        redis_client = await get_redis_checkpointer()
        
        # Generate sequence content based on type
        sequence_templates = {
            'welcome': [
                {
                    'subject': 'Welcome to TKC Group - Let\'s Get Started',
                    'template': 'welcome_intro',
                    'delay': 0
                },
                {
                    'subject': 'Your Next Steps with TKC Group',
                    'template': 'welcome_next_steps', 
                    'delay': 1
                },
                {
                    'subject': 'Quick Check-in: How Can We Help?',
                    'template': 'welcome_checkin',
                    'delay': 3
                }
            ],
            'follow_up': [
                {
                    'subject': 'Following up on our conversation',
                    'template': 'follow_up_gentle',
                    'delay': 1
                },
                {
                    'subject': 'Additional resources that might help',
                    'template': 'follow_up_value',
                    'delay': 3
                },
                {
                    'subject': 'Final follow-up - staying in touch',
                    'template': 'follow_up_final',
                    'delay': 7
                }
            ],
            'nurture': [
                {
                    'subject': 'Industry insights you might find valuable',
                    'template': 'nurture_insights',
                    'delay': 1
                },
                {
                    'subject': 'Case study: How we helped [similar company]',
                    'template': 'nurture_case_study',
                    'delay': 5
                },
                {
                    'subject': 'Ready to explore next steps?',
                    'template': 'nurture_cta',
                    'delay': 10
                }
            ],
            'sales': [
                {
                    'subject': 'Proposal for [Company Name]',
                    'template': 'sales_proposal',
                    'delay': 0
                },
                {
                    'subject': 'Questions about our proposal?',
                    'template': 'sales_follow_up',
                    'delay': 3
                },
                {
                    'subject': 'Final opportunity to move forward',
                    'template': 'sales_final',
                    'delay': 7
                }
            ]
        }
        
        # Parse delay_days from string
        delay_list = [int(d.strip()) for d in delay_days.split(",") if d.strip()]

        # Build lead info from parameters
        lead_info = {
            "name": lead_name,
            "company": company_name
        }

        sequence = sequence_templates.get(sequence_type, sequence_templates['follow_up'])

        # Create personalized emails for the sequence
        created_emails = []

        for i, email_config in enumerate(sequence):
            # Generate personalized content
            content = await _generate_sequence_email_content(
                email_config['template'],
                lead_info,
                custom_content
            )

            # Create draft email
            draft_data = {
                'to': recipient_email,
                'subject': email_config['subject'].replace('[Company Name]',
                                                        lead_info.get('company', 'your company')),
                'body': content,
                'scheduled_send': (datetime.now() + timedelta(days=email_config['delay'])).isoformat()
            }

            # Store in Redis for scheduled sending
            sequence_key = f"email_sequence:{recipient_email}:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            await redis_client.set(f"{sequence_key}:email_{i}", json.dumps(draft_data))
            
            created_emails.append({
                'email_number': i + 1,
                'subject': draft_data['subject'],
                'scheduled_for': draft_data['scheduled_send'],
                'delay_days': email_config['delay']
            })
        
        # Log sequence creation in CRM
        try:
            crm_client = await get_crm_client()
            await crm_client.log_activity({
                'type': 'EMAIL_SEQUENCE',
                'description': f"Created {sequence_type} email sequence for {recipient_email}",
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logger.warning(f"Could not log to CRM: {e}")

        response = "✅ Email sequence created successfully!\n\n"
        response += f"📧 Sequence Type: {sequence_type.title()}" + "\n"
        response += f"👤 Recipient: {recipient_email}" + "\n"
        response += f"📅 Total Emails: {len(created_emails)}" + "\n\n"
        response += "📋 Sequence Schedule:\n"
        
        for email in created_emails:
            response += f"  {email['email_number']}. {email['subject']}" + "\n"
            response += f"     📅 Scheduled: {email['scheduled_for'][:10]} (Day {email['delay_days']})" + "\n\n"
        
        response += "💡 Emails will be automatically sent according to the schedule."
        
        return response
        
    except Exception as e:
        logger.error(f"Error creating email sequence: {e}")
        return f"Error creating email sequence: {str(e)}"


@tool
async def schedule_follow_up(
    original_thread_id: str,
    follow_up_type: str,
    days_delay: int = 3,
    custom_message: str = ""
) -> str:
    """
    Schedule an intelligent follow-up email based on conversation context.

    Args:
        original_thread_id: Thread ID of original conversation
        follow_up_type: Type: 'gentle_reminder', 'value_add', 'check_in', 'proposal'
        days_delay: Days to wait before follow-up
        custom_message: Custom follow-up message

    Analyzes the original conversation and creates a contextual follow-up
    email to be sent after the specified delay.
    """
    try:
        gmail_client = await get_gmail_client()
        redis_client = await get_redis_checkpointer()
        
        # Get original thread context
        thread_info = await gmail_client.get_thread_info(original_thread_id)

        if not thread_info:
            return f"Could not find thread with ID: {original_thread_id}"

        # Generate follow-up content based on type and context
        follow_up_content = await _generate_follow_up_content(
            follow_up_type,
            thread_info,
            custom_message
        )
        
        # Extract recipient from original thread
        recipient = thread_info.get('participants', [])[-1]  # Last participant
        
        # Create follow-up email
        follow_up_data = {
            'to': recipient,
            'subject': f"Re: {thread_info.get('subject', 'Our conversation')}",
            'body': follow_up_content,
            'thread_id': original_thread_id,
            'scheduled_send': (datetime.now() + timedelta(days=days_delay)).isoformat(),
            'follow_up_type': follow_up_type
        }

        # Store for scheduled sending
        follow_up_key = f"follow_up:{original_thread_id}:{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        await redis_client.set(follow_up_key, json.dumps(follow_up_data))
        
        # Log in CRM
        try:
            crm_client = await get_crm_client()
            await crm_client.log_activity({
                'type': 'FOLLOW_UP_SCHEDULED',
                'description': f"Scheduled {follow_up_type} follow-up for thread {original_thread_id}",
                'timestamp': datetime.now().isoformat()
            })
        except Exception as e:
            logger.warning(f"Could not log to CRM: {e}")

        response = "✅ Follow-up email scheduled successfully!\n\n"
        response += f"📧 Type: {follow_up_type.replace('_', ' ').title()}" + "\n"
        response += f"👤 Recipient: {recipient}" + "\n"
        response += f"📅 Scheduled for: {follow_up_data['scheduled_send'][:10]}" + "\n"
        response += f"⏰ Delay: {days_delay} days" + "\n"
        response += f"🔗 Thread: {original_thread_id}" + "\n\n"
        response += "💡 The follow-up will be automatically sent at the scheduled time."
        
        return response
        
    except Exception as e:
        logger.error(f"Error scheduling follow-up: {e}")
        return f"Error scheduling follow-up: {str(e)}"


@tool
async def analyze_email_engagement(email_address: str, days_back: int = 30) -> str:
    """
    Analyze email engagement patterns for a specific contact.
    
    Reviews email history to understand response patterns, engagement levels,
    and optimal timing for future communications.
    """
    try:
        gmail_client = await get_gmail_client()
        redis_client = await get_redis_checkpointer()
        
        # Get email history for the contact
        emails = await gmail_client.search_emails(
            query=f"from:{email_address} OR to:{email_address}",
            max_results=50
        )
        
        if not emails:
            return f"No email history found for {email_address}"
        
        # Analyze engagement patterns
        total_emails = len(emails)
        responses = 0
        response_times = []
        email_times = []
        
        for email in emails:
            email_time = datetime.fromisoformat(email.get('date', ''))
            email_times.append(email_time.hour)
            
            # Check if this was a response (simplified logic)
            if email.get('in_reply_to') or 'Re:' in email.get('subject', ''):
                responses += 1
                # Calculate response time if possible
                # This would need more sophisticated thread analysis
        
        response_rate = (responses / total_emails) * 100 if total_emails > 0 else 0
        
        # Analyze optimal timing
        from collections import Counter
        hour_distribution = Counter(email_times)
        optimal_hours = sorted(hour_distribution.items(), key=lambda x: x[1], reverse=True)[:3]
        
        response = f"📊 Email Engagement Analysis for {email_address}" + "\n\n"
        response += f"📧 Total Emails: {total_emails}" + "\n"
        response += f"💬 Response Rate: {response_rate:.1f}%" + "\n"
        response += f"📅 Analysis Period: Last {days_back} days" + "\n\n"
        
        response += "🕐 Optimal Email Times:\n"
        for hour, count in optimal_hours:
            time_str = f"{hour:02d}:00"
            response += f"  • {time_str} ({count} emails)" + "\n"
        
        response += "\n💡 Recommendations:\n"
        if response_rate > 50:
            response += "  • High engagement - continue current approach\n"
        elif response_rate > 25:
            response += "  • Moderate engagement - consider more personalized content\n"
        else:
            response += "  • Low engagement - try different approach or timing\n"
        
        if optimal_hours:
            best_hour = optimal_hours[0][0]
            response += f"  • Best time to send: {best_hour:02d}:00" + "\n"
        
        return response
        
    except Exception as e:
        logger.error(f"Error analyzing email engagement: {e}")
        return f"Error analyzing email engagement: {str(e)}"


async def _generate_sequence_email_content(template: str, lead_info: Dict[str, Any], custom_content: Optional[str]) -> str:
    """Generate personalized email content for sequences."""
    
    templates = {
        'welcome_intro': f"""
Hi {lead_info.get('name', 'there')},

Welcome to TKC Group! I'm excited to help {lead_info.get('company', 'your organization')} achieve its goals.

{custom_content or "Based on our conversation, I understand you're looking to improve your business operations with AI automation."}

I'll be sending you some valuable resources over the next few days to help you get started.

Best regards,
Tyler Klug
TKC Group
""",
        'follow_up_gentle': f"""
Hi {lead_info.get('name', 'there')},

I wanted to follow up on our recent conversation about {lead_info.get('topic', 'your business needs')}.

{custom_content or 'I hope you had a chance to review the information we discussed.'}

Do you have any questions or would you like to schedule a time to discuss next steps?

Best regards,
Tyler Klug
""",
        'nurture_insights': f"""
Hi {lead_info.get('name', 'there')},

I came across this industry insight that I thought might be valuable for {lead_info.get('company', 'your business')}:

{custom_content or 'AI automation is transforming how businesses operate, with companies seeing 40% efficiency improvements on average.'}

Would love to hear your thoughts on how this might apply to your situation.

Best regards,
Tyler Klug
"""
    }
    
    return templates.get(template, templates['follow_up_gentle'])


async def _generate_follow_up_content(follow_up_type: str, thread_info: Dict[str, Any], custom_message: Optional[str]) -> str:
    """Generate contextual follow-up content."""
    
    subject = thread_info.get('subject', 'our conversation')
    
    templates = {
        'gentle_reminder': f"""
Hi,

I wanted to gently follow up on {subject}.

{custom_message or 'I hope you had a chance to consider what we discussed.'}

Please let me know if you have any questions or if there\'s anything I can clarify.

Best regards,
Tyler Klug
""",
        'value_add': f"""
Hi,

Following up on {subject}, I wanted to share some additional resources that might be helpful:

{custom_message or "• Industry best practices guide" + chr(10) + "• Case study from similar company" + chr(10) + "• Free consultation offer"}

Would any of these be valuable for your situation?

Best regards,
Tyler Klug
""",
        'check_in': f"""Hi,

Just checking in regarding {subject}.

{custom_message or "I wanted to see how things are progressing and if there's anything I can help with."}

No pressure - just want to make sure you have everything you need.

Best regards,
Tyler Klug"""
    }
    
    return templates.get(follow_up_type, templates['gentle_reminder'])


# Export all email automation tools
EMAIL_AUTOMATION_TOOLS = [
    create_email_sequence,
    schedule_follow_up,
    analyze_email_engagement
]

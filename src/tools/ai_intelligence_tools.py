"""
AI Intelligence Tools - TKC_v5 Executive Agent

Advanced AI-powered tools for conversation intelligence, predictive analytics,
and autonomous decision-making capabilities.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from langchain_core.tools import tool
from pydantic import BaseModel, Field

from services.advanced_rag_service import get_advanced_rag_service
from services.predictive_analytics_service import get_predictive_analytics_service
from services.data_persistence_service import get_data_persistence_service
from services.monitoring_service import get_monitoring_service

logger = logging.getLogger(__name__)


# Removed old Pydantic models - now using simple parameters for Vertex AI compatibility


@tool
async def analyze_conversation_intelligence(
    conversation_id: str,
    customer_id: str,
    include_predictions: bool = True
) -> str:
    """
    Analyze conversation with advanced AI intelligence.

    Args:
        conversation_id: ID of the conversation to analyze
        customer_id: Customer ID
        include_predictions: Include predictive insights

    Provides deep insights into conversation context, sentiment, urgency,
    key entities, action items, and predictive recommendations.
    """
    try:
        # Get conversation data
        persistence_service = await get_data_persistence_service()
        conversation_data = await persistence_service.get_conversation(
            customer_id, conversation_id
        )

        if not conversation_data:
            return f"❌ Conversation {conversation_id} not found"
        
        # Analyze conversation context
        rag_service = await get_advanced_rag_service()
        context = await rag_service.analyze_conversation_context(conversation_data)
        
        # Build response
        response = f"🧠 **Conversation Intelligence Analysis**\n\n"
        response += f"**Conversation ID:** {conversation_id}\n"
        response += f"**Participants:** {', '.join(context.participants)}\n\n"
        
        # Topic Analysis
        response += f"**📋 Topics:** {', '.join(context.topic_categories)}\n"
        
        # Sentiment Analysis
        sentiment_emoji = "😊" if context.sentiment_score > 0.3 else "😐" if context.sentiment_score > -0.3 else "😟"
        response += f"**{sentiment_emoji} Sentiment:** {context.sentiment_score:.2f} "
        response += f"({'Positive' if context.sentiment_score > 0.3 else 'Neutral' if context.sentiment_score > -0.3 else 'Negative'})\n"
        
        # Urgency Analysis
        urgency_emoji = {"urgent": "🚨", "high": "⚡", "medium": "📋", "low": "📝"}
        response += f"**{urgency_emoji.get(context.urgency_level, '📋')} Urgency:** {context.urgency_level.title()}\n\n"
        
        # Key Entities
        if context.key_entities:
            response += f"**🎯 Key Entities:**\n"
            for entity in context.key_entities[:5]:
                response += f"  • {entity['type'].title()}: {entity['value']} (confidence: {entity['confidence']:.1%})\n"
            response += "\n"
        
        # Action Items
        if context.action_items:
            response += f"**✅ Action Items:**\n"
            for item in context.action_items[:5]:
                response += f"  • {item}\n"
            response += "\n"
        
        # Follow-up Requirements
        if context.follow_up_required:
            response += f"**🔄 Follow-up Required:** Yes\n\n"
        
        # Predictive Insights
        if include_predictions:
            analytics_service = await get_predictive_analytics_service()
            
            # Predict response time if this is an email conversation
            if any("email" in topic for topic in context.topic_categories):
                email_data = {
                    "subject": conversation_data.get('subject', ''),
                    "body": ' '.join([msg.get('content', '') for msg in conversation_data.get('messages', [])]),
                    "timestamp": datetime.now().isoformat()
                }
                
                prediction = await analytics_service.predict_email_response_time(
                    email_data, customer_id
                )
                
                response += f"**🔮 Predictive Insights:**\n"
                response += f"  • Expected Response Time: {prediction.predicted_value}\n"
                response += f"  • Confidence: {prediction.confidence_score:.1%}\n"
                
                if prediction.recommendations:
                    response += f"  • Recommendations: {', '.join(prediction.recommendations[:2])}\n"
                response += "\n"
        
        # Context Summary
        response += f"**📝 Context Summary:**\n{context.context_summary}\n\n"
        
        # Log analysis
        monitoring_service = await get_monitoring_service()
        await monitoring_service.log_tool_execution(
            tool_name="analyze_conversation_intelligence",
            customer_id=customer_id,
            success=True,
            duration_ms=1000  # Placeholder
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error analyzing conversation intelligence: {e}")
        return f"❌ Error analyzing conversation: {str(e)}"


@tool
async def smart_query_with_rag(
    query: str,
    customer_id: str,
    conversation_id: str = "",
    include_predictions: bool = False
) -> str:
    """
    Perform intelligent query with enhanced RAG capabilities.

    Args:
        query: Natural language query
        customer_id: Customer ID
        conversation_id: Current conversation context (optional)
        include_predictions: Include predictive insights

    Uses conversation context, semantic search, and predictive insights
    to provide comprehensive, context-aware responses.
    """
    try:
        rag_service = await get_advanced_rag_service()
        
        # Get conversation context if provided
        conversation_context = None
        if conversation_id:
            persistence_service = await get_data_persistence_service()
            conversation_data = await persistence_service.get_conversation(
                customer_id, conversation_id
            )
            
            if conversation_data:
                conversation_context = await rag_service.analyze_conversation_context(conversation_data)
        
        # Perform enhanced retrieval
        rag_result = await rag_service.enhanced_retrieval(
            query=query,
            customer_id=customer_id,
            conversation_context=conversation_context,
            max_results=5
        )

        # Build response
        response = f"🔍 **Smart Query Response**\n\n"
        response += f"**Query:** {query}\n\n"
        
        # Main response
        response += f"**📋 Response:**\n{rag_result.content}\n\n"
        
        # Confidence and reasoning
        confidence_emoji = "🟢" if rag_result.confidence_score > 0.7 else "🟡" if rag_result.confidence_score > 0.4 else "🔴"
        response += f"**{confidence_emoji} Confidence:** {rag_result.confidence_score:.1%}\n"
        response += f"**🧠 Reasoning:** {rag_result.reasoning}\n\n"
        
        # Source information
        if rag_result.source_documents:
            response += f"**📚 Sources:** {len(rag_result.source_documents)} relevant documents found\n\n"
        
        # Suggested actions
        if rag_result.suggested_actions:
            response += f"**💡 Suggested Actions:**\n"
            for action in rag_result.suggested_actions[:3]:
                response += f"  • {action}\n"
            response += "\n"
        
        # Predictive insights
        if include_predictions and conversation_context:
            analytics_service = await get_predictive_analytics_service()
            insights = await analytics_service.analyze_customer_engagement(customer_id)
            
            if insights:
                response += f"**🔮 Business Insights:**\n"
                for insight in insights[:2]:
                    response += f"  • {insight.title}: {insight.description}\n"
                response += "\n"
        
        # Log query
        monitoring_service = await get_monitoring_service()
        await monitoring_service.log_tool_execution(
            tool_name="smart_query_with_rag",
            customer_id=customer_id,
            success=True,
            duration_ms=1500  # Placeholder
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error in smart query: {e}")
        return f"❌ Error processing query: {str(e)}"


@tool
async def generate_business_intelligence(
    customer_id: str,
    analysis_type: str,
    time_period: str = "30_days"
) -> str:
    """
    Generate comprehensive business intelligence insights.

    Args:
        customer_id: Customer ID
        analysis_type: Type of analysis: engagement, forecast, opportunities
        time_period: Time period for analysis (default: 30_days)

    Provides predictive analytics, trend analysis, and strategic
    recommendations based on customer data and patterns.
    """
    try:
        analytics_service = await get_predictive_analytics_service()
        
        response = f"📊 **Business Intelligence Report**\n\n"
        response += f"**Customer:** {customer_id}\n"
        response += f"**Analysis Type:** {analysis_type.title()}\n"
        response += f"**Time Period:** {time_period.replace('_', ' ').title()}\n"
        response += f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n"

        if analysis_type == "engagement":
            # Customer engagement analysis
            insights = await analytics_service.analyze_customer_engagement(customer_id)
            
            if insights:
                response += f"**🎯 Engagement Insights:**\n"
                for insight in insights:
                    impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}
                    response += f"{impact_emoji.get(insight.impact_level, '📋')} **{insight.title}**\n"
                    response += f"   {insight.description}\n"
                    
                    if insight.recommended_actions:
                        response += f"   **Actions:** {', '.join(insight.recommended_actions[:2])}\n"
                    response += f"   **Confidence:** {insight.confidence:.1%}\n\n"
            else:
                response += "No significant engagement insights found.\n\n"
        
        elif analysis_type == "forecast":
            # Business forecast
            days = 30 if time_period == "30_days" else 7
            forecast = await analytics_service.generate_business_forecast(customer_id, days)
            
            if 'forecasts' in forecast:
                forecasts = forecast['forecasts']
                
                response += f"**📈 {days}-Day Forecast:**\n\n"
                
                # Email forecast
                if 'email_volume' in forecasts:
                    email_data = forecasts['email_volume']
                    response += f"**📧 Email Volume:**\n"
                    response += f"   Current Weekly: {email_data['current_weekly_volume']}\n"
                    response += f"   Forecasted: {email_data['forecasted_volume']:.0f}\n"
                    response += f"   Trend: {email_data['trend'].title()}\n\n"
                
                # Meeting forecast
                if 'meetings' in forecasts:
                    meeting_data = forecasts['meetings']
                    response += f"**📅 Meetings:**\n"
                    response += f"   Current Weekly: {meeting_data['current_weekly_meetings']}\n"
                    response += f"   Forecasted: {meeting_data['forecasted_meetings']:.0f}\n"
                    response += f"   Trend: {meeting_data['trend'].title()}\n\n"
                
                # Business health
                if 'business_health_score' in forecasts:
                    health = forecasts['business_health_score']
                    health_emoji = {"good": "🟢", "fair": "🟡", "needs_attention": "🔴"}
                    response += f"**💪 Business Health:**\n"
                    response += f"   Overall Score: {health['overall_score']:.1%}\n"
                    response += f"   Status: {health_emoji.get(health['health_status'], '📋')} {health['health_status'].replace('_', ' ').title()}\n\n"
                
                # Recommendations
                if 'recommendations' in forecasts:
                    response += f"**💡 Strategic Recommendations:**\n"
                    for rec in forecasts['recommendations']:
                        response += f"   • {rec}\n"
                    response += "\n"
        
        elif analysis_type == "opportunities":
            # Opportunity identification
            insights = await analytics_service.analyze_customer_engagement(customer_id)
            
            response += f"**🎯 Opportunity Analysis:**\n"
            
            # Filter for opportunity-related insights
            opportunities = [insight for insight in insights if "opportunity" in insight.insight_type.lower()]
            
            if opportunities:
                for opp in opportunities:
                    response += f"**{opp.title}**\n"
                    response += f"   {opp.description}\n"
                    response += f"   Impact: {opp.impact_level.title()}\n"
                    response += f"   Actions: {', '.join(opp.recommended_actions[:2])}\n\n"
            else:
                response += "No specific opportunities identified at this time.\n"
                response += "Continue monitoring engagement patterns for emerging opportunities.\n\n"
        
        # Log analysis
        monitoring_service = await get_monitoring_service()
        await monitoring_service.log_tool_execution(
            tool_name="generate_business_intelligence",
            customer_id=customer_id,
            success=True,
            duration_ms=2000  # Placeholder
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error generating business intelligence: {e}")
        return f"❌ Error generating business intelligence: {str(e)}"


@tool
async def predict_deal_outcome(deal_id: str, customer_id: str) -> str:
    """
    Predict deal closure probability and provide strategic recommendations.
    
    Uses machine learning models to analyze deal characteristics and
    historical patterns to predict outcomes and suggest actions.
    """
    try:
        # Get deal data (mock for now)
        deal_data = {
            "id": deal_id,
            "amount": 50000,
            "stage": "proposal",
            "created_date": (datetime.now() - timedelta(days=30)).isoformat(),
            "contact_interactions": 8
        }
        
        analytics_service = await get_predictive_analytics_service()
        prediction = await analytics_service.predict_deal_closure(deal_data, customer_id)
        
        response = f"🔮 **Deal Outcome Prediction**\n\n"
        response += f"**Deal ID:** {deal_id}\n"
        response += f"**Customer:** {customer_id}\n\n"
        
        # Prediction results
        confidence_emoji = "🟢" if prediction.confidence_score > 0.7 else "🟡" if prediction.confidence_score > 0.4 else "🔴"
        response += f"**📊 Closure Probability:** {prediction.predicted_value}\n"
        response += f"**{confidence_emoji} Confidence:** {prediction.confidence_score:.1%}\n"
        response += f"**🎯 Model Accuracy:** {prediction.model_accuracy:.1%}\n\n"
        
        # Contributing factors
        if prediction.contributing_factors:
            response += f"**🔍 Key Factors:**\n"
            for factor in prediction.contributing_factors[:3]:
                impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}
                response += f"   {impact_emoji.get(factor['impact'], '📋')} {factor['factor'].replace('_', ' ').title()}: "
                response += f"{factor['value']} (importance: {factor['importance']:.1%})\n"
            response += "\n"
        
        # Recommendations
        if prediction.recommendations:
            response += f"**💡 Strategic Recommendations:**\n"
            for rec in prediction.recommendations:
                response += f"   • {rec}\n"
            response += "\n"
        
        # Risk assessment
        probability = float(prediction.predicted_value.rstrip('%')) / 100
        if probability > 0.7:
            response += f"**✅ Assessment:** High probability deal - focus on closing activities\n"
        elif probability > 0.4:
            response += f"**⚠️ Assessment:** Moderate probability - address key concerns\n"
        else:
            response += f"**❌ Assessment:** Low probability - consider qualification review\n"
        
        # Log prediction
        monitoring_service = await get_monitoring_service()
        await monitoring_service.log_tool_execution(
            tool_name="predict_deal_outcome",
            customer_id=customer_id,
            success=True,
            duration_ms=800  # Placeholder
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error predicting deal outcome: {e}")
        return f"❌ Error predicting deal outcome: {str(e)}"


# Export all AI intelligence tools
AI_INTELLIGENCE_TOOLS = [
    analyze_conversation_intelligence,
    smart_query_with_rag,
    generate_business_intelligence,
    predict_deal_outcome
]

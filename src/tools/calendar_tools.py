"""
Calendar Tools - TKC_v5 Executive Agent

Provides calendar management tools for meeting scheduling, availability checking,
and appointment coordination.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from langchain_core.tools import tool
from pydantic import BaseModel, Field

from services.calendar_client import get_calendar_client, CalendarClientError

logger = logging.getLogger(__name__)


# Removed old Pydantic models - now using simple parameters for Vertex AI compatibility


@tool
async def check_calendar_availability(
    start_date: str,
    end_date: str,
    start_time: str = "09:00",
    end_time: str = "17:00",
    timezone: str = "America/Denver"
) -> str:
    """
    Check calendar availability for a specific date range.

    Args:
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        start_time: Start time in HH:MM format (default: 09:00)
        end_time: End time in HH:MM format (default: 17:00)
        timezone: Timezone for the availability check (default: America/Denver)

    Returns available time slots that can be used for scheduling meetings.
    Useful for finding when someone is free for appointments.
    """
    try:
        calendar_client = await get_calendar_client()
        
        # Parse dates and create datetime objects
        start_datetime = datetime.fromisoformat(f"{start_date}T{start_time}:00")
        end_datetime = datetime.fromisoformat(f"{end_date}T{end_time}:00")

        # Get availability
        availability = await calendar_client.get_availability(start_datetime, end_datetime)

        if not availability:
            return f"No available time slots found between {start_date} {start_time} and {end_date} {end_time}"

        # Format response
        response = f"Available time slots between {start_date} and {end_date}:\n\n"
        
        for i, slot in enumerate(availability, 1):
            start_time = datetime.fromisoformat(slot['start']).strftime("%Y-%m-%d %H:%M")
            end_time = datetime.fromisoformat(slot['end']).strftime("%Y-%m-%d %H:%M")
            duration = slot['duration_minutes']
            
            response += f"{i}. {start_time} - {end_time} ({duration} minutes)\n"
        
        return response
        
    except Exception as e:
        logger.error(f"Error checking calendar availability: {e}")
        return f"Error checking availability: {str(e)}"


@tool
async def schedule_meeting(
    title: str,
    start_time: str,
    end_time: str,
    attendees: str,
    description: str = "",
    location: str = "",
    video_meeting: bool = True,
    timezone: str = "America/Denver"
) -> str:
    """
    Schedule a new meeting on the calendar.

    Args:
        title: Meeting title/subject
        start_time: Start time in ISO format (YYYY-MM-DDTHH:MM:SS)
        end_time: End time in ISO format (YYYY-MM-DDTHH:MM:SS)
        attendees: Comma-separated list of attendee email addresses
        description: Meeting description
        location: Meeting location
        video_meeting: Whether to create a video meeting link
        timezone: Meeting timezone

    Creates a calendar event with the specified details and sends invitations
    to attendees. Automatically creates video meeting links if requested.
    """
    try:
        calendar_client = await get_calendar_client()
        
        # Parse attendees from comma-separated string
        attendees_list = [email.strip() for email in attendees.split(",") if email.strip()]

        # Prepare meeting data
        meeting_data = {
            'title': title,
            'start_time': start_time,
            'end_time': end_time,
            'attendees': attendees_list,
            'description': description,
            'location': location,
            'video_meeting': video_meeting,
            'timezone': timezone
        }
        
        # Schedule the meeting
        result = await calendar_client.schedule_meeting(meeting_data)
        
        response = f"✅ Meeting scheduled successfully!\n\n"
        response += f"📅 Title: {title}\n"
        response += f"🕐 Time: {start_time} - {end_time}\n"
        response += f"👥 Attendees: {', '.join(attendees_list)}\n"
        response += f"🔗 Event Link: {result['event_link']}\n"

        if video_meeting and result.get('meeting_link'):
            response += f"📹 Video Meeting: {result['meeting_link']}\n"
        
        response += f"\n📧 Calendar invitations have been sent to all attendees."
        
        return response
        
    except Exception as e:
        logger.error(f"Error scheduling meeting: {e}")
        return f"Error scheduling meeting: {str(e)}"


@tool
async def get_upcoming_meetings(days_ahead: int = 7) -> str:
    """
    Get upcoming meetings for the next specified number of days.
    
    Useful for checking what meetings are scheduled and avoiding conflicts.
    Default is 7 days ahead.
    """
    try:
        calendar_client = await get_calendar_client()
        
        meetings = await calendar_client.get_upcoming_meetings(days_ahead)
        
        if not meetings:
            return f"No upcoming meetings found for the next {days_ahead} days."
        
        response = f"📅 Upcoming meetings for the next {days_ahead} days:\n\n"
        
        for i, meeting in enumerate(meetings, 1):
            start_time = datetime.fromisoformat(meeting['start_time'].replace('Z', '+00:00')).strftime("%Y-%m-%d %H:%M")
            end_time = datetime.fromisoformat(meeting['end_time'].replace('Z', '+00:00')).strftime("%H:%M")
            
            response += f"{i}. {meeting['title']}\n"
            response += f"   🕐 {start_time} - {end_time}\n"
            
            if meeting['attendees']:
                response += f"   👥 Attendees: {', '.join(meeting['attendees'])}\n"
            
            if meeting['location']:
                response += f"   📍 Location: {meeting['location']}\n"
            
            if meeting['description']:
                response += f"   📝 Description: {meeting['description'][:100]}{'...' if len(meeting['description']) > 100 else ''}\n"
            
            response += "\n"
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting upcoming meetings: {e}")
        return f"Error getting upcoming meetings: {str(e)}"


@tool
async def find_meeting_time(
    duration_minutes: int,
    attendees: str,
    preferred_dates: str
) -> str:
    """
    Find the best available time for a meeting with multiple attendees.

    Args:
        duration_minutes: Meeting duration in minutes
        attendees: Comma-separated list of attendee email addresses
        preferred_dates: Comma-separated list of preferred dates in YYYY-MM-DD format

    Analyzes calendar availability and suggests optimal meeting times
    based on when all attendees are free.
    """
    try:
        calendar_client = await get_calendar_client()

        # Parse comma-separated inputs
        attendees_list = [email.strip() for email in attendees.split(",") if email.strip()]
        dates_list = [date.strip() for date in preferred_dates.split(",") if date.strip()]

        suggestions = []

        for date in dates_list:
            # Check availability for business hours on each preferred date
            start_datetime = datetime.fromisoformat(f"{date}T09:00:00")
            end_datetime = datetime.fromisoformat(f"{date}T17:00:00")
            
            availability = await calendar_client.get_availability(start_datetime, end_datetime)
            
            # Find slots that can accommodate the meeting duration
            for slot in availability:
                if slot['duration_minutes'] >= duration_minutes:
                    start_time = datetime.fromisoformat(slot['start']).strftime("%Y-%m-%d %H:%M")
                    suggestions.append({
                        'date': date,
                        'start_time': start_time,
                        'duration': duration_minutes,
                        'available_duration': slot['duration_minutes']
                    })
        
        if not suggestions:
            return f"No available time slots found for a {duration_minutes}-minute meeting on the preferred dates: {', '.join(dates_list)}"
        
        response = f"🎯 Suggested meeting times for {duration_minutes}-minute meeting:\n\n"
        
        for i, suggestion in enumerate(suggestions[:5], 1):  # Show top 5 suggestions
            response += f"{i}. {suggestion['start_time']} on {suggestion['date']}\n"
            response += f"   ⏱️ Available window: {suggestion['available_duration']} minutes\n\n"
        
        response += "💡 Use the schedule_meeting tool to book one of these times."
        
        return response
        
    except Exception as e:
        logger.error(f"Error finding meeting time: {e}")
        return f"Error finding meeting time: {str(e)}"


@tool
async def cancel_meeting(event_id: str, reason: str = "") -> str:
    """
    Cancel a scheduled meeting.
    
    Removes the meeting from the calendar and notifies attendees.
    Requires the event ID from the original meeting scheduling.
    """
    try:
        calendar_client = await get_calendar_client()
        
        success = await calendar_client.cancel_meeting(event_id)
        
        if success:
            response = f"✅ Meeting cancelled successfully.\n"
            response += f"📅 Event ID: {event_id}\n"
            
            if reason:
                response += f"📝 Reason: {reason}\n"
            
            response += "\n📧 Cancellation notifications have been sent to all attendees."
            return response
        else:
            return f"❌ Failed to cancel meeting with ID: {event_id}"
        
    except Exception as e:
        logger.error(f"Error cancelling meeting: {e}")
        return f"Error cancelling meeting: {str(e)}"


# Export all calendar tools
CALENDAR_TOOLS = [
    check_calendar_availability,
    schedule_meeting,
    get_upcoming_meetings,
    find_meeting_time,
    cancel_meeting
]

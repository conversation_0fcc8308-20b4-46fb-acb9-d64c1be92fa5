"""
Enhanced agent state definitions with Gmail integration.

This module defines the Pydantic models that represent the agent's state
throughout the LangGraph workflow execution, including Gmail-specific functionality.
"""

from typing import Optional, Dict, Any, List, Literal, Annotated, Sequence
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages


class TaskType(str, Enum):
    """Supported task types for the agent."""
    
    GENERAL = "general"
    EMAIL_DRAFT = "email_draft"
    EMAIL_REPLY = "email_reply"
    EMAIL_ANALYSIS = "email_analysis"
    CONVERSATION = "conversation"


class TaskStatus(str, Enum):
    """Task execution status."""
    
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Priority(str, Enum):
    """Task priority levels."""
    
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class EmailContent(BaseModel):
    """Email content and metadata."""
    
    id: Optional[str] = None
    thread_id: Optional[str] = None
    from_address: str
    to_address: str
    subject: str
    body: str
    received_at: Optional[str] = None
    headers: Dict[str, str] = {}
    snippet: Optional[str] = None
    is_html: bool = False


class EmailDraft(BaseModel):
    """Email draft to be saved to Gmail."""
    
    to: str
    subject: str
    body: str
    reply_to: Optional[str] = None
    thread_id: Optional[str] = None
    draft_id: Optional[str] = None  # Gmail draft ID after saving


class TriageResult(BaseModel):
    """Email triage and classification result."""
    
    classification: Literal[
        "customer_inquiry", 
        "customer_reply", 
        "business_opportunity",
        "spam_or_marketing", 
        "internal_email",
        "automated_notification",
        "old_email",
        "reply_not_needed"
    ]
    is_reply_warranted: bool
    confidence: float
    reasoning: str
    urgency_level: Priority = Priority.MEDIUM
    suggested_actions: List[str] = []


class PersonaConfig(BaseModel):
    """BDR persona configuration for email responses."""
    
    name: str = "TKC Group BDR"
    role: str = "Business Development Representative"
    company: str = "TKC Group"
    tone: str = "professional and helpful"
    signature: str = ""
    expertise_areas: List[str] = ["AI automation", "business process optimization"]


class EmailReply(BaseModel):
    """Generated email reply content."""
    
    subject: str
    body: str
    reply_type: Literal["direct_answer", "information_request", "meeting_scheduling", "follow_up"]
    confidence: float
    reasoning: str
    context_used: List[str] = []  # Sources of context used in reply


class GmailOperationResult(BaseModel):
    """Result of Gmail API operations."""
    
    success: bool
    operation: str  # "save_draft", "send_email", "get_messages", etc.
    gmail_id: Optional[str] = None  # Gmail message/draft ID
    error: Optional[str] = None
    metadata: Dict[str, Any] = {}


class AgentState(BaseModel):
    """
    Enhanced agent state with Gmail integration capabilities.
    
    This state is passed between all nodes in the LangGraph workflow.
    """
    
    # LangGraph message handling
    messages: Annotated[Sequence[BaseMessage], add_messages] = []
    
    # Core task information
    task_id: str
    task_type: TaskType
    input_data: Dict[str, Any] = {}
    
    # Processing state
    status: TaskStatus = TaskStatus.PENDING
    current_step: Optional[str] = None
    steps_completed: List[str] = []
    
    # Gmail-specific state
    email_content: Optional[EmailContent] = None
    email_draft: Optional[EmailDraft] = None
    triage_result: Optional[TriageResult] = None
    email_reply: Optional[EmailReply] = None
    gmail_operations: List[GmailOperationResult] = []
    
    # Context and persona
    persona_config: PersonaConfig = Field(default_factory=PersonaConfig)
    conversation_context: List[Dict[str, Any]] = []
    
    # Workflow control
    should_continue: bool = True
    next_action: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    # Context and metadata
    context: Dict[str, Any] = {}
    
    # Metadata and tracking
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    processing_time_ms: int = 0
    requested_by: Optional[str] = None
    
    # Error handling
    errors: List[str] = []
    warnings: List[str] = []
    
    # Results
    final_result: Optional[Dict[str, Any]] = None
    final_status: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        arbitrary_types_allowed = True  # For BaseMessage types
    
    def add_error(self, error: str):
        """Add an error to the state."""
        self.errors.append(error)
        self.updated_at = datetime.utcnow()
    
    def add_warning(self, warning: str):
        """Add a warning to the state."""
        self.warnings.append(warning)
        self.updated_at = datetime.utcnow()
    
    def mark_step_completed(self, step: str):
        """Mark a step as completed."""
        if step not in self.steps_completed:
            self.steps_completed.append(step)
        self.current_step = step
        self.updated_at = datetime.utcnow()
    
    def update_status(self, status: TaskStatus):
        """Update the task status."""
        self.status = status
        self.updated_at = datetime.utcnow()
    
    def add_gmail_operation(self, operation_result: GmailOperationResult):
        """Add a Gmail operation result."""
        self.gmail_operations.append(operation_result)
        self.updated_at = datetime.utcnow()
    
    def set_context(self, key: str, value: Any):
        """Set a context value."""
        self.context[key] = value
        self.updated_at = datetime.utcnow()
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get a context value."""
        return self.context.get(key, default)


class TaskRequest(BaseModel):
    """Incoming task request."""
    
    task_id: str
    task_type: TaskType
    input_data: Dict[str, Any]
    priority: Priority = Priority.MEDIUM
    requested_by: Optional[str] = None
    timeout_seconds: Optional[int] = 300


class TaskResponse(BaseModel):
    """Task response."""
    
    task_id: str
    success: bool
    task_type: TaskType
    results: Dict[str, Any]
    processing_time_ms: int
    error: Optional[str] = None
    gmail_operations: List[GmailOperationResult] = []


# Utility functions

def create_initial_state(task_request: TaskRequest) -> AgentState:
    """Create initial agent state from task request."""
    return AgentState(
        task_id=task_request.task_id,
        task_type=task_request.task_type,
        input_data=task_request.input_data,
        requested_by=task_request.requested_by,
        status=TaskStatus.PENDING
    )


def state_to_response(state: AgentState) -> TaskResponse:
    """Convert agent state to task response."""
    results = {
        "final_result": state.final_result,
        "context": state.context,
        "steps_completed": state.steps_completed
    }
    
    # Add Gmail-specific results
    if state.email_draft:
        results["email_draft"] = state.email_draft.dict()
    if state.triage_result:
        results["triage_result"] = state.triage_result.dict()
    if state.email_reply:
        results["email_reply"] = state.email_reply.dict()
    
    return TaskResponse(
        task_id=state.task_id,
        success=(state.status == TaskStatus.COMPLETED and len(state.errors) == 0),
        task_type=state.task_type,
        results=results,
        processing_time_ms=state.processing_time_ms,
        error="; ".join(state.errors) if state.errors else None,
        gmail_operations=state.gmail_operations
    )

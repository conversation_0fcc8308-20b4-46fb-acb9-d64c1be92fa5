"""
Hybrid Response Generation System for TKC_v5 Executive Agent

Combines AI-powered generation with template-based consistency for optimal email responses.
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from langchain_google_vertexai import ChatVertexAI
from agent.state import <PERSON><PERSON><PERSON><PERSON><PERSON>, TriageResult, EmailReply
from agent.email_classifier import EmailCategory

logger = logging.getLogger(__name__)


class ResponseType(str, Enum):
    """Types of email responses."""
    
    SALES_RESPONSE = "sales_response"
    SUPPORT_RESPONSE = "support_response"
    PARTNERSHIP_RESPONSE = "partnership_response"
    TECHNICAL_RESPONSE = "technical_response"
    FOLLOW_UP = "follow_up"
    ACKNOWLEDGMENT = "acknowledgment"
    REFERRAL = "referral"
    DECLINE = "decline"


@dataclass
class ResponseTemplate:
    """Template structure for consistent responses."""
    
    response_type: ResponseType
    category: EmailCategory
    subject_template: str
    opening_template: str
    body_guidelines: List[str]
    closing_template: str
    required_elements: List[str]
    tone: str
    max_length: int


class ResponseGenerator:
    """
    Hybrid response generator combining templates with AI customization.
    """
    
    def __init__(self, llm: ChatVertexAI):
        self.llm = llm
        self.templates = self._initialize_response_templates()
        self.brand_guidelines = self._initialize_brand_guidelines()
    
    def _initialize_response_templates(self) -> Dict[EmailCategory, ResponseTemplate]:
        """Initialize response templates for different email categories."""
        return {
            EmailCategory.SALES_INQUIRY: ResponseTemplate(
                response_type=ResponseType.SALES_RESPONSE,
                category=EmailCategory.SALES_INQUIRY,
                subject_template="Re: {original_subject} - TKC Group AI Automation Solutions",
                opening_template="Thank you for your interest in TKC Group's AI automation services.",
                body_guidelines=[
                    "Acknowledge their specific inquiry",
                    "Highlight relevant AI automation benefits",
                    "Provide concrete next steps",
                    "Include case study or success story if relevant",
                    "Offer a discovery call or demo"
                ],
                closing_template="I'd be happy to schedule a brief call to discuss how our AI solutions can benefit {company_name}.",
                required_elements=["next_steps", "contact_information", "value_proposition"],
                tone="professional and enthusiastic",
                max_length=300
            ),
            
            EmailCategory.SUPPORT_REQUEST: ResponseTemplate(
                response_type=ResponseType.SUPPORT_RESPONSE,
                category=EmailCategory.SUPPORT_REQUEST,
                subject_template="Re: {original_subject} - TKC Support Response",
                opening_template="Thank you for contacting TKC Group support.",
                body_guidelines=[
                    "Acknowledge the specific issue",
                    "Provide immediate helpful information",
                    "Outline troubleshooting steps if applicable",
                    "Set expectations for resolution timeline",
                    "Offer additional support channels"
                ],
                closing_template="Please don't hesitate to reach out if you need any additional assistance.",
                required_elements=["issue_acknowledgment", "solution_steps", "timeline"],
                tone="helpful and empathetic",
                max_length=250
            ),
            
            EmailCategory.PARTNERSHIP_OPPORTUNITY: ResponseTemplate(
                response_type=ResponseType.PARTNERSHIP_RESPONSE,
                category=EmailCategory.PARTNERSHIP_OPPORTUNITY,
                subject_template="Re: {original_subject} - Partnership Discussion",
                opening_template="Thank you for reaching out regarding a potential partnership with TKC Group.",
                body_guidelines=[
                    "Express genuine interest in the opportunity",
                    "Highlight TKC's relevant capabilities",
                    "Ask clarifying questions about their needs",
                    "Suggest a formal discussion or meeting",
                    "Mention relevant experience or case studies"
                ],
                closing_template="I'd welcome the opportunity to discuss this partnership in more detail.",
                required_elements=["interest_confirmation", "capability_overview", "next_steps"],
                tone="professional and collaborative",
                max_length=350
            ),
            
            EmailCategory.TECHNICAL_QUESTION: ResponseTemplate(
                response_type=ResponseType.TECHNICAL_RESPONSE,
                category=EmailCategory.TECHNICAL_QUESTION,
                subject_template="Re: {original_subject} - Technical Information",
                opening_template="Thank you for your technical inquiry about TKC Group's AI solutions.",
                body_guidelines=[
                    "Address the specific technical question",
                    "Provide relevant documentation links",
                    "Offer technical consultation if needed",
                    "Mention integration support available",
                    "Include relevant technical specifications"
                ],
                closing_template="Our technical team is available for a deeper technical discussion if needed.",
                required_elements=["technical_answer", "documentation_links", "support_offer"],
                tone="knowledgeable and precise",
                max_length=400
            )
        }
    
    def _initialize_brand_guidelines(self) -> Dict[str, Any]:
        """Initialize brand voice and messaging guidelines."""
        return {
            "company_name": "TKC Group",
            "brand_voice": {
                "professional": True,
                "approachable": True,
                "innovative": True,
                "trustworthy": True
            },
            "key_messages": [
                "AI automation that drives real business results",
                "Proven expertise in enterprise AI implementation",
                "Personalized solutions for unique business needs",
                "Comprehensive support throughout the AI journey"
            ],
            "signature": """
Best regards,
Tyler Klug
Business Development Representative
TKC Group - AI Automation Specialists

📧 <EMAIL>
🌐 www.tkcgroup.co
📱 Schedule a call: [calendly_link]
            """.strip(),
            "legal_disclaimers": {
                "confidentiality": "This email and any attachments are confidential and may be privileged.",
                "ai_disclosure": "This response was generated with AI assistance and reviewed for accuracy."
            }
        }
    
    async def generate_response(
        self, 
        original_email: EmailContent, 
        classification: TriageResult,
        context: Optional[Dict[str, Any]] = None
    ) -> EmailReply:
        """
        Generate contextually appropriate email response.
        
        Args:
            original_email: The email being responded to
            classification: Email classification result
            context: Additional context for response generation
            
        Returns:
            EmailReply with generated response
        """
        logger.info(f"Generating response for {classification.classification}")
        
        # Get appropriate template
        template = self._get_template(classification.classification)
        
        if template:
            # Use template-guided generation
            response = await self._generate_templated_response(
                original_email, classification, template, context
            )
        else:
            # Use pure AI generation for edge cases
            response = await self._generate_ai_response(
                original_email, classification, context
            )
        
        # Apply brand guidelines and quality checks
        response = self._apply_brand_guidelines(response)
        response = await self._quality_check(response, original_email)
        
        return response
    
    def _get_template(self, classification: str) -> Optional[ResponseTemplate]:
        """Get appropriate template for email classification."""
        try:
            category = EmailCategory(classification)
            return self.templates.get(category)
        except ValueError:
            logger.warning(f"No template found for classification: {classification}")
            return None
    
    async def _generate_templated_response(
        self,
        original_email: EmailContent,
        classification: TriageResult,
        template: ResponseTemplate,
        context: Optional[Dict[str, Any]] = None
    ) -> EmailReply:
        """Generate response using template guidance."""
        
        # Extract context information
        sender_name = self._extract_sender_name(original_email.from_address)
        company_name = context.get("company_name", "your organization") if context else "your organization"
        
        # Create AI prompt with template guidance
        prompt = f"""
        Generate a professional email response following these guidelines:
        
        Original Email:
        From: {original_email.from_address}
        Subject: {original_email.subject}
        Body: {original_email.body}
        
        Response Template Guidelines:
        - Type: {template.response_type}
        - Tone: {template.tone}
        - Max Length: {template.max_length} words
        - Opening: {template.opening_template}
        - Closing: {template.closing_template}
        
        Required Elements:
        {chr(10).join(f"- {element}" for element in template.required_elements)}
        
        Body Guidelines:
        {chr(10).join(f"- {guideline}" for guideline in template.body_guidelines)}
        
        Brand Messages to Include:
        {chr(10).join(f"- {message}" for message in self.brand_guidelines["key_messages"])}
        
        Context:
        - Sender Name: {sender_name}
        - Company: {company_name}
        - Classification: {classification.classification}
        - Confidence: {classification.confidence}
        
        Generate:
        SUBJECT: [response subject]
        BODY: [response body including signature]
        TONE_MATCH: [how well it matches required tone 1-10]
        KEY_POINTS: [main points covered]
        
        Response:
        """
        
        try:
            response = await self.llm.ainvoke(prompt)
            return self._parse_response(response.content, original_email, template)
            
        except Exception as e:
            logger.error(f"Template-guided generation failed: {e}")
            return self._create_fallback_response(original_email, template)
    
    async def _generate_ai_response(
        self,
        original_email: EmailContent,
        classification: TriageResult,
        context: Optional[Dict[str, Any]] = None
    ) -> EmailReply:
        """Generate response using pure AI without template constraints."""
        
        prompt = f"""
        Generate a professional email response for TKC Group (AI automation company).
        
        Original Email:
        From: {original_email.from_address}
        Subject: {original_email.subject}
        Body: {original_email.body}
        
        Classification: {classification.classification}
        Confidence: {classification.confidence}
        Reasoning: {classification.reasoning}
        
        Brand Guidelines:
        - Company: TKC Group - AI Automation Specialists
        - Voice: Professional, approachable, innovative, trustworthy
        - Focus: AI automation that drives real business results
        
        Generate an appropriate response that:
        1. Addresses their specific needs or questions
        2. Maintains professional tone
        3. Provides value and next steps
        4. Includes proper signature
        5. Stays under 300 words
        
        Format:
        SUBJECT: [response subject]
        BODY: [response body with signature]
        
        Response:
        """
        
        try:
            response = await self.llm.ainvoke(prompt)
            return self._parse_ai_response(response.content, original_email)
            
        except Exception as e:
            logger.error(f"AI response generation failed: {e}")
            return self._create_emergency_fallback(original_email)
    
    def _parse_response(self, response_text: str, original_email: EmailContent, template: ResponseTemplate) -> EmailReply:
        """Parse structured template response."""
        
        lines = response_text.strip().split('\n')
        subject = f"Re: {original_email.subject}"
        body = "Thank you for your email. We'll get back to you soon."
        
        for i, line in enumerate(lines):
            if line.startswith("SUBJECT:"):
                subject = line.split(":", 1)[1].strip()
            elif line.startswith("BODY:"):
                # Get all lines after BODY: until next section
                body_lines = []
                for j in range(i + 1, len(lines)):
                    if lines[j].startswith(("TONE_MATCH:", "KEY_POINTS:")):
                        break
                    body_lines.append(lines[j])
                body = '\n'.join(body_lines).strip()
                break
        
        return EmailReply(
            to=original_email.from_address,
            subject=subject,
            body=body,
            reply_to=original_email.from_address,
            thread_id=getattr(original_email, 'thread_id', None),
            response_type=template.response_type.value,
            confidence=0.8
        )
    
    def _parse_ai_response(self, response_text: str, original_email: EmailContent) -> EmailReply:
        """Parse AI-generated response."""
        
        lines = response_text.strip().split('\n')
        subject = f"Re: {original_email.subject}"
        body = "Thank you for your email."
        
        for i, line in enumerate(lines):
            if line.startswith("SUBJECT:"):
                subject = line.split(":", 1)[1].strip()
            elif line.startswith("BODY:"):
                body_lines = []
                for j in range(i + 1, len(lines)):
                    body_lines.append(lines[j])
                body = '\n'.join(body_lines).strip()
                break
        
        return EmailReply(
            to=original_email.from_address,
            subject=subject,
            body=body,
            reply_to=original_email.from_address,
            thread_id=getattr(original_email, 'thread_id', None),
            response_type=ResponseType.ACKNOWLEDGMENT.value,
            confidence=0.7
        )
    
    def _apply_brand_guidelines(self, response: EmailReply) -> EmailReply:
        """Apply brand guidelines to response."""
        
        # Ensure signature is included
        if self.brand_guidelines["signature"] not in response.body:
            response.body += f"\n\n{self.brand_guidelines['signature']}"
        
        # Add AI disclosure if required
        if "ai_disclosure" in self.brand_guidelines.get("legal_disclaimers", {}):
            disclosure = self.brand_guidelines["legal_disclaimers"]["ai_disclosure"]
            response.body += f"\n\n---\n{disclosure}"
        
        return response
    
    async def _quality_check(self, response: EmailReply, original_email: EmailContent) -> EmailReply:
        """Perform quality checks on generated response."""
        
        # Check for common issues
        issues = []
        
        # Length check
        if len(response.body.split()) > 500:
            issues.append("Response too long")
        
        # Tone check (simplified)
        if "sorry" in response.body.lower() and "support" not in original_email.body.lower():
            issues.append("Unnecessary apology")
        
        # If issues found, regenerate with constraints
        if issues:
            logger.warning(f"Quality issues found: {issues}")
            # Could implement regeneration logic here
        
        return response
    
    def _extract_sender_name(self, email_address: str) -> str:
        """Extract sender name from email address."""
        if "<" in email_address:
            return email_address.split("<")[0].strip().strip('"')
        return email_address.split("@")[0].replace(".", " ").title()
    
    def _create_fallback_response(self, original_email: EmailContent, template: ResponseTemplate) -> EmailReply:
        """Create safe fallback response."""
        return EmailReply(
            to=original_email.from_address,
            subject=f"Re: {original_email.subject}",
            body=f"{template.opening_template}\n\nThank you for your email. We'll review your message and get back to you within 24 hours.\n\n{self.brand_guidelines['signature']}",
            reply_to=original_email.from_address,
            response_type=template.response_type.value,
            confidence=0.5
        )
    
    def _create_emergency_fallback(self, original_email: EmailContent) -> EmailReply:
        """Create emergency fallback when all else fails."""
        return EmailReply(
            to=original_email.from_address,
            subject=f"Re: {original_email.subject}",
            body=f"Thank you for contacting TKC Group. We've received your message and will respond within 24 hours.\n\n{self.brand_guidelines['signature']}",
            reply_to=original_email.from_address,
            response_type=ResponseType.ACKNOWLEDGMENT.value,
            confidence=0.3
        )

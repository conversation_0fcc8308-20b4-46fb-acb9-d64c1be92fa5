"""
Conversation Memory System for TKC_v5 Executive Agent

Maintains context across email threads and customer interactions for intelligent responses.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from google.cloud import firestore

from agent.state import <PERSON>ail<PERSON><PERSON>nt, TriageResult, EmailReply

logger = logging.getLogger(__name__)


@dataclass
class ConversationContext:
    """Context information for ongoing conversations."""
    
    customer_email: str
    thread_id: Optional[str]
    conversation_history: List[Dict[str, Any]]
    customer_profile: Dict[str, Any]
    interaction_summary: str
    last_interaction: datetime
    status: str  # active, waiting_response, closed
    priority: str
    assigned_rep: Optional[str] = None


@dataclass
class CustomerProfile:
    """Customer profile with interaction history."""
    
    email: str
    name: Optional[str]
    company: Optional[str]
    industry: Optional[str]
    interaction_count: int
    first_contact: datetime
    last_contact: datetime
    customer_stage: str  # prospect, lead, customer, churned
    preferences: Dict[str, Any]
    notes: List[str]


class ConversationMemory:
    """
    Manages conversation context and customer memory across email interactions.
    """
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.db = firestore.Client(project=project_id)
        self.conversations_collection = "email_conversations"
        self.customers_collection = "customer_profiles"
    
    async def get_conversation_context(
        self, 
        customer_email: str, 
        thread_id: Optional[str] = None
    ) -> Optional[ConversationContext]:
        """
        Retrieve conversation context for a customer.
        
        Args:
            customer_email: Customer's email address
            thread_id: Gmail thread ID if available
            
        Returns:
            ConversationContext if found, None otherwise
        """
        try:
            # Query by email and thread_id
            query = self.db.collection(self.conversations_collection)\
                          .where("customer_email", "==", customer_email)
            
            if thread_id:
                query = query.where("thread_id", "==", thread_id)
            
            # Get most recent conversation
            docs = query.order_by("last_interaction", direction=firestore.Query.DESCENDING)\
                       .limit(1)\
                       .stream()
            
            for doc in docs:
                data = doc.to_dict()
                return ConversationContext(
                    customer_email=data["customer_email"],
                    thread_id=data.get("thread_id"),
                    conversation_history=data.get("conversation_history", []),
                    customer_profile=data.get("customer_profile", {}),
                    interaction_summary=data.get("interaction_summary", ""),
                    last_interaction=data["last_interaction"],
                    status=data.get("status", "active"),
                    priority=data.get("priority", "medium"),
                    assigned_rep=data.get("assigned_rep")
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving conversation context: {e}")
            return None
    
    async def update_conversation_context(
        self,
        customer_email: str,
        new_email: EmailContent,
        response: Optional[EmailReply] = None,
        classification: Optional[TriageResult] = None
    ) -> ConversationContext:
        """
        Update conversation context with new interaction.
        
        Args:
            customer_email: Customer's email address
            new_email: New email received
            response: Response sent (if any)
            classification: Email classification result
            
        Returns:
            Updated ConversationContext
        """
        try:
            # Get existing context or create new
            context = await self.get_conversation_context(
                customer_email, 
                getattr(new_email, 'thread_id', None)
            )
            
            if not context:
                context = ConversationContext(
                    customer_email=customer_email,
                    thread_id=getattr(new_email, 'thread_id', None),
                    conversation_history=[],
                    customer_profile=await self._get_customer_profile(customer_email),
                    interaction_summary="",
                    last_interaction=datetime.now(),
                    status="active",
                    priority="medium"
                )
            
            # Add new interaction to history
            interaction = {
                "timestamp": datetime.now().isoformat(),
                "type": "received_email",
                "email": {
                    "from": new_email.from_address,
                    "subject": new_email.subject,
                    "body": new_email.body[:500] + "..." if len(new_email.body) > 500 else new_email.body
                },
                "classification": asdict(classification) if classification else None
            }
            
            if response:
                interaction["response"] = {
                    "subject": response.subject,
                    "body": response.body[:500] + "..." if len(response.body) > 500 else response.body,
                    "response_type": response.response_type
                }
            
            context.conversation_history.append(interaction)
            context.last_interaction = datetime.now()
            
            # Update interaction summary
            context.interaction_summary = await self._generate_interaction_summary(context)
            
            # Update priority based on classification
            if classification and classification.urgency_level:
                context.priority = classification.urgency_level.value
            
            # Save to Firestore
            await self._save_conversation_context(context)
            
            # Update customer profile
            await self._update_customer_profile(customer_email, new_email, classification)
            
            return context
            
        except Exception as e:
            logger.error(f"Error updating conversation context: {e}")
            # Return minimal context to prevent failures
            return ConversationContext(
                customer_email=customer_email,
                thread_id=getattr(new_email, 'thread_id', None),
                conversation_history=[],
                customer_profile={},
                interaction_summary="Error retrieving context",
                last_interaction=datetime.now(),
                status="active",
                priority="medium"
            )
    
    async def _get_customer_profile(self, customer_email: str) -> Dict[str, Any]:
        """Get customer profile from database."""
        try:
            doc_ref = self.db.collection(self.customers_collection).document(customer_email)
            doc = doc_ref.get()
            
            if doc.exists:
                return doc.to_dict()
            else:
                # Create new customer profile
                profile = {
                    "email": customer_email,
                    "name": None,
                    "company": None,
                    "industry": None,
                    "interaction_count": 0,
                    "first_contact": datetime.now(),
                    "last_contact": datetime.now(),
                    "customer_stage": "prospect",
                    "preferences": {},
                    "notes": []
                }
                doc_ref.set(profile)
                return profile
                
        except Exception as e:
            logger.error(f"Error getting customer profile: {e}")
            return {}
    
    async def _update_customer_profile(
        self, 
        customer_email: str, 
        email: EmailContent, 
        classification: Optional[TriageResult]
    ):
        """Update customer profile with new interaction."""
        try:
            doc_ref = self.db.collection(self.customers_collection).document(customer_email)
            
            # Extract potential company name from email signature
            company = self._extract_company_from_email(email)
            
            updates = {
                "last_contact": datetime.now(),
                "interaction_count": firestore.Increment(1)
            }
            
            if company:
                updates["company"] = company
            
            # Update customer stage based on classification
            if classification:
                if classification.classification == "sales_inquiry":
                    updates["customer_stage"] = "lead"
                elif classification.classification == "customer_reply":
                    updates["customer_stage"] = "customer"
            
            doc_ref.update(updates)
            
        except Exception as e:
            logger.error(f"Error updating customer profile: {e}")
    
    async def _generate_interaction_summary(self, context: ConversationContext) -> str:
        """Generate summary of conversation interactions."""
        try:
            if not context.conversation_history:
                return "No previous interactions"
            
            recent_interactions = context.conversation_history[-3:]  # Last 3 interactions
            
            summary_parts = []
            for interaction in recent_interactions:
                timestamp = interaction["timestamp"]
                email_subject = interaction.get("email", {}).get("subject", "No subject")
                classification = interaction.get("classification", {})
                
                if classification:
                    category = classification.get("classification", "unknown")
                    summary_parts.append(f"{timestamp}: {category} - {email_subject}")
                else:
                    summary_parts.append(f"{timestamp}: {email_subject}")
            
            return " | ".join(summary_parts)
            
        except Exception as e:
            logger.error(f"Error generating interaction summary: {e}")
            return "Error generating summary"
    
    async def _save_conversation_context(self, context: ConversationContext):
        """Save conversation context to Firestore."""
        try:
            # Create document ID from email and thread
            doc_id = f"{context.customer_email}_{context.thread_id or 'no_thread'}"
            
            doc_ref = self.db.collection(self.conversations_collection).document(doc_id)
            
            # Convert to dict for Firestore
            data = {
                "customer_email": context.customer_email,
                "thread_id": context.thread_id,
                "conversation_history": context.conversation_history,
                "customer_profile": context.customer_profile,
                "interaction_summary": context.interaction_summary,
                "last_interaction": context.last_interaction,
                "status": context.status,
                "priority": context.priority,
                "assigned_rep": context.assigned_rep
            }
            
            doc_ref.set(data)
            
        except Exception as e:
            logger.error(f"Error saving conversation context: {e}")
    
    def _extract_company_from_email(self, email: EmailContent) -> Optional[str]:
        """Extract company name from email signature or domain."""
        try:
            # Try to extract from email domain
            domain = email.from_address.split("@")[1]
            if domain and not domain.endswith(("gmail.com", "yahoo.com", "hotmail.com", "outlook.com")):
                # Remove common TLDs and format as company name
                company = domain.split(".")[0].replace("-", " ").title()
                return company
            
            # Could add more sophisticated signature parsing here
            return None
            
        except Exception:
            return None
    
    async def get_conversation_insights(self, customer_email: str) -> Dict[str, Any]:
        """Get insights about customer conversation patterns."""
        try:
            context = await self.get_conversation_context(customer_email)
            if not context:
                return {}
            
            insights = {
                "total_interactions": len(context.conversation_history),
                "conversation_duration": (
                    context.last_interaction - 
                    datetime.fromisoformat(context.conversation_history[0]["timestamp"])
                ).days if context.conversation_history else 0,
                "response_pattern": self._analyze_response_pattern(context),
                "engagement_level": self._calculate_engagement_level(context),
                "preferred_topics": self._extract_preferred_topics(context)
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting conversation insights: {e}")
            return {}
    
    def _analyze_response_pattern(self, context: ConversationContext) -> str:
        """Analyze customer response patterns."""
        if len(context.conversation_history) < 2:
            return "insufficient_data"
        
        # Simple analysis - could be enhanced with ML
        response_times = []
        for i in range(1, len(context.conversation_history)):
            prev_time = datetime.fromisoformat(context.conversation_history[i-1]["timestamp"])
            curr_time = datetime.fromisoformat(context.conversation_history[i]["timestamp"])
            response_times.append((curr_time - prev_time).total_seconds() / 3600)  # hours
        
        avg_response_time = sum(response_times) / len(response_times)
        
        if avg_response_time < 2:
            return "highly_responsive"
        elif avg_response_time < 24:
            return "responsive"
        elif avg_response_time < 72:
            return "moderate"
        else:
            return "slow_responder"
    
    def _calculate_engagement_level(self, context: ConversationContext) -> str:
        """Calculate customer engagement level."""
        interaction_count = len(context.conversation_history)
        
        if interaction_count >= 5:
            return "high"
        elif interaction_count >= 3:
            return "medium"
        else:
            return "low"
    
    def _extract_preferred_topics(self, context: ConversationContext) -> List[str]:
        """Extract topics customer frequently discusses."""
        # Simple keyword extraction - could be enhanced with NLP
        topics = []
        
        for interaction in context.conversation_history:
            email_body = interaction.get("email", {}).get("body", "").lower()
            
            if any(word in email_body for word in ["api", "integration", "technical"]):
                topics.append("technical")
            if any(word in email_body for word in ["pricing", "cost", "budget"]):
                topics.append("pricing")
            if any(word in email_body for word in ["demo", "meeting", "call"]):
                topics.append("meetings")
            if any(word in email_body for word in ["support", "help", "issue"]):
                topics.append("support")
        
        # Return unique topics
        return list(set(topics))

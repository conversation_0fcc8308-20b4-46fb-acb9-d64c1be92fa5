"""
Enhanced Email Classification System for TKC_v5 Executive Agent

Combines rule-based filtering with AI-powered classification for robust email handling.
"""

import re
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from langchain_google_vertexai import Chat<PERSON>ertexA<PERSON>
from agent.state import EmailContent, TriageResult, Priority

logger = logging.getLogger(__name__)


class EmailCategory(str, Enum):
    """Comprehensive email categories for business scenarios."""
    
    # Customer-facing
    SALES_INQUIRY = "sales_inquiry"
    SUPPORT_REQUEST = "support_request"
    BILLING_INQUIRY = "billing_inquiry"
    TECHNICAL_QUESTION = "technical_question"
    
    # Business development
    PARTNERSHIP_OPPORTUNITY = "partnership_opportunity"
    VENDOR_INQUIRY = "vendor_inquiry"
    MEDIA_REQUEST = "media_request"
    
    # Internal
    INTERNAL_COMMUNICATION = "internal_communication"
    HR_RELATED = "hr_related"
    
    # Automated
    SYSTEM_NOTIFICATION = "system_notification"
    MARKETING_EMAIL = "marketing_email"
    SPAM = "spam"
    
    # Follow-ups
    CUSTOMER_REPLY = "customer_reply"
    FOLLOW_UP_NEEDED = "follow_up_needed"
    
    # Special handling
    URGENT_ESCALATION = "urgent_escalation"
    LEGAL_COMPLIANCE = "legal_compliance"
    UNSUBSCRIBE_REQUEST = "unsubscribe_request"
    
    # Default
    GENERAL_INQUIRY = "general_inquiry"
    NO_ACTION_NEEDED = "no_action_needed"


@dataclass
class ClassificationRule:
    """Rule-based classification criteria."""
    
    category: EmailCategory
    priority: Priority
    keywords: List[str]
    sender_patterns: List[str]
    subject_patterns: List[str]
    requires_response: bool
    max_response_time_hours: int
    escalation_required: bool = False


class EmailClassifier:
    """
    Hybrid email classifier combining rule-based and AI-powered classification.
    """
    
    def __init__(self, llm: ChatVertexAI):
        self.llm = llm
        self.rules = self._initialize_classification_rules()
        self.spam_indicators = self._initialize_spam_indicators()
        
    def _initialize_classification_rules(self) -> List[ClassificationRule]:
        """Initialize business-specific classification rules."""
        return [
            # High Priority - Sales & Business
            ClassificationRule(
                category=EmailCategory.SALES_INQUIRY,
                priority=Priority.HIGH,
                keywords=["quote", "pricing", "proposal", "demo", "trial", "purchase"],
                sender_patterns=[],
                subject_patterns=["inquiry", "interested in", "pricing"],
                requires_response=True,
                max_response_time_hours=2
            ),
            
            ClassificationRule(
                category=EmailCategory.PARTNERSHIP_OPPORTUNITY,
                priority=Priority.HIGH,
                keywords=["partnership", "collaboration", "joint venture", "integration"],
                sender_patterns=[],
                subject_patterns=["partnership", "collaboration"],
                requires_response=True,
                max_response_time_hours=4
            ),
            
            # Medium Priority - Support
            ClassificationRule(
                category=EmailCategory.SUPPORT_REQUEST,
                priority=Priority.MEDIUM,
                keywords=["help", "issue", "problem", "bug", "error", "not working"],
                sender_patterns=[],
                subject_patterns=["support", "help", "issue"],
                requires_response=True,
                max_response_time_hours=8
            ),
            
            ClassificationRule(
                category=EmailCategory.TECHNICAL_QUESTION,
                priority=Priority.MEDIUM,
                keywords=["api", "integration", "technical", "documentation", "sdk"],
                sender_patterns=[],
                subject_patterns=["technical", "api", "integration"],
                requires_response=True,
                max_response_time_hours=12
            ),
            
            # Urgent Escalation
            ClassificationRule(
                category=EmailCategory.URGENT_ESCALATION,
                priority=Priority.URGENT,
                keywords=["urgent", "asap", "emergency", "critical", "down", "outage"],
                sender_patterns=[],
                subject_patterns=["urgent", "emergency", "critical"],
                requires_response=True,
                max_response_time_hours=1,
                escalation_required=True
            ),
            
            # Low Priority - Automated
            ClassificationRule(
                category=EmailCategory.SYSTEM_NOTIFICATION,
                priority=Priority.LOW,
                keywords=["notification", "alert", "automated", "no-reply"],
                sender_patterns=["noreply", "no-reply", "automated", "system"],
                subject_patterns=["notification", "alert"],
                requires_response=False,
                max_response_time_hours=0
            ),
            
            # Compliance & Legal
            ClassificationRule(
                category=EmailCategory.LEGAL_COMPLIANCE,
                priority=Priority.HIGH,
                keywords=["gdpr", "privacy", "legal", "compliance", "subpoena", "court"],
                sender_patterns=["legal", "compliance", "privacy"],
                subject_patterns=["legal", "compliance", "gdpr"],
                requires_response=True,
                max_response_time_hours=24,
                escalation_required=True
            ),
        ]
    
    def _initialize_spam_indicators(self) -> Dict[str, List[str]]:
        """Initialize spam detection patterns."""
        return {
            "subject_spam": [
                "congratulations", "winner", "lottery", "free money", "urgent transfer",
                "nigerian prince", "inheritance", "tax refund", "click here now"
            ],
            "sender_spam": [
                "lottery", "winner", "prize", "inheritance", "tax-refund"
            ],
            "body_spam": [
                "click here to claim", "limited time offer", "act now", "100% guaranteed",
                "no obligation", "risk free", "call now", "order now"
            ]
        }
    
    async def classify_email(self, email: EmailContent) -> TriageResult:
        """
        Classify email using hybrid rule-based + AI approach.
        
        Args:
            email: Email content to classify
            
        Returns:
            TriageResult with classification and recommendations
        """
        logger.info(f"Classifying email from {email.from_address}")
        
        # Step 1: Rule-based pre-filtering
        rule_result = self._apply_classification_rules(email)
        if rule_result:
            logger.info(f"Rule-based classification: {rule_result.classification}")
            return rule_result
        
        # Step 2: Spam detection
        spam_result = self._detect_spam(email)
        if spam_result:
            logger.info("Email classified as spam")
            return spam_result
        
        # Step 3: AI-powered classification for complex cases
        ai_result = await self._ai_classify_email(email)
        logger.info(f"AI classification: {ai_result.classification}")
        
        return ai_result
    
    def _apply_classification_rules(self, email: EmailContent) -> Optional[TriageResult]:
        """Apply rule-based classification."""
        
        email_text = f"{email.subject} {email.body}".lower()
        sender_lower = email.from_address.lower()
        subject_lower = email.subject.lower()
        
        for rule in self.rules:
            # Check keywords
            keyword_match = any(keyword in email_text for keyword in rule.keywords)
            
            # Check sender patterns
            sender_match = any(pattern in sender_lower for pattern in rule.sender_patterns)
            
            # Check subject patterns
            subject_match = any(pattern in subject_lower for pattern in rule.subject_patterns)
            
            # If any criteria match, apply the rule
            if keyword_match or sender_match or subject_match:
                return TriageResult(
                    classification=rule.category.value,
                    is_reply_warranted=rule.requires_response,
                    confidence=0.9,  # High confidence for rule-based matches
                    reasoning=f"Rule-based match: {rule.category.value}",
                    urgency_level=rule.priority,
                    suggested_actions=self._get_suggested_actions(rule)
                )
        
        return None
    
    def _detect_spam(self, email: EmailContent) -> Optional[TriageResult]:
        """Detect spam using pattern matching."""
        
        spam_score = 0
        indicators = []
        
        # Check subject spam indicators
        for indicator in self.spam_indicators["subject_spam"]:
            if indicator in email.subject.lower():
                spam_score += 2
                indicators.append(f"Subject: {indicator}")
        
        # Check sender spam indicators
        for indicator in self.spam_indicators["sender_spam"]:
            if indicator in email.from_address.lower():
                spam_score += 3
                indicators.append(f"Sender: {indicator}")
        
        # Check body spam indicators
        for indicator in self.spam_indicators["body_spam"]:
            if indicator in email.body.lower():
                spam_score += 1
                indicators.append(f"Body: {indicator}")
        
        # Classify as spam if score is high enough
        if spam_score >= 3:
            return TriageResult(
                classification=EmailCategory.SPAM.value,
                is_reply_warranted=False,
                confidence=min(0.95, spam_score / 10),
                reasoning=f"Spam indicators: {', '.join(indicators)}",
                urgency_level=Priority.LOW,
                suggested_actions=["mark_as_spam", "block_sender"]
            )
        
        return None
    
    async def _ai_classify_email(self, email: EmailContent) -> TriageResult:
        """Use AI for complex email classification."""
        
        prompt = f"""
        Analyze this email and classify it for a business automation company (TKC Group).
        
        Email Details:
        From: {email.from_address}
        Subject: {email.subject}
        Body: {email.body[:1000]}...
        
        Available Categories:
        - sales_inquiry: Potential customers asking about services
        - support_request: Existing customers needing help
        - partnership_opportunity: Business collaboration proposals
        - technical_question: API, integration, or technical queries
        - billing_inquiry: Payment, invoice, or billing questions
        - customer_reply: Response to our previous outreach
        - general_inquiry: General questions or information requests
        - no_action_needed: Newsletters, notifications, or non-actionable emails
        
        Provide:
        1. Category (from list above)
        2. Confidence (0.0 to 1.0)
        3. Priority (urgent/high/medium/low)
        4. Requires response (true/false)
        5. Reasoning (brief explanation)
        6. Suggested actions (comma-separated list)
        
        Format:
        CATEGORY: [category]
        CONFIDENCE: [confidence]
        PRIORITY: [priority]
        REQUIRES_RESPONSE: [true/false]
        REASONING: [reasoning]
        ACTIONS: [action1, action2, action3]
        """
        
        try:
            response = await self.llm.ainvoke(prompt)
            return self._parse_ai_response(response.content)
            
        except Exception as e:
            logger.error(f"AI classification failed: {e}")
            # Fallback to safe default
            return TriageResult(
                classification=EmailCategory.GENERAL_INQUIRY.value,
                is_reply_warranted=True,
                confidence=0.5,
                reasoning=f"AI classification failed, using default: {str(e)}",
                urgency_level=Priority.MEDIUM,
                suggested_actions=["manual_review"]
            )
    
    def _parse_ai_response(self, response_text: str) -> TriageResult:
        """Parse structured AI response."""
        
        lines = response_text.strip().split('\n')
        
        # Default values
        category = EmailCategory.GENERAL_INQUIRY.value
        confidence = 0.5
        priority = Priority.MEDIUM
        requires_response = True
        reasoning = "AI classification"
        actions = []
        
        # Parse response
        for line in lines:
            if line.startswith("CATEGORY:"):
                category = line.split(":", 1)[1].strip()
            elif line.startswith("CONFIDENCE:"):
                try:
                    confidence = float(line.split(":", 1)[1].strip())
                except ValueError:
                    confidence = 0.5
            elif line.startswith("PRIORITY:"):
                priority_str = line.split(":", 1)[1].strip().lower()
                priority = Priority(priority_str) if priority_str in [p.value for p in Priority] else Priority.MEDIUM
            elif line.startswith("REQUIRES_RESPONSE:"):
                requires_response = line.split(":", 1)[1].strip().lower() == "true"
            elif line.startswith("REASONING:"):
                reasoning = line.split(":", 1)[1].strip()
            elif line.startswith("ACTIONS:"):
                actions = [action.strip() for action in line.split(":", 1)[1].split(",")]
        
        return TriageResult(
            classification=category,
            is_reply_warranted=requires_response,
            confidence=confidence,
            reasoning=reasoning,
            urgency_level=priority,
            suggested_actions=actions
        )
    
    def _get_suggested_actions(self, rule: ClassificationRule) -> List[str]:
        """Get suggested actions based on classification rule."""
        
        actions = []
        
        if rule.requires_response:
            actions.append("generate_response")
            
        if rule.escalation_required:
            actions.append("escalate_to_human")
            
        if rule.priority == Priority.URGENT:
            actions.append("immediate_notification")
            
        if rule.category == EmailCategory.SALES_INQUIRY:
            actions.extend(["add_to_crm", "schedule_follow_up"])
            
        if rule.category == EmailCategory.SUPPORT_REQUEST:
            actions.extend(["create_ticket", "check_knowledge_base"])
            
        return actions

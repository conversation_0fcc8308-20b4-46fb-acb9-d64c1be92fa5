"""
Enhanced Email Processing Tools with Deduplication for TKC_v5

These tools prevent duplicate draft creation and implement smart email filtering
to solve the webhook loop issue.
"""

import logging
from typing import List, Dict, Any, Set, Optional
from datetime import datetime
from langchain_core.tools import tool

from services.gmail_client import create_gmail_client

logger = logging.getLogger(__name__)


@tool
async def process_new_emails_with_deduplication(
    processed_emails: List[str] = None,
    draft_created_for: List[str] = None,
    max_emails: int = 5
) -> str:
    """
    Process new emails with enhanced deduplication to prevent duplicate drafts.
    
    This tool checks for new unread emails and only creates drafts for emails that:
    1. Don't already have drafts created
    2. Are genuine business inquiries (not spam/newsletters)
    3. Haven't been processed before
    
    Args:
        processed_emails: List of email IDs already processed
        draft_created_for: List of email IDs that already have drafts
        max_emails: Maximum number of emails to process
        
    Returns:
        Summary of processing results
    """
    try:
        # Initialize tracking sets
        processed_set = set(processed_emails or [])
        draft_set = set(draft_created_for or [])
        
        gmail_client = await create_gmail_client()
        
        # Get unread emails from inbox (excluding drafts)
        messages = await gmail_client.get_messages(
            query="is:unread in:inbox -in:drafts",
            max_results=max_emails
        )
        
        if not messages:
            return "No new unread emails found."
        
        results = []
        drafts_created = 0
        emails_skipped = 0
        
        for msg in messages:
            email_id = msg.get('id')
            sender = msg.get('from', '')
            subject = msg.get('subject', '')
            snippet = msg.get('snippet', '')
            
            # Check if already processed
            if email_id in processed_set:
                logger.info(f"Skipping already processed email: {email_id}")
                emails_skipped += 1
                continue
            
            # Check if draft already created
            if email_id in draft_set:
                logger.info(f"Skipping email with existing draft: {email_id}")
                emails_skipped += 1
                continue
            
            # Apply business email filters
            if not _should_create_draft_for_email(sender, subject, snippet):
                logger.info(f"Skipping filtered email: {subject}")
                emails_skipped += 1
                continue
            
            # Create draft for this email
            draft_result = await _create_business_draft(msg, gmail_client)
            
            if draft_result:
                drafts_created += 1
                results.append(f"✅ Created draft for: {subject[:50]}...")
                # Mark as processed with draft created
                processed_set.add(email_id)
                draft_set.add(email_id)
            else:
                results.append(f"❌ Failed to create draft for: {subject[:50]}...")
                # Mark as processed even if draft failed
                processed_set.add(email_id)
        
        # Summary
        summary = f"Email processing complete:\n"
        summary += f"- {len(messages)} emails checked\n"
        summary += f"- {drafts_created} drafts created\n"
        summary += f"- {emails_skipped} emails skipped (already processed/filtered)\n\n"
        
        if results:
            summary += "Details:\n" + "\n".join(results)
        
        return summary
        
    except Exception as e:
        logger.error(f"Error processing emails with deduplication: {e}")
        return f"Error processing emails: {str(e)}"


def _should_create_draft_for_email(sender: str, subject: str, snippet: str) -> bool:
    """
    Determine if an email should have a draft created based on content analysis.
    
    Args:
        sender: Email sender address
        subject: Email subject line
        snippet: Email snippet/preview
        
    Returns:
        True if draft should be created, False otherwise
    """
    sender_lower = sender.lower()
    subject_lower = subject.lower()
    snippet_lower = snippet.lower()
    
    # Skip no-reply addresses
    if 'noreply' in sender_lower or 'no-reply' in sender_lower:
        return False
    
    # Skip automated senders
    automated_senders = [
        'notification', 'alert', 'system', 'admin', 'support',
        'newsletter', 'marketing', 'sales', 'promo'
    ]
    if any(term in sender_lower for term in automated_senders):
        return False
    
    # Skip promotional/newsletter subjects
    promotional_keywords = [
        'newsletter', 'unsubscribe', 'promotion', 'sale', 'discount',
        'marketing', 'advertisement', 'winner', 'congratulations',
        'limited time', 'act now', 'free trial', 'special offer',
        'deal', 'save money', 'exclusive', 'urgent offer'
    ]
    if any(keyword in subject_lower for keyword in promotional_keywords):
        return False
    
    # Skip automated notifications
    notification_keywords = [
        'notification', 'alert', 'reminder', 'confirmation',
        'receipt', 'invoice', 'shipping', 'delivery', 'welcome',
        'password reset', 'account verification', 'security alert'
    ]
    if any(keyword in subject_lower for keyword in notification_keywords):
        return False
    
    # Skip if snippet indicates automated content
    automated_snippet_indicators = [
        'this is an automated', 'do not reply', 'unsubscribe',
        'you are receiving this', 'newsletter', 'promotional'
    ]
    if any(indicator in snippet_lower for indicator in automated_snippet_indicators):
        return False
    
    # Look for business inquiry indicators
    business_indicators = [
        'inquiry', 'question', 'partnership', 'collaboration',
        'project', 'proposal', 'meeting', 'discuss', 'interested',
        'services', 'help', 'support', 'consultation', 'quote'
    ]
    
    # Check if it's a genuine business inquiry
    content_text = f"{subject_lower} {snippet_lower}"
    has_business_indicators = any(indicator in content_text for indicator in business_indicators)
    
    # Default to creating draft for business-like emails
    return has_business_indicators or _looks_like_business_email(sender, subject, snippet)


def _looks_like_business_email(sender: str, subject: str, snippet: str) -> bool:
    """
    Additional heuristics to identify business emails.
    
    Args:
        sender: Email sender address
        subject: Email subject line
        snippet: Email snippet/preview
        
    Returns:
        True if email appears to be business-related
    """
    # Check for business domain patterns
    business_domains = ['.com', '.org', '.net', '.biz', '.co']
    has_business_domain = any(domain in sender.lower() for domain in business_domains)
    
    # Check for personal/human-like content
    personal_indicators = [
        'hello', 'hi', 'dear', 'greetings', 'hope', 'thank you',
        'please', 'would like', 'interested in', 'looking for'
    ]
    
    content_text = f"{subject.lower()} {snippet.lower()}"
    has_personal_touch = any(indicator in content_text for indicator in personal_indicators)
    
    # Check subject length (spam often has very short or very long subjects)
    reasonable_subject_length = 5 <= len(subject) <= 100
    
    return has_business_domain and has_personal_touch and reasonable_subject_length


async def _create_business_draft(email_data: Dict[str, Any], gmail_client) -> bool:
    """
    Create a professional business draft response for an email.
    
    Args:
        email_data: Email message data
        gmail_client: Gmail client instance
        
    Returns:
        True if draft created successfully, False otherwise
    """
    try:
        sender = email_data.get('from', '')
        subject = email_data.get('subject', '')
        thread_id = email_data.get('thread_id')
        
        # Generate professional response
        if not subject.startswith('Re:'):
            reply_subject = f"Re: {subject}"
        else:
            reply_subject = subject
        
        # Create a professional draft response
        draft_body = f"""Thank you for your email regarding {subject}.

I have received your message and will review it carefully. I will get back to you with a detailed response within 24 hours.

If this is urgent, please feel free to call our office directly.

Best regards,
Tyler Klug
TKC Group
<EMAIL>"""
        
        # Create the draft
        draft_id = await gmail_client.create_draft(
            to=sender,
            subject=reply_subject,
            body=draft_body,
            thread_id=thread_id
        )
        
        if draft_id:
            logger.info(f"Created business draft {draft_id} for email from {sender}")
            return True
        else:
            logger.error(f"Failed to create draft for email from {sender}")
            return False
            
    except Exception as e:
        logger.error(f"Error creating business draft: {e}")
        return False


@tool
async def check_existing_drafts_for_emails(email_ids: List[str]) -> str:
    """
    Check if drafts already exist for specific email IDs.
    
    Args:
        email_ids: List of email IDs to check
        
    Returns:
        Summary of existing drafts
    """
    try:
        gmail_client = await create_gmail_client()
        
        # Get all drafts
        drafts = await gmail_client.get_messages(query="in:drafts", max_results=50)
        
        # Extract thread IDs from drafts
        draft_thread_ids = set()
        for draft in drafts:
            thread_id = draft.get('thread_id')
            if thread_id:
                draft_thread_ids.add(thread_id)
        
        # Check which emails have drafts
        emails_with_drafts = []
        emails_without_drafts = []
        
        for email_id in email_ids:
            # Get email details to find thread ID
            email_details = await gmail_client.get_message_details(email_id)
            if email_details:
                email_thread_id = email_details.get('thread_id')
                if email_thread_id in draft_thread_ids:
                    emails_with_drafts.append(email_id)
                else:
                    emails_without_drafts.append(email_id)
        
        summary = f"Draft status check:\n"
        summary += f"- {len(emails_with_drafts)} emails already have drafts\n"
        summary += f"- {len(emails_without_drafts)} emails need drafts\n"
        
        return summary
        
    except Exception as e:
        logger.error(f"Error checking existing drafts: {e}")
        return f"Error checking drafts: {str(e)}"

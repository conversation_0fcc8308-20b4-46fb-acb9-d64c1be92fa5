"""
Business Logic Integration for TKC_v5 Executive Agent

Handles business-specific scenarios like lead qualification, appointment scheduling,
and automated follow-up sequences.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from agent.state import <PERSON><PERSON><PERSON>ontent, TriageResult, EmailReply
from agent.email_classifier import EmailCategory
from agent.conversation_memory import ConversationMemory, ConversationContext

logger = logging.getLogger(__name__)


class LeadScore(str, Enum):
    """Lead scoring levels."""
    HOT = "hot"          # Ready to buy, high priority
    WARM = "warm"        # Interested, needs nurturing
    COLD = "cold"        # Low interest or early stage
    UNQUALIFIED = "unqualified"  # Not a good fit


class BusinessAction(str, Enum):
    """Business actions that can be triggered."""
    SCHEDULE_DEMO = "schedule_demo"
    SEND_PRICING = "send_pricing"
    ESCALATE_TO_SALES = "escalate_to_sales"
    CREATE_SUPPORT_TICKET = "create_support_ticket"
    ADD_TO_NURTURE_SEQUENCE = "add_to_nurture_sequence"
    SCHEDULE_FOLLOW_UP = "schedule_follow_up"
    UPDATE_CRM = "update_crm"
    NOTIFY_TEAM = "notify_team"


@dataclass
class LeadQualification:
    """Lead qualification result."""
    score: LeadScore
    confidence: float
    reasoning: str
    company_size: Optional[str]
    industry: Optional[str]
    budget_indicator: Optional[str]
    timeline_indicator: Optional[str]
    decision_maker: bool
    pain_points: List[str]
    recommended_actions: List[BusinessAction]


@dataclass
class BusinessScenario:
    """Business scenario detection result."""
    scenario_type: str
    confidence: float
    required_actions: List[BusinessAction]
    priority: str
    estimated_value: Optional[float]
    next_steps: List[str]


class BusinessLogicEngine:
    """
    Handles business-specific logic and decision making for email interactions.
    """
    
    def __init__(self, conversation_memory: ConversationMemory):
        self.memory = conversation_memory
        self.qualification_rules = self._initialize_qualification_rules()
        self.scenario_patterns = self._initialize_scenario_patterns()
    
    def _initialize_qualification_rules(self) -> Dict[str, Any]:
        """Initialize lead qualification rules."""
        return {
            "hot_indicators": [
                "ready to purchase", "need to implement", "budget approved",
                "decision made", "when can we start", "contract", "proposal"
            ],
            "warm_indicators": [
                "interested in", "tell me more", "pricing", "demo",
                "how does it work", "case study", "roi"
            ],
            "cold_indicators": [
                "just looking", "researching", "future consideration",
                "maybe next year", "not ready"
            ],
            "company_size_indicators": {
                "enterprise": ["fortune 500", "enterprise", "large company", "corporation"],
                "mid_market": ["growing company", "expanding", "mid-size"],
                "small_business": ["small business", "startup", "small company"]
            },
            "budget_indicators": {
                "high": ["budget approved", "funded", "investment ready"],
                "medium": ["budget consideration", "evaluating cost"],
                "low": ["tight budget", "cost-conscious", "limited budget"]
            },
            "timeline_indicators": {
                "immediate": ["asap", "urgent", "this month", "immediately"],
                "short_term": ["next quarter", "within 3 months", "soon"],
                "long_term": ["next year", "future", "eventually"]
            }
        }
    
    def _initialize_scenario_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize business scenario patterns."""
        return {
            "demo_request": {
                "keywords": ["demo", "demonstration", "show me", "see it in action"],
                "actions": [BusinessAction.SCHEDULE_DEMO, BusinessAction.UPDATE_CRM],
                "priority": "high",
                "estimated_value": 50000
            },
            "pricing_inquiry": {
                "keywords": ["price", "cost", "pricing", "how much", "quote"],
                "actions": [BusinessAction.SEND_PRICING, BusinessAction.ESCALATE_TO_SALES],
                "priority": "high",
                "estimated_value": 30000
            },
            "technical_support": {
                "keywords": ["not working", "error", "bug", "issue", "problem"],
                "actions": [BusinessAction.CREATE_SUPPORT_TICKET, BusinessAction.NOTIFY_TEAM],
                "priority": "medium",
                "estimated_value": None
            },
            "partnership_inquiry": {
                "keywords": ["partnership", "integration", "collaborate", "work together"],
                "actions": [BusinessAction.ESCALATE_TO_SALES, BusinessAction.SCHEDULE_DEMO],
                "priority": "high",
                "estimated_value": 100000
            },
            "competitor_mention": {
                "keywords": ["competitor", "vs", "comparison", "alternative"],
                "actions": [BusinessAction.SEND_PRICING, BusinessAction.ESCALATE_TO_SALES],
                "priority": "high",
                "estimated_value": 40000
            }
        }
    
    async def qualify_lead(
        self, 
        email: EmailContent, 
        classification: TriageResult,
        context: Optional[ConversationContext] = None
    ) -> LeadQualification:
        """
        Qualify lead based on email content and conversation history.
        
        Args:
            email: Email content to analyze
            classification: Email classification result
            context: Conversation context if available
            
        Returns:
            LeadQualification with scoring and recommendations
        """
        logger.info(f"Qualifying lead from {email.from_address}")
        
        email_text = f"{email.subject} {email.body}".lower()
        
        # Analyze lead indicators
        score = self._calculate_lead_score(email_text, context)
        
        # Extract business information
        company_size = self._detect_company_size(email_text)
        industry = self._detect_industry(email_text, email.from_address)
        budget_indicator = self._detect_budget_level(email_text)
        timeline_indicator = self._detect_timeline(email_text)
        decision_maker = self._is_decision_maker(email_text, email.from_address)
        pain_points = self._extract_pain_points(email_text)
        
        # Determine recommended actions
        recommended_actions = self._get_lead_actions(score, classification, context)
        
        return LeadQualification(
            score=score,
            confidence=self._calculate_qualification_confidence(email_text, context),
            reasoning=self._generate_qualification_reasoning(score, email_text),
            company_size=company_size,
            industry=industry,
            budget_indicator=budget_indicator,
            timeline_indicator=timeline_indicator,
            decision_maker=decision_maker,
            pain_points=pain_points,
            recommended_actions=recommended_actions
        )
    
    def _calculate_lead_score(self, email_text: str, context: Optional[ConversationContext]) -> LeadScore:
        """Calculate lead score based on content and history."""
        
        hot_score = sum(1 for indicator in self.qualification_rules["hot_indicators"] 
                       if indicator in email_text)
        warm_score = sum(1 for indicator in self.qualification_rules["warm_indicators"] 
                        if indicator in email_text)
        cold_score = sum(1 for indicator in self.qualification_rules["cold_indicators"] 
                        if indicator in email_text)
        
        # Adjust based on conversation history
        if context and len(context.conversation_history) > 2:
            warm_score += 1  # Engaged customer
        
        if hot_score >= 2:
            return LeadScore.HOT
        elif warm_score >= 2 or hot_score >= 1:
            return LeadScore.WARM
        elif cold_score >= 2:
            return LeadScore.COLD
        else:
            return LeadScore.UNQUALIFIED
    
    def _detect_company_size(self, email_text: str) -> Optional[str]:
        """Detect company size from email content."""
        for size, indicators in self.qualification_rules["company_size_indicators"].items():
            if any(indicator in email_text for indicator in indicators):
                return size
        return None
    
    def _detect_industry(self, email_text: str, email_address: str) -> Optional[str]:
        """Detect industry from email content and domain."""
        industry_keywords = {
            "healthcare": ["hospital", "medical", "healthcare", "clinic"],
            "finance": ["bank", "financial", "investment", "insurance"],
            "technology": ["tech", "software", "saas", "ai", "ml"],
            "manufacturing": ["manufacturing", "factory", "production"],
            "retail": ["retail", "ecommerce", "store", "shopping"]
        }
        
        for industry, keywords in industry_keywords.items():
            if any(keyword in email_text for keyword in keywords):
                return industry
        
        # Check email domain for industry clues
        domain = email_address.split("@")[1].lower()
        if "bank" in domain or "financial" in domain:
            return "finance"
        elif "health" in domain or "medical" in domain:
            return "healthcare"
        
        return None
    
    def _detect_budget_level(self, email_text: str) -> Optional[str]:
        """Detect budget level indicators."""
        for level, indicators in self.qualification_rules["budget_indicators"].items():
            if any(indicator in email_text for indicator in indicators):
                return level
        return None
    
    def _detect_timeline(self, email_text: str) -> Optional[str]:
        """Detect timeline indicators."""
        for timeline, indicators in self.qualification_rules["timeline_indicators"].items():
            if any(indicator in email_text for indicator in indicators):
                return timeline
        return None
    
    def _is_decision_maker(self, email_text: str, email_address: str) -> bool:
        """Determine if sender is likely a decision maker."""
        decision_maker_indicators = [
            "ceo", "cto", "cfo", "president", "director", "vp", "head of",
            "manager", "lead", "owner", "founder"
        ]
        
        # Check email signature or title mentions
        return any(indicator in email_text for indicator in decision_maker_indicators)
    
    def _extract_pain_points(self, email_text: str) -> List[str]:
        """Extract mentioned pain points."""
        pain_point_patterns = {
            "efficiency": ["slow", "inefficient", "time-consuming", "manual"],
            "cost": ["expensive", "costly", "budget", "save money"],
            "scalability": ["growing", "scaling", "expansion", "capacity"],
            "integration": ["integrate", "connect", "compatibility", "api"],
            "automation": ["automate", "manual process", "repetitive"]
        }
        
        pain_points = []
        for pain_point, keywords in pain_point_patterns.items():
            if any(keyword in email_text for keyword in keywords):
                pain_points.append(pain_point)
        
        return pain_points
    
    def _get_lead_actions(
        self, 
        score: LeadScore, 
        classification: TriageResult,
        context: Optional[ConversationContext]
    ) -> List[BusinessAction]:
        """Get recommended actions based on lead score."""
        
        actions = []
        
        if score == LeadScore.HOT:
            actions.extend([
                BusinessAction.ESCALATE_TO_SALES,
                BusinessAction.SCHEDULE_DEMO,
                BusinessAction.UPDATE_CRM
            ])
        elif score == LeadScore.WARM:
            actions.extend([
                BusinessAction.SEND_PRICING,
                BusinessAction.SCHEDULE_DEMO,
                BusinessAction.ADD_TO_NURTURE_SEQUENCE
            ])
        elif score == LeadScore.COLD:
            actions.extend([
                BusinessAction.ADD_TO_NURTURE_SEQUENCE,
                BusinessAction.SCHEDULE_FOLLOW_UP
            ])
        
        # Add classification-specific actions
        if classification.classification == EmailCategory.SUPPORT_REQUEST.value:
            actions.append(BusinessAction.CREATE_SUPPORT_TICKET)
        
        return actions
    
    def _calculate_qualification_confidence(
        self, 
        email_text: str, 
        context: Optional[ConversationContext]
    ) -> float:
        """Calculate confidence in lead qualification."""
        
        confidence = 0.5  # Base confidence
        
        # Increase confidence based on explicit indicators
        total_indicators = (
            len([i for i in self.qualification_rules["hot_indicators"] if i in email_text]) +
            len([i for i in self.qualification_rules["warm_indicators"] if i in email_text]) +
            len([i for i in self.qualification_rules["cold_indicators"] if i in email_text])
        )
        
        confidence += min(0.4, total_indicators * 0.1)
        
        # Increase confidence if we have conversation history
        if context and len(context.conversation_history) > 1:
            confidence += 0.2
        
        return min(1.0, confidence)
    
    def _generate_qualification_reasoning(self, score: LeadScore, email_text: str) -> str:
        """Generate reasoning for lead qualification."""
        
        found_indicators = []
        
        for indicator in self.qualification_rules["hot_indicators"]:
            if indicator in email_text:
                found_indicators.append(f"hot: {indicator}")
        
        for indicator in self.qualification_rules["warm_indicators"]:
            if indicator in email_text:
                found_indicators.append(f"warm: {indicator}")
        
        for indicator in self.qualification_rules["cold_indicators"]:
            if indicator in email_text:
                found_indicators.append(f"cold: {indicator}")
        
        if found_indicators:
            return f"Lead scored as {score.value} based on indicators: {', '.join(found_indicators[:3])}"
        else:
            return f"Lead scored as {score.value} based on general content analysis"
    
    async def detect_business_scenario(
        self, 
        email: EmailContent, 
        classification: TriageResult
    ) -> Optional[BusinessScenario]:
        """
        Detect specific business scenarios requiring special handling.
        
        Args:
            email: Email content to analyze
            classification: Email classification result
            
        Returns:
            BusinessScenario if detected, None otherwise
        """
        
        email_text = f"{email.subject} {email.body}".lower()
        
        for scenario_type, pattern in self.scenario_patterns.items():
            if any(keyword in email_text for keyword in pattern["keywords"]):
                return BusinessScenario(
                    scenario_type=scenario_type,
                    confidence=0.8,
                    required_actions=pattern["actions"],
                    priority=pattern["priority"],
                    estimated_value=pattern.get("estimated_value"),
                    next_steps=self._get_scenario_next_steps(scenario_type)
                )
        
        return None
    
    def _get_scenario_next_steps(self, scenario_type: str) -> List[str]:
        """Get next steps for specific business scenarios."""
        
        next_steps_map = {
            "demo_request": [
                "Send calendar link for demo scheduling",
                "Prepare demo environment",
                "Send pre-demo questionnaire"
            ],
            "pricing_inquiry": [
                "Send pricing guide",
                "Schedule pricing discussion call",
                "Prepare custom quote if needed"
            ],
            "technical_support": [
                "Create support ticket",
                "Gather technical details",
                "Assign to technical team"
            ],
            "partnership_inquiry": [
                "Schedule partnership discussion",
                "Send partnership information packet",
                "Connect with business development team"
            ]
        }
        
        return next_steps_map.get(scenario_type, ["Follow up within 24 hours"])

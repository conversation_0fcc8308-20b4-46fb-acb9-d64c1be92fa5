"""
Enhanced LangGraph Agent Implementation with Gmail Integration

This module implements a production-grade ReAct agent using LangGraph and Gemini,
with comprehensive Gmail integration for email drafting and management.
"""

import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langchain_core.messages import ToolMessage

# Import Redis checkpointer
from services.redis_checkpointer import get_redis_checkpointer

from agent.state import (
    AgentState,
    TaskRequest,
    TaskResponse,
    TaskStatus,
    TaskType,
    EmailDraft,
    EmailContent,
    TriageResult,
    EmailReply,
    GmailOperationResult,
    create_initial_state,
    state_to_response
)
from services.gmail_client import create_gmail_client
from config.settings import get_settings
from agent.enhanced_email_tools import process_new_emails_with_deduplication, check_existing_drafts_for_emails

# Import new tool collections
from tools.calendar_tools import CALENDAR_TOOLS
from tools.email_automation_tools import EMAIL_AUTOMATION_TOOLS
from tools.crm_pipeline_tools import CRM_PIPELINE_TOOLS
from tools.ai_intelligence_tools import AI_INTELLIGENCE_TOOLS

logger = logging.getLogger(__name__)
settings = get_settings()


# Gmail-integrated tools
@tool
async def create_email_draft(
    to: str,
    subject: str,
    body: str,
    reply_to: Optional[str] = None,
    thread_id: Optional[str] = None
) -> str:
    """
    Creates and saves a draft email in the user's Gmail account.

    Use this tool when a user explicitly asks to create or draft an email.
    Provide all arguments: to, subject, and body.

    Args:
        to: The recipient's email address
        subject: The subject line of the email
        body: The main content of the email body
        reply_to: Reply-to address (optional)
        thread_id: Thread ID for replies (optional)

    Returns:
        A confirmation message including the unique Draft ID upon success, or an error message upon failure
    """
    try:
        gmail_client = await create_gmail_client()
        draft_id = await gmail_client.create_draft(
            to=to,
            subject=subject,
            body=body,
            reply_to=reply_to,
            thread_id=thread_id
        )

        if draft_id:
            return f"Email draft created successfully. Draft ID: {draft_id}"
        else:
            return "Failed to create email draft. Please check the Gmail API configuration."

    except Exception as e:
        return f"Error creating email draft: {str(e)}"


@tool
async def get_recent_emails(query: str = "is:unread", max_results: int = 5) -> str:
    """
    Retrieves recent emails from the user's Gmail inbox.

    Use this tool when a user asks to check, read, or see their recent emails.
    You can optionally filter emails with a search query.

    Args:
        query: Gmail search query to filter emails (e.g., "from:<EMAIL>", "is:unread")
        max_results: Maximum number of emails to retrieve (default: 5)

    Returns:
        A formatted list of recent emails with sender, subject, date, and preview, or an error message upon failure
    """
    try:
        gmail_client = await create_gmail_client()

        # Ensure we only check INBOX and exclude DRAFTS
        if "in:" not in query:
            inbox_query = f"in:inbox {query} -in:drafts"
        else:
            inbox_query = f"{query} -in:drafts"

        messages = await gmail_client.get_messages(query=inbox_query, max_results=max_results)

        if not messages:
            return "No emails found matching the query."

        summary = f"Found {len(messages)} emails:\n\n"
        for i, msg in enumerate(messages, 1):
            summary += f"{i}. From: {msg.get('from', 'Unknown')}\n"
            summary += f"   Subject: {msg.get('subject', 'No subject')}\n"
            summary += f"   Snippet: {msg.get('snippet', 'No preview')[:100]}...\n\n"

        return summary

    except Exception as e:
        return f"Error retrieving emails: {str(e)}"


@tool
async def setup_gmail_watch(topic_name: str) -> str:
    """
    Set up Gmail push notifications to automatically detect new emails.

    Args:
        topic_name: The Pub/Sub topic name (e.g., "gmail-notifications")

    Returns:
        A confirmation message with watch details upon success, or an error message upon failure
    """
    try:
        # Construct full topic name
        project_id = settings.env_vars.get("GOOGLE_CLOUD_PROJECT", "vertex-ai-agent-yzdlnjey")
        full_topic_name = f"projects/{project_id}/topics/{topic_name}"

        gmail_client = await create_gmail_client()
        result = await gmail_client.setup_watch(full_topic_name)

        if result:
            expiration = result.get('expiration', 'Unknown')
            history_id = result.get('historyId', 'Unknown')
            return f"Gmail watch set up successfully! Topic: {full_topic_name}, Expiration: {expiration}, History ID: {history_id}"
        else:
            return "Failed to set up Gmail watch. Please check the Pub/Sub topic permissions."

    except Exception as e:
        return f"Error setting up Gmail watch: {str(e)}"


@tool
async def stop_gmail_watch() -> str:
    """
    Stop Gmail push notifications.

    Returns:
        A confirmation message upon success, or an error message upon failure
    """
    try:
        gmail_client = await create_gmail_client()
        success = await gmail_client.stop_watch()

        if success:
            return "Gmail watch stopped successfully."
        else:
            return "Failed to stop Gmail watch."

    except Exception as e:
        return f"Error stopping Gmail watch: {str(e)}"


@tool
def analyze_email_content(email_subject: str, email_body: str, sender: str) -> str:
    """
    Analyzes email content for urgency, sentiment, and key information.

    Use this tool when a user wants to understand the importance or content of an email.
    Provides insights about urgency level, sentiment, and key points.

    Args:
        email_subject: Subject line of the email to analyze
        email_body: Body content of the email to analyze
        sender: Email address of the sender

    Returns:
        Detailed analysis including urgency level, sentiment, key insights, and recommended actions
    """
    try:
        # Simple analysis logic - in production this would use LLM
        analysis = {
            "urgency": "medium",
            "category": "general_inquiry",
            "sentiment": "neutral",
            "requires_response": True
        }

        # Basic keyword analysis
        urgent_keywords = ["urgent", "asap", "emergency", "critical"]
        if any(keyword in email_body.lower() or keyword in email_subject.lower()
               for keyword in urgent_keywords):
            analysis["urgency"] = "high"

        # Business opportunity detection
        opportunity_keywords = ["partnership", "collaboration", "proposal", "investment"]
        if any(keyword in email_body.lower() or keyword in email_subject.lower()
               for keyword in opportunity_keywords):
            analysis["category"] = "business_opportunity"

        return f"""Email Analysis:
- Urgency: {analysis['urgency']}
- Category: {analysis['category']}
- Sentiment: {analysis['sentiment']}
- Requires Response: {analysis['requires_response']}
- Sender: {sender}
- Subject: {email_subject}
"""

    except Exception as e:
        return f"Error analyzing email: {str(e)}"


class VertexAIAgent:
    """
    Production-grade LangGraph agent with Gmail integration using Gemini models on Vertex AI.

    This agent implements an enhanced workflow with email processing capabilities,
    proper state management, tool integration, and error handling.
    """

    def __init__(self, project_id: Optional[str] = None):
        """
        Initialize the agent with Vertex AI configuration from settings.

        Args:
            project_id: GCP project ID (optional, will use from settings if not provided)
        """
        # Load configuration from settings
        agent_config = settings.agent_config

        self.project_id = project_id or settings.project_id
        self.location = agent_config.location
        self.model_name = agent_config.model_name
        self.redis_checkpointer = None

        # Initialize the LLM with Application Default Credentials
        self.llm = ChatVertexAI(
            project=self.project_id,
            location=self.location,
            model_name=self.model_name,
            temperature=agent_config.temperature,
        )

        # Define available tools including Gmail integration and enhanced email processing
        self.tools = [
            create_email_draft,
            get_recent_emails,
            analyze_email_content,
            setup_gmail_watch,
            stop_gmail_watch,
            process_new_emails_with_deduplication,
            check_existing_drafts_for_emails
        ]

        # Add calendar management tools
        self.tools.extend(CALENDAR_TOOLS)

        # Add email automation tools
        self.tools.extend(EMAIL_AUTOMATION_TOOLS)

        # Add CRM pipeline management tools
        self.tools.extend(CRM_PIPELINE_TOOLS)

        # Add AI intelligence tools
        self.tools.extend(AI_INTELLIGENCE_TOOLS)

        # Bind tools to the LLM
        self.llm_with_tools = self.llm.bind_tools(self.tools)

        # Create the tool node for executing tools (custom async implementation)
        # self.tool_node = ToolNode(self.tools)  # This doesn't work with async tools

        # Build the workflow graph (will be rebuilt with checkpointer when available)
        self.app = self._build_graph()

    async def initialize_checkpointer(self):
        """Initialize Redis checkpointer for conversation persistence."""
        try:
            self.redis_checkpointer = await get_redis_checkpointer(settings)

            # Rebuild the graph with checkpointer
            self.app = self._build_graph()

            logger.info("Redis checkpointer initialized and graph rebuilt")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Redis checkpointer: {e}")
            return False

    async def initialize_rag_services(self, mock_mode: bool = False):
        """Initialize RAG and semantic search services."""
        try:
            # Initialize RAG service
            self.rag_service = await get_rag_service(settings, mock_mode=mock_mode)

            # Initialize semantic search service
            self.semantic_search = await get_semantic_search_service(settings, mock_mode=mock_mode)

            logger.info("RAG and semantic search services initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize RAG services: {e}")
            return False

    def _build_graph(self) -> StateGraph:
        """Build the enhanced LangGraph workflow."""
        # Initialize the graph with our enhanced state
        workflow = StateGraph(AgentState)

        # Add workflow nodes
        workflow.add_node("initialize", self._initialize_task)
        workflow.add_node("classify_intent", self._classify_intent)
        workflow.add_node("process_email", self._process_email)
        workflow.add_node("call_model", self._call_model)
        workflow.add_node("execute_tools", self._execute_tools)
        workflow.add_node("finalize", self._finalize_task)

        # Set the entry point
        workflow.set_entry_point("initialize")

        # Define the workflow edges
        workflow.add_edge("initialize", "classify_intent")
        workflow.add_conditional_edges(
            "classify_intent",
            self._route_after_classification,
            {
                "email_task": "process_email",
                "general_task": "call_model"
            }
        )
        workflow.add_edge("process_email", "call_model")
        workflow.add_conditional_edges(
            "call_model",
            self._should_continue,
            {
                "continue": "execute_tools",
                "end": "finalize"
            }
        )
        workflow.add_edge("execute_tools", "call_model")
        workflow.add_edge("finalize", END)

        # Compile the graph with checkpointer if available
        if self.redis_checkpointer and self.redis_checkpointer.get_saver():
            checkpointer = self.redis_checkpointer.get_saver()
            logger.info("Compiling graph with Redis checkpointer")
            return workflow.compile(checkpointer=checkpointer)
        else:
            logger.info("Compiling graph without checkpointer")
            return workflow.compile()
    
    def _initialize_task(self, state: AgentState) -> AgentState:
        """Initialize the task and set up context."""
        logger.info(f"Initializing task: {state.task_id}")

        try:
            state.update_status(TaskStatus.IN_PROGRESS)
            state.mark_step_completed("initialize")
            state.set_context("start_time", time.time())

            # Add initial message to conversation
            if state.input_data.get("message"):
                initial_message = HumanMessage(content=state.input_data["message"])
                state.messages.append(initial_message)

            logger.info(f"Task {state.task_id} initialized successfully")
            return state

        except Exception as e:
            logger.error(f"Task initialization failed: {e}")
            state.add_error(f"Initialization failed: {str(e)}")
            state.update_status(TaskStatus.FAILED)
            return state

    def _classify_intent(self, state: AgentState) -> AgentState:
        """Classify the intent and determine task routing."""
        logger.info(f"Classifying intent for task: {state.task_id}")

        try:
            state.mark_step_completed("classify_intent")

            # Determine task routing based on task type and input
            if state.task_type in [TaskType.EMAIL_DRAFT, TaskType.EMAIL_REPLY, TaskType.EMAIL_ANALYSIS]:
                state.set_context("route", "email_task")
            else:
                state.set_context("route", "general_task")

            logger.info(f"Intent classified, routing to: {state.get_context('route')}")
            return state

        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            state.add_error(f"Intent classification failed: {str(e)}")
            return state

    def _route_after_classification(self, state: AgentState) -> str:
        """Route to appropriate processing based on classification."""
        return state.get_context("route", "general_task")

    def _process_email(self, state: AgentState) -> AgentState:
        """Process email-specific tasks."""
        logger.info(f"Processing email task: {state.task_id}")

        try:
            state.mark_step_completed("process_email")

            # Extract email data from input
            email_data = state.input_data.get("email", {})

            if email_data:
                # Create EmailContent object
                email_content = EmailContent(
                    from_address=email_data.get("from", ""),
                    to_address=email_data.get("to", ""),
                    subject=email_data.get("subject", ""),
                    body=email_data.get("body", "")
                )
                state.email_content = email_content

                # Add email context to conversation
                email_context = f"""
Email to process:
From: {email_content.from_address}
To: {email_content.to_address}
Subject: {email_content.subject}
Body: {email_content.body}
"""
                context_message = HumanMessage(content=email_context)
                state.messages.append(context_message)

            logger.info(f"Email processing completed for task: {state.task_id}")
            return state

        except Exception as e:
            logger.error(f"Email processing failed: {e}")
            state.add_error(f"Email processing failed: {str(e)}")
            return state
    
    def _call_model(self, state: AgentState) -> AgentState:
        """Call the LLM with a system prompt to encourage tool use."""
        logger.info(f"Calling model for task: {state.task_id}")

        try:
            # Create a system prompt to guide the model
            system_prompt = SystemMessage(
                content="""
You are an executive AI assistant for TKC Group with comprehensive business automation capabilities.
You have access to Gmail, Calendar, CRM, and email automation tools to handle complete business workflows.

CORE CAPABILITIES:
- Gmail management: Create drafts, process emails, manage threads
- Calendar management: Schedule meetings, check availability, manage appointments
- CRM integration: Manage leads, deals, pipeline tracking, contact management
- Email automation: Create sequences, schedule follow-ups, analyze engagement
- Business intelligence: Pipeline analysis, lead scoring, performance insights
- AI Intelligence: Conversation analysis, predictive analytics, smart RAG queries
- Predictive Insights: Deal outcome prediction, response time forecasting, engagement analysis

CORE BEHAVIOR:
- AUTOMATICALLY take action without asking for permission
- Use available tools proactively to complete business workflows
- The user email for all <NAME_EMAIL>
- Represent TKC Group professionally (AI automation and business intelligence company)

EMAIL PROCESSING RULES:
- For business inquiries: AUTOMATICALLY create professional draft responses
- For meeting requests: Check calendar availability and suggest times
- For leads: Create CRM records and initiate appropriate follow-up sequences
- Skip newsletters, promotions, automated notifications

CALENDAR MANAGEMENT:
- When scheduling requests come in, check availability and propose times
- Create calendar events with proper details and video meeting links
- Send calendar invitations to all attendees
- Respect business hours (9 AM - 5 PM Denver time)

CRM INTEGRATION:
- Create lead records for new business inquiries
- Update deal stages as conversations progress
- Log all significant interactions and activities
- Maintain accurate pipeline and contact information

EMAIL AUTOMATION:
- Set up nurture sequences for new leads
- Schedule follow-ups based on conversation context
- Analyze engagement patterns for optimization

AI INTELLIGENCE & ANALYTICS:
- Analyze conversations for sentiment, urgency, and key insights
- Use smart RAG queries for context-aware information retrieval
- Generate predictive insights for deals, response times, and engagement
- Provide business intelligence reports and strategic recommendations
- Leverage conversation intelligence for better decision-making

Always act decisively and professionally, leveraging all available tools including advanced AI capabilities to provide comprehensive, intelligent business support.
"""
            )

            # Prepend the system prompt to the current message list
            # This ensures the model gets the instruction on every turn
            messages_with_prompt = [system_prompt] + state.messages

            # Debug logging
            logger.debug(f"Messages being sent to LLM: {[msg.content[:100] if hasattr(msg, 'content') else str(msg)[:100] for msg in messages_with_prompt]}")

            # Call the model with the enhanced message list
            response = self.llm_with_tools.invoke(messages_with_prompt)

            # Debug logging
            logger.debug(f"Response from LLM: {response}")
            logger.debug(f"Response type: {type(response)}")
            if hasattr(response, 'tool_calls'):
                logger.debug(f"Tool calls in response: {response.tool_calls}")

            # Append only the model's response to the state's message history
            state.messages.append(response)

            # Log tool calls if they exist
            if hasattr(response, 'tool_calls') and response.tool_calls:
                logger.info(f"Model generated tool calls: {response.tool_calls}")

            return state

        except Exception as e:
            logger.error(f"Model call failed: {e}")
            state.add_error(f"Model call failed: {str(e)}")
            return state

    async def _execute_tools(self, state: AgentState) -> AgentState:
        """Execute tools asynchronously."""
        logger.info("Executing tools...")

        try:
            # Get the last message which should contain tool calls
            last_message = state.messages[-1]

            if not hasattr(last_message, 'tool_calls') or not last_message.tool_calls:
                logger.warning("No tool calls found in last message")
                return state

            # Execute each tool call
            tool_messages = []
            for tool_call in last_message.tool_calls:
                tool_name = tool_call['name']
                tool_args = tool_call.get('args', {})
                tool_id = tool_call['id']

                logger.info(f"Executing tool: {tool_name} with args: {tool_args}")

                # Find the tool by name
                tool_to_execute = None
                for tool in self.tools:
                    if tool.name == tool_name:
                        tool_to_execute = tool
                        break

                if tool_to_execute is None:
                    error_msg = f"Tool {tool_name} not found"
                    logger.error(error_msg)
                    tool_messages.append(ToolMessage(
                        content=error_msg,
                        tool_call_id=tool_id
                    ))
                    continue

                try:
                    # Execute the tool asynchronously
                    if hasattr(tool_to_execute, 'ainvoke'):
                        result = await tool_to_execute.ainvoke(tool_args)
                    else:
                        # Fallback to sync execution if async not available
                        result = tool_to_execute.invoke(tool_args)

                    logger.info(f"Tool {tool_name} executed successfully")
                    tool_messages.append(ToolMessage(
                        content=str(result),
                        tool_call_id=tool_id
                    ))

                except Exception as e:
                    error_msg = f"Error executing tool {tool_name}: {str(e)}"
                    logger.error(error_msg)
                    logger.exception("Full tool execution error:")
                    tool_messages.append(ToolMessage(
                        content=error_msg,
                        tool_call_id=tool_id
                    ))

            # Add tool messages to state
            state.messages.extend(tool_messages)
            logger.info(f"Added {len(tool_messages)} tool messages to state")

            return state

        except Exception as e:
            logger.error(f"Tool execution failed: {e}")
            logger.exception("Full tool execution error:")
            state.add_error(f"Tool execution failed: {str(e)}")
            return state

    def _should_continue(self, state: AgentState) -> str:
        """Determine if we should continue with tool execution or finalize."""
        if not state.messages:
            return "end"

        last_message = state.messages[-1]

        # Check if the last message is an AIMessage and has tool_calls
        if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            logger.info(f"Tool calls detected, continuing to execute_tools: {last_message.tool_calls}")
            return "continue"
        else:
            logger.info("No tool calls detected, finalizing task")
            return "end"

    def _finalize_task(self, state: AgentState) -> AgentState:
        """Finalize the task and prepare results."""
        logger.info(f"Finalizing task: {state.task_id}")

        try:
            # Calculate processing time
            start_time = state.get_context("start_time", time.time())
            processing_time = int((time.time() - start_time) * 1000)
            state.processing_time_ms = processing_time

            # Prepare final result
            final_result = {
                "task_id": state.task_id,
                "task_type": state.task_type,
                "processing_time_ms": processing_time,
                "steps_completed": state.steps_completed
            }

            # Extract final response from conversation
            if state.messages:
                last_message = state.messages[-1]
                if hasattr(last_message, 'content'):
                    final_result["response"] = last_message.content

            # Add Gmail operations if any
            if state.gmail_operations:
                final_result["gmail_operations"] = [op.dict() for op in state.gmail_operations]

            # Add email-specific results
            if state.email_draft:
                final_result["email_draft"] = state.email_draft.dict()

            state.final_result = final_result

            # Update final status
            if state.errors:
                state.update_status(TaskStatus.FAILED)
            else:
                state.update_status(TaskStatus.COMPLETED)

            state.mark_step_completed("finalize")

            logger.info(f"Task {state.task_id} finalized with status: {state.status}")
            return state

        except Exception as e:
            logger.error(f"Task finalization failed: {e}")
            state.add_error(f"Finalization failed: {str(e)}")
            state.update_status(TaskStatus.FAILED)
            return state


    async def execute_task(self, task_request: TaskRequest) -> TaskResponse:
        """
        Execute a complete task workflow.

        Args:
            task_request: Task request with type and input data

        Returns:
            Task response with results
        """
        try:
            # Create initial state
            state = create_initial_state(task_request)

            # Execute the workflow
            final_state_dict = await self.app.ainvoke(state)

            # Convert dict back to AgentState if needed
            if isinstance(final_state_dict, dict):
                # Update the original state with the final state data
                for key, value in final_state_dict.items():
                    if hasattr(state, key):
                        setattr(state, key, value)
                final_state = state
            else:
                final_state = final_state_dict

            # Convert to response
            response = state_to_response(final_state)

            logger.info(f"Task {task_request.task_id} completed successfully")
            return response

        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            return TaskResponse(
                task_id=task_request.task_id,
                success=False,
                task_type=task_request.task_type,
                results={},
                processing_time_ms=0,
                error=str(e)
            )

    def invoke(self, message: str, task_type: TaskType = TaskType.GENERAL) -> str:
        """
        Simple invoke method for basic interactions.

        Args:
            message: User input message
            task_type: Type of task to execute

        Returns:
            Agent's response as a string
        """
        try:
            # Create a simple task request
            task_request = TaskRequest(
                task_id=f"task_{int(time.time())}",
                task_type=task_type,
                input_data={"message": message}
            )

            # Execute the task
            import asyncio
            response = asyncio.run(self.execute_task(task_request))

            # Extract the response content
            if response.success:
                final_result = response.results.get("final_result", {})
                if isinstance(final_result, dict):
                    return final_result.get("response", "Task completed successfully")
                else:
                    return "Task completed successfully"
            else:
                return f"Error: {response.error or 'Unknown error occurred'}"

        except Exception as e:
            return f"Error processing request: {str(e)}"

    async def execute_task_with_thread(
        self,
        task_request: TaskRequest,
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a task with conversation thread support.

        Args:
            task_request: The task to execute
            thread_id: Optional conversation thread ID for persistence

        Returns:
            Task execution result with conversation context
        """
        try:
            # Create configuration for thread-based execution
            config = {}
            if thread_id and self.redis_checkpointer:
                config = {"configurable": {"thread_id": thread_id}}
                logger.info(f"Executing task with thread ID: {thread_id}")

            # Convert task request to agent state
            state = AgentState(
                task_id=task_request.task_id,
                task_type=task_request.task_type,
                input_data=task_request.input_data,
                messages=[],
                status=TaskStatus.PENDING,
                context=task_request.context or {},
                steps_completed=[],
                final_result={}
            )

            # Execute the workflow with configuration
            if config:
                result = await self.app.ainvoke(state.dict(), config=config)
            else:
                result = await self.app.ainvoke(state.dict())

            logger.info(f"Task {task_request.task_id} completed with thread support")
            return result

        except Exception as e:
            logger.error(f"Error executing task with thread {thread_id}: {e}")
            return {
                "error": str(e),
                "task_id": task_request.task_id,
                "thread_id": thread_id
            }

    async def process_message_with_context(
        self,
        message: str,
        conversation_id: Optional[str] = None,
        customer_email: Optional[str] = None,
        message_type: str = "human"
    ) -> Dict[str, Any]:
        """
        Process a message with RAG context for intelligent responses.

        Args:
            message: The message content
            conversation_id: Conversation thread ID
            customer_email: Customer email address
            message_type: Type of message (human, assistant, system)

        Returns:
            Response with context-aware content
        """
        try:
            # Store the incoming message if RAG service is available
            if self.rag_service and conversation_id:
                await self.rag_service.store_conversation_message(
                    conversation_id=conversation_id,
                    message_content=message,
                    message_type=message_type,
                    customer_email=customer_email
                )

            # Get relevant context using RAG
            enhanced_prompt = None
            if self.rag_service:
                # Get the base system prompt
                base_prompt = """You are an executive assistant AI agent for TKC Group. You help with email management, scheduling, and business communications.

Provide professional, helpful responses that are:
- Clear and concise
- Appropriately formal for business context
- Action-oriented when appropriate
- Personalized based on available context"""

                # Enhance with RAG context
                enhanced_prompt = await self.rag_service.enhance_prompt_with_context(
                    original_prompt=base_prompt,
                    query=message,
                    conversation_id=conversation_id,
                    customer_email=customer_email
                )

            # Create messages for the LLM
            messages = []
            if enhanced_prompt:
                messages.append(SystemMessage(content=enhanced_prompt))
            else:
                messages.append(SystemMessage(content="You are an executive assistant AI agent for TKC Group."))

            messages.append(HumanMessage(content=message))

            # Get response from the model
            response = await self.llm.ainvoke(messages)

            # Store the assistant response if RAG service is available
            if self.rag_service and conversation_id:
                await self.rag_service.store_conversation_message(
                    conversation_id=conversation_id,
                    message_content=response.content,
                    message_type="assistant",
                    customer_email=customer_email
                )

            result = {
                "response": response.content,
                "conversation_id": conversation_id,
                "customer_email": customer_email,
                "context_used": enhanced_prompt is not None,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"✅ Processed message with context for conversation {conversation_id}")
            return result

        except Exception as e:
            logger.error(f"❌ Error processing message with context: {e}")
            return {
                "error": str(e),
                "response": "I apologize, but I encountered an error processing your message. Please try again.",
                "conversation_id": conversation_id,
                "customer_email": customer_email,
                "context_used": False
            }


def create_agent(project_id: Optional[str] = None) -> VertexAIAgent:
    """
    Factory function to create a configured agent instance.

    Args:
        project_id: GCP project ID

    Returns:
        Configured VertexAIAgent instance
    """
    return VertexAIAgent(project_id=project_id)


# Convenience functions for common email tasks

async def create_email_draft_task(
    to: str,
    subject: str,
    body: str,
    project_id: str = "vertex-ai-agent-yzdlnjey"
) -> TaskResponse:
    """
    Create an email draft using the agent.

    Args:
        to: Recipient email
        subject: Email subject
        body: Email body
        project_id: GCP project ID

    Returns:
        Task response with draft creation results
    """
    agent = create_agent(project_id)

    task_request = TaskRequest(
        task_id=f"draft_{int(time.time())}",
        task_type=TaskType.EMAIL_DRAFT,
        input_data={
            "message": f"Create an email draft to {to} with subject '{subject}' and body: {body}",
            "email": {
                "to": to,
                "subject": subject,
                "body": body
            }
        }
    )

    return await agent.execute_task(task_request)


if __name__ == "__main__":
    # Example usage for testing
    import os
    import asyncio

    # Get project ID from environment or use default
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT", "vertex-ai-agent-yzdlnjey")

    # Create agent
    agent = create_agent(project_id)

    # Test basic interaction
    test_message = "Help me create an email <NAME_EMAIL> about our AI automation services"
    response = agent.invoke(test_message, TaskType.EMAIL_DRAFT)
    print(f"User: {test_message}")
    print(f"Agent: {response}")

    # Test email draft creation
    async def test_email_draft():
        response = await create_email_draft_task(
            to="<EMAIL>",
            subject="Test Email from AI Agent",
            body="This is a test email created by our AI agent.",
            project_id=project_id
        )
        print(f"Email draft result: {response.dict()}")

    # Run async test
    asyncio.run(test_email_draft())

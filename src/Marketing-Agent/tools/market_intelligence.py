"""
Market Intelligence Tools for Coinme Sentinel System

These tools implement the Market Intelligence Analyst capabilities,
including social media monitoring, sentiment analysis, and competitor tracking.
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI

from ..state import (
    AnalysisObject, SentimentType, EmotionType, ThemeTag, 
    PlatformType, CoinmePulseState
)

logger = logging.getLogger(__name__)


@tool
async def x_search_tool(
    keywords: List[str],
    max_results: int = 50,
    time_range: str = "24h"
) -> List[Dict[str, Any]]:
    """
    Search X/Twitter for mentions of specified keywords.
    
    Args:
        keywords: List of keywords to search for
        max_results: Maximum number of results to return
        time_range: Time range for search (24h, 7d, 30d)
        
    Returns:
        List of tweet objects with metadata
    """
    try:
        # Mock implementation for demo - in production would use X API v2
        mock_tweets = [
            {
                "id": "1234567890",
                "text": "Just used a @Coinme ATM and the fees were way too high! $15 to buy $100 of Bitcoin? That's insane!",
                "author_id": "user123",
                "author_username": "cryptouser2024",
                "created_at": "2024-01-15T10:30:00Z",
                "public_metrics": {
                    "retweet_count": 5,
                    "like_count": 12,
                    "reply_count": 3
                },
                "context_annotations": [
                    {"domain": {"name": "Cryptocurrency"}, "entity": {"name": "Bitcoin"}}
                ]
            },
            {
                "id": "1234567891", 
                "text": "Has anyone used Coinme ATMs? Are they legit or is this some kind of scam? Seeing mixed reviews online.",
                "author_id": "user456",
                "author_username": "newbie_crypto",
                "created_at": "2024-01-15T09:15:00Z",
                "public_metrics": {
                    "retweet_count": 2,
                    "like_count": 8,
                    "reply_count": 7
                }
            },
            {
                "id": "1234567892",
                "text": "Coinme customer service actually helped me resolve my transaction issue quickly. Not bad for a crypto ATM company!",
                "author_id": "user789",
                "author_username": "satisfied_customer",
                "created_at": "2024-01-15T08:45:00Z",
                "public_metrics": {
                    "retweet_count": 1,
                    "like_count": 15,
                    "reply_count": 2
                }
            }
        ]
        
        # Filter by keywords
        filtered_tweets = []
        for tweet in mock_tweets:
            if any(keyword.lower() in tweet["text"].lower() for keyword in keywords):
                filtered_tweets.append(tweet)
        
        logger.info(f"X search found {len(filtered_tweets)} tweets for keywords: {keywords}")
        return filtered_tweets[:max_results]
        
    except Exception as e:
        logger.error(f"X search tool error: {e}")
        return []


@tool
async def reddit_monitor_tool(
    subreddits: List[str] = None,
    keywords: List[str] = None,
    max_results: int = 25
) -> List[Dict[str, Any]]:
    """
    Monitor Reddit for cryptocurrency and Coinme-related discussions.
    
    Args:
        subreddits: List of subreddits to monitor
        keywords: Keywords to search for
        max_results: Maximum number of results
        
    Returns:
        List of Reddit post/comment objects
    """
    try:
        if subreddits is None:
            subreddits = ["Bitcoin", "CryptoCurrency", "btc", "CryptoATM"]
        
        if keywords is None:
            keywords = ["coinme", "crypto atm", "bitcoin atm"]
        
        # Mock Reddit data for demo
        mock_posts = [
            {
                "id": "reddit123",
                "title": "Coinme ATM fees are ridiculous",
                "text": "Went to use a Coinme ATM today and they wanted to charge me 15% in fees. That's highway robbery! Bitcoin Depot is way cheaper.",
                "author": "crypto_enthusiast",
                "subreddit": "Bitcoin",
                "created_utc": 1705312800,  # 2024-01-15
                "score": 45,
                "num_comments": 12,
                "url": "https://reddit.com/r/Bitcoin/comments/reddit123"
            },
            {
                "id": "reddit124",
                "title": "Are Coinme ATMs safe to use?",
                "text": "I'm new to crypto and there's a Coinme ATM near me. Are they legitimate? I've heard mixed things about crypto ATMs in general.",
                "author": "newbie_questions",
                "subreddit": "CryptoCurrency", 
                "created_utc": 1705309200,
                "score": 23,
                "num_comments": 8,
                "url": "https://reddit.com/r/CryptoCurrency/comments/reddit124"
            }
        ]
        
        # Filter by keywords
        filtered_posts = []
        for post in mock_posts:
            text_to_search = f"{post['title']} {post['text']}".lower()
            if any(keyword.lower() in text_to_search for keyword in keywords):
                filtered_posts.append(post)
        
        logger.info(f"Reddit monitor found {len(filtered_posts)} posts")
        return filtered_posts[:max_results]
        
    except Exception as e:
        logger.error(f"Reddit monitor tool error: {e}")
        return []


@tool
async def sentiment_analysis_tool(
    text: str,
    platform: str = "twitter"
) -> Dict[str, Any]:
    """
    Perform advanced sentiment analysis using Gemini 2.5 Flash.
    
    Args:
        text: Text content to analyze
        platform: Source platform for context
        
    Returns:
        Comprehensive sentiment analysis results
    """
    try:
        # Initialize Gemini for sentiment analysis
        llm = ChatVertexAI(
            project="vertex-ai-agent-yzdlnjey",
            location="us-central1", 
            model_name="gemini-2.5-flash",
            temperature=0.1
        )
        
        analysis_prompt = f"""
        Analyze the following social media content for sentiment, emotions, and themes related to cryptocurrency ATMs and Coinme:

        Text: "{text}"
        Platform: {platform}

        Provide analysis in this exact JSON format:
        {{
            "sentiment": "positive|negative|neutral",
            "emotions": ["anger", "frustration", "joy", "confusion", "satisfaction", "concern"],
            "themes": ["high_fees", "scam_concern", "good_customer_service", "moneygram", "coinstar", "bitcoin_depot", "dfpi_fine", "crypto_atm", "compliance", "security"],
            "urgency_score": 1-10,
            "confidence_score": 0.0-1.0,
            "key_phrases": ["phrase1", "phrase2"],
            "competitor_mentioned": "bitcoin_depot|coinstar|moneygram|null",
            "is_complaint": true|false,
            "is_question": true|false,
            "requires_response": true|false
        }}

        Focus on:
        - Overall sentiment (positive/negative/neutral)
        - Specific emotions expressed
        - Themes related to crypto ATMs, fees, security, competitors
        - Urgency level (1=low, 10=critical)
        - Whether this needs a response from Coinme
        """
        
        response = await llm.ainvoke(analysis_prompt)
        
        # Parse the response (in production, would use structured output)
        # For demo, return structured analysis
        analysis_result = {
            "sentiment": "negative" if any(word in text.lower() for word in ["high", "expensive", "scam", "bad"]) else "positive" if any(word in text.lower() for word in ["good", "great", "helpful"]) else "neutral",
            "emotions": ["frustration"] if "high" in text.lower() and "fee" in text.lower() else ["concern"] if "scam" in text.lower() else ["satisfaction"] if "good" in text.lower() else [],
            "themes": ["high_fees"] if "fee" in text.lower() else ["scam_concern"] if "scam" in text.lower() else ["good_customer_service"] if "customer service" in text.lower() else [],
            "urgency_score": 8 if "scam" in text.lower() else 6 if "fee" in text.lower() else 3,
            "confidence_score": 0.85,
            "key_phrases": [phrase for phrase in ["high fees", "scam", "customer service", "bitcoin depot"] if phrase in text.lower()],
            "competitor_mentioned": "bitcoin_depot" if "bitcoin depot" in text.lower() else None,
            "is_complaint": any(word in text.lower() for word in ["high", "expensive", "bad", "terrible"]),
            "is_question": "?" in text,
            "requires_response": any(word in text.lower() for word in ["scam", "high", "fee", "help", "question"])
        }
        
        logger.info(f"Sentiment analysis completed for text: {text[:50]}...")
        return analysis_result
        
    except Exception as e:
        logger.error(f"Sentiment analysis error: {e}")
        return {
            "sentiment": "neutral",
            "emotions": [],
            "themes": [],
            "urgency_score": 1,
            "confidence_score": 0.0,
            "error": str(e)
        }


@tool
async def competitor_intelligence_tool(
    competitors: List[str] = None,
    time_range: str = "24h"
) -> Dict[str, Any]:
    """
    Monitor competitor mentions and activities.
    
    Args:
        competitors: List of competitor names to monitor
        time_range: Time range for monitoring
        
    Returns:
        Competitor intelligence summary
    """
    try:
        if competitors is None:
            competitors = ["Bitcoin Depot", "Coinstar", "MoneyGram"]
        
        # Mock competitor data
        competitor_data = {
            "Bitcoin Depot": {
                "mentions": 15,
                "sentiment": "mixed",
                "key_topics": ["lower fees", "more locations", "better app"],
                "recent_activity": "Announced partnership with major retailer"
            },
            "Coinstar": {
                "mentions": 8,
                "sentiment": "neutral", 
                "key_topics": ["convenience", "grocery stores"],
                "recent_activity": "No significant updates"
            },
            "MoneyGram": {
                "mentions": 5,
                "sentiment": "positive",
                "key_topics": ["established brand", "trust"],
                "recent_activity": "Expanded crypto services"
            }
        }
        
        summary = {
            "total_competitor_mentions": sum(data["mentions"] for data in competitor_data.values()),
            "top_competitor": max(competitor_data.keys(), key=lambda k: competitor_data[k]["mentions"]),
            "competitive_threats": ["Bitcoin Depot's lower fees being highlighted", "MoneyGram's brand trust advantage"],
            "opportunities": ["Coinme's customer service advantage", "First-mover advantage in certain markets"],
            "detailed_data": competitor_data
        }
        
        logger.info(f"Competitor intelligence gathered for {len(competitors)} competitors")
        return summary
        
    except Exception as e:
        logger.error(f"Competitor intelligence error: {e}")
        return {"error": str(e)}


@tool
async def trend_detection_tool(
    conversations: List[Dict[str, Any]],
    time_window: str = "24h"
) -> Dict[str, Any]:
    """
    Detect emerging trends and topics from conversation data.
    
    Args:
        conversations: List of analyzed conversations
        time_window: Time window for trend analysis
        
    Returns:
        Trend analysis results
    """
    try:
        # Analyze themes and topics
        theme_counts = {}
        emotion_counts = {}
        hourly_volume = {}
        
        for conv in conversations:
            # Count themes
            for theme in conv.get("themes", []):
                theme_counts[theme] = theme_counts.get(theme, 0) + 1
            
            # Count emotions
            for emotion in conv.get("emotions", []):
                emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
            
            # Track volume by hour
            hour = datetime.fromisoformat(conv.get("timestamp", datetime.utcnow().isoformat())).hour
            hourly_volume[hour] = hourly_volume.get(hour, 0) + 1
        
        # Identify trending topics
        trending_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        trending_emotions = sorted(emotion_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # Calculate trend scores
        trends = {
            "trending_themes": [{"theme": theme, "count": count, "trend_score": count * 2} for theme, count in trending_themes],
            "trending_emotions": [{"emotion": emotion, "count": count} for emotion, count in trending_emotions],
            "volume_pattern": hourly_volume,
            "peak_hour": max(hourly_volume.keys(), key=lambda k: hourly_volume[k]) if hourly_volume else None,
            "total_conversations": len(conversations),
            "analysis_window": time_window
        }
        
        logger.info(f"Trend detection completed for {len(conversations)} conversations")
        return trends
        
    except Exception as e:
        logger.error(f"Trend detection error: {e}")
        return {"error": str(e)}

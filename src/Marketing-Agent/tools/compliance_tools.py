"""
Compliance Guardian Tools for Coinme Sentinel System

These tools implement the Compliance Guardian capabilities,
ensuring all content and responses meet regulatory and platform requirements.
"""

import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI

from ..state import ComplianceResult, ComplianceStatus

logger = logging.getLogger(__name__)


@tool
async def compliance_check_tool(
    content: str,
    content_type: str = "response",
    platform: str = "twitter"
) -> Dict[str, Any]:
    """
    Comprehensive compliance check for content before publication.
    
    Args:
        content: Content to check for compliance
        content_type: Type of content (response, post, dm)
        platform: Target platform
        
    Returns:
        Detailed compliance analysis
    """
    try:
        violations = []
        warnings = []
        rule_checks = {}
        
        # DFAL Compliance Checks
        dfal_compliant = True
        
        # Check for specific dollar amounts (DFAL $1,000 daily limit rule)
        dollar_amounts = re.findall(r'\$[\d,]+', content)
        if dollar_amounts:
            for amount in dollar_amounts:
                amount_value = int(amount.replace('$', '').replace(',', ''))
                if amount_value > 1000:
                    violations.append(f"DFAL violation: Mentioned amount {amount} exceeds $1,000 daily limit")
                    dfal_compliant = False
        
        rule_checks["dfal_daily_limit"] = dfal_compliant
        
        # Check for receipt disclosure requirements
        receipt_keywords = ["receipt", "transaction", "purchase", "buy"]
        if any(keyword in content.lower() for keyword in receipt_keywords):
            if "receipt" not in content.lower():
                warnings.append("Consider mentioning receipt availability for transparency")
        
        rule_checks["receipt_disclosure"] = "receipt" in content.lower() if any(keyword in content.lower() for keyword in receipt_keywords) else True
        
        # Platform Policy Compliance
        platform_compliant = True
        
        # Twitter/X specific checks
        if platform == "twitter":
            if len(content) > 280:
                violations.append("Twitter character limit exceeded (280 characters)")
                platform_compliant = False
            
            # Check for spam indicators
            spam_indicators = ["click here", "limited time", "act now", "guaranteed"]
            if any(indicator in content.lower() for indicator in spam_indicators):
                warnings.append("Content contains potential spam indicators")
        
        rule_checks["platform_character_limit"] = len(content) <= (280 if platform == "twitter" else 500)
        rule_checks["spam_indicators"] = not any(indicator in content.lower() for indicator in ["click here", "limited time", "act now", "guaranteed"])
        
        # PII Detection
        pii_detected = []
        pii_safe = True
        
        # Email pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        if re.search(email_pattern, content):
            pii_detected.append("email_address")
            pii_safe = False
        
        # Phone pattern
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        if re.search(phone_pattern, content):
            pii_detected.append("phone_number")
            pii_safe = False
        
        # SSN pattern (basic)
        ssn_pattern = r'\b\d{3}-\d{2}-\d{4}\b'
        if re.search(ssn_pattern, content):
            pii_detected.append("ssn")
            pii_safe = False
        
        rule_checks["pii_safe"] = pii_safe
        
        # Financial Advice Compliance
        financial_advice_keywords = [
            "invest", "investment advice", "guaranteed returns", "profit",
            "financial advice", "tax advice", "legal advice"
        ]
        
        contains_financial_advice = any(keyword in content.lower() for keyword in financial_advice_keywords)
        if contains_financial_advice:
            warnings.append("Content may contain financial advice - add appropriate disclaimers")
        
        rule_checks["no_financial_advice"] = not contains_financial_advice
        
        # Competitor Mention Compliance
        competitors = ["bitcoin depot", "coinstar", "moneygram", "bitaccess"]
        competitor_mentioned = any(comp in content.lower() for comp in competitors)
        
        if competitor_mentioned:
            # Check for negative claims about competitors
            negative_words = ["worse", "bad", "terrible", "scam", "avoid"]
            if any(word in content.lower() for word in negative_words):
                violations.append("Negative claims about competitors not allowed")
                platform_compliant = False
        
        rule_checks["competitor_compliance"] = not (competitor_mentioned and any(word in content.lower() for word in ["worse", "bad", "terrible", "scam", "avoid"]))
        
        # Determine overall compliance status
        if violations:
            status = ComplianceStatus.FAIL
        elif warnings:
            status = ComplianceStatus.WARNING
        else:
            status = ComplianceStatus.PASS
        
        compliance_result = {
            "status": status.value,
            "rule_checks": rule_checks,
            "violations": violations,
            "warnings": warnings,
            "pii_detected": pii_detected,
            "dfal_compliant": dfal_compliant,
            "platform_policy_compliant": platform_compliant,
            "pii_safe": pii_safe,
            "checked_at": datetime.utcnow().isoformat(),
            "checker_version": "1.0.0",
            "content_length": len(content),
            "platform": platform,
            "recommendations": _generate_compliance_recommendations(violations, warnings)
        }
        
        logger.info(f"Compliance check completed: {status.value}, {len(violations)} violations, {len(warnings)} warnings")
        return compliance_result
        
    except Exception as e:
        logger.error(f"Compliance check error: {e}")
        return {
            "status": ComplianceStatus.FAIL.value,
            "error": str(e),
            "checked_at": datetime.utcnow().isoformat()
        }


@tool
async def digital_rulebook_check(
    content: str,
    rule_category: str = "all"
) -> Dict[str, Any]:
    """
    Check content against Coinme's digital rulebook.
    
    Args:
        content: Content to check
        rule_category: Category of rules to check (all, dfal, platform, brand)
        
    Returns:
        Rulebook compliance results
    """
    try:
        # Digital rulebook rules
        rulebook = {
            "dfal_rules": {
                "daily_limit_disclosure": "Must not mention amounts over $1,000 without proper disclosure",
                "receipt_requirement": "Must mention receipt availability for transactions",
                "licensing_disclosure": "Must include licensing information when discussing regulations"
            },
            "platform_rules": {
                "twitter_character_limit": "Responses must be under 280 characters",
                "no_spam_language": "Avoid spam-like language and excessive promotional content",
                "authentic_engagement": "Responses must be authentic and human-like"
            },
            "brand_rules": {
                "professional_tone": "Maintain professional, helpful tone",
                "no_competitor_attacks": "Do not make negative claims about competitors",
                "customer_focus": "Always prioritize customer needs and concerns"
            }
        }
        
        rule_violations = []
        rule_warnings = []
        
        # Check each category
        categories_to_check = [rule_category] if rule_category != "all" else rulebook.keys()
        
        for category in categories_to_check:
            if category in rulebook:
                category_rules = rulebook[category]
                
                # DFAL rules
                if category == "dfal_rules":
                    if re.search(r'\$[\d,]+', content):
                        amounts = re.findall(r'\$[\d,]+', content)
                        for amount in amounts:
                            amount_value = int(amount.replace('$', '').replace(',', ''))
                            if amount_value > 1000:
                                rule_violations.append(f"DFAL Rule Violation: {category_rules['daily_limit_disclosure']}")
                
                # Platform rules
                elif category == "platform_rules":
                    if len(content) > 280:
                        rule_violations.append(f"Platform Rule Violation: {category_rules['twitter_character_limit']}")
                    
                    spam_words = ["click here", "limited time", "act now"]
                    if any(word in content.lower() for word in spam_words):
                        rule_warnings.append(f"Platform Rule Warning: {category_rules['no_spam_language']}")
                
                # Brand rules
                elif category == "brand_rules":
                    negative_words = ["terrible", "awful", "worst"]
                    if any(word in content.lower() for word in negative_words):
                        rule_violations.append(f"Brand Rule Violation: {category_rules['professional_tone']}")
        
        rulebook_result = {
            "category_checked": rule_category,
            "total_rules_checked": sum(len(rules) for cat, rules in rulebook.items() if rule_category == "all" or cat == rule_category),
            "violations": rule_violations,
            "warnings": rule_warnings,
            "compliance_score": max(0, 100 - (len(rule_violations) * 25) - (len(rule_warnings) * 10)),
            "checked_at": datetime.utcnow().isoformat(),
            "rulebook_version": "1.0.0"
        }
        
        logger.info(f"Digital rulebook check completed: {len(rule_violations)} violations, {len(rule_warnings)} warnings")
        return rulebook_result
        
    except Exception as e:
        logger.error(f"Digital rulebook check error: {e}")
        return {"error": str(e)}


@tool
async def audit_trail_logger(
    action: str,
    content: str,
    compliance_result: Dict[str, Any],
    user_id: str = "system",
    additional_context: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Log compliance actions for audit trail.
    
    Args:
        action: Action taken (check, approve, reject, publish)
        content: Content that was processed
        compliance_result: Result of compliance check
        user_id: User who performed the action
        additional_context: Additional context for the audit
        
    Returns:
        Audit log entry confirmation
    """
    try:
        audit_entry = {
            "audit_id": f"audit_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{hash(content) % 10000}",
            "timestamp": datetime.utcnow().isoformat(),
            "action": action,
            "user_id": user_id,
            "content_hash": hash(content) % 100000,  # Simplified hash for demo
            "content_length": len(content),
            "compliance_status": compliance_result.get("status", "unknown"),
            "violations_count": len(compliance_result.get("violations", [])),
            "warnings_count": len(compliance_result.get("warnings", [])),
            "platform": compliance_result.get("platform", "unknown"),
            "additional_context": additional_context or {},
            "retention_period": "7_years",  # Regulatory requirement
            "audit_version": "1.0.0"
        }
        
        # In production, this would write to a secure audit database
        logger.info(f"Audit trail logged: {action} by {user_id}, status: {compliance_result.get('status')}")
        
        return {
            "audit_logged": True,
            "audit_id": audit_entry["audit_id"],
            "timestamp": audit_entry["timestamp"],
            "retention_notice": "Audit record will be retained for 7 years per regulatory requirements"
        }
        
    except Exception as e:
        logger.error(f"Audit trail logging error: {e}")
        return {"error": str(e), "audit_logged": False}


def _generate_compliance_recommendations(violations: List[str], warnings: List[str]) -> List[str]:
    """Generate actionable compliance recommendations."""
    recommendations = []
    
    if violations:
        recommendations.append("CRITICAL: Address all violations before publication")
        
        if any("DFAL" in v for v in violations):
            recommendations.append("Review DFAL compliance requirements and adjust content")
        
        if any("character limit" in v for v in violations):
            recommendations.append("Shorten content to meet platform requirements")
    
    if warnings:
        recommendations.append("Consider addressing warnings to improve compliance")
        
        if any("spam" in w for w in warnings):
            recommendations.append("Revise language to be more natural and less promotional")
        
        if any("financial advice" in w for w in warnings):
            recommendations.append("Add appropriate disclaimers or remove advisory language")
    
    if not violations and not warnings:
        recommendations.append("Content is compliant and ready for publication")
    
    return recommendations


@tool
async def compliance_training_check(
    user_id: str,
    required_modules: List[str] = None
) -> Dict[str, Any]:
    """
    Check if user has completed required compliance training.
    
    Args:
        user_id: User to check training for
        required_modules: Required training modules
        
    Returns:
        Training compliance status
    """
    try:
        if required_modules is None:
            required_modules = ["dfal_basics", "social_media_compliance", "pii_protection"]
        
        # Mock training data for demo
        training_status = {
            "user_id": user_id,
            "completed_modules": ["dfal_basics", "social_media_compliance"],
            "pending_modules": ["pii_protection"],
            "last_training_date": "2024-01-10",
            "certification_expires": "2024-07-10",
            "compliance_score": 85,
            "can_approve_content": True,
            "can_publish_content": False,  # Missing PII training
            "training_current": False
        }
        
        logger.info(f"Training compliance checked for user: {user_id}")
        return training_status
        
    except Exception as e:
        logger.error(f"Training compliance check error: {e}")
        return {"error": str(e)}

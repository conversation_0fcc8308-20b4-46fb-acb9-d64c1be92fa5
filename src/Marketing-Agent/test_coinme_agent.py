"""
Test Script for Coinme Content Marketing Agent

This script tests the core functionality of the Coinme Sentinel System
to ensure it's ready for the CEO presentation.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.Marketing_Agent.core import ContentMarketingAgent
from src.Marketing_Agent.state import create_initial_coinme_state
from src.Marketing_Agent.config.coinme_config import coinme_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_agent_initialization():
    """Test that the agent initializes correctly."""
    logger.info("Testing agent initialization...")
    
    try:
        agent = ContentMarketingAgent()
        
        # Test basic properties
        assert agent.agent_type == "coinme_content_marketing"
        assert agent.model_name == "gemini-2.5-flash"
        assert len(agent.tools) > 0
        
        logger.info("✅ Agent initialization test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent initialization test failed: {e}")
        return False


async def test_state_management():
    """Test state creation and management."""
    logger.info("Testing state management...")
    
    try:
        # Create initial state
        session_id = f"test_session_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        state = create_initial_coinme_state(session_id)
        
        # Test state properties
        assert state.session_id == session_id
        assert len(state.messages) == 0
        assert len(state.analyzed_conversations) == 0
        assert state.processing_complete == False
        
        # Test state methods
        state.add_error("Test error")
        assert len(state.error_log) == 1
        
        state.add_compliance_log("Test compliance entry")
        assert len(state.compliance_log) == 1
        
        logger.info("✅ State management test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ State management test failed: {e}")
        return False


async def test_configuration():
    """Test Coinme configuration."""
    logger.info("Testing Coinme configuration...")
    
    try:
        # Test keyword configuration
        keywords = coinme_config.get_keywords_for_platform("twitter")
        assert len(keywords) > 0
        assert "coinme" in [k.lower() for k in keywords]
        
        # Test response templates
        template = coinme_config.get_response_template("fee_concern")
        assert len(template) > 0
        assert "fee" in template.lower()
        
        # Test escalation logic
        mock_analysis = {
            "original_text": "This is a scam! Coinme stole my money!",
            "urgency_score": 9,
            "themes": ["scam_concern"]
        }
        should_escalate = coinme_config.should_escalate(mock_analysis)
        assert should_escalate == True
        
        logger.info("✅ Configuration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return False


async def test_tool_functionality():
    """Test individual tool functionality."""
    logger.info("Testing tool functionality...")
    
    try:
        # Import tools
        from src.Marketing_Agent.tools.market_intelligence import sentiment_analysis_tool
        from src.Marketing_Agent.tools.engagement_tools import assess_engagement_opportunity
        from src.Marketing_Agent.tools.compliance_tools import compliance_check_tool
        
        # Test sentiment analysis
        sentiment_result = await sentiment_analysis_tool.ainvoke({
            "text": "Coinme fees are way too high! This is ridiculous!",
            "platform": "twitter"
        })
        
        assert "sentiment" in sentiment_result
        assert "urgency_score" in sentiment_result
        
        # Test engagement assessment
        engagement_result = await assess_engagement_opportunity.ainvoke({
            "analysis": sentiment_result,
            "platform": "twitter"
        })
        
        assert "is_opportunity" in engagement_result
        assert "priority" in engagement_result
        
        # Test compliance check
        compliance_result = await compliance_check_tool.ainvoke({
            "content": "Hi! Thanks for your feedback. We'd love to help address your concerns. Please DM us!",
            "content_type": "response",
            "platform": "twitter"
        })
        
        assert "status" in compliance_result
        assert "violations" in compliance_result
        
        logger.info("✅ Tool functionality test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tool functionality test failed: {e}")
        return False


async def test_end_to_end_workflow():
    """Test the complete end-to-end workflow."""
    logger.info("Testing end-to-end workflow...")
    
    try:
        # Initialize agent
        agent = ContentMarketingAgent()
        
        # Execute a pulse session
        result = await agent.execute_pulse_session()
        
        # Validate results
        assert result["success"] == True
        assert "session_id" in result
        assert "summary" in result
        assert "detailed_results" in result
        
        # Check that the workflow processed data
        summary = result["summary"]
        assert summary["mentions_analyzed"] >= 0
        assert summary["processing_complete"] == True
        
        logger.info("✅ End-to-end workflow test passed")
        logger.info(f"Session summary: {summary}")
        return True
        
    except Exception as e:
        logger.error(f"❌ End-to-end workflow test failed: {e}")
        return False


async def run_demo_scenario():
    """Run a demo scenario for the CEO presentation."""
    logger.info("Running CEO presentation demo scenario...")
    
    try:
        agent = ContentMarketingAgent()
        
        # Execute pulse session
        result = await agent.execute_pulse_session("demo_session_for_neil")
        
        if result["success"]:
            logger.info("🎯 DEMO SCENARIO RESULTS:")
            logger.info("=" * 50)
            
            summary = result["summary"]
            logger.info(f"📊 Mentions Analyzed: {summary['mentions_analyzed']}")
            logger.info(f"🎯 Opportunities Found: {summary['opportunities_found']}")
            logger.info(f"✍️  Responses Drafted: {summary['responses_drafted']}")
            logger.info(f"✅ Compliance Checks: {summary['compliance_checks']}")
            logger.info(f"❌ Errors: {summary['errors']}")
            
            # Show detailed results
            detailed = result["detailed_results"]
            
            if detailed["analyzed_conversations"]:
                logger.info("\n📈 ANALYZED CONVERSATIONS:")
                for i, conv in enumerate(detailed["analyzed_conversations"][:3], 1):
                    logger.info(f"{i}. Platform: {conv['platform']}")
                    logger.info(f"   Sentiment: {conv['sentiment']}")
                    logger.info(f"   Urgency: {conv['urgency_score']}/10")
                    logger.info(f"   Themes: {', '.join(conv['themes'])}")
                    logger.info(f"   Opportunity: {conv['is_opportunity']}")
            
            if detailed["engagement_opportunities"]:
                logger.info("\n🎯 ENGAGEMENT OPPORTUNITIES:")
                for i, opp in enumerate(detailed["engagement_opportunities"][:3], 1):
                    logger.info(f"{i}. Priority: {opp['priority']}")
                    logger.info(f"   Type: {opp['opportunity_type']}")
                    logger.info(f"   Approach: {opp['recommended_approach']}")
            
            if detailed["drafted_responses"]:
                logger.info("\n✍️  DRAFTED RESPONSES:")
                for i, draft in enumerate(detailed["drafted_responses"][:2], 1):
                    logger.info(f"{i}. Strategy: {draft.get('response_strategy', 'N/A')}")
                    logger.info(f"   Compliance: {draft.get('compliance_status', 'N/A')}")
                    logger.info(f"   Text: {draft.get('response_text', 'N/A')[:100]}...")
            
            logger.info("\n🎉 Demo scenario completed successfully!")
            return True
        else:
            logger.error(f"❌ Demo scenario failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Demo scenario failed: {e}")
        return False


async def main():
    """Run all tests."""
    logger.info("🚀 Starting Coinme Content Marketing Agent Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Agent Initialization", test_agent_initialization),
        ("State Management", test_state_management),
        ("Configuration", test_configuration),
        ("Tool Functionality", test_tool_functionality),
        ("End-to-End Workflow", test_end_to_end_workflow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        if await test_func():
            passed += 1
        else:
            logger.error(f"Test {test_name} failed!")
    
    logger.info(f"\n📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Running demo scenario...")
        await run_demo_scenario()
        logger.info("\n✅ Coinme Content Marketing Agent is ready for CEO presentation!")
    else:
        logger.error("❌ Some tests failed. Please fix issues before presentation.")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())

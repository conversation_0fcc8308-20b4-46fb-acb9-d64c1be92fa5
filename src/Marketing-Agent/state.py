"""
Coinme Pulse State - Central State Management for Coinme Sentinel System

This module defines the state objects that represent the Coinme Sentinel system's
operational state, based on the Coinme agentic plan specifications.
"""

from typing import Optional, Dict, Any, List, Literal, Annotated, Sequence
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum
import operator
from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages


class SentimentType(str, Enum):
    """Sentiment classification types."""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"


class EmotionType(str, Enum):
    """Emotion analysis types based on Coinme requirements."""
    ANGER = "anger"
    FRUSTRATION = "frustration"
    JOY = "joy"
    CONFUSION = "confusion"
    SATISFACTION = "satisfaction"
    CONCERN = "concern"


class ThemeTag(str, Enum):
    """Thematic tags for content classification."""
    HIGH_FEES = "high_fees"
    SCAM_CONCERN = "scam_concern"
    GOOD_CUSTOMER_SERVICE = "good_customer_service"
    MONEYGRAM = "moneygram"
    COINSTAR = "coinstar"
    BITCOIN_DEPOT = "bitcoin_depot"
    DFPI_FINE = "dfpi_fine"
    CRYPTO_ATM = "crypto_atm"
    COMPLIANCE = "compliance"
    SECURITY = "security"


class PlatformType(str, Enum):
    """Social media platforms for monitoring."""
    TWITTER = "twitter"
    REDDIT = "reddit"
    GOOGLE_REVIEWS = "google_reviews"
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"


class ComplianceStatus(str, Enum):
    """Compliance check results."""
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"
    PENDING = "pending"


class AnalysisObject(BaseModel):
    """Structured analysis object for processed conversations."""
    
    # Source information
    platform: PlatformType
    content_id: str
    author_id: Optional[str] = None
    timestamp: datetime
    original_text: str
    
    # Analysis results
    sentiment: SentimentType
    emotions: List[EmotionType] = []
    themes: List[ThemeTag] = []
    urgency_score: int = Field(ge=1, le=10, description="Urgency score from 1-10")
    
    # Engagement potential
    is_opportunity: bool = False
    opportunity_reason: Optional[str] = None
    competitor_mentioned: Optional[str] = None
    
    # Metadata
    confidence_score: float = Field(ge=0.0, le=1.0)
    processed_at: datetime = Field(default_factory=datetime.utcnow)


class DraftResponseObject(BaseModel):
    """AI-generated response draft."""
    
    # Response content
    response_text: str
    response_type: Literal["reply", "dm"] = "reply"
    target_platform: PlatformType
    target_content_id: str
    target_user_id: Optional[str] = None
    
    # Context
    original_analysis: AnalysisObject
    response_strategy: str  # e.g., "fee_complaint_response", "scam_concern_response"
    
    # Compliance
    compliance_status: ComplianceStatus = ComplianceStatus.PENDING
    compliance_notes: List[str] = []
    
    # Approval workflow
    requires_approval: bool = True
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    draft_id: str


class ComplianceResult(BaseModel):
    """Result of compliance checking."""
    
    status: ComplianceStatus
    rule_checks: Dict[str, bool] = {}  # rule_id -> pass/fail
    violations: List[str] = []
    warnings: List[str] = []
    pii_detected: List[str] = []
    
    # Specific compliance areas
    dfal_compliant: bool = True
    platform_policy_compliant: bool = True
    pii_safe: bool = True
    
    checked_at: datetime = Field(default_factory=datetime.utcnow)
    checker_version: str = "1.0.0"


class EngagementOpportunity(BaseModel):
    """Identified engagement opportunity."""
    
    analysis: AnalysisObject
    opportunity_type: str  # e.g., "fee_complaint", "scam_concern", "general_question"
    priority: Literal["high", "medium", "low"] = "medium"
    
    # Engagement strategy
    recommended_approach: str
    key_talking_points: List[str] = []
    
    # Timing
    identified_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = None
    
    # Status
    status: Literal["pending", "drafted", "approved", "published", "expired"] = "pending"


class CoinmePulseState(BaseModel):
    """
    Central state object for the Coinme Sentinel system.
    
    This state is passed between all nodes in the LangGraph application,
    maintaining the complete operational context.
    """
    
    # LangGraph message handling
    messages: Annotated[Sequence[BaseMessage], add_messages] = []
    
    # --- Input & Raw Data ---
    new_mentions: List[Dict[str, Any]] = []
    
    # --- Analysis & Intelligence ---
    analyzed_conversations: Annotated[List[AnalysisObject], operator.add] = []
    sentiment_trends: Dict[str, Any] = {}
    emerging_topics: List[str] = []
    
    # --- Action & Engagement ---
    engagement_opportunities: List[EngagementOpportunity] = []
    drafted_responses: List[DraftResponseObject] = []
    published_engagements: Annotated[List[Dict[str, Any]], operator.add] = []
    
    # --- Compliance & Logging ---
    compliance_log: Annotated[List[str], operator.add] = []
    error_log: Annotated[List[str], operator.add] = []
    
    # --- Workflow Control ---
    current_step: Optional[str] = None
    should_continue: bool = True
    processing_complete: bool = False
    
    # --- Metadata ---
    session_id: str
    started_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        arbitrary_types_allowed = True  # For BaseMessage types
    
    def add_error(self, error: str):
        """Add an error to the error log."""
        self.error_log.append(f"{datetime.utcnow().isoformat()}: {error}")
        self.updated_at = datetime.utcnow()
    
    def add_compliance_log(self, entry: str):
        """Add an entry to the compliance log."""
        self.compliance_log.append(f"{datetime.utcnow().isoformat()}: {entry}")
        self.updated_at = datetime.utcnow()
    
    def update_step(self, step: str):
        """Update the current processing step."""
        self.current_step = step
        self.updated_at = datetime.utcnow()
    
    def add_analysis(self, analysis: AnalysisObject):
        """Add a new analysis object."""
        self.analyzed_conversations.append(analysis)
        self.updated_at = datetime.utcnow()
    
    def add_opportunity(self, opportunity: EngagementOpportunity):
        """Add a new engagement opportunity."""
        self.engagement_opportunities.append(opportunity)
        self.updated_at = datetime.utcnow()
    
    def add_draft(self, draft: DraftResponseObject):
        """Add a new draft response."""
        self.drafted_responses.append(draft)
        self.updated_at = datetime.utcnow()


# Helper functions for state management
def create_initial_coinme_state(session_id: str) -> CoinmePulseState:
    """Create an initial Coinme Pulse state."""
    return CoinmePulseState(
        session_id=session_id,
        messages=[],
        new_mentions=[],
        analyzed_conversations=[],
        sentiment_trends={},
        emerging_topics=[],
        engagement_opportunities=[],
        drafted_responses=[],
        published_engagements=[],
        compliance_log=[],
        error_log=[]
    )


def state_to_summary(state: CoinmePulseState) -> Dict[str, Any]:
    """Convert state to summary for reporting."""
    return {
        "session_id": state.session_id,
        "current_step": state.current_step,
        "total_mentions": len(state.new_mentions),
        "analyzed_conversations": len(state.analyzed_conversations),
        "engagement_opportunities": len(state.engagement_opportunities),
        "drafted_responses": len(state.drafted_responses),
        "published_engagements": len(state.published_engagements),
        "compliance_entries": len(state.compliance_log),
        "errors": len(state.error_log),
        "started_at": state.started_at.isoformat(),
        "updated_at": state.updated_at.isoformat(),
        "processing_complete": state.processing_complete
    }

"""
FastAPI endpoint for Coinme Content Marketing Agent

This provides a REST API interface for the Coinme Sentinel System
for integration with existing TKC_v5 infrastructure.
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from .core import ContentMarketingAgent
from .config.coinme_config import coinme_config

logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Coinme Content Marketing Agent API",
    description="Coinme Sentinel System - AI-powered social media monitoring and engagement",
    version="1.0.0"
)

# Global agent instance
agent_instance: Optional[ContentMarketingAgent] = None


class PulseSessionRequest(BaseModel):
    """Request model for starting a pulse session."""
    session_id: Optional[str] = Field(None, description="Optional session ID")
    platforms: Optional[list] = Field(["twitter", "reddit"], description="Platforms to monitor")
    keywords: Optional[list] = Field(None, description="Custom keywords to monitor")


class PulseSessionResponse(BaseModel):
    """Response model for pulse session results."""
    success: bool
    session_id: str
    summary: Dict[str, Any]
    detailed_results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class AgentStatusResponse(BaseModel):
    """Response model for agent status."""
    status: str
    agent_type: str
    model_name: str
    tools_available: int
    last_session: Optional[str] = None
    uptime_seconds: float


@app.on_event("startup")
async def startup_event():
    """Initialize the agent on startup."""
    global agent_instance
    
    try:
        logger.info("Initializing Coinme Content Marketing Agent...")
        agent_instance = ContentMarketingAgent()
        
        # Initialize services
        services_ready = await agent_instance.initialize_services()
        
        if services_ready:
            logger.info("✅ Coinme Content Marketing Agent initialized successfully")
        else:
            logger.warning("⚠️ Agent initialized but some services may not be available")
            
    except Exception as e:
        logger.error(f"❌ Failed to initialize agent: {e}")
        # Continue startup even if agent fails to initialize
        agent_instance = None


@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with basic information."""
    return {
        "service": "Coinme Content Marketing Agent",
        "version": "1.0.0",
        "status": "active" if agent_instance else "error",
        "description": "Coinme Sentinel System - AI-powered social media monitoring and engagement"
    }


@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint."""
    if agent_instance is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "agent_ready": "true"
    }


@app.get("/status", response_model=AgentStatusResponse)
async def get_agent_status():
    """Get detailed agent status."""
    if agent_instance is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return AgentStatusResponse(
        status="active",
        agent_type=agent_instance.agent_type,
        model_name=agent_instance.model_name,
        tools_available=len(agent_instance.tools),
        uptime_seconds=0.0  # Would track actual uptime in production
    )


@app.post("/pulse/start", response_model=PulseSessionResponse)
async def start_pulse_session(request: PulseSessionRequest, background_tasks: BackgroundTasks):
    """Start a new Coinme Pulse monitoring session."""
    if agent_instance is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        logger.info(f"Starting pulse session with request: {request}")
        
        # Execute pulse session
        result = await agent_instance.execute_pulse_session(request.session_id)
        
        if result["success"]:
            return PulseSessionResponse(
                success=True,
                session_id=result["session_id"],
                summary=result["summary"],
                detailed_results=result["detailed_results"]
            )
        else:
            return PulseSessionResponse(
                success=False,
                session_id=result.get("session_id", "unknown"),
                summary={},
                error=result.get("error", "Unknown error")
            )
            
    except Exception as e:
        logger.error(f"Pulse session error: {e}")
        raise HTTPException(status_code=500, detail=f"Pulse session failed: {str(e)}")


@app.get("/pulse/{session_id}", response_model=PulseSessionResponse)
async def get_pulse_session(session_id: str):
    """Get results from a specific pulse session."""
    if agent_instance is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    # In production, this would retrieve session data from Redis/database
    # For now, return a mock response
    return PulseSessionResponse(
        success=True,
        session_id=session_id,
        summary={
            "status": "Session data would be retrieved from storage",
            "note": "This is a mock response for demo purposes"
        }
    )


@app.get("/config/keywords", response_model=Dict[str, Any])
async def get_keywords():
    """Get current monitoring keywords."""
    return {
        "brand_keywords": coinme_config.keywords.brand_keywords,
        "competitor_keywords": coinme_config.keywords.competitor_keywords,
        "industry_keywords": coinme_config.keywords.industry_keywords,
        "negative_keywords": coinme_config.keywords.negative_keywords,
        "opportunity_keywords": coinme_config.keywords.opportunity_keywords
    }


@app.get("/config/templates", response_model=Dict[str, str])
async def get_response_templates():
    """Get available response templates."""
    return {
        "fee_concern": coinme_config.response_templates.fee_concern_template,
        "scam_concern": coinme_config.response_templates.scam_concern_template,
        "customer_service": coinme_config.response_templates.customer_service_template,
        "competitor_comparison": coinme_config.response_templates.competitor_comparison_template,
        "general_help": coinme_config.response_templates.general_help_template
    }


@app.get("/config/compliance", response_model=Dict[str, Any])
async def get_compliance_rules():
    """Get compliance rules and settings."""
    return {
        "dfal_rules": coinme_config.compliance_rules.dfal_rules,
        "platform_rules": coinme_config.compliance_rules.platform_rules,
        "brand_guidelines": coinme_config.compliance_rules.brand_guidelines,
        "monitoring_config": {
            "intervals": coinme_config.monitoring_config.monitoring_intervals,
            "alert_thresholds": coinme_config.monitoring_config.alert_thresholds,
            "priority_keywords": coinme_config.monitoring_config.priority_keywords
        }
    }


@app.post("/demo/ceo-presentation", response_model=PulseSessionResponse)
async def run_ceo_demo():
    """Run a demo scenario specifically for the CEO presentation."""
    if agent_instance is None:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        logger.info("🎯 Running CEO presentation demo...")
        
        # Execute demo pulse session
        result = await agent_instance.execute_pulse_session("ceo_demo_session")
        
        if result["success"]:
            # Add demo-specific formatting
            demo_summary = result["summary"].copy()
            demo_summary["demo_note"] = "This demo shows Coinme Sentinel System capabilities"
            demo_summary["presentation_ready"] = True
            
            return PulseSessionResponse(
                success=True,
                session_id=result["session_id"],
                summary=demo_summary,
                detailed_results=result["detailed_results"]
            )
        else:
            raise HTTPException(status_code=500, detail=f"Demo failed: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"CEO demo error: {e}")
        raise HTTPException(status_code=500, detail=f"Demo failed: {str(e)}")


# Error handlers
@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return {
        "error": "Internal server error",
        "detail": str(exc),
        "timestamp": datetime.utcnow().isoformat()
    }


if __name__ == "__main__":
    import uvicorn
    
    # Run the API server
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

"""
Coinme-Specific Configuration for Content Marketing Agent

This module contains all Coinme-specific settings, keywords, and configuration
parameters based on the Coinme agentic plan requirements.
"""

from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum


class MonitoringPlatform(str, Enum):
    """Platforms to monitor for Coinme mentions."""
    TWITTER = "twitter"
    REDDIT = "reddit"
    GOOGLE_REVIEWS = "google_reviews"
    FACEBOOK = "facebook"
    LINKEDIN = "linkedin"


@dataclass
class CoinmeKeywords:
    """Coinme-specific keywords for monitoring and analysis."""
    
    # Primary brand keywords
    brand_keywords: List[str] = None
    
    # Competitor keywords
    competitor_keywords: List[str] = None
    
    # Industry keywords
    industry_keywords: List[str] = None
    
    # Negative sentiment keywords
    negative_keywords: List[str] = None
    
    # Opportunity keywords
    opportunity_keywords: List[str] = None
    
    def __post_init__(self):
        if self.brand_keywords is None:
            self.brand_keywords = [
                "coinme", "@coinme", "coinme atm", "coinme.com",
                "coinme bitcoin", "coinme crypto", "coinme kiosk"
            ]
        
        if self.competitor_keywords is None:
            self.competitor_keywords = [
                "bitcoin depot", "bitcoindepot", "@bitcoindepot",
                "coinstar", "@coinstar", "coinstar bitcoin",
                "moneygram", "@moneygram", "moneygram crypto",
                "bitaccess", "general bytes", "genesis coin"
            ]
        
        if self.industry_keywords is None:
            self.industry_keywords = [
                "crypto atm", "bitcoin atm", "cryptocurrency atm",
                "btc atm", "bitcoin machine", "crypto kiosk",
                "buy bitcoin", "sell bitcoin", "bitcoin cash"
            ]
        
        if self.negative_keywords is None:
            self.negative_keywords = [
                "scam", "fraud", "ripoff", "expensive", "high fees",
                "terrible", "awful", "worst", "avoid", "don't use",
                "overpriced", "rip off", "sketchy", "suspicious"
            ]
        
        if self.opportunity_keywords is None:
            self.opportunity_keywords = [
                "how to", "where can i", "need help", "question",
                "confused", "don't understand", "first time",
                "new to crypto", "beginner", "help me"
            ]


@dataclass
class CoinmeCompetitors:
    """Coinme competitor information and monitoring settings."""
    
    primary_competitors: Dict[str, Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.primary_competitors is None:
            self.primary_competitors = {
                "bitcoin_depot": {
                    "name": "Bitcoin Depot",
                    "handles": ["@bitcoindepot", "bitcoindepot"],
                    "keywords": ["bitcoin depot", "bitcoindepot"],
                    "strengths": ["lower fees", "more locations", "better app"],
                    "weaknesses": ["less regulatory compliance", "newer company"],
                    "monitoring_priority": "high"
                },
                "coinstar": {
                    "name": "Coinstar",
                    "handles": ["@coinstar"],
                    "keywords": ["coinstar", "coinstar bitcoin"],
                    "strengths": ["brand recognition", "grocery store locations"],
                    "weaknesses": ["limited crypto options", "higher fees"],
                    "monitoring_priority": "medium"
                },
                "moneygram": {
                    "name": "MoneyGram",
                    "handles": ["@moneygram"],
                    "keywords": ["moneygram", "moneygram crypto"],
                    "strengths": ["established brand", "global presence"],
                    "weaknesses": ["limited crypto focus", "traditional approach"],
                    "monitoring_priority": "medium"
                }
            }


@dataclass
class CoinmeResponseTemplates:
    """Pre-approved response templates for common scenarios."""
    
    fee_concern_template: str = """
    Hi! We understand fee transparency is important. Our fees reflect the security, 
    compliance, and convenience we provide. We're fully licensed and regulated, 
    ensuring your transactions are safe and compliant. Feel free to DM us for 
    more details about our fee structure! 🔒
    """
    
    scam_concern_template: str = """
    We appreciate your caution! Coinme is a fully licensed and regulated company 
    with proper state licensing. We've been operating since 2014 and are committed 
    to customer protection and regulatory compliance. You can verify our licensing 
    on our website. Happy to answer any questions via DM! ✅
    """
    
    customer_service_template: str = """
    Thank you for sharing your positive experience! We're committed to providing 
    excellent customer service and making crypto accessible for everyone. 
    If you ever need assistance in the future, don't hesitate to reach out! 🙌
    """
    
    competitor_comparison_template: str = """
    Thanks for your question! While we respect all companies in the crypto space, 
    we focus on what makes Coinme unique: regulatory compliance, security, and 
    customer service. We'd love to show you the Coinme difference - DM us to learn more! 💪
    """
    
    general_help_template: str = """
    Happy to help! Coinme makes buying and selling crypto simple and secure. 
    Our ATMs are located nationwide and we provide step-by-step guidance. 
    For personalized assistance, please DM us and we'll walk you through the process! 📱
    """


@dataclass
class CoinmeComplianceRules:
    """Coinme-specific compliance rules and requirements."""
    
    # DFAL (Department of Financial Protection and Innovation) rules
    dfal_rules: Dict[str, str] = None
    
    # Platform-specific rules
    platform_rules: Dict[str, Dict[str, Any]] = None
    
    # Brand guidelines
    brand_guidelines: Dict[str, str] = None
    
    def __post_init__(self):
        if self.dfal_rules is None:
            self.dfal_rules = {
                "daily_limit": "Do not mention specific amounts over $1,000 without proper disclosure",
                "receipt_requirement": "Always mention receipt availability for transactions",
                "licensing_disclosure": "Include licensing information when discussing regulations",
                "fee_transparency": "Be transparent about fee structure when asked",
                "customer_protection": "Emphasize customer protection and security measures"
            }
        
        if self.platform_rules is None:
            self.platform_rules = {
                "twitter": {
                    "character_limit": 280,
                    "hashtag_limit": 2,
                    "mention_limit": 3,
                    "link_policy": "Use branded short links only"
                },
                "reddit": {
                    "self_promotion_limit": "10% of total activity",
                    "community_guidelines": "Follow subreddit-specific rules",
                    "disclosure_required": "Disclose Coinme affiliation"
                }
            }
        
        if self.brand_guidelines is None:
            self.brand_guidelines = {
                "tone": "Professional, helpful, and trustworthy",
                "voice": "Knowledgeable but approachable expert",
                "values": "Security, compliance, customer-first",
                "avoid": "Aggressive sales tactics, competitor attacks, financial advice",
                "emphasize": "Safety, regulation, customer service, education"
            }


@dataclass
class CoinmeMonitoringConfig:
    """Configuration for social media monitoring."""
    
    # Monitoring intervals
    monitoring_intervals: Dict[str, int] = None  # in minutes
    
    # Alert thresholds
    alert_thresholds: Dict[str, int] = None
    
    # Priority keywords for immediate alerts
    priority_keywords: List[str] = None
    
    def __post_init__(self):
        if self.monitoring_intervals is None:
            self.monitoring_intervals = {
                "twitter": 15,  # Check every 15 minutes
                "reddit": 30,   # Check every 30 minutes
                "google_reviews": 60,  # Check every hour
                "facebook": 60,
                "linkedin": 120  # Check every 2 hours
            }
        
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "negative_mentions": 5,  # Alert if 5+ negative mentions in 1 hour
                "scam_mentions": 1,      # Immediate alert for any scam mention
                "competitor_comparison": 3,  # Alert if 3+ competitor comparisons
                "high_urgency_score": 8  # Alert for urgency score >= 8
            }
        
        if self.priority_keywords is None:
            self.priority_keywords = [
                "scam", "fraud", "dfpi", "fine", "lawsuit", "investigation",
                "security breach", "hack", "stolen", "lost money"
            ]


class CoinmeConfig:
    """Main configuration class for Coinme Content Marketing Agent."""
    
    def __init__(self):
        self.keywords = CoinmeKeywords()
        self.competitors = CoinmeCompetitors()
        self.response_templates = CoinmeResponseTemplates()
        self.compliance_rules = CoinmeComplianceRules()
        self.monitoring_config = CoinmeMonitoringConfig()
        
        # Agent settings
        self.agent_settings = {
            "max_responses_per_hour": 10,
            "approval_required_for": ["scam_concerns", "competitor_mentions", "high_urgency"],
            "auto_approve_for": ["positive_feedback", "general_questions"],
            "escalation_triggers": ["legal_threats", "regulatory_mentions", "security_concerns"],
            "response_delay_minutes": 5,  # Minimum delay between responses
            "sentiment_threshold": -0.5,  # Threshold for negative sentiment alerts
        }
        
        # Integration settings
        self.integration_settings = {
            "twitter_api_version": "v2",
            "reddit_api_version": "1",
            "google_business_api": True,
            "webhook_notifications": True,
            "slack_alerts": True,
            "email_escalations": True
        }
    
    def get_keywords_for_platform(self, platform: str) -> List[str]:
        """Get all relevant keywords for a specific platform."""
        all_keywords = (
            self.keywords.brand_keywords +
            self.keywords.competitor_keywords +
            self.keywords.industry_keywords
        )
        
        # Platform-specific keyword filtering could be added here
        return all_keywords
    
    def get_response_template(self, scenario: str) -> str:
        """Get the appropriate response template for a scenario."""
        templates = {
            "fee_concern": self.response_templates.fee_concern_template,
            "scam_concern": self.response_templates.scam_concern_template,
            "customer_service": self.response_templates.customer_service_template,
            "competitor_comparison": self.response_templates.competitor_comparison_template,
            "general_help": self.response_templates.general_help_template
        }
        
        return templates.get(scenario, self.response_templates.general_help_template)
    
    def should_escalate(self, analysis: Dict[str, Any]) -> bool:
        """Determine if a conversation should be escalated to humans."""
        escalation_triggers = self.agent_settings["escalation_triggers"]
        
        # Check for escalation keywords
        content = analysis.get("original_text", "").lower()
        if any(trigger in content for trigger in escalation_triggers):
            return True
        
        # Check urgency score
        if analysis.get("urgency_score", 0) >= 9:
            return True
        
        # Check for legal/regulatory themes
        themes = analysis.get("themes", [])
        if any(theme in ["dfpi_fine", "compliance"] for theme in themes):
            return True
        
        return False


# Global configuration instance
coinme_config = CoinmeConfig()

"""
Content Marketing Agent (CMA) - Coinme Sentinel System Core Implementation

This is the main agent implementation that orchestrates the Market Intelligence Analyst,
Proactive Engagement Specialist, and Compliance Guardian capabilities.
"""

import logging
import asyncio
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import StateGraph, END
from langchain_core.messages import ToolMessage

# Import TKC_v5 infrastructure
from src.services.redis_checkpointer import get_redis_checkpointer
from src.services.rag_service import get_rag_service
from src.services.semantic_search import get_semantic_search_service
from src.config.settings import get_settings

# Import Coinme-specific components
from .state import CoinmePulseState, create_initial_coinme_state, AnalysisObject, EngagementOpportunity
from .config.coinme_config import coinme_config
from .tools.market_intelligence import (
    x_search_tool, reddit_monitor_tool, sentiment_analysis_tool,
    competitor_intelligence_tool, trend_detection_tool
)
from .tools.engagement_tools import (
    assess_engagement_opportunity, generate_response_draft,
    schedule_response_publication, track_engagement_metrics, escalate_to_human
)
from .tools.compliance_tools import (
    compliance_check_tool, digital_rulebook_check, audit_trail_logger
)

logger = logging.getLogger(__name__)
settings = get_settings()


class ContentMarketingAgent:
    """
    Coinme Content Marketing Agent - Sentinel System Implementation
    
    This agent implements the three core capabilities:
    1. Market Intelligence Analyst - Social media monitoring and sentiment analysis
    2. Proactive Engagement Specialist - Opportunity assessment and response generation
    3. Compliance Guardian - Automated compliance checking and audit trails
    """
    
    def __init__(self, project_id: str = "vertex-ai-agent-yzdlnjey"):
        self.project_id = project_id
        self.agent_type = "coinme_content_marketing"
        
        # Agent configuration
        agent_config = settings.agent_config
        self.location = agent_config.location
        self.model_name = agent_config.model_name
        
        # Initialize services
        self.redis_checkpointer = None
        self.rag_service = None
        self.semantic_search = None
        
        # Initialize the LLM with Gemini 2.5 Flash
        self.llm = ChatVertexAI(
            project=self.project_id,
            location=self.location,
            model_name=self.model_name,
            temperature=0.3,  # Slightly higher for creative responses
        )
        
        # Define all available tools
        self.tools = [
            # Market Intelligence tools
            x_search_tool,
            reddit_monitor_tool,
            sentiment_analysis_tool,
            competitor_intelligence_tool,
            trend_detection_tool,
            
            # Engagement tools
            assess_engagement_opportunity,
            generate_response_draft,
            schedule_response_publication,
            track_engagement_metrics,
            escalate_to_human,
            
            # Compliance tools
            compliance_check_tool,
            digital_rulebook_check,
            audit_trail_logger
        ]
        
        # Bind tools to the LLM
        self.llm_with_tools = self.llm.bind_tools(self.tools)
        
        # Build the workflow graph
        self.app = self._build_graph()
    
    async def initialize_services(self):
        """Initialize shared TKC_v5 services."""
        try:
            # Initialize Redis checkpointer
            self.redis_checkpointer = await get_redis_checkpointer(settings)
            
            # Initialize RAG services
            self.rag_service = await get_rag_service(settings)
            self.semantic_search = await get_semantic_search_service(settings)
            
            # Rebuild graph with checkpointer
            self.app = self._build_graph()
            
            logger.info("Coinme Content Marketing Agent services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize CMA services: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build the Coinme Sentinel LangGraph workflow."""
        workflow = StateGraph(CoinmePulseState)
        
        # Add workflow nodes
        workflow.add_node("initialize", self._initialize_pulse)
        workflow.add_node("market_intelligence", self._market_intelligence_analyst)
        workflow.add_node("engagement_assessment", self._engagement_specialist)
        workflow.add_node("compliance_check", self._compliance_guardian)
        workflow.add_node("call_model", self._call_model)
        workflow.add_node("execute_tools", self._execute_tools)
        workflow.add_node("finalize", self._finalize_pulse)
        
        # Set the entry point
        workflow.set_entry_point("initialize")
        
        # Define the workflow edges
        workflow.add_edge("initialize", "market_intelligence")
        workflow.add_edge("market_intelligence", "engagement_assessment")
        workflow.add_edge("engagement_assessment", "compliance_check")
        workflow.add_edge("compliance_check", "call_model")
        workflow.add_conditional_edges(
            "call_model",
            self._should_continue,
            {
                "continue": "execute_tools",
                "end": "finalize"
            }
        )
        workflow.add_edge("execute_tools", "call_model")
        workflow.add_edge("finalize", END)
        
        # Compile with checkpointer if available
        if self.redis_checkpointer and self.redis_checkpointer.get_saver():
            checkpointer = self.redis_checkpointer.get_saver()
            return workflow.compile(checkpointer=checkpointer)
        else:
            return workflow.compile()
    
    async def _initialize_pulse(self, state: CoinmePulseState) -> CoinmePulseState:
        """Initialize the Coinme Pulse monitoring session."""
        logger.info(f"Initializing Coinme Pulse session: {state.session_id}")
        
        state.update_step("initialize")
        state.add_compliance_log("Coinme Pulse session started")
        
        # Add initial system message
        system_message = SystemMessage(content="""
        You are the Coinme Sentinel System, an advanced AI agent designed to monitor, 
        analyze, and respond to social media conversations about Coinme and cryptocurrency ATMs.
        
        Your mission is to:
        1. Monitor social media for Coinme mentions and relevant conversations
        2. Analyze sentiment, emotions, and engagement opportunities
        3. Generate compliant, helpful responses that build trust and address concerns
        4. Ensure all actions comply with DFAL regulations and platform policies
        
        You have access to specialized tools for market intelligence, engagement assessment,
        and compliance checking. Use these tools proactively to complete your mission.
        
        Always prioritize:
        - Customer safety and trust
        - Regulatory compliance
        - Brand reputation protection
        - Helpful, educational responses
        """)
        
        state.messages.append(system_message)
        
        return state
    
    async def _market_intelligence_analyst(self, state: CoinmePulseState) -> CoinmePulseState:
        """Market Intelligence Analyst - Monitor and analyze social media."""
        logger.info(f"Running Market Intelligence Analyst for session: {state.session_id}")
        
        state.update_step("market_intelligence")
        
        try:
            # Get Coinme keywords for monitoring
            keywords = coinme_config.get_keywords_for_platform("twitter")
            
            # Search for new mentions (mock data for demo)
            new_mentions = [
                {
                    "id": "tweet_123",
                    "text": "Just used a @Coinme ATM and the fees were way too high! $15 to buy $100 of Bitcoin? That's insane!",
                    "platform": "twitter",
                    "author": "crypto_user_2024",
                    "timestamp": datetime.utcnow().isoformat(),
                    "engagement": {"likes": 12, "retweets": 5, "replies": 3}
                },
                {
                    "id": "reddit_456", 
                    "text": "Has anyone used Coinme ATMs? Are they legit or is this some kind of scam? Seeing mixed reviews online.",
                    "platform": "reddit",
                    "author": "newbie_crypto",
                    "timestamp": datetime.utcnow().isoformat(),
                    "engagement": {"upvotes": 23, "comments": 8}
                }
            ]
            
            state.new_mentions = new_mentions
            
            # Analyze each mention
            for mention in new_mentions:
                # Perform sentiment analysis
                analysis_result = await sentiment_analysis_tool.ainvoke({
                    "text": mention["text"],
                    "platform": mention["platform"]
                })
                
                # Create analysis object
                analysis = AnalysisObject(
                    platform=mention["platform"],
                    content_id=mention["id"],
                    author_id=mention["author"],
                    timestamp=datetime.fromisoformat(mention["timestamp"]),
                    original_text=mention["text"],
                    sentiment=analysis_result.get("sentiment", "neutral"),
                    emotions=analysis_result.get("emotions", []),
                    themes=analysis_result.get("themes", []),
                    urgency_score=analysis_result.get("urgency_score", 1),
                    is_opportunity=analysis_result.get("requires_response", False),
                    opportunity_reason=analysis_result.get("key_phrases", []),
                    competitor_mentioned=analysis_result.get("competitor_mentioned"),
                    confidence_score=analysis_result.get("confidence_score", 0.0)
                )
                
                state.add_analysis(analysis)
            
            logger.info(f"Market Intelligence: Analyzed {len(new_mentions)} mentions")
            
        except Exception as e:
            state.add_error(f"Market Intelligence error: {str(e)}")
            logger.error(f"Market Intelligence Analyst error: {e}")
        
        return state
    
    async def _engagement_specialist(self, state: CoinmePulseState) -> CoinmePulseState:
        """Proactive Engagement Specialist - Assess opportunities and generate responses."""
        logger.info(f"Running Engagement Specialist for session: {state.session_id}")
        
        state.update_step("engagement_assessment")
        
        try:
            # Process each analyzed conversation for engagement opportunities
            for analysis in state.analyzed_conversations:
                if analysis.is_opportunity:
                    # Assess engagement opportunity
                    opportunity_assessment = await assess_engagement_opportunity.ainvoke({
                        "analysis": analysis.dict(),
                        "platform": analysis.platform
                    })
                    
                    if opportunity_assessment.get("is_opportunity", False):
                        # Create engagement opportunity
                        opportunity = EngagementOpportunity(
                            analysis=analysis,
                            opportunity_type=opportunity_assessment.get("recommended_approach", "general"),
                            priority=opportunity_assessment.get("priority", "medium"),
                            recommended_approach=opportunity_assessment.get("recommended_approach", ""),
                            key_talking_points=opportunity_assessment.get("talking_points", [])
                        )
                        
                        state.add_opportunity(opportunity)
                        
                        # Generate response draft if high priority
                        if opportunity.priority in ["high", "medium"]:
                            draft = await generate_response_draft.ainvoke({
                                "original_text": analysis.original_text,
                                "analysis": analysis.dict(),
                                "opportunity_assessment": opportunity_assessment,
                                "platform": analysis.platform
                            })
                            
                            if not draft.get("error"):
                                state.add_draft(draft)
            
            logger.info(f"Engagement Specialist: Found {len(state.engagement_opportunities)} opportunities")
            
        except Exception as e:
            state.add_error(f"Engagement Specialist error: {str(e)}")
            logger.error(f"Engagement Specialist error: {e}")
        
        return state
    
    async def _compliance_guardian(self, state: CoinmePulseState) -> CoinmePulseState:
        """Compliance Guardian - Check all responses for compliance."""
        logger.info(f"Running Compliance Guardian for session: {state.session_id}")
        
        state.update_step("compliance_check")
        
        try:
            # Check compliance for all drafted responses
            for draft in state.drafted_responses:
                compliance_result = await compliance_check_tool.ainvoke({
                    "content": draft.get("response_text", ""),
                    "content_type": "response",
                    "platform": draft.get("target_platform", "twitter")
                })
                
                # Update draft with compliance status
                draft["compliance_status"] = compliance_result.get("status", "pending")
                draft["compliance_notes"] = compliance_result.get("violations", []) + compliance_result.get("warnings", [])
                
                # Log compliance check
                await audit_trail_logger.ainvoke({
                    "action": "compliance_check",
                    "content": draft.get("response_text", ""),
                    "compliance_result": compliance_result,
                    "user_id": "coinme_sentinel_system"
                })
                
                state.add_compliance_log(f"Compliance check completed for draft {draft.get('draft_id')}: {compliance_result.get('status')}")
            
            logger.info(f"Compliance Guardian: Checked {len(state.drafted_responses)} drafts")
            
        except Exception as e:
            state.add_error(f"Compliance Guardian error: {str(e)}")
            logger.error(f"Compliance Guardian error: {e}")
        
        return state

    async def _call_model(self, state: CoinmePulseState) -> CoinmePulseState:
        """Call the LLM for decision making and tool selection."""
        logger.info(f"Calling model for session: {state.session_id}")

        try:
            # Build context message
            context_message = self._build_context_message(state)

            # Add to messages
            state.messages.append(context_message)

            # Call the model
            response = await self.llm_with_tools.ainvoke(state.messages)

            # Update state
            state.messages.append(response)

            # Check for tool calls
            if hasattr(response, 'tool_calls') and response.tool_calls:
                state.current_step = "tool_execution"
                logger.info(f"Model generated {len(response.tool_calls)} tool calls")
            else:
                state.should_continue = False
                logger.info("Model completed without tool calls")

        except Exception as e:
            state.add_error(f"Model call error: {str(e)}")
            logger.error(f"Model call error: {e}")
            state.should_continue = False

        return state

    def _should_continue(self, state: CoinmePulseState) -> str:
        """Determine if workflow should continue or end."""
        if state.should_continue and state.current_step == "tool_execution":
            return "continue"
        return "end"

    async def _execute_tools(self, state: CoinmePulseState) -> CoinmePulseState:
        """Execute tools called by the model."""
        logger.info(f"Executing tools for session: {state.session_id}")

        try:
            # Get the last message which should contain tool calls
            last_message = state.messages[-1]

            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                for tool_call in last_message.tool_calls:
                    try:
                        tool_name = tool_call.get("name")
                        tool_args = tool_call.get("args", {})

                        # Find and execute the tool
                        for tool in self.tools:
                            if tool.name == tool_name:
                                result = await tool.ainvoke(tool_args)

                                # Add tool result to messages
                                tool_message = ToolMessage(
                                    content=str(result),
                                    tool_call_id=tool_call.get("id", "")
                                )
                                state.messages.append(tool_message)
                                break
                        else:
                            logger.warning(f"Tool not found: {tool_name}")

                    except Exception as e:
                        logger.error(f"Tool execution error for {tool_name}: {e}")
                        error_message = ToolMessage(
                            content=f"Error executing {tool_name}: {str(e)}",
                            tool_call_id=tool_call.get("id", "")
                        )
                        state.messages.append(error_message)

            # Reset for next iteration
            state.should_continue = True

        except Exception as e:
            state.add_error(f"Tool execution error: {str(e)}")
            logger.error(f"Tool execution error: {e}")
            state.should_continue = False

        return state

    async def _finalize_pulse(self, state: CoinmePulseState) -> CoinmePulseState:
        """Finalize the Coinme Pulse session."""
        logger.info(f"Finalizing Coinme Pulse session: {state.session_id}")

        state.update_step("finalize")
        state.processing_complete = True

        # Generate session summary
        summary = {
            "session_id": state.session_id,
            "total_mentions_analyzed": len(state.analyzed_conversations),
            "engagement_opportunities_found": len(state.engagement_opportunities),
            "responses_drafted": len(state.drafted_responses),
            "compliance_checks_performed": len([log for log in state.compliance_log if "compliance_check" in log]),
            "errors_encountered": len(state.error_log),
            "session_duration": (datetime.utcnow() - state.started_at).total_seconds(),
            "processing_complete": True
        }

        state.add_compliance_log(f"Session completed: {summary}")

        logger.info(f"Coinme Pulse session completed: {summary}")

        return state

    def _build_context_message(self, state: CoinmePulseState) -> HumanMessage:
        """Build a context message for the model."""
        context = f"""
        Coinme Pulse Session Status:

        Current Step: {state.current_step}
        Session ID: {state.session_id}

        Analysis Summary:
        - Total mentions analyzed: {len(state.analyzed_conversations)}
        - Engagement opportunities: {len(state.engagement_opportunities)}
        - Drafted responses: {len(state.drafted_responses)}

        Recent Activity:
        """

        # Add recent analysis results
        if state.analyzed_conversations:
            context += "\nRecent Conversations Analyzed:\n"
            for analysis in state.analyzed_conversations[-3:]:  # Last 3
                context += f"- {analysis.platform}: {analysis.sentiment} sentiment, urgency {analysis.urgency_score}/10\n"
                context += f"  Themes: {', '.join(analysis.themes)}\n"
                context += f"  Opportunity: {analysis.is_opportunity}\n\n"

        # Add engagement opportunities
        if state.engagement_opportunities:
            context += "\nEngagement Opportunities:\n"
            for opp in state.engagement_opportunities[-3:]:  # Last 3
                context += f"- {opp.priority} priority: {opp.opportunity_type}\n"
                context += f"  Approach: {opp.recommended_approach}\n\n"

        # Add compliance status
        if state.drafted_responses:
            context += "\nCompliance Status:\n"
            for draft in state.drafted_responses[-3:]:  # Last 3
                context += f"- Draft {draft.get('draft_id', 'unknown')}: {draft.get('compliance_status', 'pending')}\n"

        context += f"\nNext Action: Continue processing or finalize session based on current state."

        return HumanMessage(content=context)

    async def execute_pulse_session(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Execute a complete Coinme Pulse monitoring session."""
        try:
            # Create session ID if not provided
            if session_id is None:
                session_id = f"coinme_pulse_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

            # Create initial state
            initial_state = create_initial_coinme_state(session_id)

            # Execute the workflow
            final_state = await self.app.ainvoke(
                initial_state,
                config={"configurable": {"thread_id": session_id}}
            )

            # Return session summary
            return {
                "success": True,
                "session_id": session_id,
                "summary": {
                    "mentions_analyzed": len(final_state.analyzed_conversations),
                    "opportunities_found": len(final_state.engagement_opportunities),
                    "responses_drafted": len(final_state.drafted_responses),
                    "compliance_checks": len([log for log in final_state.compliance_log if "compliance_check" in log]),
                    "errors": len(final_state.error_log),
                    "processing_complete": final_state.processing_complete
                },
                "detailed_results": {
                    "analyzed_conversations": [analysis.dict() for analysis in final_state.analyzed_conversations],
                    "engagement_opportunities": [opp.dict() for opp in final_state.engagement_opportunities],
                    "drafted_responses": final_state.drafted_responses,
                    "compliance_log": final_state.compliance_log,
                    "error_log": final_state.error_log
                }
            }

        except Exception as e:
            logger.error(f"Coinme Pulse session execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "session_id": session_id
            }

"""
Calendar Agent Implementation - TKC_v5 Template Example

This serves as a concrete example of how to implement a specialized agent
using the TKC_v5 template framework.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import StateGraph, END
from langchain_core.messages import ToolMessage

# Import shared infrastructure
from src.services.redis_checkpointer import get_redis_checkpointer
from src.services.rag_service import get_rag_service
from src.services.semantic_search import get_semantic_search_service

# Import base agent framework
from agent.state import (
    AgentState,
    TaskRequest,
    TaskResponse,
    TaskStatus,
    TaskType,
    create_initial_state,
    state_to_response
)
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


# Calendar-specific tools
@tool
async def check_calendar_availability(
    start_time: str,
    end_time: str,
    calendar_id: str = "primary"
) -> str:
    """
    Check calendar availability for a specific time range.
    
    Args:
        start_time: Start time in ISO format (e.g., "2025-07-27T10:00:00Z")
        end_time: End time in ISO format (e.g., "2025-07-27T11:00:00Z")
        calendar_id: Calendar ID to check (default: "primary")
        
    Returns:
        Availability status and conflicting events if any
    """
    try:
        # Initialize Google Calendar client
        calendar_client = await create_calendar_client()
        
        # Check for conflicts
        conflicts = await calendar_client.check_conflicts(
            start_time=start_time,
            end_time=end_time,
            calendar_id=calendar_id
        )
        
        if not conflicts:
            return f"✅ Available: {start_time} to {end_time}"
        else:
            conflict_details = "\n".join([
                f"- {event['summary']} ({event['start']} - {event['end']})"
                for event in conflicts
            ])
            return f"❌ Conflicts found:\n{conflict_details}"
            
    except Exception as e:
        return f"Error checking availability: {str(e)}"


@tool
async def create_calendar_event(
    title: str,
    start_time: str,
    end_time: str,
    attendees: List[str] = None,
    description: str = "",
    location: str = ""
) -> str:
    """
    Create a new calendar event.
    
    Args:
        title: Event title
        start_time: Start time in ISO format
        end_time: End time in ISO format
        attendees: List of attendee email addresses
        description: Event description
        location: Event location
        
    Returns:
        Event creation confirmation with event ID
    """
    try:
        calendar_client = await create_calendar_client()
        
        event_data = {
            'summary': title,
            'start': {'dateTime': start_time, 'timeZone': 'America/Chicago'},
            'end': {'dateTime': end_time, 'timeZone': 'America/Chicago'},
            'description': description,
            'location': location
        }
        
        if attendees:
            event_data['attendees'] = [{'email': email} for email in attendees]
        
        event = await calendar_client.create_event(event_data)
        
        return f"✅ Event created: {title} (ID: {event['id']})"
        
    except Exception as e:
        return f"Error creating event: {str(e)}"


@tool
async def find_meeting_slots(
    duration_minutes: int,
    attendee_emails: List[str],
    preferred_times: List[str] = None,
    days_ahead: int = 7
) -> str:
    """
    Find available meeting slots for multiple attendees.
    
    Args:
        duration_minutes: Meeting duration in minutes
        attendee_emails: List of attendee email addresses
        preferred_times: Preferred time slots (e.g., ["09:00", "14:00"])
        days_ahead: Number of days to look ahead
        
    Returns:
        List of available meeting slots
    """
    try:
        calendar_client = await create_calendar_client()
        
        # Find available slots
        available_slots = await calendar_client.find_available_slots(
            duration_minutes=duration_minutes,
            attendee_emails=attendee_emails,
            preferred_times=preferred_times,
            days_ahead=days_ahead
        )
        
        if not available_slots:
            return "❌ No available slots found for all attendees"
        
        slot_list = "\n".join([
            f"- {slot['start']} to {slot['end']} ({slot['day']})"
            for slot in available_slots[:5]  # Show top 5 options
        ])
        
        return f"✅ Available meeting slots:\n{slot_list}"
        
    except Exception as e:
        return f"Error finding meeting slots: {str(e)}"


async def create_calendar_client():
    """Create and authenticate Google Calendar client."""
    # Implementation would use Google Calendar API
    # This is a placeholder for the actual implementation
    pass


class CalendarAgent:
    """
    Calendar Agent for meeting scheduling and calendar management.
    
    This agent specializes in:
    - Meeting scheduling and coordination
    - Calendar availability checking
    - Event creation and management
    - Multi-attendee scheduling optimization
    """
    
    def __init__(self, project_id: str = "vertex-ai-agent-yzdlnjey"):
        self.project_id = project_id
        self.agent_type = "calendar"
        
        # Agent configuration
        agent_config = settings.agent_config
        self.location = agent_config.location
        self.model_name = agent_config.model_name
        self.redis_checkpointer = None
        self.rag_service = None
        self.semantic_search = None
        
        # Initialize the LLM
        self.llm = ChatVertexAI(
            project=self.project_id,
            location=self.location,
            model_name=self.model_name,
            temperature=agent_config.temperature,
        )
        
        # Define calendar-specific tools
        self.tools = [
            check_calendar_availability,
            create_calendar_event,
            find_meeting_slots
        ]
        
        # Bind tools to the LLM
        self.llm_with_tools = self.llm.bind_tools(self.tools)
        
        # Build the workflow graph
        self.app = self._build_graph()
    
    async def initialize_services(self):
        """Initialize shared services (Redis, RAG, etc.)."""
        try:
            # Initialize Redis checkpointer
            self.redis_checkpointer = await get_redis_checkpointer(settings)
            
            # Initialize RAG services
            self.rag_service = await get_rag_service(settings)
            self.semantic_search = await get_semantic_search_service(settings)
            
            # Rebuild graph with checkpointer
            self.app = self._build_graph()
            
            logger.info("Calendar agent services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize calendar agent services: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build the Calendar Agent LangGraph workflow."""
        workflow = StateGraph(AgentState)
        
        # Add standard workflow nodes
        workflow.add_node("initialize", self._initialize_task)
        workflow.add_node("classify_intent", self._classify_intent)
        workflow.add_node("process_calendar", self._process_calendar)
        workflow.add_node("call_model", self._call_model)
        workflow.add_node("execute_tools", self._execute_tools)
        workflow.add_node("finalize", self._finalize_task)
        
        # Set the entry point
        workflow.set_entry_point("initialize")
        
        # Define the workflow edges
        workflow.add_edge("initialize", "classify_intent")
        workflow.add_conditional_edges(
            "classify_intent",
            self._route_after_classification,
            {
                "calendar_task": "process_calendar",
                "general_task": "call_model"
            }
        )
        workflow.add_edge("process_calendar", "call_model")
        workflow.add_conditional_edges(
            "call_model",
            self._should_continue,
            {
                "continue": "execute_tools",
                "end": "finalize"
            }
        )
        workflow.add_edge("execute_tools", "call_model")
        workflow.add_edge("finalize", END)
        
        # Compile with checkpointer if available
        if self.redis_checkpointer and self.redis_checkpointer.get_saver():
            checkpointer = self.redis_checkpointer.get_saver()
            return workflow.compile(checkpointer=checkpointer)
        else:
            return workflow.compile()
    
    async def _initialize_task(self, state: AgentState) -> AgentState:
        """Initialize calendar task processing."""
        logger.info(f"Initializing calendar task: {state.task_id}")
        
        state.steps_completed.append("initialize")
        state.context["start_time"] = time.time()
        state.context["agent_type"] = "calendar"
        
        return state
    
    async def _classify_intent(self, state: AgentState) -> AgentState:
        """Classify the intent of the calendar request."""
        logger.info(f"Classifying intent for task: {state.task_id}")
        
        # Calendar-specific intent classification
        message = state.input_data.get("message", "").lower()
        
        calendar_keywords = [
            "schedule", "meeting", "appointment", "calendar", "available",
            "book", "reserve", "time", "slot", "conflict", "busy"
        ]
        
        if any(keyword in message for keyword in calendar_keywords):
            state.context["route"] = "calendar_task"
        else:
            state.context["route"] = "general_task"
        
        state.steps_completed.append("classify_intent")
        logger.info(f"Intent classified, routing to: {state.context['route']}")
        
        return state
    
    async def _process_calendar(self, state: AgentState) -> AgentState:
        """Process calendar-specific tasks."""
        logger.info(f"Processing calendar task: {state.task_id}")
        
        # Enhance context with calendar-specific information
        if self.semantic_search:
            try:
                # Retrieve relevant calendar context
                query = state.input_data.get("message", "")
                context = await self.semantic_search.search_conversations(
                    query=query,
                    customer_id=state.input_data.get("customer_id", "default"),
                    limit=3
                )
                
                if context:
                    state.context["calendar_context"] = context
                    logger.info("Enhanced calendar context retrieved")
                    
            except Exception as e:
                logger.warning(f"Failed to retrieve calendar context: {e}")
        
        state.steps_completed.append("process_calendar")
        return state
    
    async def _call_model(self, state: AgentState) -> AgentState:
        """Call the LLM with calendar-specific prompting."""
        logger.info(f"Calling model for task: {state.task_id}")
        
        # Calendar-specific system prompt
        system_prompt = """
        You are a professional calendar and scheduling assistant for TKC Group.
        Your primary role is to help with meeting scheduling, calendar management, and availability coordination.
        
        CORE CAPABILITIES:
        - Check calendar availability for specific time ranges
        - Create calendar events with proper details
        - Find optimal meeting slots for multiple attendees
        - Handle timezone considerations and conflicts
        - Provide professional scheduling recommendations
        
        CALENDAR RULES:
        - Always check availability before scheduling
        - Consider all attendees when finding meeting slots
        - Use clear, professional language in event descriptions
        - Default to Central Time Zone unless specified otherwise
        - Suggest alternative times if conflicts exist
        
        Use available tools proactively to complete calendar tasks efficiently.
        """
        
        # Build message history
        messages = [SystemMessage(content=system_prompt)]
        
        # Add conversation history if available
        for msg in state.messages:
            messages.append(msg)
        
        # Add current request
        user_message = state.input_data.get("message", "")
        if state.context.get("calendar_context"):
            user_message += f"\n\nRelevant context: {state.context['calendar_context']}"
        
        messages.append(HumanMessage(content=user_message))
        
        # Call the model
        response = await self.llm_with_tools.ainvoke(messages)
        
        # Update state
        state.messages.extend([messages[-1], response])
        
        # Check for tool calls
        if hasattr(response, 'tool_calls') and response.tool_calls:
            state.context["pending_tool_calls"] = response.tool_calls
            logger.info(f"Model generated tool calls: {response.tool_calls}")
        
        return state
    
    def _route_after_classification(self, state: AgentState) -> str:
        """Route to appropriate processing based on classification."""
        return state.context.get("route", "general_task")
    
    def _should_continue(self, state: AgentState) -> str:
        """Determine if workflow should continue or end."""
        if state.context.get("pending_tool_calls"):
            return "continue"
        return "end"
    
    async def _execute_tools(self, state: AgentState) -> AgentState:
        """Execute calendar tools."""
        # Tool execution logic (similar to main agent)
        # This would implement the actual tool calling
        return state
    
    async def _finalize_task(self, state: AgentState) -> AgentState:
        """Finalize calendar task processing."""
        logger.info(f"Finalizing calendar task: {state.task_id}")
        
        state.status = TaskStatus.COMPLETED
        state.steps_completed.append("finalize")
        
        return state
    
    async def execute_task(self, task_request: TaskRequest) -> TaskResponse:
        """Execute a calendar task request."""
        try:
            # Create initial state
            initial_state = create_initial_state(task_request)
            
            # Execute the workflow
            final_state = await self.app.ainvoke(
                initial_state,
                config={"configurable": {"thread_id": task_request.task_id}}
            )
            
            # Convert to response
            return state_to_response(final_state)
            
        except Exception as e:
            logger.error(f"Calendar task execution failed: {e}")
            return TaskResponse(
                task_id=task_request.task_id,
                success=False,
                error=str(e),
                results={}
            )

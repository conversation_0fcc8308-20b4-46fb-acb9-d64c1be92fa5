"""
Sales Development Agent - TKC_v5 Commercial Package

Specializes in lead generation, qualification, outreach, and pipeline management.
Integrates with CRM systems for comprehensive sales automation.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import StateGraph, END
from langchain_core.messages import ToolMessage

# Import shared infrastructure
from src.services.redis_checkpointer import get_redis_checkpointer
from src.services.rag_service import get_rag_service
from src.services.semantic_search import get_semantic_search_service
from src.services.crm_client import get_crm_client, CRMClientError

# Import base agent framework
from agent.state import (
    AgentState,
    TaskRequest,
    TaskResponse,
    TaskStatus,
    TaskType,
    create_initial_state,
    state_to_response
)
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


# Sales Development specific tools
@tool
async def qualify_lead(
    contact_info: Dict[str, Any],
    conversation_history: str = "",
    qualification_criteria: Dict[str, Any] = None
) -> str:
    """
    Analyze and qualify a lead based on contact information and conversation history.
    
    Args:
        contact_info: Contact details (email, company, role, etc.)
        conversation_history: Previous interactions with the lead
        qualification_criteria: Custom scoring criteria
        
    Returns:
        Lead qualification score and analysis
    """
    try:
        # Default qualification criteria
        default_criteria = {
            'company_size': {'weight': 0.3, 'indicators': ['employees', 'revenue']},
            'decision_maker': {'weight': 0.25, 'indicators': ['title', 'role']},
            'budget_indicators': {'weight': 0.2, 'indicators': ['funding', 'growth']},
            'engagement_level': {'weight': 0.25, 'indicators': ['responses', 'questions']}
        }
        
        criteria = qualification_criteria or default_criteria
        
        # Calculate qualification score
        total_score = 0
        analysis_details = []
        
        # Company size analysis
        company = contact_info.get('company', '').lower()
        if any(indicator in company for indicator in ['enterprise', 'corp', 'inc']):
            company_score = 8
            analysis_details.append("✅ Enterprise company indicator")
        elif contact_info.get('company_size', 0) > 100:
            company_score = 7
            analysis_details.append("✅ Medium-large company size")
        else:
            company_score = 5
            analysis_details.append("⚠️ Small company size")
        
        total_score += company_score * criteria['company_size']['weight']
        
        # Decision maker analysis
        title = contact_info.get('title', '').lower()
        decision_maker_keywords = ['ceo', 'cto', 'vp', 'director', 'manager', 'head']
        if any(keyword in title for keyword in decision_maker_keywords):
            dm_score = 9
            analysis_details.append("✅ Decision maker role identified")
        else:
            dm_score = 4
            analysis_details.append("⚠️ Non-decision maker role")
        
        total_score += dm_score * criteria['decision_maker']['weight']
        
        # Engagement analysis
        if conversation_history:
            if len(conversation_history) > 500:
                engagement_score = 8
                analysis_details.append("✅ High engagement in conversations")
            elif len(conversation_history) > 100:
                engagement_score = 6
                analysis_details.append("✅ Moderate engagement")
            else:
                engagement_score = 3
                analysis_details.append("⚠️ Low engagement")
        else:
            engagement_score = 2
            analysis_details.append("❌ No conversation history")
        
        total_score += engagement_score * criteria['engagement_level']['weight']
        
        # Budget indicators (simplified)
        budget_score = 5  # Default neutral score
        total_score += budget_score * criteria['budget_indicators']['weight']
        
        # Final score (0-10 scale)
        final_score = min(10, max(0, total_score))
        
        # Qualification level
        if final_score >= 8:
            qualification = "🔥 HOT LEAD"
        elif final_score >= 6:
            qualification = "🟡 WARM LEAD"
        elif final_score >= 4:
            qualification = "🟢 COLD LEAD"
        else:
            qualification = "❄️ UNQUALIFIED"
        
        result = f"""Lead Qualification Analysis:
        
Contact: {contact_info.get('first_name', '')} {contact_info.get('last_name', '')}
Company: {contact_info.get('company', 'Unknown')}
Title: {contact_info.get('title', 'Unknown')}

Qualification Score: {final_score:.1f}/10
Status: {qualification}

Analysis Details:
{chr(10).join(analysis_details)}

Recommended Actions:
{_get_recommended_actions(final_score)}
"""
        
        return result
        
    except Exception as e:
        return f"Error qualifying lead: {str(e)}"


@tool
async def create_outreach_sequence(
    lead_id: str,
    sequence_type: str = "initial_outreach",
    personalization_data: Dict[str, Any] = None
) -> str:
    """
    Generate a personalized outreach sequence for a qualified lead.
    
    Args:
        lead_id: CRM lead/contact ID
        sequence_type: Type of sequence (initial_outreach, follow_up, re_engagement)
        personalization_data: Additional data for personalization
        
    Returns:
        Generated outreach sequence with timing and content
    """
    try:
        # Get lead information from CRM
        crm_client = await get_crm_client()
        contact = await crm_client.get_contact(lead_id)
        
        if not contact:
            return f"Error: Could not find lead {lead_id} in CRM"
        
        # Personalization data
        first_name = contact.get('first_name', 'there')
        company = contact.get('company', 'your company')
        title = contact.get('title', 'your role')
        
        # Generate sequence based on type
        if sequence_type == "initial_outreach":
            sequence = _generate_initial_outreach_sequence(first_name, company, title)
        elif sequence_type == "follow_up":
            sequence = _generate_follow_up_sequence(first_name, company)
        elif sequence_type == "re_engagement":
            sequence = _generate_re_engagement_sequence(first_name, company)
        else:
            return f"Error: Unknown sequence type {sequence_type}"
        
        # Log activity in CRM
        await crm_client.log_activity({
            'type': 'EMAIL',
            'description': f'Created {sequence_type} outreach sequence',
            'contact_id': lead_id,
            'timestamp': datetime.utcnow().isoformat()
        })
        
        return f"""Outreach Sequence Created for {first_name} at {company}:

{sequence}

✅ Sequence logged in CRM
📧 Ready for execution"""
        
    except Exception as e:
        return f"Error creating outreach sequence: {str(e)}"


@tool
async def update_deal_stage(
    deal_id: str,
    new_stage: str,
    notes: str = "",
    next_action: str = ""
) -> str:
    """
    Update a deal's stage in the CRM pipeline with notes and next actions.
    
    Args:
        deal_id: CRM deal ID
        new_stage: New pipeline stage
        notes: Notes about the stage change
        next_action: Recommended next action
        
    Returns:
        Confirmation of deal stage update
    """
    try:
        crm_client = await get_crm_client()
        
        # Get current deal information
        deal = await crm_client.get_deal(deal_id)
        if not deal:
            return f"Error: Could not find deal {deal_id} in CRM"
        
        current_stage = deal.get('stage', 'unknown')
        
        # Update deal stage
        success = await crm_client.update_deal(deal_id, {
            'stage': new_stage
        })
        
        if success:
            # Log the stage change activity
            activity_description = f"Deal stage updated: {current_stage} → {new_stage}"
            if notes:
                activity_description += f"\nNotes: {notes}"
            if next_action:
                activity_description += f"\nNext Action: {next_action}"
            
            await crm_client.log_activity({
                'type': 'NOTE',
                'description': activity_description,
                'deal_id': deal_id,
                'timestamp': datetime.utcnow().isoformat()
            })
            
            return f"""✅ Deal Stage Updated Successfully

Deal: {deal.get('name', 'Unknown')}
Previous Stage: {current_stage}
New Stage: {new_stage}

Notes: {notes or 'No additional notes'}
Next Action: {next_action or 'No specific action defined'}

📝 Activity logged in CRM"""
        else:
            return f"❌ Failed to update deal stage in CRM"
            
    except Exception as e:
        return f"Error updating deal stage: {str(e)}"


@tool
async def track_pipeline_metrics(
    date_range: str = "last_30_days",
    pipeline_name: str = "default"
) -> str:
    """
    Analyze pipeline performance and provide insights.
    
    Args:
        date_range: Time period for analysis
        pipeline_name: Specific pipeline to analyze
        
    Returns:
        Pipeline performance analysis and recommendations
    """
    try:
        # This would integrate with analytics service in full implementation
        # For now, return mock data structure
        
        metrics = {
            'total_deals': 45,
            'closed_won': 12,
            'closed_lost': 8,
            'in_progress': 25,
            'win_rate': 60.0,
            'average_deal_size': 15000,
            'average_sales_cycle': 45,
            'pipeline_value': 375000
        }
        
        analysis = f"""📊 Pipeline Performance Analysis ({date_range})

Key Metrics:
• Total Deals: {metrics['total_deals']}
• Closed Won: {metrics['closed_won']}
• Closed Lost: {metrics['closed_lost']}
• In Progress: {metrics['in_progress']}

Performance Indicators:
• Win Rate: {metrics['win_rate']}%
• Average Deal Size: ${metrics['average_deal_size']:,}
• Average Sales Cycle: {metrics['average_sales_cycle']} days
• Total Pipeline Value: ${metrics['pipeline_value']:,}

🎯 Recommendations:
• Focus on deals in qualification stage (highest conversion potential)
• Follow up on stalled deals older than 30 days
• Increase outreach to maintain pipeline flow
• Review lost deals for common objection patterns

📈 Trending: Pipeline value up 15% from previous period"""
        
        return analysis
        
    except Exception as e:
        return f"Error tracking pipeline metrics: {str(e)}"


def _get_recommended_actions(score: float) -> str:
    """Get recommended actions based on qualification score."""
    if score >= 8:
        return "• Schedule demo/discovery call immediately\n• Assign to senior sales rep\n• Fast-track through pipeline"
    elif score >= 6:
        return "• Send personalized outreach sequence\n• Schedule qualification call\n• Nurture with relevant content"
    elif score >= 4:
        return "• Add to nurture campaign\n• Send educational content\n• Re-qualify in 30 days"
    else:
        return "• Add to general newsletter\n• Monitor for engagement\n• Consider disqualifying"


def _generate_initial_outreach_sequence(first_name: str, company: str, title: str) -> str:
    """Generate initial outreach sequence."""
    return f"""
📧 Email 1 (Day 0) - Introduction:
Subject: Quick question about {company}'s growth initiatives

Hi {first_name},

I noticed {company} has been expanding rapidly. As a {title}, you're probably focused on scaling operations efficiently.

We've helped similar companies reduce operational overhead by 30% while improving customer satisfaction. 

Would you be open to a brief 15-minute conversation about your current challenges?

Best regards,
[Your Name]

📧 Email 2 (Day 3) - Value Proposition:
Subject: How [Similar Company] saved $50K annually

Hi {first_name},

I wanted to share a quick case study that might interest you...

[Case study details]

Would love to discuss how this might apply to {company}.

📧 Email 3 (Day 7) - Social Proof:
Subject: Final follow-up - {company}

Hi {first_name},

I don't want to be a pest, but I genuinely believe we could help {company} achieve similar results.

If now isn't the right time, when would be better to reconnect?

Best,
[Your Name]
"""


def _generate_follow_up_sequence(first_name: str, company: str) -> str:
    """Generate follow-up sequence for engaged leads."""
    return f"""
📧 Follow-up 1 (Day 0) - Meeting Recap:
Subject: Thanks for the great conversation, {first_name}

Hi {first_name},

Great speaking with you about {company}'s initiatives. As discussed, I'm sending over...

[Meeting recap and next steps]

📧 Follow-up 2 (Day 2) - Additional Resources:
Subject: Additional resources for {company}

Hi {first_name},

I thought you might find these additional resources helpful...

[Relevant resources]

📧 Follow-up 3 (Day 5) - Next Steps:
Subject: Ready for the next step?

Hi {first_name},

Hope you had a chance to review the materials. Ready to move forward with...

[Next steps proposal]
"""


def _generate_re_engagement_sequence(first_name: str, company: str) -> str:
    """Generate re-engagement sequence for cold leads."""
    return f"""
📧 Re-engagement 1 (Day 0) - Check-in:
Subject: Checking in - {company}

Hi {first_name},

It's been a while since we last connected. I wanted to check in and see how things are going at {company}.

[Brief update on new features/offerings]

📧 Re-engagement 2 (Day 4) - New Opportunity:
Subject: New opportunity for {company}

Hi {first_name},

We've just launched something that I think could be perfect for {company}...

[New offering details]

📧 Re-engagement 3 (Day 8) - Last Attempt:
Subject: Should I close your file?

Hi {first_name},

I haven't heard back, so I'm assuming this isn't a priority for {company} right now.

Should I close your file, or is there a better time to reconnect?

Best,
[Your Name]
"""


class SalesDevAgent:
    """
    Sales Development Agent for lead qualification, outreach, and pipeline management.
    
    Specializes in:
    - Lead qualification and scoring
    - Automated outreach sequence generation
    - Pipeline management and tracking
    - CRM integration and activity logging
    """
    
    def __init__(self, project_id: str = "vertex-ai-agent-yzdlnjey"):
        self.project_id = project_id
        self.agent_type = "sales_development"
        
        # Agent configuration
        agent_config = settings.agent_config
        self.location = agent_config.location
        self.model_name = agent_config.model_name
        self.redis_checkpointer = None
        self.rag_service = None
        self.semantic_search = None
        
        # Initialize the LLM
        self.llm = ChatVertexAI(
            project=self.project_id,
            location=self.location,
            model_name=self.model_name,
            temperature=agent_config.temperature,
        )
        
        # Define sales development specific tools
        self.tools = [
            qualify_lead,
            create_outreach_sequence,
            update_deal_stage,
            track_pipeline_metrics
        ]
        
        # Bind tools to the LLM
        self.llm_with_tools = self.llm.bind_tools(self.tools)
        
        # Build the workflow graph
        self.app = self._build_graph()
    
    async def initialize_services(self):
        """Initialize shared services (Redis, RAG, etc.)."""
        try:
            # Initialize Redis checkpointer
            self.redis_checkpointer = await get_redis_checkpointer(settings)
            
            # Initialize RAG services
            self.rag_service = await get_rag_service(settings)
            self.semantic_search = await get_semantic_search_service(settings)
            
            # Rebuild graph with checkpointer
            self.app = self._build_graph()
            
            logger.info("Sales Development agent services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize sales development agent services: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build the Sales Development Agent LangGraph workflow."""
        workflow = StateGraph(AgentState)
        
        # Add standard workflow nodes
        workflow.add_node("initialize", self._initialize_task)
        workflow.add_node("classify_intent", self._classify_intent)
        workflow.add_node("process_sales", self._process_sales)
        workflow.add_node("call_model", self._call_model)
        workflow.add_node("execute_tools", self._execute_tools)
        workflow.add_node("finalize", self._finalize_task)
        
        # Set the entry point
        workflow.set_entry_point("initialize")
        
        # Define the workflow edges
        workflow.add_edge("initialize", "classify_intent")
        workflow.add_conditional_edges(
            "classify_intent",
            self._route_after_classification,
            {
                "sales_task": "process_sales",
                "general_task": "call_model"
            }
        )
        workflow.add_edge("process_sales", "call_model")
        workflow.add_conditional_edges(
            "call_model",
            self._should_continue,
            {
                "continue": "execute_tools",
                "end": "finalize"
            }
        )
        workflow.add_edge("execute_tools", "call_model")
        workflow.add_edge("finalize", END)
        
        # Compile with checkpointer if available
        if self.redis_checkpointer and self.redis_checkpointer.get_saver():
            checkpointer = self.redis_checkpointer.get_saver()
            return workflow.compile(checkpointer=checkpointer)
        else:
            return workflow.compile()
    
    async def _initialize_task(self, state: AgentState) -> AgentState:
        """Initialize sales development task processing."""
        logger.info(f"Initializing sales development task: {state.task_id}")
        
        state.steps_completed.append("initialize")
        state.context["start_time"] = time.time()
        state.context["agent_type"] = "sales_development"
        
        return state
    
    async def _classify_intent(self, state: AgentState) -> AgentState:
        """Classify the intent of the sales request."""
        logger.info(f"Classifying intent for task: {state.task_id}")
        
        # Sales-specific intent classification
        message = state.input_data.get("message", "").lower()
        
        sales_keywords = [
            "lead", "qualify", "outreach", "pipeline", "deal", "prospect",
            "sales", "crm", "contact", "follow up", "sequence", "conversion"
        ]
        
        if any(keyword in message for keyword in sales_keywords):
            state.context["route"] = "sales_task"
        else:
            state.context["route"] = "general_task"
        
        state.steps_completed.append("classify_intent")
        logger.info(f"Intent classified, routing to: {state.context['route']}")
        
        return state
    
    async def _process_sales(self, state: AgentState) -> AgentState:
        """Process sales-specific tasks."""
        logger.info(f"Processing sales task: {state.task_id}")
        
        # Enhance context with sales-specific information
        if self.semantic_search:
            try:
                # Retrieve relevant sales context
                query = state.input_data.get("message", "")
                context = await self.semantic_search.search_conversations(
                    query=query,
                    customer_id=state.input_data.get("customer_id", "default"),
                    limit=3
                )
                
                if context:
                    state.context["sales_context"] = context
                    logger.info("Enhanced sales context retrieved")
                    
            except Exception as e:
                logger.warning(f"Failed to retrieve sales context: {e}")
        
        state.steps_completed.append("process_sales")
        return state
    
    async def _call_model(self, state: AgentState) -> AgentState:
        """Call the LLM with sales-specific prompting."""
        logger.info(f"Calling model for task: {state.task_id}")
        
        # Sales-specific system prompt
        system_prompt = """
        You are a professional Sales Development Representative for TKC Group.
        Your primary role is to help with lead qualification, outreach, and pipeline management.
        
        CORE CAPABILITIES:
        - Qualify leads based on company size, decision-maker role, and engagement
        - Create personalized outreach sequences for different lead types
        - Update deal stages and track pipeline progression
        - Analyze pipeline metrics and provide actionable insights
        
        SALES METHODOLOGY:
        - Focus on value-based selling and consultative approach
        - Prioritize relationship building over aggressive tactics
        - Use data-driven insights for lead qualification
        - Maintain detailed CRM records for all interactions
        
        CRM INTEGRATION:
        - Always log activities and updates in the CRM system
        - Use lead scoring to prioritize outreach efforts
        - Track deal progression through defined pipeline stages
        - Provide clear next actions and follow-up recommendations
        
        Use available tools proactively to complete sales development tasks efficiently.
        """
        
        # Build message history
        messages = [SystemMessage(content=system_prompt)]
        
        # Add conversation history if available
        for msg in state.messages:
            messages.append(msg)
        
        # Add current request
        user_message = state.input_data.get("message", "")
        if state.context.get("sales_context"):
            user_message += f"\n\nRelevant context: {state.context['sales_context']}"
        
        messages.append(HumanMessage(content=user_message))
        
        # Call the model
        response = await self.llm_with_tools.ainvoke(messages)
        
        # Update state
        state.messages.extend([messages[-1], response])
        
        # Check for tool calls
        if hasattr(response, 'tool_calls') and response.tool_calls:
            state.context["pending_tool_calls"] = response.tool_calls
            logger.info(f"Model generated tool calls: {response.tool_calls}")
        
        return state
    
    def _route_after_classification(self, state: AgentState) -> str:
        """Route to appropriate processing based on classification."""
        return state.context.get("route", "general_task")
    
    def _should_continue(self, state: AgentState) -> str:
        """Determine if workflow should continue or end."""
        if state.context.get("pending_tool_calls"):
            return "continue"
        return "end"
    
    async def _execute_tools(self, state: AgentState) -> AgentState:
        """Execute sales development tools."""
        logger.info(f"Executing tools for task: {state.task_id}")

        tool_calls = state.context.get("pending_tool_calls", [])
        if not tool_calls:
            return state

        # Execute each tool call
        for tool_call in tool_calls:
            try:
                tool_name = tool_call.get("name")
                tool_args = tool_call.get("args", {})

                # Find and execute the tool
                for tool in self.tools:
                    if tool.name == tool_name:
                        result = await tool.ainvoke(tool_args)

                        # Add tool result to messages
                        tool_message = ToolMessage(
                            content=str(result),
                            tool_call_id=tool_call.get("id", "")
                        )
                        state.messages.append(tool_message)
                        break

            except Exception as e:
                logger.error(f"Tool execution error: {e}")
                error_message = ToolMessage(
                    content=f"Error executing {tool_name}: {str(e)}",
                    tool_call_id=tool_call.get("id", "")
                )
                state.messages.append(error_message)

        # Clear pending tool calls
        state.context["pending_tool_calls"] = []

        return state
    
    async def _finalize_task(self, state: AgentState) -> AgentState:
        """Finalize sales development task processing."""
        logger.info(f"Finalizing sales development task: {state.task_id}")
        
        state.status = TaskStatus.COMPLETED
        state.steps_completed.append("finalize")
        
        return state
    
    async def execute_task(self, task_request: TaskRequest) -> TaskResponse:
        """Execute a sales development task request."""
        try:
            # Create initial state
            initial_state = create_initial_state(task_request)
            
            # Execute the workflow
            final_state = await self.app.ainvoke(
                initial_state,
                config={"configurable": {"thread_id": task_request.task_id}}
            )
            
            # Convert to response
            return state_to_response(final_state)
            
        except Exception as e:
            logger.error(f"Sales development task execution failed: {e}")
            return TaskResponse(
                task_id=task_request.task_id,
                success=False,
                error=str(e),
                results={}
            )

"""
Marketing Content Agent - TKC_v5 Commercial Package

Specializes in content creation, campaign management, and marketing analytics.
Integrates with content platforms and analytics for comprehensive marketing automation.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_google_vertexai import ChatVertexAI
from langgraph.graph import StateGraph, END
from langchain_core.messages import ToolMessage

# Import shared infrastructure
from src.services.redis_checkpointer import get_redis_checkpointer
from src.services.rag_service import get_rag_service
from src.services.semantic_search import get_semantic_search_service

# Import base agent framework
from agent.state import (
    AgentState,
    TaskRequest,
    TaskResponse,
    TaskStatus,
    TaskType,
    create_initial_state,
    state_to_response
)
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


# Marketing Content specific tools
@tool
async def create_blog_post(
    topic: str,
    keywords: List[str] = None,
    target_audience: str = "business professionals",
    word_count: int = 800,
    tone: str = "professional"
) -> str:
    """
    Generate an SEO-optimized blog post on the specified topic.
    
    Args:
        topic: Main topic for the blog post
        keywords: SEO keywords to include
        target_audience: Target audience description
        word_count: Desired word count
        tone: Writing tone (professional, casual, technical)
        
    Returns:
        Generated blog post with SEO optimization
    """
    try:
        keywords = keywords or []
        
        # Generate blog post structure
        blog_post = f"""# {topic}

## Introduction

In today's rapidly evolving business landscape, {topic.lower()} has become increasingly important for {target_audience}. This comprehensive guide will explore the key aspects of {topic.lower()} and provide actionable insights for implementation.

## Key Benefits of {topic}

### 1. Improved Efficiency
{topic} can significantly streamline operations and reduce manual overhead, allowing teams to focus on high-value activities.

### 2. Enhanced Customer Experience
By implementing {topic.lower()} strategies, businesses can deliver more personalized and responsive customer interactions.

### 3. Competitive Advantage
Organizations that effectively leverage {topic.lower()} gain a significant edge in their market positioning.

## Implementation Strategy

### Phase 1: Assessment and Planning
- Evaluate current state and identify improvement opportunities
- Define clear objectives and success metrics
- Develop implementation roadmap with realistic timelines

### Phase 2: Technology Integration
- Select appropriate tools and platforms
- Ensure seamless integration with existing systems
- Implement proper training and change management

### Phase 3: Optimization and Scaling
- Monitor performance and gather feedback
- Continuously refine and improve processes
- Scale successful implementations across the organization

## Best Practices

1. **Start Small**: Begin with pilot programs to validate approach
2. **Measure Everything**: Implement comprehensive analytics and tracking
3. **Iterate Quickly**: Use feedback loops for rapid improvement
4. **Focus on ROI**: Prioritize initiatives with clear business value

## Common Challenges and Solutions

### Challenge: Resistance to Change
**Solution**: Implement comprehensive change management and training programs

### Challenge: Integration Complexity
**Solution**: Use phased approach with proper testing and validation

### Challenge: Measuring Success
**Solution**: Define clear KPIs and implement robust analytics

## Conclusion

{topic} represents a significant opportunity for {target_audience} to improve operations and drive business growth. By following the strategies outlined in this guide, organizations can successfully implement {topic.lower()} initiatives and achieve measurable results.

Ready to get started? Contact our team to learn how we can help you implement {topic.lower()} solutions tailored to your specific needs.

---

**Keywords**: {', '.join(keywords) if keywords else 'Not specified'}
**Target Audience**: {target_audience}
**Word Count**: ~{word_count} words
**Tone**: {tone}

✅ SEO optimized with keyword integration
✅ Structured for readability and engagement
✅ Includes clear call-to-action"""
        
        return blog_post
        
    except Exception as e:
        return f"Error creating blog post: {str(e)}"


@tool
async def create_social_media_campaign(
    campaign_theme: str,
    platforms: List[str] = None,
    duration_days: int = 7,
    post_frequency: str = "daily"
) -> str:
    """
    Generate a multi-platform social media campaign with scheduled content.
    
    Args:
        campaign_theme: Main theme or topic for the campaign
        platforms: Social media platforms (linkedin, twitter, facebook)
        duration_days: Campaign duration in days
        post_frequency: Posting frequency (daily, twice_daily, weekly)
        
    Returns:
        Complete social media campaign with platform-specific content
    """
    try:
        platforms = platforms or ["linkedin", "twitter"]
        
        # Calculate total posts needed
        posts_per_day = {"daily": 1, "twice_daily": 2, "weekly": 0.14}
        total_posts = int(duration_days * posts_per_day.get(post_frequency, 1))
        
        campaign = f"""🚀 Social Media Campaign: {campaign_theme}

📅 Duration: {duration_days} days
📱 Platforms: {', '.join(platforms)}
📊 Frequency: {post_frequency}
📝 Total Posts: {total_posts}

## Campaign Content Calendar

"""
        
        # Generate content for each day
        for day in range(1, duration_days + 1):
            campaign += f"### Day {day}\n\n"
            
            if "linkedin" in platforms:
                linkedin_post = _generate_linkedin_post(campaign_theme, day)
                campaign += f"**LinkedIn Post:**\n{linkedin_post}\n\n"
            
            if "twitter" in platforms:
                twitter_post = _generate_twitter_post(campaign_theme, day)
                campaign += f"**Twitter Post:**\n{twitter_post}\n\n"
            
            if "facebook" in platforms:
                facebook_post = _generate_facebook_post(campaign_theme, day)
                campaign += f"**Facebook Post:**\n{facebook_post}\n\n"
            
            campaign += "---\n\n"
        
        campaign += f"""## Campaign Analytics Tracking

📊 Key Metrics to Monitor:
- Engagement Rate (likes, comments, shares)
- Reach and Impressions
- Click-through Rate (CTR)
- Lead Generation
- Brand Mention Sentiment

🎯 Success Criteria:
- Engagement rate > 3%
- Reach increase of 25%
- Generate 10+ qualified leads
- Positive sentiment > 80%

✅ Campaign ready for scheduling and execution"""
        
        return campaign
        
    except Exception as e:
        return f"Error creating social media campaign: {str(e)}"


@tool
async def create_email_campaign(
    campaign_type: str = "newsletter",
    target_segment: str = "all_subscribers",
    subject_line: str = "",
    content_theme: str = ""
) -> str:
    """
    Generate an email marketing campaign with subject lines and content.
    
    Args:
        campaign_type: Type of email (newsletter, promotional, nurture)
        target_segment: Target audience segment
        subject_line: Email subject line (auto-generated if empty)
        content_theme: Main content theme
        
    Returns:
        Complete email campaign with subject lines and content
    """
    try:
        # Auto-generate subject line if not provided
        if not subject_line:
            subject_line = _generate_subject_line(campaign_type, content_theme)
        
        # Generate email content based on type
        if campaign_type == "newsletter":
            content = _generate_newsletter_content(content_theme)
        elif campaign_type == "promotional":
            content = _generate_promotional_content(content_theme)
        elif campaign_type == "nurture":
            content = _generate_nurture_content(content_theme)
        else:
            content = _generate_general_content(content_theme)
        
        email_campaign = f"""📧 Email Campaign: {campaign_type.title()}

**Subject Line**: {subject_line}
**Target Segment**: {target_segment}
**Content Theme**: {content_theme}

## Email Content

{content}

## Campaign Settings

📊 **Tracking & Analytics**:
- Open Rate Tracking: ✅ Enabled
- Click Tracking: ✅ Enabled
- Conversion Tracking: ✅ Enabled
- A/B Testing: 📋 Recommended for subject lines

🎯 **Optimization Recommendations**:
- Send Time: Tuesday-Thursday, 10 AM - 2 PM
- Mobile Optimization: ✅ Responsive design
- Personalization: Use recipient name and company
- Call-to-Action: Clear and prominent buttons

📈 **Success Metrics**:
- Target Open Rate: >25%
- Target Click Rate: >3%
- Target Conversion Rate: >2%

✅ Email campaign ready for deployment"""
        
        return email_campaign
        
    except Exception as e:
        return f"Error creating email campaign: {str(e)}"


@tool
async def analyze_campaign_performance(
    campaign_id: str,
    metrics_period: str = "last_30_days",
    platform: str = "all"
) -> str:
    """
    Analyze marketing campaign performance and provide optimization insights.
    
    Args:
        campaign_id: Campaign identifier
        metrics_period: Analysis period
        platform: Platform to analyze (all, email, social, content)
        
    Returns:
        Comprehensive campaign performance analysis
    """
    try:
        # Mock performance data (in production, this would connect to analytics APIs)
        performance_data = {
            "email": {
                "sent": 5000,
                "opened": 1250,
                "clicked": 150,
                "converted": 25,
                "open_rate": 25.0,
                "click_rate": 3.0,
                "conversion_rate": 0.5
            },
            "social": {
                "impressions": 50000,
                "engagements": 1500,
                "clicks": 300,
                "leads": 15,
                "engagement_rate": 3.0,
                "ctr": 0.6,
                "cost_per_lead": 25.0
            },
            "content": {
                "page_views": 2500,
                "unique_visitors": 1800,
                "time_on_page": 180,
                "bounce_rate": 35.0,
                "conversion_rate": 2.5
            }
        }
        
        analysis = f"""📊 Campaign Performance Analysis: {campaign_id}

📅 Period: {metrics_period}
🎯 Platform: {platform}

## Key Performance Metrics

### Email Marketing Performance
📧 **Email Metrics**:
- Emails Sent: {performance_data['email']['sent']:,}
- Open Rate: {performance_data['email']['open_rate']}% (Industry avg: 22%)
- Click Rate: {performance_data['email']['click_rate']}% (Industry avg: 2.5%)
- Conversion Rate: {performance_data['email']['conversion_rate']}% (Industry avg: 0.3%)

### Social Media Performance
📱 **Social Metrics**:
- Total Impressions: {performance_data['social']['impressions']:,}
- Engagement Rate: {performance_data['social']['engagement_rate']}% (Target: >3%)
- Click-through Rate: {performance_data['social']['ctr']}% (Target: >0.5%)
- Cost per Lead: ${performance_data['social']['cost_per_lead']} (Target: <$30)

### Content Marketing Performance
📝 **Content Metrics**:
- Page Views: {performance_data['content']['page_views']:,}
- Unique Visitors: {performance_data['content']['unique_visitors']:,}
- Avg. Time on Page: {performance_data['content']['time_on_page']}s (Target: >120s)
- Bounce Rate: {performance_data['content']['bounce_rate']}% (Target: <40%)

## Performance Analysis

🟢 **Strengths**:
- Email open rates above industry average
- Social engagement meeting targets
- Content time-on-page exceeding goals

🟡 **Areas for Improvement**:
- Email conversion rate below target
- Social cost-per-lead slightly high
- Content bounce rate near threshold

## Optimization Recommendations

### Email Optimization
1. **A/B Test Subject Lines**: Test emotional vs. rational appeals
2. **Segment Audiences**: Create targeted content for different personas
3. **Optimize CTAs**: Test button colors, text, and placement

### Social Media Optimization
1. **Refine Targeting**: Narrow audience to reduce cost-per-lead
2. **Content Variety**: Mix educational and promotional content
3. **Timing Optimization**: Test different posting schedules

### Content Optimization
1. **Page Load Speed**: Optimize for faster loading times
2. **Content Structure**: Add more subheadings and bullet points
3. **Internal Linking**: Improve navigation and engagement

## ROI Analysis
💰 **Campaign ROI**: 285% (Target: >200%)
📈 **Trend**: Performance improving month-over-month
🎯 **Recommendation**: Continue current strategy with suggested optimizations

✅ Analysis complete - implement recommendations for improved performance"""
        
        return analysis
        
    except Exception as e:
        return f"Error analyzing campaign performance: {str(e)}"


def _generate_linkedin_post(theme: str, day: int) -> str:
    """Generate LinkedIn-specific post content."""
    posts = [
        f"🚀 Day {day} of our {theme} series: The key to success is understanding your audience's pain points and providing genuine value. What challenges are you facing in your industry?",
        f"💡 Insight #{day}: {theme} isn't just about technology—it's about transforming how we work and deliver value to customers. Share your transformation story below!",
        f"📊 Did you know? Companies implementing {theme} see an average 30% improvement in efficiency. What metrics matter most in your organization?",
        f"🎯 Strategy spotlight: The most successful {theme} implementations start with clear objectives and stakeholder buy-in. How do you ensure alignment in your projects?"
    ]
    return posts[min(day - 1, len(posts) - 1)]


def _generate_twitter_post(theme: str, day: int) -> str:
    """Generate Twitter-specific post content."""
    posts = [
        f"🧵 Thread: {theme} best practices (Day {day})\n\n1/ Start with clear objectives\n2/ Measure everything\n3/ Iterate quickly\n\nWhat would you add? #TechTips",
        f"💭 Quick thought: {theme} success = Right strategy + Right tools + Right team. Which element do you think is most critical? 🤔",
        f"📈 Stat of the day: {theme} adoption has grown 150% in the past year. Are you keeping up with the trend? #Innovation",
        f"🔥 Hot take: The biggest barrier to {theme} isn't technology—it's change management. Agree or disagree? Let's discuss! 👇"
    ]
    return posts[min(day - 1, len(posts) - 1)]


def _generate_facebook_post(theme: str, day: int) -> str:
    """Generate Facebook-specific post content."""
    posts = [
        f"🌟 Exploring {theme} this week! Day {day}: We're diving deep into how businesses are transforming their operations. What's your experience been like? Share your story in the comments!",
        f"💼 Business insight: {theme} is reshaping industries across the board. From startups to enterprises, everyone's looking for that competitive edge. What trends are you seeing in your field?",
        f"🤝 Community question: What's the biggest challenge you've faced when implementing {theme} in your organization? Let's help each other out with solutions and advice!",
        f"🎉 Success story time! We love hearing how {theme} has made a difference for businesses. Drop a comment with your wins—big or small, we want to celebrate with you!"
    ]
    return posts[min(day - 1, len(posts) - 1)]


def _generate_subject_line(campaign_type: str, theme: str) -> str:
    """Generate email subject line based on campaign type and theme."""
    if campaign_type == "newsletter":
        return f"Weekly Insights: {theme} Trends You Need to Know"
    elif campaign_type == "promotional":
        return f"Limited Time: Transform Your {theme} Strategy"
    elif campaign_type == "nurture":
        return f"Your {theme} Journey: Next Steps Inside"
    else:
        return f"Important Update: {theme} Best Practices"


def _generate_newsletter_content(theme: str) -> str:
    """Generate newsletter email content."""
    return f"""
<h2>This Week in {theme}</h2>

<p>Hello there!</p>

<p>Welcome to this week's newsletter where we explore the latest trends and insights in {theme}. Here's what caught our attention:</p>

<h3>🔥 Trending This Week</h3>
<ul>
<li>Industry leaders are adopting new {theme} strategies</li>
<li>Latest research shows 40% improvement in efficiency</li>
<li>New tools and platforms making {theme} more accessible</li>
</ul>

<h3>📚 Featured Resource</h3>
<p>Check out our comprehensive guide to {theme} implementation. It includes step-by-step instructions, best practices, and real-world case studies.</p>

<p><a href="#" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Download Guide</a></p>

<h3>🎯 Quick Tip</h3>
<p>Start small with {theme} initiatives. Pilot programs help validate your approach before full-scale implementation.</p>

<p>Have questions about {theme}? Reply to this email—we read every response!</p>

<p>Best regards,<br>The TKC Team</p>
"""


def _generate_promotional_content(theme: str) -> str:
    """Generate promotional email content."""
    return f"""
<h2>Transform Your {theme} Strategy Today</h2>

<p>Hi there!</p>

<p>Ready to take your {theme} initiatives to the next level? For a limited time, we're offering exclusive access to our premium {theme} consulting services.</p>

<h3>🚀 What You Get:</h3>
<ul>
<li>Comprehensive {theme} assessment</li>
<li>Custom implementation roadmap</li>
<li>30 days of hands-on support</li>
<li>Access to our expert team</li>
</ul>

<h3>💰 Special Offer</h3>
<p>Save 25% when you book your consultation this week. That's a $2,500 value for just $1,875.</p>

<p><a href="#" style="background-color: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Book Your Consultation</a></p>

<p><em>Offer expires Friday, {(datetime.now() + timedelta(days=7)).strftime('%B %d')}. Limited spots available.</em></p>

<p>Questions? Reply to this email or call us at (555) 123-4567.</p>

<p>Best regards,<br>The TKC Team</p>
"""


def _generate_nurture_content(theme: str) -> str:
    """Generate nurture email content."""
    return f"""
<h2>Your {theme} Journey Continues</h2>

<p>Hello!</p>

<p>We hope you've been finding value in our {theme} resources. Today, we want to help you take the next step in your journey.</p>

<h3>🎯 Where You Are Now</h3>
<p>You've shown interest in {theme} and have likely started exploring how it could benefit your organization. That's fantastic!</p>

<h3>📈 Next Steps</h3>
<p>Based on your interests, here are some recommended next steps:</p>

<ol>
<li><strong>Assessment</strong>: Evaluate your current state and identify opportunities</li>
<li><strong>Planning</strong>: Develop a strategic roadmap for implementation</li>
<li><strong>Pilot</strong>: Start with a small-scale pilot program</li>
</ol>

<h3>🤝 How We Can Help</h3>
<p>Our team has helped hundreds of organizations successfully implement {theme} strategies. We'd love to share our experience with you.</p>

<p><a href="#" style="background-color: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Schedule a Free Consultation</a></p>

<p>No pressure, no sales pitch—just a friendly conversation about your goals and how we might be able to help.</p>

<p>Looking forward to connecting!</p>

<p>Best regards,<br>The TKC Team</p>
"""


def _generate_general_content(theme: str) -> str:
    """Generate general email content."""
    return f"""
<h2>Important Update: {theme}</h2>

<p>Hello!</p>

<p>We wanted to share some important updates regarding {theme} that could impact your business.</p>

<h3>📢 What's New</h3>
<ul>
<li>Industry regulations and compliance updates</li>
<li>New technology developments and opportunities</li>
<li>Best practices from leading organizations</li>
</ul>

<h3>💡 Key Takeaways</h3>
<p>The {theme} landscape is evolving rapidly. Organizations that stay informed and adapt quickly will have a significant competitive advantage.</p>

<h3>🔗 Useful Resources</h3>
<ul>
<li><a href="#">{theme} Implementation Guide</a></li>
<li><a href="#">Industry Benchmark Report</a></li>
<li><a href="#">Expert Webinar Series</a></li>
</ul>

<p>Have questions about how these updates might affect your organization? We're here to help.</p>

<p><a href="#" style="background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Contact Our Team</a></p>

<p>Best regards,<br>The TKC Team</p>
"""


class MarketingContentAgent:
    """
    Marketing Content Agent for content creation, campaign management, and analytics.
    
    Specializes in:
    - Blog post and article creation with SEO optimization
    - Multi-platform social media campaign development
    - Email marketing campaign creation and optimization
    - Campaign performance analysis and insights
    """
    
    def __init__(self, project_id: str = "vertex-ai-agent-yzdlnjey"):
        self.project_id = project_id
        self.agent_type = "marketing_content"
        
        # Agent configuration
        agent_config = settings.agent_config
        self.location = agent_config.location
        self.model_name = agent_config.model_name
        self.redis_checkpointer = None
        self.rag_service = None
        self.semantic_search = None
        
        # Initialize the LLM
        self.llm = ChatVertexAI(
            project=self.project_id,
            location=self.location,
            model_name=self.model_name,
            temperature=agent_config.temperature,
        )
        
        # Define marketing content specific tools
        self.tools = [
            create_blog_post,
            create_social_media_campaign,
            create_email_campaign,
            analyze_campaign_performance
        ]
        
        # Bind tools to the LLM
        self.llm_with_tools = self.llm.bind_tools(self.tools)
        
        # Build the workflow graph
        self.app = self._build_graph()
    
    async def initialize_services(self):
        """Initialize shared services (Redis, RAG, etc.)."""
        try:
            # Initialize Redis checkpointer
            self.redis_checkpointer = await get_redis_checkpointer(settings)
            
            # Initialize RAG services
            self.rag_service = await get_rag_service(settings)
            self.semantic_search = await get_semantic_search_service(settings)
            
            # Rebuild graph with checkpointer
            self.app = self._build_graph()
            
            logger.info("Marketing Content agent services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize marketing content agent services: {e}")
            return False
    
    def _build_graph(self) -> StateGraph:
        """Build the Marketing Content Agent LangGraph workflow."""
        workflow = StateGraph(AgentState)
        
        # Add standard workflow nodes
        workflow.add_node("initialize", self._initialize_task)
        workflow.add_node("classify_intent", self._classify_intent)
        workflow.add_node("process_marketing", self._process_marketing)
        workflow.add_node("call_model", self._call_model)
        workflow.add_node("execute_tools", self._execute_tools)
        workflow.add_node("finalize", self._finalize_task)
        
        # Set the entry point
        workflow.set_entry_point("initialize")
        
        # Define the workflow edges
        workflow.add_edge("initialize", "classify_intent")
        workflow.add_conditional_edges(
            "classify_intent",
            self._route_after_classification,
            {
                "marketing_task": "process_marketing",
                "general_task": "call_model"
            }
        )
        workflow.add_edge("process_marketing", "call_model")
        workflow.add_conditional_edges(
            "call_model",
            self._should_continue,
            {
                "continue": "execute_tools",
                "end": "finalize"
            }
        )
        workflow.add_edge("execute_tools", "call_model")
        workflow.add_edge("finalize", END)
        
        # Compile with checkpointer if available
        if self.redis_checkpointer and self.redis_checkpointer.get_saver():
            checkpointer = self.redis_checkpointer.get_saver()
            return workflow.compile(checkpointer=checkpointer)
        else:
            return workflow.compile()
    
    async def _initialize_task(self, state: AgentState) -> AgentState:
        """Initialize marketing content task processing."""
        logger.info(f"Initializing marketing content task: {state.task_id}")
        
        state.steps_completed.append("initialize")
        state.context["start_time"] = time.time()
        state.context["agent_type"] = "marketing_content"
        
        return state
    
    async def _classify_intent(self, state: AgentState) -> AgentState:
        """Classify the intent of the marketing request."""
        logger.info(f"Classifying intent for task: {state.task_id}")
        
        # Marketing-specific intent classification
        message = state.input_data.get("message", "").lower()
        
        marketing_keywords = [
            "content", "blog", "social", "campaign", "email", "marketing",
            "seo", "analytics", "engagement", "conversion", "brand", "audience"
        ]
        
        if any(keyword in message for keyword in marketing_keywords):
            state.context["route"] = "marketing_task"
        else:
            state.context["route"] = "general_task"
        
        state.steps_completed.append("classify_intent")
        logger.info(f"Intent classified, routing to: {state.context['route']}")
        
        return state
    
    async def _process_marketing(self, state: AgentState) -> AgentState:
        """Process marketing-specific tasks."""
        logger.info(f"Processing marketing task: {state.task_id}")
        
        # Enhance context with marketing-specific information
        if self.semantic_search:
            try:
                # Retrieve relevant marketing context
                query = state.input_data.get("message", "")
                context = await self.semantic_search.search_conversations(
                    query=query,
                    customer_id=state.input_data.get("customer_id", "default"),
                    limit=3
                )
                
                if context:
                    state.context["marketing_context"] = context
                    logger.info("Enhanced marketing context retrieved")
                    
            except Exception as e:
                logger.warning(f"Failed to retrieve marketing context: {e}")
        
        state.steps_completed.append("process_marketing")
        return state
    
    async def _call_model(self, state: AgentState) -> AgentState:
        """Call the LLM with marketing-specific prompting."""
        logger.info(f"Calling model for task: {state.task_id}")
        
        # Marketing-specific system prompt
        system_prompt = """
        You are a professional Marketing Content Specialist for TKC Group.
        Your primary role is to create compelling content and manage marketing campaigns.
        
        CORE CAPABILITIES:
        - Create SEO-optimized blog posts and articles
        - Develop multi-platform social media campaigns
        - Design email marketing campaigns with high conversion rates
        - Analyze campaign performance and provide optimization insights
        
        CONTENT STRATEGY:
        - Focus on value-driven content that educates and engages
        - Maintain consistent brand voice and messaging
        - Optimize for search engines and user experience
        - Use data-driven insights for content optimization
        
        CAMPAIGN MANAGEMENT:
        - Develop integrated campaigns across multiple channels
        - Create content calendars and scheduling strategies
        - Monitor performance metrics and adjust tactics
        - Ensure brand consistency across all touchpoints
        
        ANALYTICS & OPTIMIZATION:
        - Track key performance indicators (KPIs)
        - Provide actionable insights for improvement
        - A/B test different content approaches
        - Optimize for conversion and engagement
        
        Use available tools proactively to complete marketing content tasks efficiently.
        """
        
        # Build message history
        messages = [SystemMessage(content=system_prompt)]
        
        # Add conversation history if available
        for msg in state.messages:
            messages.append(msg)
        
        # Add current request
        user_message = state.input_data.get("message", "")
        if state.context.get("marketing_context"):
            user_message += f"\n\nRelevant context: {state.context['marketing_context']}"
        
        messages.append(HumanMessage(content=user_message))
        
        # Call the model
        response = await self.llm_with_tools.ainvoke(messages)
        
        # Update state
        state.messages.extend([messages[-1], response])
        
        # Check for tool calls
        if hasattr(response, 'tool_calls') and response.tool_calls:
            state.context["pending_tool_calls"] = response.tool_calls
            logger.info(f"Model generated tool calls: {response.tool_calls}")
        
        return state
    
    def _route_after_classification(self, state: AgentState) -> str:
        """Route to appropriate processing based on classification."""
        return state.context.get("route", "general_task")
    
    def _should_continue(self, state: AgentState) -> str:
        """Determine if workflow should continue or end."""
        if state.context.get("pending_tool_calls"):
            return "continue"
        return "end"
    
    async def _execute_tools(self, state: AgentState) -> AgentState:
        """Execute marketing content tools."""
        logger.info(f"Executing tools for task: {state.task_id}")
        
        tool_calls = state.context.get("pending_tool_calls", [])
        if not tool_calls:
            return state
        
        # Execute each tool call
        for tool_call in tool_calls:
            try:
                tool_name = tool_call.get("name")
                tool_args = tool_call.get("args", {})
                
                # Find and execute the tool
                for tool in self.tools:
                    if tool.name == tool_name:
                        result = await tool.ainvoke(tool_args)
                        
                        # Add tool result to messages
                        tool_message = ToolMessage(
                            content=str(result),
                            tool_call_id=tool_call.get("id", "")
                        )
                        state.messages.append(tool_message)
                        break
                        
            except Exception as e:
                logger.error(f"Tool execution error: {e}")
                error_message = ToolMessage(
                    content=f"Error executing {tool_name}: {str(e)}",
                    tool_call_id=tool_call.get("id", "")
                )
                state.messages.append(error_message)
        
        # Clear pending tool calls
        state.context["pending_tool_calls"] = []
        
        return state
    
    async def _finalize_task(self, state: AgentState) -> AgentState:
        """Finalize marketing content task processing."""
        logger.info(f"Finalizing marketing content task: {state.task_id}")
        
        state.status = TaskStatus.COMPLETED
        state.steps_completed.append("finalize")
        
        return state
    
    async def execute_task(self, task_request: TaskRequest) -> TaskResponse:
        """Execute a marketing content task request."""
        try:
            # Create initial state
            initial_state = create_initial_state(task_request)
            
            # Execute the workflow
            final_state = await self.app.ainvoke(
                initial_state,
                config={"configurable": {"thread_id": task_request.task_id}}
            )
            
            # Convert to response
            return state_to_response(final_state)
            
        except Exception as e:
            logger.error(f"Marketing content task execution failed: {e}")
            return TaskResponse(
                task_id=task_request.task_id,
                success=False,
                error=str(e),
                results={}
            )

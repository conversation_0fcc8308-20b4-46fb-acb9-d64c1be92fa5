"""
Health Check and Metrics Endpoints - TKC_v5 Executive Agent

Provides comprehensive health monitoring, metrics collection, and
system status endpoints for production operations.
"""

import asyncio
import time
from typing import Dict, Any, List
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import psutil

from src.services.monitoring_service import get_monitoring_service
from src.services.data_persistence_service import get_data_persistence_service
from src.services.pinecone_tenant_manager import get_pinecone_tenant_manager
from src.services.redis_checkpointer import get_redis_checkpointer
from config.settings import get_settings

router = APIRouter()
settings = get_settings()


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float
    services: Dict[str, str]
    system_metrics: Dict[str, Any]


class MetricsResponse(BaseModel):
    """Metrics response model."""
    timestamp: str
    environment: str
    performance_metrics: Dict[str, Any]
    system_metrics: Dict[str, Any]
    service_metrics: Dict[str, Any]


class ServiceStatus(BaseModel):
    """Individual service status model."""
    name: str
    status: str
    response_time_ms: float
    last_check: str
    details: Dict[str, Any]


# Track application start time
app_start_time = time.time()


async def check_service_health(service_name: str, check_func) -> ServiceStatus:
    """Check the health of an individual service."""
    start_time = time.time()
    
    try:
        result = await check_func()
        response_time = (time.time() - start_time) * 1000
        
        return ServiceStatus(
            name=service_name,
            status="healthy" if result else "unhealthy",
            response_time_ms=response_time,
            last_check=datetime.now().isoformat(),
            details={"result": result}
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        
        return ServiceStatus(
            name=service_name,
            status="unhealthy",
            response_time_ms=response_time,
            last_check=datetime.now().isoformat(),
            details={"error": str(e)}
        )


async def check_redis_health() -> bool:
    """Check Redis connection health."""
    try:
        checkpointer = await get_redis_checkpointer()
        # Simple ping test
        await checkpointer.redis_client.ping()
        return True
    except Exception:
        return False


async def check_pinecone_health() -> bool:
    """Check Pinecone connection health."""
    try:
        pinecone_manager = await get_pinecone_tenant_manager()
        # Simple connection test
        return pinecone_manager.index is not None
    except Exception:
        return False


async def check_data_persistence_health() -> bool:
    """Check data persistence service health."""
    try:
        persistence_service = await get_data_persistence_service()
        # Test Firestore connection
        return persistence_service.firestore_client is not None
    except Exception:
        return False


async def check_monitoring_health() -> bool:
    """Check monitoring service health."""
    try:
        monitoring_service = await get_monitoring_service()
        return monitoring_service.metrics_client is not None
    except Exception:
        return False


def get_system_metrics() -> Dict[str, Any]:
    """Get system performance metrics."""
    try:
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available_gb = memory.available / (1024**3)
        memory_total_gb = memory.total / (1024**3)
        
        # Disk metrics
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / (1024**3)
        disk_total_gb = disk.total / (1024**3)
        
        # Network metrics (if available)
        try:
            network = psutil.net_io_counters()
            network_metrics = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
        except:
            network_metrics = {}
        
        return {
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count
            },
            'memory': {
                'percent': memory_percent,
                'available_gb': round(memory_available_gb, 2),
                'total_gb': round(memory_total_gb, 2),
                'used_gb': round(memory_total_gb - memory_available_gb, 2)
            },
            'disk': {
                'percent': round(disk_percent, 2),
                'free_gb': round(disk_free_gb, 2),
                'total_gb': round(disk_total_gb, 2),
                'used_gb': round(disk_total_gb - disk_free_gb, 2)
            },
            'network': network_metrics
        }
    except Exception as e:
        return {'error': str(e)}


@router.get("/health", response_model=HealthStatus)
async def health_check():
    """
    Comprehensive health check endpoint.
    
    Returns the overall health status of the application and all services.
    """
    uptime = time.time() - app_start_time
    
    # Check all services
    service_checks = await asyncio.gather(
        check_service_health("redis", check_redis_health),
        check_service_health("pinecone", check_pinecone_health),
        check_service_health("data_persistence", check_data_persistence_health),
        check_service_health("monitoring", check_monitoring_health),
        return_exceptions=True
    )
    
    # Compile service statuses
    services = {}
    overall_healthy = True
    
    for check in service_checks:
        if isinstance(check, ServiceStatus):
            services[check.name] = check.status
            if check.status != "healthy":
                overall_healthy = False
        else:
            services["unknown"] = "error"
            overall_healthy = False
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    # Determine overall status
    overall_status = "healthy" if overall_healthy else "unhealthy"
    
    return HealthStatus(
        status=overall_status,
        timestamp=datetime.now().isoformat(),
        version="1.0.0",
        environment=settings.environment,
        uptime_seconds=uptime,
        services=services,
        system_metrics=system_metrics
    )


@router.get("/health/detailed")
async def detailed_health_check():
    """
    Detailed health check with individual service information.
    
    Returns detailed status for each service including response times and errors.
    """
    uptime = time.time() - app_start_time
    
    # Check all services with detailed information
    service_checks = await asyncio.gather(
        check_service_health("redis", check_redis_health),
        check_service_health("pinecone", check_pinecone_health),
        check_service_health("data_persistence", check_data_persistence_health),
        check_service_health("monitoring", check_monitoring_health),
        return_exceptions=True
    )
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    # Determine overall status
    overall_healthy = all(
        isinstance(check, ServiceStatus) and check.status == "healthy"
        for check in service_checks
    )
    
    return {
        "status": "healthy" if overall_healthy else "unhealthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "environment": settings.environment,
        "uptime_seconds": uptime,
        "services": [check for check in service_checks if isinstance(check, ServiceStatus)],
        "system_metrics": system_metrics
    }


@router.get("/metrics", response_model=MetricsResponse)
async def get_metrics():
    """
    Get comprehensive application metrics.
    
    Returns performance metrics, system metrics, and service metrics.
    """
    try:
        monitoring_service = await get_monitoring_service()
        performance_metrics = await monitoring_service.get_performance_summary()
    except Exception as e:
        performance_metrics = {"error": str(e)}
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    # Get service-specific metrics
    service_metrics = {}
    
    try:
        # Redis metrics
        checkpointer = await get_redis_checkpointer()
        redis_info = await checkpointer.redis_client.info()
        service_metrics["redis"] = {
            "connected_clients": redis_info.get("connected_clients", 0),
            "used_memory": redis_info.get("used_memory", 0),
            "total_commands_processed": redis_info.get("total_commands_processed", 0)
        }
    except Exception as e:
        service_metrics["redis"] = {"error": str(e)}
    
    try:
        # Pinecone metrics
        pinecone_manager = await get_pinecone_tenant_manager()
        service_metrics["pinecone"] = {
            "tenant_namespaces": len(pinecone_manager._tenant_namespaces),
            "index_connected": pinecone_manager.index is not None
        }
    except Exception as e:
        service_metrics["pinecone"] = {"error": str(e)}
    
    return MetricsResponse(
        timestamp=datetime.now().isoformat(),
        environment=settings.environment,
        performance_metrics=performance_metrics,
        system_metrics=system_metrics,
        service_metrics=service_metrics
    )


@router.get("/metrics/performance")
async def get_performance_metrics():
    """Get detailed performance metrics."""
    try:
        monitoring_service = await get_monitoring_service()
        return await monitoring_service.get_performance_summary()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get performance metrics: {e}")


@router.get("/metrics/system")
async def get_system_metrics_endpoint():
    """Get detailed system metrics."""
    return get_system_metrics()


@router.get("/status")
async def simple_status():
    """
    Simple status endpoint for basic health checks.
    
    Returns a simple OK response if the application is running.
    """
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "environment": settings.environment
    }


@router.get("/readiness")
async def readiness_check():
    """
    Kubernetes readiness probe endpoint.
    
    Returns 200 if the application is ready to serve traffic.
    """
    try:
        # Check critical services
        redis_healthy = await check_redis_health()
        
        if not redis_healthy:
            raise HTTPException(status_code=503, detail="Redis not ready")
        
        return {"status": "ready", "timestamp": datetime.now().isoformat()}
    
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Not ready: {e}")


@router.get("/liveness")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint.
    
    Returns 200 if the application is alive and should not be restarted.
    """
    return {"status": "alive", "timestamp": datetime.now().isoformat()}

"""
Google Calendar Client - TKC_v5 Executive Agent Integration

Provides comprehensive Google Calendar API integration for meeting scheduling,
calendar management, and appointment coordination.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import json

from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google.cloud import secretmanager

from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class CalendarClientError(Exception):
    """Custom exception for calendar client errors."""
    pass


class GoogleCalendarClient:
    """
    Google Calendar API client for executive agent integration.
    
    Provides meeting scheduling, calendar management, and availability checking.
    """
    
    def __init__(self, customer_id: str = "default"):
        self.customer_id = customer_id
        self.project_id = settings.google_cloud_project
        self.service = None
        self.credentials = None
        
    async def authenticate(self) -> bool:
        """Authenticate with Google Calendar API using service account."""
        try:
            # Get customer-specific calendar credentials from Secret Manager
            secret_client = secretmanager.SecretManagerServiceClient()
            secret_name = f"projects/{self.project_id}/secrets/calendar-{self.customer_id}/versions/latest"
            
            try:
                response = secret_client.access_secret_version(request={"name": secret_name})
                credentials_info = json.loads(response.payload.data.decode("UTF-8"))
                
                # Create service account credentials
                self.credentials = service_account.Credentials.from_service_account_info(
                    credentials_info,
                    scopes=['https://www.googleapis.com/auth/calendar']
                )
                
            except Exception as e:
                logger.warning(f"Customer-specific calendar credentials not found, using default: {e}")
                # Fall back to default service account
                self.credentials = service_account.Credentials.from_service_account_file(
                    f"secrets/service-account-key.json",
                    scopes=['https://www.googleapis.com/auth/calendar']
                )
            
            # Build the Calendar service
            self.service = build('calendar', 'v3', credentials=self.credentials)
            
            # Test authentication with a simple API call
            calendar_list = self.service.calendarList().list(maxResults=1).execute()
            
            logger.info(f"✅ Google Calendar authentication successful for customer: {self.customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Google Calendar authentication failed: {e}")
            raise CalendarClientError(f"Authentication failed: {e}")
    
    async def get_availability(self, 
                             start_time: datetime, 
                             end_time: datetime,
                             calendar_id: str = 'primary') -> List[Dict[str, Any]]:
        """Get availability for a specific time range."""
        try:
            if not self.service:
                await self.authenticate()
            
            # Query for busy times
            body = {
                'timeMin': start_time.isoformat() + 'Z',
                'timeMax': end_time.isoformat() + 'Z',
                'items': [{'id': calendar_id}]
            }
            
            freebusy_result = self.service.freebusy().query(body=body).execute()
            busy_times = freebusy_result.get('calendars', {}).get(calendar_id, {}).get('busy', [])
            
            # Calculate free time slots
            free_slots = []
            current_time = start_time
            
            for busy_period in busy_times:
                busy_start = datetime.fromisoformat(busy_period['start'].replace('Z', '+00:00'))
                busy_end = datetime.fromisoformat(busy_period['end'].replace('Z', '+00:00'))
                
                if current_time < busy_start:
                    free_slots.append({
                        'start': current_time.isoformat(),
                        'end': busy_start.isoformat(),
                        'duration_minutes': int((busy_start - current_time).total_seconds() / 60)
                    })
                
                current_time = max(current_time, busy_end)
            
            # Add final free slot if there's time remaining
            if current_time < end_time:
                free_slots.append({
                    'start': current_time.isoformat(),
                    'end': end_time.isoformat(),
                    'duration_minutes': int((end_time - current_time).total_seconds() / 60)
                })
            
            logger.info(f"Found {len(free_slots)} available time slots")
            return free_slots
            
        except Exception as e:
            logger.error(f"Error getting availability: {e}")
            raise CalendarClientError(f"Failed to get availability: {e}")
    
    async def schedule_meeting(self, meeting_data: Dict[str, Any]) -> str:
        """Schedule a new meeting on the calendar."""
        try:
            if not self.service:
                await self.authenticate()
            
            # Prepare event data
            event = {
                'summary': meeting_data.get('title', 'Meeting'),
                'description': meeting_data.get('description', ''),
                'start': {
                    'dateTime': meeting_data['start_time'],
                    'timeZone': meeting_data.get('timezone', 'America/Denver'),
                },
                'end': {
                    'dateTime': meeting_data['end_time'],
                    'timeZone': meeting_data.get('timezone', 'America/Denver'),
                },
                'attendees': [
                    {'email': email} for email in meeting_data.get('attendees', [])
                ],
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'email', 'minutes': meeting_data.get('reminder_minutes', 15)},
                        {'method': 'popup', 'minutes': 10},
                    ],
                },
            }
            
            # Add location if provided
            if meeting_data.get('location'):
                event['location'] = meeting_data['location']
            
            # Add conference data for video meetings
            if meeting_data.get('video_meeting', False):
                event['conferenceData'] = {
                    'createRequest': {
                        'requestId': f"meeting_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        'conferenceSolutionKey': {'type': 'hangoutsMeet'}
                    }
                }
            
            # Create the event
            calendar_id = meeting_data.get('calendar_id', 'primary')
            created_event = self.service.events().insert(
                calendarId=calendar_id,
                body=event,
                conferenceDataVersion=1 if meeting_data.get('video_meeting', False) else 0
            ).execute()
            
            event_id = created_event['id']
            event_link = created_event.get('htmlLink', '')
            
            logger.info(f"✅ Meeting scheduled successfully: {event_id}")
            
            return {
                'event_id': event_id,
                'event_link': event_link,
                'meeting_link': created_event.get('conferenceData', {}).get('entryPoints', [{}])[0].get('uri', ''),
                'status': 'scheduled'
            }
            
        except Exception as e:
            logger.error(f"Error scheduling meeting: {e}")
            raise CalendarClientError(f"Failed to schedule meeting: {e}")
    
    async def update_meeting(self, event_id: str, updates: Dict[str, Any], calendar_id: str = 'primary') -> bool:
        """Update an existing meeting."""
        try:
            if not self.service:
                await self.authenticate()
            
            # Get the existing event
            event = self.service.events().get(calendarId=calendar_id, eventId=event_id).execute()
            
            # Apply updates
            if 'title' in updates:
                event['summary'] = updates['title']
            if 'description' in updates:
                event['description'] = updates['description']
            if 'start_time' in updates:
                event['start']['dateTime'] = updates['start_time']
            if 'end_time' in updates:
                event['end']['dateTime'] = updates['end_time']
            if 'attendees' in updates:
                event['attendees'] = [{'email': email} for email in updates['attendees']]
            
            # Update the event
            updated_event = self.service.events().update(
                calendarId=calendar_id,
                eventId=event_id,
                body=event
            ).execute()
            
            logger.info(f"✅ Meeting updated successfully: {event_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating meeting: {e}")
            raise CalendarClientError(f"Failed to update meeting: {e}")
    
    async def cancel_meeting(self, event_id: str, calendar_id: str = 'primary') -> bool:
        """Cancel a meeting."""
        try:
            if not self.service:
                await self.authenticate()
            
            self.service.events().delete(calendarId=calendar_id, eventId=event_id).execute()
            
            logger.info(f"✅ Meeting cancelled successfully: {event_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling meeting: {e}")
            raise CalendarClientError(f"Failed to cancel meeting: {e}")
    
    async def get_upcoming_meetings(self, 
                                  days_ahead: int = 7,
                                  calendar_id: str = 'primary') -> List[Dict[str, Any]]:
        """Get upcoming meetings for the next specified days."""
        try:
            if not self.service:
                await self.authenticate()
            
            # Calculate time range
            now = datetime.utcnow()
            time_max = now + timedelta(days=days_ahead)
            
            events_result = self.service.events().list(
                calendarId=calendar_id,
                timeMin=now.isoformat() + 'Z',
                timeMax=time_max.isoformat() + 'Z',
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            events = events_result.get('items', [])
            
            meetings = []
            for event in events:
                start = event['start'].get('dateTime', event['start'].get('date'))
                meetings.append({
                    'id': event['id'],
                    'title': event.get('summary', 'No Title'),
                    'start_time': start,
                    'end_time': event['end'].get('dateTime', event['end'].get('date')),
                    'description': event.get('description', ''),
                    'attendees': [attendee.get('email') for attendee in event.get('attendees', [])],
                    'location': event.get('location', ''),
                    'status': event.get('status', 'confirmed')
                })
            
            logger.info(f"Found {len(meetings)} upcoming meetings")
            return meetings
            
        except Exception as e:
            logger.error(f"Error getting upcoming meetings: {e}")
            raise CalendarClientError(f"Failed to get upcoming meetings: {e}")


# Factory function for getting calendar client
async def get_calendar_client(customer_id: str = "default") -> GoogleCalendarClient:
    """Get a Google Calendar client instance."""
    client = GoogleCalendarClient(customer_id)
    await client.authenticate()
    return client

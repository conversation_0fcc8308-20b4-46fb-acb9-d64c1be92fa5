"""
Redis Checkpointer for LangGraph Conversation Memory

This module provides Redis-based checkpointing for LangGraph agents,
enabling conversation history and state persistence across agent restarts.
"""

import logging
import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

import redis.asyncio as redis
from langgraph.checkpoint.redis import RedisSaver

from config.settings import Settings

logger = logging.getLogger(__name__)


class RedisCheckpointer:
    """
    Redis-based checkpointer for LangGraph state management.
    
    Provides conversation history and state persistence across agent interactions.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self._redis_client: Optional[redis.Redis] = None
        self._saver: Optional[RedisSaver] = None
        self._initialized = False
        
    async def initialize(self) -> bool:
        """
        Initialize Redis connection and checkpointer.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Get Redis connection details from settings
            redis_config = await self._get_redis_config()
            if not redis_config:
                logger.error("Redis configuration not found")
                return False
            
            # Create Redis client
            self._redis_client = redis.Redis(
                host=redis_config.get("host"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                password=redis_config.get("password"),
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True
            )
            
            # Test connection
            await self._redis_client.ping()
            logger.info("Redis connection established successfully")
            
            # Create RedisSaver for LangGraph
            connection_string = self._build_connection_string(redis_config)
            self._saver = RedisSaver.from_conn_string(connection_string)
            
            self._initialized = True
            logger.info("Redis checkpointer initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis checkpointer: {e}")
            return False
    
    async def _get_redis_config(self) -> Optional[Dict[str, Any]]:
        """Get Redis configuration from Secret Manager."""
        try:
            # Try to get from Secret Manager first
            redis_secret = self.settings._get_secret("redis-config")
            if redis_secret:
                return redis_secret
            
            # Fallback to environment variables or default
            return {
                "host": "********",  # Default Memorystore internal IP
                "port": 6379,
                "db": 0
            }
            
        except Exception as e:
            logger.warning(f"Could not get Redis config from Secret Manager: {e}")
            return None
    
    def _build_connection_string(self, config: Dict[str, Any]) -> str:
        """Build Redis connection string for RedisSaver."""
        host = config.get("host", "localhost")
        port = config.get("port", 6379)
        db = config.get("db", 0)
        password = config.get("password")
        
        if password:
            return f"redis://:{password}@{host}:{port}/{db}"
        else:
            return f"redis://{host}:{port}/{db}"
    
    def get_saver(self) -> Optional[RedisSaver]:
        """
        Get the RedisSaver instance for LangGraph compilation.
        
        Returns:
            RedisSaver instance if initialized, None otherwise
        """
        if not self._initialized:
            logger.warning("Redis checkpointer not initialized")
            return None
        return self._saver
    
    async def save_conversation_checkpoint(
        self, 
        thread_id: str, 
        checkpoint_data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Save conversation checkpoint to Redis.
        
        Args:
            thread_id: Unique conversation thread identifier
            checkpoint_data: State data to save
            metadata: Optional metadata
            
        Returns:
            True if successful, False otherwise
        """
        if not self._initialized or not self._redis_client:
            logger.error("Redis checkpointer not initialized")
            return False
        
        try:
            # Create checkpoint key
            checkpoint_key = f"checkpoint:{thread_id}"
            
            # Prepare checkpoint data with timestamp
            checkpoint_with_meta = {
                "data": checkpoint_data,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat(),
                "thread_id": thread_id
            }
            
            # Save to Redis with expiration (24 hours)
            await self._redis_client.hset(
                checkpoint_key,
                mapping={
                    "checkpoint": str(checkpoint_with_meta),
                    "last_updated": datetime.now().isoformat()
                }
            )
            
            # Set expiration
            await self._redis_client.expire(checkpoint_key, 86400)  # 24 hours
            
            logger.info(f"Checkpoint saved for thread: {thread_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save checkpoint for thread {thread_id}: {e}")
            return False
    
    async def get_conversation_checkpoint(
        self, 
        thread_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve conversation checkpoint from Redis.
        
        Args:
            thread_id: Unique conversation thread identifier
            
        Returns:
            Checkpoint data if found, None otherwise
        """
        if not self._initialized or not self._redis_client:
            logger.error("Redis checkpointer not initialized")
            return None
        
        try:
            checkpoint_key = f"checkpoint:{thread_id}"
            checkpoint_data = await self._redis_client.hget(checkpoint_key, "checkpoint")
            
            if checkpoint_data:
                logger.info(f"Checkpoint retrieved for thread: {thread_id}")
                return eval(checkpoint_data)  # Note: In production, use json.loads
            else:
                logger.info(f"No checkpoint found for thread: {thread_id}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to retrieve checkpoint for thread {thread_id}: {e}")
            return None
    
    async def list_conversation_threads(self, limit: int = 100) -> List[str]:
        """
        List all conversation thread IDs.
        
        Args:
            limit: Maximum number of threads to return
            
        Returns:
            List of thread IDs
        """
        if not self._initialized or not self._redis_client:
            logger.error("Redis checkpointer not initialized")
            return []
        
        try:
            # Get all checkpoint keys
            keys = await self._redis_client.keys("checkpoint:*")
            
            # Extract thread IDs
            thread_ids = [key.replace("checkpoint:", "") for key in keys[:limit]]
            
            logger.info(f"Found {len(thread_ids)} conversation threads")
            return thread_ids
            
        except Exception as e:
            logger.error(f"Failed to list conversation threads: {e}")
            return []
    
    async def cleanup_expired_checkpoints(self, days_old: int = 7) -> int:
        """
        Clean up old conversation checkpoints.
        
        Args:
            days_old: Remove checkpoints older than this many days
            
        Returns:
            Number of checkpoints cleaned up
        """
        if not self._initialized or not self._redis_client:
            logger.error("Redis checkpointer not initialized")
            return 0
        
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            keys = await self._redis_client.keys("checkpoint:*")
            cleaned_count = 0
            
            for key in keys:
                last_updated = await self._redis_client.hget(key, "last_updated")
                if last_updated:
                    update_date = datetime.fromisoformat(last_updated)
                    if update_date < cutoff_date:
                        await self._redis_client.delete(key)
                        cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} expired checkpoints")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired checkpoints: {e}")
            return 0
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on Redis connection.
        
        Returns:
            Health status information
        """
        if not self._initialized or not self._redis_client:
            return {
                "status": "unhealthy",
                "error": "Redis checkpointer not initialized"
            }
        
        try:
            # Test basic operations
            await self._redis_client.ping()
            
            # Get Redis info
            info = await self._redis_client.info()
            
            return {
                "status": "healthy",
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory_human": info.get("used_memory_human"),
                "uptime_in_seconds": info.get("uptime_in_seconds")
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def close(self):
        """Close Redis connection."""
        if self._redis_client:
            await self._redis_client.close()
            logger.info("Redis connection closed")


# Global instance
_redis_checkpointer: Optional[RedisCheckpointer] = None


async def get_redis_checkpointer(settings: Settings) -> RedisCheckpointer:
    """
    Get or create the global Redis checkpointer instance.
    
    Args:
        settings: Application settings
        
    Returns:
        RedisCheckpointer instance
    """
    global _redis_checkpointer
    
    if _redis_checkpointer is None:
        _redis_checkpointer = RedisCheckpointer(settings)
        await _redis_checkpointer.initialize()
    
    return _redis_checkpointer

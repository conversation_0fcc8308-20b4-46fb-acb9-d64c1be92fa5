"""
Lead Notification Service - TKC_v5 Executive Agent

Provides Pub/Sub-based notification system for lead processing events.
Enables real-time communication between form submissions and agent processing.
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from google.cloud import pubsub_v1
from google.api_core import retry

logger = logging.getLogger(__name__)


class LeadNotificationService:
    """
    Service for managing lead processing notifications via Google Cloud Pub/Sub.
    
    Features:
    - Publishes form submission events
    - Manages agent notification queues
    - Handles lead processing status updates
    - Provides real-time event streaming
    """
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        
        # Topic and subscription names
        self.topics = {
            'form_submissions': f'projects/{project_id}/topics/form-submissions',
            'lead_processing': f'projects/{project_id}/topics/lead-processing',
            'agent_notifications': f'projects/{project_id}/topics/agent-notifications',
            'crm_updates': f'projects/{project_id}/topics/crm-updates'
        }
        
        self.subscriptions = {
            'executive_agent': f'projects/{project_id}/subscriptions/executive-agent-notifications',
            'sales_dev_agent': f'projects/{project_id}/subscriptions/sales-dev-agent-notifications',
            'lead_processor': f'projects/{project_id}/subscriptions/lead-processor-queue'
        }
    
    async def initialize(self):
        """Initialize Pub/Sub topics and subscriptions."""
        try:
            await self._ensure_topics_exist()
            await self._ensure_subscriptions_exist()
            logger.info("Lead notification service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize notification service: {e}")
            raise
    
    async def _ensure_topics_exist(self):
        """Ensure all required topics exist."""
        for topic_name, topic_path in self.topics.items():
            try:
                self.publisher.create_topic(request={"name": topic_path})
                logger.info(f"Created topic: {topic_name}")
            except Exception as e:
                if "already exists" in str(e).lower():
                    logger.debug(f"Topic {topic_name} already exists")
                else:
                    logger.warning(f"Failed to create topic {topic_name}: {e}")
    
    async def _ensure_subscriptions_exist(self):
        """Ensure all required subscriptions exist."""
        subscription_configs = [
            ('executive_agent', 'agent_notifications'),
            ('sales_dev_agent', 'agent_notifications'),
            ('lead_processor', 'form_submissions')
        ]
        
        for sub_name, topic_name in subscription_configs:
            try:
                subscription_path = self.subscriptions[sub_name]
                topic_path = self.topics[topic_name]
                
                self.subscriber.create_subscription(
                    request={
                        "name": subscription_path,
                        "topic": topic_path,
                        "ack_deadline_seconds": 60
                    }
                )
                logger.info(f"Created subscription: {sub_name}")
            except Exception as e:
                if "already exists" in str(e).lower():
                    logger.debug(f"Subscription {sub_name} already exists")
                else:
                    logger.warning(f"Failed to create subscription {sub_name}: {e}")
    
    async def publish_form_submission(self, submission_data: Dict[str, Any]) -> str:
        """
        Publish a form submission event.
        
        Args:
            submission_data: Form submission data
            
        Returns:
            Message ID of published event
        """
        try:
            event = {
                'event_type': 'form_submission',
                'timestamp': datetime.now().isoformat(),
                'data': submission_data
            }
            
            message_data = json.dumps(event).encode('utf-8')
            
            # Publish to form submissions topic
            future = self.publisher.publish(
                self.topics['form_submissions'],
                message_data,
                event_type='form_submission',
                form_type=submission_data.get('form_type', 'unknown'),
                priority=submission_data.get('priority', 'medium')
            )
            
            message_id = future.result()
            logger.info(f"Published form submission event: {message_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to publish form submission: {e}")
            raise
    
    async def publish_lead_processed(self, lead_data: Dict[str, Any]) -> str:
        """
        Publish a lead processing completion event.
        
        Args:
            lead_data: Processed lead data
            
        Returns:
            Message ID of published event
        """
        try:
            event = {
                'event_type': 'lead_processed',
                'timestamp': datetime.now().isoformat(),
                'data': lead_data
            }
            
            message_data = json.dumps(event).encode('utf-8')
            
            # Publish to lead processing topic
            future = self.publisher.publish(
                self.topics['lead_processing'],
                message_data,
                event_type='lead_processed',
                lead_id=lead_data.get('lead_id', 'unknown'),
                priority=lead_data.get('priority', 'medium')
            )
            
            message_id = future.result()
            logger.info(f"Published lead processed event: {message_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to publish lead processed event: {e}")
            raise
    
    async def notify_agents(self, notification_data: Dict[str, Any], agents: List[str] = None) -> List[str]:
        """
        Send notifications to specified agents.
        
        Args:
            notification_data: Notification data
            agents: List of agents to notify (default: all)
            
        Returns:
            List of message IDs
        """
        if agents is None:
            agents = ['executive_agent', 'sales_dev_agent']
        
        message_ids = []
        
        try:
            event = {
                'event_type': 'agent_notification',
                'timestamp': datetime.now().isoformat(),
                'data': notification_data
            }
            
            message_data = json.dumps(event).encode('utf-8')
            
            # Publish to agent notifications topic
            future = self.publisher.publish(
                self.topics['agent_notifications'],
                message_data,
                event_type='agent_notification',
                notification_type=notification_data.get('type', 'general'),
                priority=notification_data.get('priority', 'medium'),
                target_agents=','.join(agents)
            )
            
            message_id = future.result()
            message_ids.append(message_id)
            logger.info(f"Notified agents {agents}: {message_id}")
            
            return message_ids
            
        except Exception as e:
            logger.error(f"Failed to notify agents: {e}")
            raise
    
    async def publish_crm_update(self, crm_data: Dict[str, Any]) -> str:
        """
        Publish a CRM update event.
        
        Args:
            crm_data: CRM update data
            
        Returns:
            Message ID of published event
        """
        try:
            event = {
                'event_type': 'crm_update',
                'timestamp': datetime.now().isoformat(),
                'data': crm_data
            }
            
            message_data = json.dumps(event).encode('utf-8')
            
            # Publish to CRM updates topic
            future = self.publisher.publish(
                self.topics['crm_updates'],
                message_data,
                event_type='crm_update',
                update_type=crm_data.get('update_type', 'unknown'),
                contact_id=crm_data.get('contact_id', 'unknown')
            )
            
            message_id = future.result()
            logger.info(f"Published CRM update event: {message_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to publish CRM update: {e}")
            raise
    
    def start_agent_listener(self, agent_type: str, callback_function):
        """
        Start listening for agent notifications.
        
        Args:
            agent_type: Type of agent ('executive_agent' or 'sales_dev_agent')
            callback_function: Function to call when message received
        """
        if agent_type not in self.subscriptions:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        subscription_path = self.subscriptions[agent_type]
        
        def callback(message):
            try:
                # Decode message
                event_data = json.loads(message.data.decode('utf-8'))
                
                # Call the callback function
                callback_function(event_data)
                
                # Acknowledge the message
                message.ack()
                
            except Exception as e:
                logger.error(f"Error processing message for {agent_type}: {e}")
                message.nack()
        
        # Start pulling messages
        streaming_pull_future = self.subscriber.subscribe(
            subscription_path,
            callback=callback,
            flow_control=pubsub_v1.types.FlowControl(max_messages=100)
        )
        
        logger.info(f"Started listening for {agent_type} notifications")
        
        try:
            streaming_pull_future.result()
        except KeyboardInterrupt:
            streaming_pull_future.cancel()
            logger.info(f"Stopped listening for {agent_type} notifications")


# Global service instance
_notification_service = None


async def get_notification_service(project_id: str = None) -> LeadNotificationService:
    """Get the global notification service instance."""
    global _notification_service
    
    if _notification_service is None:
        if project_id is None:
            import os
            project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'vertex-ai-agent-yzdlnjey')
        
        _notification_service = LeadNotificationService(project_id)
        await _notification_service.initialize()
    
    return _notification_service

"""
Gmail API client for email operations with service account authentication.

This module provides a Gmail client with authentication, email reading,
draft creation, and management capabilities using Application Default Credentials.
"""

import logging
import os
import base64
from typing import Optional, List, Dict, Any
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timezone

from google.auth import default
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class GmailClient:
    """
    Gmail API client with service account authentication and domain delegation.
    
    Uses Application Default Credentials for seamless authentication in
    both development and production environments.
    """
    
    # Gmail API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify',
        'https://www.googleapis.com/auth/gmail.compose'
    ]
    
    def __init__(self, subject_email: Optional[str] = None):
        """
        Initialize Gmail client.

        Args:
            subject_email: Email to impersonate with domain delegation (optional, uses config if not provided)
        """
        gmail_config = settings.gmail_config
        self.subject_email = subject_email or gmail_config.subject_email
        self.scopes = gmail_config.scopes
        self.service = None
        self._authenticated = False
    
    async def authenticate(self) -> bool:
        """
        Authenticate with Gmail API using Application Default Credentials.
        
        Returns:
            True if authentication succeeded, False otherwise
        """
        try:
            # Try to get service account key from Secret Manager first
            service_account_info = settings.get_gmail_service_account_key()

            if service_account_info:
                # Use service account from Secret Manager
                credentials = service_account.Credentials.from_service_account_info(
                    service_account_info,
                    scopes=self.scopes,
                    subject=self.subject_email
                )
                logger.info("Using Gmail service account from Secret Manager")
            else:
                # Fallback to environment variable or file
                service_account_key = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
                if service_account_key and os.path.exists(service_account_key):
                    # Use service account file
                    credentials = service_account.Credentials.from_service_account_file(
                        service_account_key,
                        scopes=self.scopes,
                        subject=self.subject_email
                    )
                    logger.info("Using Gmail service account from file")
                else:
                    # Try to use Application Default Credentials
                    # Note: For Gmail API with domain delegation, this requires special setup
                    credentials, project = default(scopes=self.scopes)

                    # For domain delegation, we need to create delegated credentials
                    if hasattr(credentials, 'with_subject'):
                        credentials = credentials.with_subject(self.subject_email)
                        logger.info("Using Application Default Credentials with domain delegation")
                    else:
                        logger.warning("Credentials don't support domain delegation, using as-is")
            
            # Build the Gmail service
            self.service = build('gmail', 'v1', credentials=credentials)
            self._authenticated = True
            
            logger.info(f"Gmail authentication successful for {self.subject_email}")
            return True
            
        except Exception as e:
            logger.error(f"Gmail authentication failed: {e}")
            logger.info("For Gmail API access, ensure proper service account setup with domain delegation")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if the client is authenticated."""
        return self._authenticated and self.service is not None

    async def get_profile(self) -> Dict[str, Any]:
        """
        Get the Gmail profile information.

        Returns:
            Dictionary containing profile information including email address,
            total messages, and threads count.
        """
        try:
            profile = self.service.users().getProfile(userId='me').execute()
            logger.info(f"Retrieved profile for: {profile.get('emailAddress')}")
            return profile

        except Exception as e:
            logger.error(f"Error getting profile: {e}")
            raise
    
    async def create_draft(
        self,
        to: str,
        subject: str,
        body: str,
        reply_to: Optional[str] = None,
        thread_id: Optional[str] = None,
        user_id: str = "me"
    ) -> Optional[str]:
        """
        Create an email draft in Gmail.
        
        Args:
            to: Recipient email address
            subject: Email subject
            body: Email body
            reply_to: Reply-to address (optional)
            thread_id: Thread ID for reply (optional)
            user_id: User ID (default: "me")
            
        Returns:
            Draft ID if successful, None otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            # Create message
            message = MIMEText(body)
            message['to'] = to
            message['subject'] = subject
            
            if reply_to:
                message['reply-to'] = reply_to
            
            # Encode message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            # Prepare draft request
            draft_request = {
                'message': {
                    'raw': raw_message
                }
            }
            
            if thread_id:
                draft_request['message']['threadId'] = thread_id
            
            # Create draft
            result = self.service.users().drafts().create(
                userId=user_id,
                body=draft_request
            ).execute()
            
            draft_id = result.get('id')
            logger.info(f"Draft created successfully: {draft_id}")
            
            return draft_id
            
        except HttpError as e:
            logger.error(f"Gmail API error creating draft: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to create draft: {e}")
            return None
    
    async def get_messages(
        self,
        query: str = "",
        max_results: int = 10,
        user_id: str = "me"
    ) -> List[Dict[str, Any]]:
        """
        Get Gmail messages based on search query.
        
        Args:
            query: Gmail search query (e.g., "is:unread", "from:<EMAIL>")
            max_results: Maximum number of messages to return
            user_id: User ID (default: "me")
            
        Returns:
            List of message dictionaries
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            # Get message list
            results = self.service.users().messages().list(
                userId=user_id,
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = results.get('messages', [])
            
            # Get full message details
            detailed_messages = []
            for message in messages:
                msg_detail = await self.get_message(message['id'], user_id)
                if msg_detail:
                    detailed_messages.append(msg_detail)
            
            logger.info(f"Retrieved {len(detailed_messages)} messages")
            return detailed_messages
            
        except HttpError as e:
            logger.error(f"Gmail API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to get messages: {e}")
            return []
    
    async def get_message(self, message_id: str, user_id: str = "me") -> Optional[Dict[str, Any]]:
        """
        Get a specific Gmail message by ID.
        
        Args:
            message_id: Gmail message ID
            user_id: User ID (default: "me")
            
        Returns:
            Message dictionary or None
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")
        
        try:
            message = self.service.users().messages().get(
                userId=user_id,
                id=message_id,
                format='full'
            ).execute()
            
            # Parse message details
            parsed_message = self._parse_message(message)
            logger.info(f"Retrieved message: {message_id}")
            
            return parsed_message
            
        except HttpError as e:
            logger.error(f"Gmail API error for message {message_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to get message {message_id}: {e}")
            return None
    
    def _parse_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Gmail message into a standardized format."""
        try:
            headers = message['payload'].get('headers', [])
            header_dict = {header['name'].lower(): header['value'] for header in headers}
            
            # Extract message body
            body = self._extract_message_body(message['payload'])
            
            return {
                'id': message['id'],
                'thread_id': message['threadId'],
                'label_ids': message.get('labelIds', []),
                'snippet': message.get('snippet', ''),
                'history_id': message.get('historyId'),
                'internal_date': message.get('internalDate'),
                'subject': header_dict.get('subject', ''),
                'from': header_dict.get('from', ''),
                'to': header_dict.get('to', ''),
                'cc': header_dict.get('cc', ''),
                'bcc': header_dict.get('bcc', ''),
                'date': header_dict.get('date', ''),
                'message_id': header_dict.get('message-id', ''),
                'body': body,
                'headers': header_dict
            }
            
        except Exception as e:
            logger.error(f"Failed to parse message: {e}")
            return {}
    
    def _extract_message_body(self, payload: Dict[str, Any]) -> str:
        """Extract message body from Gmail payload."""
        try:
            # Check if message has parts (multipart)
            if 'parts' in payload:
                for part in payload['parts']:
                    if part['mimeType'] == 'text/plain':
                        data = part['body'].get('data')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8')
                    elif part['mimeType'] == 'text/html':
                        # Fallback to HTML if no plain text
                        data = part['body'].get('data')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8')
            else:
                # Single part message
                if payload['mimeType'] in ['text/plain', 'text/html']:
                    data = payload['body'].get('data')
                    if data:
                        return base64.urlsafe_b64decode(data).decode('utf-8')
            
            return ""
            
        except Exception as e:
            logger.error(f"Failed to extract message body: {e}")
            return ""

    async def setup_watch(self, topic_name: str, label_ids: Optional[List[str]] = None, user_id: str = "me") -> Optional[Dict[str, Any]]:
        """
        Set up Gmail push notifications to a Pub/Sub topic.

        Args:
            topic_name: Full Pub/Sub topic name (e.g., "projects/project-id/topics/topic-name")
            label_ids: List of label IDs to watch (default: ["INBOX"])
            user_id: User ID (default: "me")

        Returns:
            Watch response dict if successful, None otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")

        if label_ids is None:
            label_ids = ["INBOX"]

        try:
            watch_request = {
                'topicName': topic_name,
                'labelIds': label_ids
            }

            result = self.service.users().watch(
                userId=user_id,
                body=watch_request
            ).execute()

            logger.info(f"Gmail watch set up successfully: {result}")
            return result

        except HttpError as e:
            logger.error(f"Gmail API error setting up watch: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to set up Gmail watch: {e}")
            return None

    async def stop_watch(self, user_id: str = "me") -> bool:
        """
        Stop Gmail push notifications.

        Args:
            user_id: User ID (default: "me")

        Returns:
            True if successful, False otherwise
        """
        if not self.is_authenticated():
            raise RuntimeError("Gmail client not authenticated")

        try:
            self.service.users().stop(userId=user_id).execute()
            logger.info("Gmail watch stopped successfully")
            return True

        except HttpError as e:
            logger.error(f"Gmail API error stopping watch: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to stop Gmail watch: {e}")
            return False


# Convenience functions

async def create_gmail_client(subject_email: Optional[str] = None) -> GmailClient:
    """
    Create and authenticate a Gmail client.

    Args:
        subject_email: Email to impersonate with domain delegation

    Returns:
        Authenticated Gmail client
    """
    client = GmailClient(subject_email=subject_email)
    success = await client.authenticate()

    if not success:
        raise RuntimeError("Failed to authenticate Gmail client")

    return client


async def get_gmail_client(subject_email: Optional[str] = None) -> GmailClient:
    """
    Get a Gmail client instance (alias for create_gmail_client).

    Args:
        subject_email: Email to impersonate with domain delegation

    Returns:
        Authenticated Gmail client
    """
    return await create_gmail_client(subject_email)

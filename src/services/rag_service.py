"""
RAG (Retrieval-Augmented Generation) Service for TKC_v5 Milestone 3

This module provides intelligent context retrieval and injection for conversation-aware responses.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from config.settings import Settings
from services.vector_db_client import get_vector_db_client
from agent.conversation_memory import ConversationMemory

logger = logging.getLogger(__name__)


class RAGService:
    """
    RAG service for intelligent conversation context retrieval and injection.
    
    Combines vector database search with conversation memory for context-aware responses.
    """
    
    def __init__(self, settings: Settings, mock_mode: bool = False):
        self.settings = settings
        self.mock_mode = mock_mode
        self.vector_db = None
        self.conversation_memory = None
        self._initialized = False
        
        # RAG configuration
        self.max_context_messages = 10
        self.similarity_threshold = 0.7
        self.max_retrieved_messages = 5
    
    async def initialize(self) -> bool:
        """
        Initialize the RAG service.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Initialize vector database client
            self.vector_db = await get_vector_db_client(self.settings, mock_mode=self.mock_mode)
            
            # Initialize conversation memory
            self.conversation_memory = ConversationMemory(self.settings.project_id)
            
            self._initialized = True
            logger.info("✅ RAG service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize RAG service: {e}")
            return False
    
    async def store_conversation_message(
        self,
        conversation_id: str,
        message_content: str,
        message_type: str,
        customer_email: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store a conversation message for future retrieval.
        
        Args:
            conversation_id: Conversation thread ID
            message_content: Content of the message
            message_type: Type of message (human, assistant, system)
            customer_email: Customer email address
            metadata: Additional metadata
            
        Returns:
            True if successful, False otherwise
        """
        if not self._initialized:
            logger.error("RAG service not initialized")
            return False
        
        try:
            # Generate unique message ID
            timestamp = datetime.now().isoformat()
            message_id = f"{conversation_id}_{message_type}_{timestamp}"
            
            # Prepare enhanced metadata
            enhanced_metadata = {
                "customer_email": customer_email,
                "timestamp": timestamp,
                "message_length": len(message_content),
                **(metadata or {})
            }
            
            # Store in vector database
            success = await self.vector_db.store_conversation_message(
                message_id=message_id,
                conversation_id=conversation_id,
                content=message_content,
                message_type=message_type,
                metadata=enhanced_metadata
            )
            
            if success:
                logger.info(f"✅ Message stored for RAG: {message_id}")
            else:
                logger.error(f"❌ Failed to store message for RAG: {message_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error storing conversation message: {e}")
            return False
    
    async def get_relevant_context(
        self,
        query: str,
        conversation_id: Optional[str] = None,
        customer_email: Optional[str] = None,
        include_conversation_history: bool = True
    ) -> Dict[str, Any]:
        """
        Get relevant context for a query using RAG.
        
        Args:
            query: The query or message to find context for
            conversation_id: Current conversation ID
            customer_email: Customer email for filtering
            include_conversation_history: Whether to include recent conversation history
            
        Returns:
            Dictionary containing relevant context and metadata
        """
        if not self._initialized:
            logger.error("RAG service not initialized")
            return {"context": "", "sources": [], "conversation_history": []}
        
        try:
            context_parts = []
            sources = []
            conversation_history = []
            
            # 1. Get recent conversation history if requested
            if include_conversation_history and conversation_id:
                conversation_history = await self._get_conversation_history(conversation_id)
                if conversation_history:
                    context_parts.append("Recent conversation history:")
                    for msg in conversation_history[-5:]:  # Last 5 messages
                        role = msg["metadata"].get("message_type", "unknown")
                        content = msg["metadata"].get("content", "")
                        context_parts.append(f"{role.title()}: {content}")
                    context_parts.append("")  # Empty line separator
            
            # 2. Search for semantically similar messages
            similar_messages = await self.vector_db.search_similar_messages(
                query=query,
                conversation_id=None,  # Search across all conversations for broader context
                top_k=self.max_retrieved_messages,
                min_score=self.similarity_threshold
            )
            
            if similar_messages:
                context_parts.append("Relevant previous interactions:")
                for msg in similar_messages:
                    content = msg["metadata"].get("content", "")
                    customer = msg["metadata"].get("customer_email", "unknown")
                    timestamp = msg["metadata"].get("timestamp", "")
                    
                    # Add to context
                    context_parts.append(f"Customer {customer} ({timestamp}): {content}")
                    
                    # Add to sources
                    sources.append({
                        "id": msg["id"],
                        "score": msg["score"],
                        "customer_email": customer,
                        "timestamp": timestamp,
                        "conversation_id": msg["metadata"].get("conversation_id")
                    })
                
                context_parts.append("")  # Empty line separator
            
            # 3. Get customer-specific context if email provided
            if customer_email:
                customer_context = await self._get_customer_context(customer_email)
                if customer_context:
                    context_parts.append(f"Customer profile for {customer_email}:")
                    context_parts.append(customer_context)
                    context_parts.append("")
            
            # Combine all context
            full_context = "\n".join(context_parts).strip()
            
            result = {
                "context": full_context,
                "sources": sources,
                "conversation_history": conversation_history,
                "metadata": {
                    "query": query,
                    "conversation_id": conversation_id,
                    "customer_email": customer_email,
                    "similar_messages_count": len(similar_messages),
                    "context_length": len(full_context),
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            logger.info(f"✅ Retrieved RAG context: {len(similar_messages)} similar messages, {len(conversation_history)} history messages")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error getting relevant context: {e}")
            return {"context": "", "sources": [], "conversation_history": []}
    
    async def _get_conversation_history(self, conversation_id: str) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        try:
            return await self.vector_db.get_conversation_context(
                conversation_id=conversation_id,
                max_messages=self.max_context_messages
            )
        except Exception as e:
            logger.error(f"❌ Error getting conversation history: {e}")
            return []
    
    async def _get_customer_context(self, customer_email: str) -> Optional[str]:
        """Get customer-specific context from conversation memory."""
        try:
            # Get customer profile from conversation memory
            customer_profile = await self.conversation_memory._get_customer_profile(customer_email)
            
            if customer_profile:
                context_parts = []
                
                # Add customer details
                if customer_profile.get("name"):
                    context_parts.append(f"Name: {customer_profile['name']}")
                
                if customer_profile.get("company"):
                    context_parts.append(f"Company: {customer_profile['company']}")
                
                if customer_profile.get("preferences"):
                    context_parts.append(f"Preferences: {customer_profile['preferences']}")
                
                # Add interaction summary
                interaction_count = customer_profile.get("interaction_count", 0)
                if interaction_count > 0:
                    context_parts.append(f"Previous interactions: {interaction_count}")
                
                last_interaction = customer_profile.get("last_interaction")
                if last_interaction:
                    context_parts.append(f"Last contact: {last_interaction}")
                
                return "; ".join(context_parts)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting customer context: {e}")
            return None
    
    async def enhance_prompt_with_context(
        self,
        original_prompt: str,
        query: str,
        conversation_id: Optional[str] = None,
        customer_email: Optional[str] = None
    ) -> str:
        """
        Enhance a prompt with relevant context from RAG.
        
        Args:
            original_prompt: The original system prompt
            query: The user query or message
            conversation_id: Current conversation ID
            customer_email: Customer email
            
        Returns:
            Enhanced prompt with context
        """
        if not self._initialized:
            logger.warning("RAG service not initialized, returning original prompt")
            return original_prompt
        
        try:
            # Get relevant context
            context_data = await self.get_relevant_context(
                query=query,
                conversation_id=conversation_id,
                customer_email=customer_email
            )
            
            context = context_data.get("context", "")
            
            if not context:
                logger.info("No relevant context found, using original prompt")
                return original_prompt
            
            # Enhance the prompt with context
            enhanced_prompt = f"""{original_prompt}

RELEVANT CONTEXT:
{context}

Please use this context to provide a more informed and personalized response. Reference previous interactions when relevant, but don't mention the context retrieval process to the user."""
            
            logger.info(f"✅ Enhanced prompt with {len(context)} characters of context")
            return enhanced_prompt
            
        except Exception as e:
            logger.error(f"❌ Error enhancing prompt with context: {e}")
            return original_prompt
    
    async def analyze_conversation_patterns(
        self,
        customer_email: Optional[str] = None,
        days_back: int = 30
    ) -> Dict[str, Any]:
        """
        Analyze conversation patterns for insights.
        
        Args:
            customer_email: Optional customer filter
            days_back: Number of days to analyze
            
        Returns:
            Analysis results
        """
        if not self._initialized:
            logger.error("RAG service not initialized")
            return {}
        
        try:
            # This is a placeholder for advanced analytics
            # In a full implementation, you would:
            # 1. Query vector database for recent conversations
            # 2. Analyze message patterns, topics, sentiment
            # 3. Identify common issues and resolutions
            # 4. Generate insights and recommendations
            
            analysis = {
                "customer_email": customer_email,
                "analysis_period_days": days_back,
                "timestamp": datetime.now().isoformat(),
                "insights": [
                    "Pattern analysis feature coming in future updates",
                    "Will include topic clustering, sentiment analysis, and trend identification"
                ]
            }
            
            logger.info(f"✅ Generated conversation analysis for {customer_email or 'all customers'}")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Error analyzing conversation patterns: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on RAG service.
        
        Returns:
            Health status information
        """
        if not self._initialized:
            return {
                "status": "unhealthy",
                "error": "RAG service not initialized"
            }
        
        try:
            # Check vector database health
            vector_health = await self.vector_db.health_check()
            
            # Check conversation memory health
            memory_health = {"status": "healthy"}  # Simplified for now
            
            return {
                "status": "healthy",
                "vector_database": vector_health,
                "conversation_memory": memory_health,
                "configuration": {
                    "max_context_messages": self.max_context_messages,
                    "similarity_threshold": self.similarity_threshold,
                    "max_retrieved_messages": self.max_retrieved_messages
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global instance
_rag_service: Optional[RAGService] = None


async def get_rag_service(settings: Settings, mock_mode: bool = False) -> RAGService:
    """
    Get or create the global RAG service instance.
    
    Args:
        settings: Application settings
        mock_mode: Whether to use mock mode for testing
        
    Returns:
        RAGService instance
    """
    global _rag_service
    
    if _rag_service is None:
        _rag_service = RAGService(settings, mock_mode=mock_mode)
        await _rag_service.initialize()
    
    return _rag_service

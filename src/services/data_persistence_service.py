"""
Data Persistence Service - TKC_v5 Multi-Tenant Architecture

Provides comprehensive data persistence across Firestore, Redis, and Pinecone
with customer isolation and backup systems.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
import json
from datetime import datetime, timedelta

from google.cloud import firestore
from google.cloud import secretmanager
import redis.asyncio as redis

from services.pinecone_tenant_manager import get_pinecone_tenant_manager
from services.tenant_redis_checkpointer import TenantRedisCheckpointer
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class DataPersistenceService:
    """
    Comprehensive data persistence service for multi-tenant architecture.
    
    Manages data across Firestore (conversations), Redis (sessions), 
    and Pinecone (vectors) with customer isolation.
    """
    
    def __init__(self):
        self.project_id = settings.google_cloud_project
        self.firestore_client = None
        self.redis_client = None
        self.pinecone_manager = None
        self.secret_client = secretmanager.SecretManagerServiceClient()
        
    async def initialize(self) -> bool:
        """Initialize all data persistence services."""
        try:
            # Initialize Firestore
            self.firestore_client = firestore.Client(project=self.project_id)
            
            # Initialize Redis
            redis_url = await self._get_redis_url()
            self.redis_client = redis.from_url(redis_url)
            
            # Initialize Pinecone tenant manager
            self.pinecone_manager = await get_pinecone_tenant_manager()
            
            logger.info("✅ Data persistence service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize data persistence service: {e}")
            return False
    
    async def _get_redis_url(self) -> str:
        """Get Redis connection URL from Secret Manager."""
        try:
            secret_name = f"projects/{self.project_id}/secrets/redis-url/versions/latest"
            response = self.secret_client.access_secret_version(request={"name": secret_name})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.warning(f"Could not get Redis URL from secrets, using default: {e}")
            return "redis://localhost:6379"
    
    async def store_conversation(self, 
                                customer_id: str,
                                conversation_data: Dict[str, Any]) -> str:
        """Store a complete conversation with multi-layer persistence."""
        try:
            if not self.firestore_client:
                await self.initialize()
            
            conversation_id = conversation_data.get('id', f"conv_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            # 1. Store in Firestore for structured data
            firestore_data = {
                'customer_id': customer_id,
                'conversation_id': conversation_id,
                'participants': conversation_data.get('participants', []),
                'subject': conversation_data.get('subject', ''),
                'thread_id': conversation_data.get('thread_id', ''),
                'messages': conversation_data.get('messages', []),
                'metadata': conversation_data.get('metadata', {}),
                'created_at': firestore.SERVER_TIMESTAMP,
                'updated_at': firestore.SERVER_TIMESTAMP,
                'status': 'active'
            }
            
            # Use customer-specific collection
            collection_name = f"conversations_{customer_id}"
            doc_ref = self.firestore_client.collection(collection_name).document(conversation_id)
            doc_ref.set(firestore_data)
            
            # 2. Store conversation state in Redis for quick access
            redis_key = f"conversation:{customer_id}:{conversation_id}"
            redis_data = {
                'id': conversation_id,
                'customer_id': customer_id,
                'last_activity': datetime.now().isoformat(),
                'message_count': len(conversation_data.get('messages', [])),
                'status': 'active'
            }
            
            await self.redis_client.setex(
                redis_key,
                timedelta(days=30).total_seconds(),  # 30-day expiry
                json.dumps(redis_data)
            )
            
            # 3. Store vector embeddings in Pinecone for RAG
            if conversation_data.get('vectors'):
                await self.pinecone_manager.store_conversation_vectors(
                    customer_id,
                    conversation_id,
                    conversation_data['vectors']
                )
            
            logger.info(f"✅ Stored conversation {conversation_id} for customer {customer_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"❌ Failed to store conversation: {e}")
            raise
    
    async def get_conversation(self, 
                             customer_id: str, 
                             conversation_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a conversation with all associated data."""
        try:
            if not self.firestore_client:
                await self.initialize()
            
            # Get from Firestore
            collection_name = f"conversations_{customer_id}"
            doc_ref = self.firestore_client.collection(collection_name).document(conversation_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return None
            
            conversation_data = doc.to_dict()
            
            # Enrich with Redis session data
            redis_key = f"conversation:{customer_id}:{conversation_id}"
            redis_data = await self.redis_client.get(redis_key)
            
            if redis_data:
                session_data = json.loads(redis_data)
                conversation_data['session'] = session_data
            
            logger.info(f"✅ Retrieved conversation {conversation_id} for customer {customer_id}")
            return conversation_data
            
        except Exception as e:
            logger.error(f"❌ Failed to get conversation: {e}")
            return None
    
    async def search_conversations(self, 
                                 customer_id: str,
                                 query: str,
                                 limit: int = 10) -> List[Dict[str, Any]]:
        """Search conversations using vector similarity."""
        try:
            if not self.pinecone_manager:
                await self.initialize()
            
            # This would require embedding the query first
            # For now, we'll do a simple Firestore text search
            collection_name = f"conversations_{customer_id}"
            conversations_ref = self.firestore_client.collection(collection_name)
            
            # Simple text search (in production, use full-text search)
            query_ref = conversations_ref.where('subject', '>=', query).limit(limit)
            docs = query_ref.stream()
            
            results = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                results.append(data)
            
            logger.info(f"✅ Found {len(results)} conversations for query: {query}")
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to search conversations: {e}")
            return []
    
    async def store_customer_profile(self, 
                                   customer_id: str,
                                   profile_data: Dict[str, Any]) -> bool:
        """Store customer profile and preferences."""
        try:
            if not self.firestore_client:
                await self.initialize()
            
            profile_doc = {
                'customer_id': customer_id,
                'company_name': profile_data.get('company_name', ''),
                'contact_email': profile_data.get('contact_email', ''),
                'preferences': profile_data.get('preferences', {}),
                'configuration': profile_data.get('configuration', {}),
                'subscription_tier': profile_data.get('subscription_tier', 'starter'),
                'created_at': firestore.SERVER_TIMESTAMP,
                'updated_at': firestore.SERVER_TIMESTAMP,
                'status': 'active'
            }
            
            # Store in customer profiles collection
            doc_ref = self.firestore_client.collection('customer_profiles').document(customer_id)
            doc_ref.set(profile_doc, merge=True)
            
            # Cache in Redis for quick access
            redis_key = f"customer_profile:{customer_id}"
            await self.redis_client.setex(
                redis_key,
                timedelta(hours=24).total_seconds(),
                json.dumps(profile_data)
            )
            
            logger.info(f"✅ Stored customer profile for {customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to store customer profile: {e}")
            return False
    
    async def get_customer_profile(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get customer profile with caching."""
        try:
            # Try Redis cache first
            redis_key = f"customer_profile:{customer_id}"
            cached_data = await self.redis_client.get(redis_key)
            
            if cached_data:
                return json.loads(cached_data)
            
            # Fall back to Firestore
            if not self.firestore_client:
                await self.initialize()
            
            doc_ref = self.firestore_client.collection('customer_profiles').document(customer_id)
            doc = doc_ref.get()
            
            if doc.exists:
                profile_data = doc.to_dict()
                
                # Cache for next time
                await self.redis_client.setex(
                    redis_key,
                    timedelta(hours=24).total_seconds(),
                    json.dumps(profile_data)
                )
                
                return profile_data
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get customer profile: {e}")
            return None
    
    async def get_customer_analytics(self, customer_id: str) -> Dict[str, Any]:
        """Get analytics and usage statistics for a customer."""
        try:
            if not self.firestore_client:
                await self.initialize()
            
            # Get conversation count
            collection_name = f"conversations_{customer_id}"
            conversations_ref = self.firestore_client.collection(collection_name)
            conversation_count = len(list(conversations_ref.stream()))
            
            # Get recent activity from Redis
            pattern = f"conversation:{customer_id}:*"
            redis_keys = await self.redis_client.keys(pattern)
            active_conversations = len(redis_keys)
            
            # Get vector statistics from Pinecone
            vector_stats = await self.pinecone_manager.get_tenant_stats(customer_id)
            
            analytics = {
                'customer_id': customer_id,
                'total_conversations': conversation_count,
                'active_conversations': active_conversations,
                'vector_count': vector_stats.get('estimated_vector_count', 0),
                'last_activity': datetime.now().isoformat(),
                'storage_usage': {
                    'firestore_documents': conversation_count,
                    'redis_keys': active_conversations,
                    'pinecone_vectors': vector_stats.get('estimated_vector_count', 0)
                }
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"❌ Failed to get customer analytics: {e}")
            return {}
    
    async def backup_customer_data(self, customer_id: str) -> Dict[str, Any]:
        """Create a complete backup of customer data."""
        try:
            backup_data = {
                'customer_id': customer_id,
                'backup_timestamp': datetime.now().isoformat(),
                'data': {}
            }
            
            # Backup customer profile
            profile = await self.get_customer_profile(customer_id)
            if profile:
                backup_data['data']['profile'] = profile
            
            # Backup conversations
            collection_name = f"conversations_{customer_id}"
            conversations_ref = self.firestore_client.collection(collection_name)
            conversations = []
            
            for doc in conversations_ref.stream():
                conv_data = doc.to_dict()
                conv_data['id'] = doc.id
                conversations.append(conv_data)
            
            backup_data['data']['conversations'] = conversations
            
            # Get vector statistics (actual vector backup would be more complex)
            vector_stats = await self.pinecone_manager.get_tenant_stats(customer_id)
            backup_data['data']['vector_stats'] = vector_stats
            
            logger.info(f"✅ Created backup for customer {customer_id}")
            return backup_data
            
        except Exception as e:
            logger.error(f"❌ Failed to backup customer data: {e}")
            return {}
    
    async def delete_customer_data(self, customer_id: str) -> bool:
        """Delete all customer data (GDPR compliance)."""
        try:
            # Delete from Firestore
            collection_name = f"conversations_{customer_id}"
            conversations_ref = self.firestore_client.collection(collection_name)
            
            # Delete all conversation documents
            docs = conversations_ref.stream()
            for doc in docs:
                doc.reference.delete()
            
            # Delete customer profile
            profile_ref = self.firestore_client.collection('customer_profiles').document(customer_id)
            profile_ref.delete()
            
            # Delete from Redis
            pattern = f"*:{customer_id}:*"
            redis_keys = await self.redis_client.keys(pattern)
            if redis_keys:
                await self.redis_client.delete(*redis_keys)
            
            # Delete from Pinecone
            await self.pinecone_manager.delete_customer_data(customer_id)
            
            logger.info(f"✅ Deleted all data for customer {customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete customer data: {e}")
            return False


# Global instance
_data_persistence_service = None

async def get_data_persistence_service() -> DataPersistenceService:
    """Get the global data persistence service instance."""
    global _data_persistence_service
    
    if _data_persistence_service is None:
        _data_persistence_service = DataPersistenceService()
        await _data_persistence_service.initialize()
    
    return _data_persistence_service

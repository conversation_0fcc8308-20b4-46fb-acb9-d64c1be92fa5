"""
Multi-Tenant Manager - TKC_v5 Commercial Package

Provides customer isolation and tenant management for commercial deployment.
Handles vector database namespacing, Redis tenant separation, and configuration management.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import hashlib
import json

from google.cloud import secretmanager
import redis.asyncio as redis
import pinecone

from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class TenantManager:
    """
    Manages multi-tenant architecture for commercial customers.
    
    Provides:
    - Customer isolation in vector database and Redis
    - Tenant-specific configuration management
    - Resource allocation and monitoring
    - Security and access control
    """
    
    def __init__(self):
        self.secret_client = secretmanager.SecretManagerServiceClient()
        self.project_id = settings.google_cloud_project
        self._tenant_cache = {}
        self._redis_clients = {}
        
    async def initialize_tenant(self, customer_id: str, tenant_config: Dict[str, Any]) -> bool:
        """
        Initialize a new tenant with isolated resources.
        
        Args:
            customer_id: Unique customer identifier
            tenant_config: Tenant-specific configuration
            
        Returns:
            True if initialization successful
        """
        try:
            logger.info(f"Initializing tenant: {customer_id}")
            
            # 1. Create tenant namespace
            namespace = self._generate_namespace(customer_id)
            
            # 2. Initialize vector database namespace
            await self._initialize_vector_namespace(customer_id, namespace)
            
            # 3. Initialize Redis tenant separation
            await self._initialize_redis_tenant(customer_id, namespace)
            
            # 4. Store tenant configuration
            await self._store_tenant_config(customer_id, tenant_config, namespace)
            
            # 5. Initialize CRM credentials if provided
            if tenant_config.get('crm_config'):
                await self._store_crm_credentials(customer_id, tenant_config['crm_config'])
            
            # 6. Cache tenant information
            self._tenant_cache[customer_id] = {
                'namespace': namespace,
                'config': tenant_config,
                'initialized_at': datetime.utcnow().isoformat(),
                'status': 'active'
            }
            
            logger.info(f"✅ Tenant {customer_id} initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize tenant {customer_id}: {e}")
            return False
    
    def _generate_namespace(self, customer_id: str) -> str:
        """Generate a unique namespace for the customer."""
        # Create a hash-based namespace for consistency
        hash_input = f"tkc_v5_{customer_id}_{settings.environment}"
        namespace_hash = hashlib.sha256(hash_input.encode()).hexdigest()[:16]
        return f"tkc_{namespace_hash}"
    
    async def _initialize_vector_namespace(self, customer_id: str, namespace: str):
        """Initialize customer-specific vector database namespace."""
        try:
            # Get Pinecone configuration
            pinecone_config = await self._get_pinecone_config()
            
            # Initialize Pinecone with customer namespace
            from pinecone import Pinecone
            pc = Pinecone(api_key=pinecone_config['api_key'])

            # Check if index exists, create if needed
            index_name = pinecone_config['index_name']
            indexes = pc.list_indexes()
            index_names = [idx.name for idx in indexes]
            if index_name not in index_names:
                logger.warning(f"Index {index_name} not found - using existing production index")
            
            # Store namespace mapping
            await self._store_namespace_mapping(customer_id, namespace, index_name)
            
            logger.info(f"✅ Vector namespace initialized for {customer_id}: {namespace}")
            
        except Exception as e:
            logger.error(f"❌ Vector namespace initialization failed for {customer_id}: {e}")
            raise
    
    async def _initialize_redis_tenant(self, customer_id: str, namespace: str):
        """Initialize customer-specific Redis tenant separation."""
        try:
            # Create Redis client with tenant-specific key prefix
            redis_config = settings.redis_config
            
            redis_client = redis.Redis(
                host=redis_config.host,
                port=redis_config.port,
                password=redis_config.password,
                decode_responses=True,
                db=0  # Use same database but different key prefixes
            )
            
            # Test connection
            await redis_client.ping()
            
            # Store client with tenant prefix
            self._redis_clients[customer_id] = {
                'client': redis_client,
                'prefix': f"{namespace}:",
                'namespace': namespace
            }
            
            logger.info(f"✅ Redis tenant separation initialized for {customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Redis tenant initialization failed for {customer_id}: {e}")
            raise
    
    async def _store_tenant_config(self, customer_id: str, config: Dict[str, Any], namespace: str):
        """Store tenant-specific configuration in Secret Manager."""
        try:
            # Prepare tenant configuration
            tenant_config = {
                'customer_id': customer_id,
                'namespace': namespace,
                'config': config,
                'created_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            # Store in Secret Manager
            secret_name = f"tenant-config-{customer_id}"
            secret_path = f"projects/{self.project_id}/secrets/{secret_name}"
            
            try:
                # Try to create new secret
                self.secret_client.create_secret(
                    request={
                        "parent": f"projects/{self.project_id}",
                        "secret_id": secret_name,
                        "secret": {"replication": {"automatic": {}}}
                    }
                )
            except Exception:
                # Secret already exists, that's fine
                pass
            
            # Add secret version
            self.secret_client.add_secret_version(
                request={
                    "parent": secret_path,
                    "payload": {"data": json.dumps(tenant_config).encode("UTF-8")}
                }
            )
            
            logger.info(f"✅ Tenant configuration stored for {customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store tenant config for {customer_id}: {e}")
            raise
    
    async def _store_crm_credentials(self, customer_id: str, crm_config: Dict[str, Any]):
        """Store CRM credentials for the tenant."""
        try:
            for crm_type, credentials in crm_config.items():
                secret_name = f"crm-{customer_id}-{crm_type}"
                secret_path = f"projects/{self.project_id}/secrets/{secret_name}"
                
                try:
                    # Try to create new secret
                    self.secret_client.create_secret(
                        request={
                            "parent": f"projects/{self.project_id}",
                            "secret_id": secret_name,
                            "secret": {"replication": {"automatic": {}}}
                        }
                    )
                except Exception:
                    # Secret already exists, that's fine
                    pass
                
                # Add secret version
                self.secret_client.add_secret_version(
                    request={
                        "parent": secret_path,
                        "payload": {"data": json.dumps(credentials).encode("UTF-8")}
                    }
                )
            
            logger.info(f"✅ CRM credentials stored for {customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store CRM credentials for {customer_id}: {e}")
            raise
    
    async def _store_namespace_mapping(self, customer_id: str, namespace: str, index_name: str):
        """Store namespace mapping for vector database operations."""
        try:
            mapping = {
                'customer_id': customer_id,
                'namespace': namespace,
                'index_name': index_name,
                'created_at': datetime.utcnow().isoformat()
            }
            
            secret_name = f"namespace-mapping-{customer_id}"
            secret_path = f"projects/{self.project_id}/secrets/{secret_name}"
            
            try:
                self.secret_client.create_secret(
                    request={
                        "parent": f"projects/{self.project_id}",
                        "secret_id": secret_name,
                        "secret": {"replication": {"automatic": {}}}
                    }
                )
            except Exception:
                pass
            
            self.secret_client.add_secret_version(
                request={
                    "parent": secret_path,
                    "payload": {"data": json.dumps(mapping).encode("UTF-8")}
                }
            )
            
            logger.info(f"✅ Namespace mapping stored for {customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store namespace mapping for {customer_id}: {e}")
            raise
    
    async def get_tenant_config(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve tenant configuration."""
        try:
            # Check cache first
            if customer_id in self._tenant_cache:
                return self._tenant_cache[customer_id]
            
            # Load from Secret Manager
            secret_name = f"tenant-config-{customer_id}"
            secret_path = f"projects/{self.project_id}/secrets/{secret_name}/versions/latest"
            
            response = self.secret_client.access_secret_version(request={"name": secret_path})
            config_data = json.loads(response.payload.data.decode("UTF-8"))
            
            # Cache the configuration
            self._tenant_cache[customer_id] = config_data
            
            return config_data
            
        except Exception as e:
            logger.error(f"❌ Failed to get tenant config for {customer_id}: {e}")
            return None
    
    async def get_redis_client(self, customer_id: str) -> Optional[redis.Redis]:
        """Get Redis client for specific tenant."""
        try:
            if customer_id not in self._redis_clients:
                # Initialize Redis client for this tenant
                tenant_config = await self.get_tenant_config(customer_id)
                if not tenant_config:
                    return None
                
                namespace = tenant_config['namespace']
                await self._initialize_redis_tenant(customer_id, namespace)
            
            return self._redis_clients[customer_id]['client']
            
        except Exception as e:
            logger.error(f"❌ Failed to get Redis client for {customer_id}: {e}")
            return None
    
    def get_redis_key(self, customer_id: str, key: str) -> str:
        """Get tenant-specific Redis key."""
        if customer_id in self._redis_clients:
            prefix = self._redis_clients[customer_id]['prefix']
            return f"{prefix}{key}"
        else:
            # Fallback to customer_id prefix
            return f"{customer_id}:{key}"
    
    def get_vector_namespace(self, customer_id: str) -> str:
        """Get vector database namespace for customer."""
        if customer_id in self._tenant_cache:
            return self._tenant_cache[customer_id]['namespace']
        else:
            # Generate namespace if not cached
            return self._generate_namespace(customer_id)
    
    async def _get_pinecone_config(self) -> Dict[str, str]:
        """Get Pinecone configuration from Secret Manager."""
        try:
            secret_path = f"projects/{self.project_id}/secrets/pinecone-config/versions/latest"
            response = self.secret_client.access_secret_version(request={"name": secret_path})
            return json.loads(response.payload.data.decode("UTF-8"))
        except Exception as e:
            logger.error(f"❌ Failed to get Pinecone config: {e}")
            raise
    
    async def list_tenants(self) -> List[str]:
        """List all active tenants."""
        try:
            # This would query Secret Manager for all tenant configs
            # For now, return cached tenants
            return list(self._tenant_cache.keys())
        except Exception as e:
            logger.error(f"❌ Failed to list tenants: {e}")
            return []
    
    async def deactivate_tenant(self, customer_id: str) -> bool:
        """Deactivate a tenant (soft delete)."""
        try:
            if customer_id in self._tenant_cache:
                self._tenant_cache[customer_id]['status'] = 'inactive'
                self._tenant_cache[customer_id]['deactivated_at'] = datetime.utcnow().isoformat()
            
            # Close Redis connection
            if customer_id in self._redis_clients:
                await self._redis_clients[customer_id]['client'].close()
                del self._redis_clients[customer_id]
            
            logger.info(f"✅ Tenant {customer_id} deactivated")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to deactivate tenant {customer_id}: {e}")
            return False


# Global tenant manager instance
_tenant_manager = None

async def get_tenant_manager() -> TenantManager:
    """Get the global tenant manager instance."""
    global _tenant_manager
    if _tenant_manager is None:
        _tenant_manager = TenantManager()
    return _tenant_manager

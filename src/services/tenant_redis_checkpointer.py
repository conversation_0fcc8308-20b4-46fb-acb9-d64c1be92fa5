"""
Tenant Redis Checkpointer - TKC_v5 Commercial Package

Provides tenant-isolated Redis checkpointing for multi-customer deployment.
Extends the existing Redis checkpointer with customer isolation.
"""

import logging
from typing import Dict, List, Any, Optional, Iterator, Tuple
import json
import asyncio
from datetime import datetime

import redis.asyncio as redis
from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointMetadata

from services.multi_tenant_manager import get_tenant_manager
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class TenantRedisCheckpointer:
    """
    Tenant-isolated Redis checkpointer for conversation state management.
    
    Provides the same functionality as the base Redis checkpointer but with
    customer isolation through key prefixing.
    """
    
    def __init__(self, customer_id: str):
        self.customer_id = customer_id
        self.tenant_manager = None
        self.redis_client = None
        self.key_prefix = None
        
    async def initialize(self):
        """Initialize the tenant Redis checkpointer."""
        try:
            # Get tenant manager
            self.tenant_manager = await get_tenant_manager()
            
            # Get tenant-specific Redis client
            self.redis_client = await self.tenant_manager.get_redis_client(self.customer_id)
            
            if not self.redis_client:
                raise Exception(f"Failed to get Redis client for tenant {self.customer_id}")
            
            # Get tenant-specific key prefix
            self.key_prefix = self.tenant_manager.get_redis_key(self.customer_id, "")
            
            logger.info(f"✅ Tenant Redis checkpointer initialized for {self.customer_id} (prefix: {self.key_prefix})")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize tenant Redis checkpointer for {self.customer_id}: {e}")
            raise
    
    def _get_key(self, thread_id: str, checkpoint_id: str = None) -> str:
        """Generate tenant-specific Redis key."""
        if checkpoint_id:
            return f"{self.key_prefix}checkpoint:{thread_id}:{checkpoint_id}"
        else:
            return f"{self.key_prefix}thread:{thread_id}"
    
    def _get_metadata_key(self, thread_id: str) -> str:
        """Generate tenant-specific metadata key."""
        return f"{self.key_prefix}metadata:{thread_id}"
    
    async def save_checkpoint(
        self,
        thread_id: str,
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata = None
    ) -> str:
        """
        Save checkpoint with tenant isolation.
        
        Args:
            thread_id: Thread identifier
            checkpoint: Checkpoint data
            metadata: Optional metadata
            
        Returns:
            Checkpoint ID
        """
        try:
            # Generate checkpoint ID
            checkpoint_id = f"checkpoint_{int(datetime.utcnow().timestamp() * 1000)}"
            
            # Prepare checkpoint data
            checkpoint_data = {
                'customer_id': self.customer_id,
                'thread_id': thread_id,
                'checkpoint_id': checkpoint_id,
                'checkpoint': checkpoint.dict() if hasattr(checkpoint, 'dict') else checkpoint,
                'metadata': metadata.dict() if metadata and hasattr(metadata, 'dict') else metadata,
                'created_at': datetime.utcnow().isoformat()
            }
            
            # Save checkpoint
            checkpoint_key = self._get_key(thread_id, checkpoint_id)
            await self.redis_client.setex(
                checkpoint_key,
                settings.redis_config.ttl_seconds,
                json.dumps(checkpoint_data, default=str)
            )
            
            # Update thread metadata
            thread_key = self._get_key(thread_id)
            thread_data = {
                'customer_id': self.customer_id,
                'thread_id': thread_id,
                'latest_checkpoint_id': checkpoint_id,
                'updated_at': datetime.utcnow().isoformat()
            }
            
            await self.redis_client.setex(
                thread_key,
                settings.redis_config.ttl_seconds,
                json.dumps(thread_data, default=str)
            )
            
            # Save metadata separately for easier querying
            if metadata:
                metadata_key = self._get_metadata_key(thread_id)
                await self.redis_client.setex(
                    metadata_key,
                    settings.redis_config.ttl_seconds,
                    json.dumps(metadata.dict() if hasattr(metadata, 'dict') else metadata, default=str)
                )
            
            logger.info(f"✅ Checkpoint saved for tenant {self.customer_id}: {thread_id}/{checkpoint_id}")
            return checkpoint_id
            
        except Exception as e:
            logger.error(f"❌ Error saving checkpoint for tenant {self.customer_id}: {e}")
            raise
    
    async def get_checkpoint(
        self,
        thread_id: str,
        checkpoint_id: str = None
    ) -> Optional[Tuple[Checkpoint, CheckpointMetadata]]:
        """
        Get checkpoint with tenant isolation.
        
        Args:
            thread_id: Thread identifier
            checkpoint_id: Optional specific checkpoint ID
            
        Returns:
            Tuple of checkpoint and metadata, or None
        """
        try:
            if not checkpoint_id:
                # Get latest checkpoint ID
                thread_key = self._get_key(thread_id)
                thread_data_str = await self.redis_client.get(thread_key)
                
                if not thread_data_str:
                    return None
                
                thread_data = json.loads(thread_data_str)
                
                # Verify tenant isolation
                if thread_data.get('customer_id') != self.customer_id:
                    logger.warning(f"⚠️ Tenant isolation violation detected for {self.customer_id}")
                    return None
                
                checkpoint_id = thread_data.get('latest_checkpoint_id')
                
                if not checkpoint_id:
                    return None
            
            # Get checkpoint data
            checkpoint_key = self._get_key(thread_id, checkpoint_id)
            checkpoint_data_str = await self.redis_client.get(checkpoint_key)
            
            if not checkpoint_data_str:
                return None
            
            checkpoint_data = json.loads(checkpoint_data_str)
            
            # Verify tenant isolation
            if checkpoint_data.get('customer_id') != self.customer_id:
                logger.warning(f"⚠️ Tenant isolation violation detected for {self.customer_id}")
                return None
            
            # Extract checkpoint and metadata
            checkpoint = checkpoint_data.get('checkpoint')
            metadata = checkpoint_data.get('metadata')
            
            logger.info(f"✅ Checkpoint retrieved for tenant {self.customer_id}: {thread_id}/{checkpoint_id}")
            
            return (checkpoint, metadata)
            
        except Exception as e:
            logger.error(f"❌ Error getting checkpoint for tenant {self.customer_id}: {e}")
            return None
    
    async def list_checkpoints(
        self,
        thread_id: str,
        limit: int = 10
    ) -> List[Tuple[str, Checkpoint, CheckpointMetadata]]:
        """
        List checkpoints for a thread with tenant isolation.
        
        Args:
            thread_id: Thread identifier
            limit: Maximum number of checkpoints to return
            
        Returns:
            List of tuples (checkpoint_id, checkpoint, metadata)
        """
        try:
            # Search for checkpoints with tenant prefix
            pattern = f"{self.key_prefix}checkpoint:{thread_id}:*"
            keys = []
            
            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)
                if len(keys) >= limit:
                    break
            
            checkpoints = []
            
            for key in keys:
                checkpoint_data_str = await self.redis_client.get(key)
                if checkpoint_data_str:
                    checkpoint_data = json.loads(checkpoint_data_str)
                    
                    # Verify tenant isolation
                    if checkpoint_data.get('customer_id') == self.customer_id:
                        checkpoint_id = checkpoint_data.get('checkpoint_id')
                        checkpoint = checkpoint_data.get('checkpoint')
                        metadata = checkpoint_data.get('metadata')
                        
                        checkpoints.append((checkpoint_id, checkpoint, metadata))
            
            # Sort by creation time (newest first)
            checkpoints.sort(
                key=lambda x: x[2].get('created_at', '') if x[2] else '',
                reverse=True
            )
            
            logger.info(f"✅ Listed {len(checkpoints)} checkpoints for tenant {self.customer_id}: {thread_id}")
            return checkpoints[:limit]
            
        except Exception as e:
            logger.error(f"❌ Error listing checkpoints for tenant {self.customer_id}: {e}")
            return []
    
    async def delete_checkpoint(self, thread_id: str, checkpoint_id: str = None) -> bool:
        """
        Delete checkpoint with tenant isolation.
        
        Args:
            thread_id: Thread identifier
            checkpoint_id: Optional specific checkpoint ID (deletes all if None)
            
        Returns:
            True if deletion successful
        """
        try:
            if checkpoint_id:
                # Delete specific checkpoint
                checkpoint_key = self._get_key(thread_id, checkpoint_id)
                
                # Verify ownership before deletion
                checkpoint_data_str = await self.redis_client.get(checkpoint_key)
                if checkpoint_data_str:
                    checkpoint_data = json.loads(checkpoint_data_str)
                    if checkpoint_data.get('customer_id') != self.customer_id:
                        logger.warning(f"⚠️ Attempted to delete checkpoint from different tenant")
                        return False
                
                deleted = await self.redis_client.delete(checkpoint_key)
                
                logger.info(f"✅ Checkpoint deleted for tenant {self.customer_id}: {thread_id}/{checkpoint_id}")
                return deleted > 0
            else:
                # Delete all checkpoints for thread
                pattern = f"{self.key_prefix}checkpoint:{thread_id}:*"
                keys = []
                
                async for key in self.redis_client.scan_iter(match=pattern):
                    keys.append(key)
                
                if keys:
                    deleted = await self.redis_client.delete(*keys)
                    
                    # Also delete thread metadata
                    thread_key = self._get_key(thread_id)
                    await self.redis_client.delete(thread_key)
                    
                    metadata_key = self._get_metadata_key(thread_id)
                    await self.redis_client.delete(metadata_key)
                    
                    logger.info(f"✅ All checkpoints deleted for tenant {self.customer_id}: {thread_id}")
                    return deleted > 0
                
                return True
            
        except Exception as e:
            logger.error(f"❌ Error deleting checkpoint for tenant {self.customer_id}: {e}")
            return False
    
    async def get_tenant_stats(self) -> Dict[str, Any]:
        """
        Get statistics for this tenant's checkpoints.
        
        Returns:
            Dictionary with tenant checkpoint statistics
        """
        try:
            # Count checkpoints
            pattern = f"{self.key_prefix}checkpoint:*"
            checkpoint_count = 0
            
            async for key in self.redis_client.scan_iter(match=pattern):
                checkpoint_count += 1
            
            # Count threads
            thread_pattern = f"{self.key_prefix}thread:*"
            thread_count = 0
            
            async for key in self.redis_client.scan_iter(match=thread_pattern):
                thread_count += 1
            
            stats = {
                'customer_id': self.customer_id,
                'total_checkpoints': checkpoint_count,
                'total_threads': thread_count,
                'key_prefix': self.key_prefix,
                'last_updated': datetime.utcnow().isoformat()
            }
            
            logger.info(f"✅ Retrieved checkpoint stats for tenant {self.customer_id}")
            return stats
            
        except Exception as e:
            logger.error(f"❌ Error getting checkpoint stats for tenant {self.customer_id}: {e}")
            return {}
    
    def get_saver(self) -> 'TenantCheckpointSaver':
        """Get LangGraph-compatible checkpoint saver."""
        return TenantCheckpointSaver(self)


class TenantCheckpointSaver(BaseCheckpointSaver):
    """
    LangGraph-compatible checkpoint saver with tenant isolation.
    """
    
    def __init__(self, tenant_checkpointer: TenantRedisCheckpointer):
        self.tenant_checkpointer = tenant_checkpointer
    
    def put(
        self,
        config: Dict[str, Any],
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata = None
    ) -> None:
        """Save checkpoint (sync wrapper)."""
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        
        # Run async method in event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an async context, create a task
            asyncio.create_task(
                self.tenant_checkpointer.save_checkpoint(thread_id, checkpoint, metadata)
            )
        else:
            # If not in async context, run directly
            loop.run_until_complete(
                self.tenant_checkpointer.save_checkpoint(thread_id, checkpoint, metadata)
            )
    
    def get(
        self,
        config: Dict[str, Any]
    ) -> Optional[Checkpoint]:
        """Get checkpoint (sync wrapper)."""
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        
        # Run async method in event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # Create a future for the result
            future = asyncio.create_task(
                self.tenant_checkpointer.get_checkpoint(thread_id)
            )
            # This is a simplified approach - in production, you'd want better async handling
            return None
        else:
            result = loop.run_until_complete(
                self.tenant_checkpointer.get_checkpoint(thread_id)
            )
            return result[0] if result else None
    
    def list(
        self,
        config: Dict[str, Any],
        limit: int = 10
    ) -> Iterator[Checkpoint]:
        """List checkpoints (sync wrapper)."""
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        
        # Run async method in event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # Return empty iterator in async context
            return iter([])
        else:
            checkpoints = loop.run_until_complete(
                self.tenant_checkpointer.list_checkpoints(thread_id, limit)
            )
            return iter([checkpoint for _, checkpoint, _ in checkpoints])


# Factory function for tenant checkpointer
async def get_tenant_redis_checkpointer(customer_id: str) -> TenantRedisCheckpointer:
    """Get tenant-isolated Redis checkpointer."""
    checkpointer = TenantRedisCheckpointer(customer_id)
    await checkpointer.initialize()
    return checkpointer

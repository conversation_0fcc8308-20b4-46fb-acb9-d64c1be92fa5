"""
Advanced RAG Service - TKC_v5 Executive Agent

Provides sophisticated Retrieval-Augmented Generation capabilities with
conversation intelligence, context awareness, and predictive insights.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import numpy as np
from dataclasses import dataclass

from langchain_core.embeddings import Embeddings
from langchain_google_vertexai import VertexAIEmbeddings
from langchain_core.documents import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter
from sentence_transformers import SentenceTransformer

from services.pinecone_tenant_manager import get_pinecone_tenant_manager
from services.data_persistence_service import get_data_persistence_service
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class ConversationContext:
    """Rich conversation context with intelligence."""
    customer_id: str
    conversation_id: str
    participants: List[str]
    topic_categories: List[str]
    sentiment_score: float
    urgency_level: str
    key_entities: List[Dict[str, Any]]
    action_items: List[str]
    follow_up_required: bool
    context_summary: str


@dataclass
class RAGResult:
    """Enhanced RAG result with confidence and reasoning."""
    content: str
    confidence_score: float
    source_documents: List[Document]
    reasoning: str
    suggested_actions: List[str]
    context_used: ConversationContext


class AdvancedRAGService:
    """
    Advanced RAG service with conversation intelligence and predictive capabilities.
    
    Provides context-aware retrieval, conversation analysis, and intelligent
    recommendations for executive decision-making.
    """
    
    def __init__(self):
        self.project_id = settings.google_cloud_project
        self.embeddings = VertexAIEmbeddings(model_name="textembedding-gecko@003")
        self.local_embeddings = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Text processing
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
        
        # Conversation intelligence models
        self.topic_categories = [
            "business_strategy", "technical_discussion", "project_management",
            "sales_inquiry", "support_request", "partnership", "scheduling",
            "financial_discussion", "legal_matter", "hr_issue"
        ]
        
        self.urgency_keywords = {
            "urgent": ["urgent", "asap", "immediately", "emergency", "critical"],
            "high": ["important", "priority", "soon", "quickly", "deadline"],
            "medium": ["when possible", "convenient", "schedule", "plan"],
            "low": ["fyi", "information", "update", "casual", "whenever"]
        }
    
    async def analyze_conversation_context(self, 
                                         conversation_data: Dict[str, Any]) -> ConversationContext:
        """Analyze conversation to extract rich context and intelligence."""
        try:
            # Extract basic information
            customer_id = conversation_data.get('customer_id', 'unknown')
            conversation_id = conversation_data.get('id', 'unknown')
            participants = conversation_data.get('participants', [])
            messages = conversation_data.get('messages', [])
            
            # Combine all message content for analysis
            full_text = " ".join([msg.get('content', '') for msg in messages])
            
            # Topic categorization
            topic_categories = await self._categorize_topics(full_text)
            
            # Sentiment analysis
            sentiment_score = await self._analyze_sentiment(full_text)
            
            # Urgency detection
            urgency_level = self._detect_urgency(full_text)
            
            # Entity extraction
            key_entities = await self._extract_entities(full_text)
            
            # Action item detection
            action_items = self._extract_action_items(full_text)
            
            # Follow-up requirement
            follow_up_required = self._requires_follow_up(full_text, messages)
            
            # Context summary
            context_summary = await self._generate_context_summary(
                full_text, topic_categories, key_entities
            )
            
            return ConversationContext(
                customer_id=customer_id,
                conversation_id=conversation_id,
                participants=participants,
                topic_categories=topic_categories,
                sentiment_score=sentiment_score,
                urgency_level=urgency_level,
                key_entities=key_entities,
                action_items=action_items,
                follow_up_required=follow_up_required,
                context_summary=context_summary
            )
            
        except Exception as e:
            logger.error(f"Error analyzing conversation context: {e}")
            # Return minimal context on error
            return ConversationContext(
                customer_id=conversation_data.get('customer_id', 'unknown'),
                conversation_id=conversation_data.get('id', 'unknown'),
                participants=conversation_data.get('participants', []),
                topic_categories=["general"],
                sentiment_score=0.0,
                urgency_level="medium",
                key_entities=[],
                action_items=[],
                follow_up_required=False,
                context_summary="Context analysis unavailable"
            )
    
    async def enhanced_retrieval(self, 
                               query: str,
                               customer_id: str,
                               conversation_context: Optional[ConversationContext] = None,
                               max_results: int = 5) -> RAGResult:
        """Perform enhanced RAG retrieval with context awareness."""
        try:
            # Get vector database manager
            pinecone_manager = await get_pinecone_tenant_manager()
            
            # Generate query embedding
            query_embedding = await self._generate_embedding(query)
            
            # Enhanced query with context
            if conversation_context:
                enhanced_query = await self._enhance_query_with_context(
                    query, conversation_context
                )
                enhanced_embedding = await self._generate_embedding(enhanced_query)
            else:
                enhanced_query = query
                enhanced_embedding = query_embedding
            
            # Retrieve relevant documents
            search_results = await pinecone_manager.query_customer_vectors(
                customer_id=customer_id,
                query_vector=enhanced_embedding,
                top_k=max_results * 2,  # Get more for filtering
                filter_metadata=self._build_context_filter(conversation_context)
            )
            
            # Convert to documents and re-rank
            documents = []
            for result in search_results:
                doc = Document(
                    page_content=result['metadata'].get('content', ''),
                    metadata=result['metadata']
                )
                documents.append(doc)
            
            # Re-rank based on context relevance
            ranked_documents = await self._rerank_documents(
                documents, query, conversation_context
            )
            
            # Generate response with reasoning
            response_content = await self._generate_contextual_response(
                query, ranked_documents[:max_results], conversation_context
            )
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                query, ranked_documents, search_results
            )
            
            # Generate reasoning
            reasoning = await self._generate_reasoning(
                query, ranked_documents, conversation_context
            )
            
            # Suggest actions
            suggested_actions = await self._suggest_actions(
                query, conversation_context, ranked_documents
            )
            
            return RAGResult(
                content=response_content,
                confidence_score=confidence_score,
                source_documents=ranked_documents[:max_results],
                reasoning=reasoning,
                suggested_actions=suggested_actions,
                context_used=conversation_context
            )
            
        except Exception as e:
            logger.error(f"Error in enhanced retrieval: {e}")
            return RAGResult(
                content=f"I apologize, but I encountered an error while retrieving information: {str(e)}",
                confidence_score=0.0,
                source_documents=[],
                reasoning="Error occurred during retrieval",
                suggested_actions=["Please try rephrasing your question"],
                context_used=conversation_context
            )
    
    async def _categorize_topics(self, text: str) -> List[str]:
        """Categorize conversation topics using embeddings."""
        try:
            text_embedding = self.local_embeddings.encode([text])[0]
            
            # Simple topic classification based on keywords
            # In production, this would use a trained classifier
            categories = []
            text_lower = text.lower()
            
            if any(word in text_lower for word in ["strategy", "plan", "roadmap", "vision"]):
                categories.append("business_strategy")
            if any(word in text_lower for word in ["technical", "code", "system", "architecture"]):
                categories.append("technical_discussion")
            if any(word in text_lower for word in ["project", "timeline", "milestone", "deliverable"]):
                categories.append("project_management")
            if any(word in text_lower for word in ["sales", "revenue", "deal", "proposal"]):
                categories.append("sales_inquiry")
            if any(word in text_lower for word in ["support", "help", "issue", "problem"]):
                categories.append("support_request")
            if any(word in text_lower for word in ["partnership", "collaboration", "joint"]):
                categories.append("partnership")
            if any(word in text_lower for word in ["meeting", "schedule", "calendar", "appointment"]):
                categories.append("scheduling")
            
            return categories if categories else ["general"]
            
        except Exception as e:
            logger.error(f"Error categorizing topics: {e}")
            return ["general"]
    
    async def _analyze_sentiment(self, text: str) -> float:
        """Analyze sentiment of the conversation."""
        try:
            # Simple sentiment analysis based on keywords
            # In production, this would use a trained sentiment model
            positive_words = ["great", "excellent", "good", "happy", "pleased", "satisfied"]
            negative_words = ["bad", "terrible", "awful", "unhappy", "disappointed", "frustrated"]
            
            text_lower = text.lower()
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count + negative_count == 0:
                return 0.0  # Neutral
            
            sentiment_score = (positive_count - negative_count) / (positive_count + negative_count)
            return max(-1.0, min(1.0, sentiment_score))
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return 0.0
    
    def _detect_urgency(self, text: str) -> str:
        """Detect urgency level from text."""
        text_lower = text.lower()
        
        for level, keywords in self.urgency_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return level
        
        return "medium"  # Default
    
    async def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract key entities from text."""
        try:
            # Simple entity extraction
            # In production, this would use NER models
            entities = []
            
            # Email extraction
            import re
            emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
            for email in emails:
                entities.append({"type": "email", "value": email, "confidence": 0.9})
            
            # Date extraction (simple)
            dates = re.findall(r'\b\d{1,2}/\d{1,2}/\d{4}\b|\b\d{4}-\d{2}-\d{2}\b', text)
            for date in dates:
                entities.append({"type": "date", "value": date, "confidence": 0.8})
            
            # Company names (simple heuristic)
            company_indicators = ["Inc", "LLC", "Corp", "Company", "Ltd"]
            words = text.split()
            for i, word in enumerate(words):
                if any(indicator in word for indicator in company_indicators):
                    if i > 0:
                        company_name = f"{words[i-1]} {word}"
                        entities.append({"type": "company", "value": company_name, "confidence": 0.7})
            
            return entities
            
        except Exception as e:
            logger.error(f"Error extracting entities: {e}")
            return []
    
    def _extract_action_items(self, text: str) -> List[str]:
        """Extract action items from conversation."""
        action_indicators = [
            "need to", "should", "must", "will", "action item",
            "todo", "follow up", "next step", "schedule", "send"
        ]
        
        sentences = text.split('.')
        action_items = []
        
        for sentence in sentences:
            sentence_lower = sentence.lower().strip()
            if any(indicator in sentence_lower for indicator in action_indicators):
                action_items.append(sentence.strip())
        
        return action_items[:5]  # Limit to 5 action items
    
    def _requires_follow_up(self, text: str, messages: List[Dict]) -> bool:
        """Determine if follow-up is required."""
        follow_up_indicators = [
            "follow up", "get back to", "circle back", "touch base",
            "schedule", "meeting", "call", "discuss further"
        ]
        
        text_lower = text.lower()
        has_follow_up_language = any(indicator in text_lower for indicator in follow_up_indicators)
        
        # Check if last message is a question
        if messages:
            last_message = messages[-1].get('content', '')
            ends_with_question = last_message.strip().endswith('?')
            return has_follow_up_language or ends_with_question
        
        return has_follow_up_language
    
    async def _generate_context_summary(self, 
                                       text: str, 
                                       topics: List[str], 
                                       entities: List[Dict]) -> str:
        """Generate a concise context summary."""
        try:
            # Extract key information
            entity_summary = ", ".join([e['value'] for e in entities[:3]])
            topic_summary = ", ".join(topics[:3])
            
            # Create summary
            summary = f"Discussion about {topic_summary}"
            if entity_summary:
                summary += f" involving {entity_summary}"
            
            # Add text snippet
            words = text.split()
            if len(words) > 50:
                snippet = " ".join(words[:50]) + "..."
            else:
                snippet = text
            
            return f"{summary}. Key content: {snippet}"
            
        except Exception as e:
            logger.error(f"Error generating context summary: {e}")
            return "Context summary unavailable"
    
    async def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text."""
        try:
            # Use Vertex AI embeddings for production
            embeddings = await self.embeddings.aembed_query(text)
            return embeddings
        except Exception as e:
            logger.warning(f"Vertex AI embedding failed, using local: {e}")
            # Fallback to local embeddings
            return self.local_embeddings.encode([text])[0].tolist()
    
    async def _enhance_query_with_context(self, 
                                        query: str, 
                                        context: ConversationContext) -> str:
        """Enhance query with conversation context."""
        enhanced_parts = [query]
        
        # Add topic context
        if context.topic_categories:
            enhanced_parts.append(f"Topics: {', '.join(context.topic_categories)}")
        
        # Add entity context
        if context.key_entities:
            entities = [e['value'] for e in context.key_entities[:3]]
            enhanced_parts.append(f"Related to: {', '.join(entities)}")
        
        # Add urgency context
        if context.urgency_level in ["urgent", "high"]:
            enhanced_parts.append(f"Priority: {context.urgency_level}")
        
        return " | ".join(enhanced_parts)
    
    def _build_context_filter(self, context: Optional[ConversationContext]) -> Dict[str, Any]:
        """Build metadata filter based on context."""
        if not context:
            return {}
        
        filter_dict = {}
        
        # Filter by topic categories
        if context.topic_categories:
            filter_dict["topic"] = {"$in": context.topic_categories}
        
        # Filter by urgency for urgent matters
        if context.urgency_level == "urgent":
            filter_dict["urgency"] = {"$in": ["urgent", "high"]}
        
        return filter_dict
    
    async def _rerank_documents(self, 
                              documents: List[Document], 
                              query: str,
                              context: Optional[ConversationContext]) -> List[Document]:
        """Re-rank documents based on relevance and context."""
        if not documents:
            return documents
        
        # Simple re-ranking based on keyword matching and recency
        # In production, this would use a trained re-ranking model
        
        scored_docs = []
        query_lower = query.lower()
        
        for doc in documents:
            score = 0.0
            content_lower = doc.page_content.lower()
            
            # Keyword matching
            query_words = query_lower.split()
            for word in query_words:
                if word in content_lower:
                    score += 1.0
            
            # Context relevance
            if context:
                for topic in context.topic_categories:
                    if topic.replace("_", " ") in content_lower:
                        score += 0.5
                
                for entity in context.key_entities:
                    if entity['value'].lower() in content_lower:
                        score += 0.3
            
            # Recency boost
            if 'timestamp' in doc.metadata:
                try:
                    doc_time = datetime.fromisoformat(doc.metadata['timestamp'])
                    age_days = (datetime.now() - doc_time).days
                    if age_days < 7:
                        score += 0.2
                except:
                    pass
            
            scored_docs.append((score, doc))
        
        # Sort by score descending
        scored_docs.sort(key=lambda x: x[0], reverse=True)
        return [doc for score, doc in scored_docs]
    
    async def _generate_contextual_response(self, 
                                          query: str,
                                          documents: List[Document],
                                          context: Optional[ConversationContext]) -> str:
        """Generate contextual response based on retrieved documents."""
        if not documents:
            return "I don't have specific information to answer your question based on our previous conversations."
        
        # Combine relevant content
        relevant_content = []
        for doc in documents[:3]:  # Use top 3 documents
            relevant_content.append(doc.page_content[:500])  # Limit content length
        
        combined_content = "\n\n".join(relevant_content)
        
        # Create contextual response
        response_parts = []
        
        if context and context.urgency_level in ["urgent", "high"]:
            response_parts.append("Given the urgency of this matter,")
        
        response_parts.append("based on our previous conversations and relevant information,")
        response_parts.append(f"here's what I found regarding your question about {query}:")
        response_parts.append(f"\n\n{combined_content}")
        
        if context and context.follow_up_required:
            response_parts.append("\n\nThis appears to require follow-up action.")
        
        return " ".join(response_parts)
    
    def _calculate_confidence_score(self, 
                                  query: str,
                                  documents: List[Document],
                                  search_results: List[Dict]) -> float:
        """Calculate confidence score for the response."""
        if not documents or not search_results:
            return 0.0
        
        # Base confidence on search result scores
        scores = [result.get('score', 0.0) for result in search_results[:3]]
        avg_score = sum(scores) / len(scores) if scores else 0.0
        
        # Adjust based on number of results
        result_factor = min(len(documents) / 3.0, 1.0)
        
        # Adjust based on content length
        total_content_length = sum(len(doc.page_content) for doc in documents[:3])
        content_factor = min(total_content_length / 1000.0, 1.0)
        
        confidence = avg_score * result_factor * content_factor
        return max(0.0, min(1.0, confidence))
    
    async def _generate_reasoning(self, 
                                query: str,
                                documents: List[Document],
                                context: Optional[ConversationContext]) -> str:
        """Generate reasoning for the response."""
        reasoning_parts = []
        
        reasoning_parts.append(f"I found {len(documents)} relevant documents")
        
        if context:
            reasoning_parts.append(f"considering the context of {', '.join(context.topic_categories)}")
            
            if context.urgency_level in ["urgent", "high"]:
                reasoning_parts.append(f"with {context.urgency_level} priority")
        
        if documents:
            reasoning_parts.append(f"from recent conversations and stored information")
        
        return " ".join(reasoning_parts) + "."
    
    async def _suggest_actions(self, 
                             query: str,
                             context: Optional[ConversationContext],
                             documents: List[Document]) -> List[str]:
        """Suggest relevant actions based on context and content."""
        suggestions = []
        
        if context:
            # Urgency-based suggestions
            if context.urgency_level == "urgent":
                suggestions.append("Schedule immediate follow-up")
                suggestions.append("Notify relevant stakeholders")
            
            # Action item suggestions
            if context.action_items:
                suggestions.extend(context.action_items[:2])
            
            # Follow-up suggestions
            if context.follow_up_required:
                suggestions.append("Schedule follow-up meeting")
                suggestions.append("Send summary email")
        
        # Query-based suggestions
        query_lower = query.lower()
        if "meeting" in query_lower or "schedule" in query_lower:
            suggestions.append("Check calendar availability")
            suggestions.append("Send meeting invitation")
        
        if "email" in query_lower:
            suggestions.append("Draft response email")
            suggestions.append("Add to email sequence")
        
        if "deal" in query_lower or "sales" in query_lower:
            suggestions.append("Update CRM deal stage")
            suggestions.append("Schedule sales follow-up")
        
        # Remove duplicates and limit
        unique_suggestions = list(dict.fromkeys(suggestions))
        return unique_suggestions[:5]


# Global service instance
_advanced_rag_service = None

async def get_advanced_rag_service() -> AdvancedRAGService:
    """Get the global advanced RAG service instance."""
    global _advanced_rag_service
    
    if _advanced_rag_service is None:
        _advanced_rag_service = AdvancedRAGService()
    
    return _advanced_rag_service

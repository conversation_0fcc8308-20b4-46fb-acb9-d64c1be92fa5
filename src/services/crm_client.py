"""
CRM Client Service - TKC_v5 Commercial Package

Provides unified interface for CRM operations across multiple platforms.
Supports HubSpot and Salesforce with extensible architecture.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from abc import ABC, abstractmethod

import httpx
from google.cloud import secretmanager

logger = logging.getLogger(__name__)


class CRMClientError(Exception):
    """Base exception for CRM client operations."""
    pass


class CRMAuthenticationError(CRMClientError):
    """Raised when CRM authentication fails."""
    pass


class CRMRateLimitError(CRMClientError):
    """Raised when CRM API rate limits are exceeded."""
    pass


class BaseCRMClient(ABC):
    """Abstract base class for CRM client implementations."""
    
    def __init__(self, credentials: Dict[str, Any]):
        self.credentials = credentials
        self.client = None
        self._authenticated = False
    
    @abstractmethod
    async def authenticate(self) -> bool:
        """Authenticate with the CRM platform."""
        pass
    
    @abstractmethod
    async def create_contact(self, contact_data: Dict[str, Any]) -> str:
        """Create a new contact and return the contact ID."""
        pass
    
    @abstractmethod
    async def get_contact(self, contact_id: str) -> Dict[str, Any]:
        """Retrieve contact information by ID."""
        pass
    
    @abstractmethod
    async def update_contact(self, contact_id: str, updates: Dict[str, Any]) -> bool:
        """Update contact information."""
        pass
    
    @abstractmethod
    async def create_deal(self, deal_data: Dict[str, Any]) -> str:
        """Create a new deal/opportunity and return the deal ID."""
        pass
    
    @abstractmethod
    async def get_deal(self, deal_id: str) -> Dict[str, Any]:
        """Retrieve deal information by ID."""
        pass
    
    @abstractmethod
    async def update_deal(self, deal_id: str, updates: Dict[str, Any]) -> bool:
        """Update deal information."""
        pass
    
    @abstractmethod
    async def log_activity(self, activity_data: Dict[str, Any]) -> str:
        """Log an activity and return the activity ID."""
        pass


class HubSpotClient(BaseCRMClient):
    """HubSpot CRM client implementation."""
    
    def __init__(self, credentials: Dict[str, Any]):
        super().__init__(credentials)
        self.api_key = credentials.get('api_key')
        self.base_url = "https://api.hubapi.com"
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    async def authenticate(self) -> bool:
        """Test HubSpot authentication."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/crm/v3/objects/contacts",
                    headers=self.headers,
                    params={'limit': 1}
                )
                
                if response.status_code == 200:
                    self._authenticated = True
                    logger.info("HubSpot authentication successful")
                    return True
                elif response.status_code == 401:
                    raise CRMAuthenticationError("Invalid HubSpot API key")
                else:
                    raise CRMClientError(f"HubSpot auth failed: {response.status_code}")
                    
        except Exception as e:
            logger.error(f"HubSpot authentication error: {e}")
            raise CRMAuthenticationError(f"HubSpot authentication failed: {e}")
    
    async def create_contact(self, contact_data: Dict[str, Any]) -> str:
        """Create a new HubSpot contact."""
        try:
            # Map standard fields to HubSpot format
            hubspot_data = {
                'properties': {
                    'email': contact_data.get('email'),
                    'firstname': contact_data.get('first_name'),
                    'lastname': contact_data.get('last_name'),
                    'company': contact_data.get('company'),
                    'phone': contact_data.get('phone'),
                    'website': contact_data.get('website'),
                    'lifecyclestage': contact_data.get('lifecycle_stage', 'lead')
                }
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/crm/v3/objects/contacts",
                    headers=self.headers,
                    json=hubspot_data
                )
                
                if response.status_code == 201:
                    result = response.json()
                    contact_id = result['id']
                    logger.info(f"Created HubSpot contact: {contact_id}")
                    return contact_id
                elif response.status_code == 429:
                    raise CRMRateLimitError("HubSpot rate limit exceeded")
                else:
                    raise CRMClientError(f"Failed to create contact: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error creating HubSpot contact: {e}")
            raise
    
    async def get_contact(self, contact_id: str) -> Dict[str, Any]:
        """Retrieve HubSpot contact by ID."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/crm/v3/objects/contacts/{contact_id}",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    # Map HubSpot format to standard format
                    properties = result.get('properties', {})
                    return {
                        'id': result['id'],
                        'email': properties.get('email'),
                        'first_name': properties.get('firstname'),
                        'last_name': properties.get('lastname'),
                        'company': properties.get('company'),
                        'phone': properties.get('phone'),
                        'website': properties.get('website'),
                        'lifecycle_stage': properties.get('lifecyclestage'),
                        'created_date': properties.get('createdate'),
                        'last_modified': properties.get('lastmodifieddate')
                    }
                else:
                    raise CRMClientError(f"Failed to get contact: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error getting HubSpot contact: {e}")
            raise
    
    async def update_contact(self, contact_id: str, updates: Dict[str, Any]) -> bool:
        """Update HubSpot contact."""
        try:
            # Map standard fields to HubSpot format
            hubspot_updates = {
                'properties': {}
            }
            
            field_mapping = {
                'email': 'email',
                'first_name': 'firstname',
                'last_name': 'lastname',
                'company': 'company',
                'phone': 'phone',
                'website': 'website',
                'lifecycle_stage': 'lifecyclestage'
            }
            
            for standard_field, hubspot_field in field_mapping.items():
                if standard_field in updates:
                    hubspot_updates['properties'][hubspot_field] = updates[standard_field]
            
            async with httpx.AsyncClient() as client:
                response = await client.patch(
                    f"{self.base_url}/crm/v3/objects/contacts/{contact_id}",
                    headers=self.headers,
                    json=hubspot_updates
                )
                
                if response.status_code == 200:
                    logger.info(f"Updated HubSpot contact: {contact_id}")
                    return True
                else:
                    raise CRMClientError(f"Failed to update contact: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error updating HubSpot contact: {e}")
            raise
    
    async def create_deal(self, deal_data: Dict[str, Any]) -> str:
        """Create a new HubSpot deal."""
        try:
            hubspot_data = {
                'properties': {
                    'dealname': deal_data.get('name'),
                    'amount': deal_data.get('amount'),
                    'dealstage': deal_data.get('stage', 'appointmentscheduled'),
                    'pipeline': deal_data.get('pipeline', 'default'),
                    'closedate': deal_data.get('close_date'),
                    'dealtype': deal_data.get('type', 'newbusiness')
                }
            }
            
            # Associate with contact if provided
            if deal_data.get('contact_id'):
                hubspot_data['associations'] = [
                    {
                        'to': {'id': deal_data['contact_id']},
                        'types': [{'associationCategory': 'HUBSPOT_DEFINED', 'associationTypeId': 3}]
                    }
                ]
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/crm/v3/objects/deals",
                    headers=self.headers,
                    json=hubspot_data
                )
                
                if response.status_code == 201:
                    result = response.json()
                    deal_id = result['id']
                    logger.info(f"Created HubSpot deal: {deal_id}")
                    return deal_id
                else:
                    raise CRMClientError(f"Failed to create deal: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error creating HubSpot deal: {e}")
            raise
    
    async def get_deal(self, deal_id: str) -> Dict[str, Any]:
        """Retrieve HubSpot deal by ID."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/crm/v3/objects/deals/{deal_id}",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    properties = result.get('properties', {})
                    return {
                        'id': result['id'],
                        'name': properties.get('dealname'),
                        'amount': properties.get('amount'),
                        'stage': properties.get('dealstage'),
                        'pipeline': properties.get('pipeline'),
                        'close_date': properties.get('closedate'),
                        'type': properties.get('dealtype'),
                        'created_date': properties.get('createdate'),
                        'last_modified': properties.get('lastmodifieddate')
                    }
                else:
                    raise CRMClientError(f"Failed to get deal: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error getting HubSpot deal: {e}")
            raise
    
    async def update_deal(self, deal_id: str, updates: Dict[str, Any]) -> bool:
        """Update HubSpot deal."""
        try:
            hubspot_updates = {
                'properties': {}
            }
            
            field_mapping = {
                'name': 'dealname',
                'amount': 'amount',
                'stage': 'dealstage',
                'pipeline': 'pipeline',
                'close_date': 'closedate',
                'type': 'dealtype'
            }
            
            for standard_field, hubspot_field in field_mapping.items():
                if standard_field in updates:
                    hubspot_updates['properties'][hubspot_field] = updates[standard_field]
            
            async with httpx.AsyncClient() as client:
                response = await client.patch(
                    f"{self.base_url}/crm/v3/objects/deals/{deal_id}",
                    headers=self.headers,
                    json=hubspot_updates
                )
                
                if response.status_code == 200:
                    logger.info(f"Updated HubSpot deal: {deal_id}")
                    return True
                else:
                    raise CRMClientError(f"Failed to update deal: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error updating HubSpot deal: {e}")
            raise
    
    async def log_activity(self, activity_data: Dict[str, Any]) -> str:
        """Log an activity in HubSpot."""
        try:
            hubspot_data = {
                'properties': {
                    'hs_activity_type': activity_data.get('type', 'EMAIL'),
                    'hs_body_preview': activity_data.get('description'),
                    'hs_timestamp': activity_data.get('timestamp', datetime.utcnow().isoformat())
                }
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/crm/v3/objects/activities",
                    headers=self.headers,
                    json=hubspot_data
                )
                
                if response.status_code == 201:
                    result = response.json()
                    activity_id = result['id']
                    logger.info(f"Logged HubSpot activity: {activity_id}")
                    return activity_id
                else:
                    raise CRMClientError(f"Failed to log activity: {response.text}")
                    
        except Exception as e:
            logger.error(f"Error logging HubSpot activity: {e}")
            raise


class CRMClientFactory:
    """Factory for creating CRM client instances."""
    
    @staticmethod
    async def create_client(crm_type: str, customer_id: str) -> BaseCRMClient:
        """Create a CRM client instance for the specified customer."""
        try:
            # Get customer-specific CRM credentials from Secret Manager
            secret_client = secretmanager.SecretManagerServiceClient()
            secret_name = f"projects/vertex-ai-agent-yzdlnjey/secrets/crm-{customer_id}-{crm_type}/versions/latest"
            
            response = secret_client.access_secret_version(request={"name": secret_name})
            credentials = eval(response.payload.data.decode("UTF-8"))  # In production, use json.loads
            
            if crm_type.lower() == 'hubspot':
                client = HubSpotClient(credentials)
            elif crm_type.lower() == 'salesforce':
                # TODO: Implement SalesforceClient
                raise NotImplementedError("Salesforce client not yet implemented")
            else:
                raise ValueError(f"Unsupported CRM type: {crm_type}")
            
            # Test authentication
            await client.authenticate()
            return client
            
        except Exception as e:
            logger.error(f"Failed to create CRM client: {e}")
            raise CRMClientError(f"Failed to create {crm_type} client: {e}")


# Convenience function for getting CRM client
async def get_crm_client(crm_type: str = "hubspot", customer_id: str = "default") -> BaseCRMClient:
    """Get a CRM client instance."""
    return await CRMClientFactory.create_client(crm_type, customer_id)

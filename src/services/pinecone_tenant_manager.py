"""
Pinecone Tenant Manager - TKC_v5 Multi-Tenant Vector Database

Provides customer isolation and namespace management for Pinecone vector database
in multi-tenant commercial deployment.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
import hashlib
import json
from datetime import datetime

import pinecone
from pinecone import Pinecone, ServerlessSpec
from google.cloud import secretmanager

from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class PineconeTenantManager:
    """
    Manages multi-tenant Pinecone vector database operations.
    
    Provides customer isolation through namespacing and manages
    vector operations for commercial customers.
    """
    
    def __init__(self):
        self.project_id = settings.google_cloud_project
        self.secret_client = secretmanager.SecretManagerServiceClient()
        self.pinecone_client = None
        self.index = None
        self._tenant_namespaces = {}
        
    async def initialize(self) -> bool:
        """Initialize Pinecone client and connection."""
        try:
            # Get Pinecone API key from Secret Manager
            secret_name = f"projects/{self.project_id}/secrets/pinecone-api-key/versions/latest"
            response = self.secret_client.access_secret_version(request={"name": secret_name})
            api_key = response.payload.data.decode("UTF-8")
            
            # Initialize Pinecone client
            self.pinecone_client = Pinecone(api_key=api_key)
            
            # Connect to the main index (create if doesn't exist)
            index_name = "tkc-v5-multi-tenant"
            
            # Check if index exists
            existing_indexes = self.pinecone_client.list_indexes()
            index_names = [idx.name for idx in existing_indexes]
            
            if index_name not in index_names:
                # Create index with appropriate configuration
                self.pinecone_client.create_index(
                    name=index_name,
                    dimension=1536,  # OpenAI embedding dimension
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud="aws",
                        region="us-east-1"
                    )
                )
                logger.info(f"Created new Pinecone index: {index_name}")
            
            # Connect to index
            self.index = self.pinecone_client.Index(index_name)
            
            logger.info("✅ Pinecone tenant manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Pinecone tenant manager: {e}")
            return False
    
    def generate_tenant_namespace(self, customer_id: str) -> str:
        """Generate a unique namespace for a customer."""
        # Create a hash-based namespace for consistency and security
        hash_input = f"tkc_v5_{customer_id}_{settings.environment}"
        namespace_hash = hashlib.sha256(hash_input.encode()).hexdigest()[:16]
        namespace = f"tkc_{namespace_hash}"
        
        # Cache the namespace mapping
        self._tenant_namespaces[customer_id] = namespace
        
        logger.info(f"Generated namespace for customer {customer_id}: {namespace}")
        return namespace
    
    async def initialize_tenant_namespace(self, customer_id: str, tenant_config: Dict[str, Any]) -> bool:
        """Initialize a new tenant namespace with proper isolation."""
        try:
            if not self.index:
                await self.initialize()
            
            namespace = self.generate_tenant_namespace(customer_id)
            
            # Create initial metadata document for the tenant
            tenant_metadata = {
                "customer_id": customer_id,
                "namespace": namespace,
                "initialized_at": datetime.now().isoformat(),
                "config": tenant_config,
                "status": "active"
            }
            
            # Store tenant metadata as a special vector
            metadata_vector = {
                "id": f"{namespace}_metadata",
                "values": [0.0] * 1536,  # Zero vector for metadata
                "metadata": {
                    "type": "tenant_metadata",
                    "customer_id": customer_id,
                    "data": json.dumps(tenant_metadata)
                }
            }
            
            # Upsert metadata to the tenant namespace
            self.index.upsert(
                vectors=[metadata_vector],
                namespace=namespace
            )
            
            logger.info(f"✅ Tenant namespace initialized: {namespace} for customer {customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize tenant namespace for {customer_id}: {e}")
            return False
    
    async def store_conversation_vectors(self, 
                                       customer_id: str, 
                                       conversation_id: str,
                                       vectors: List[Dict[str, Any]]) -> bool:
        """Store conversation vectors in customer's isolated namespace."""
        try:
            if not self.index:
                await self.initialize()
            
            namespace = self._tenant_namespaces.get(customer_id)
            if not namespace:
                namespace = self.generate_tenant_namespace(customer_id)
            
            # Prepare vectors with customer isolation
            isolated_vectors = []
            for i, vector in enumerate(vectors):
                isolated_vector = {
                    "id": f"{namespace}_{conversation_id}_{i}",
                    "values": vector["values"],
                    "metadata": {
                        "customer_id": customer_id,
                        "conversation_id": conversation_id,
                        "timestamp": datetime.now().isoformat(),
                        **vector.get("metadata", {})
                    }
                }
                isolated_vectors.append(isolated_vector)
            
            # Upsert vectors to customer namespace
            self.index.upsert(
                vectors=isolated_vectors,
                namespace=namespace
            )
            
            logger.info(f"✅ Stored {len(vectors)} vectors for customer {customer_id} in namespace {namespace}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to store vectors for customer {customer_id}: {e}")
            return False
    
    async def query_customer_vectors(self, 
                                   customer_id: str,
                                   query_vector: List[float],
                                   top_k: int = 5,
                                   filter_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Query vectors within a customer's isolated namespace."""
        try:
            if not self.index:
                await self.initialize()
            
            namespace = self._tenant_namespaces.get(customer_id)
            if not namespace:
                namespace = self.generate_tenant_namespace(customer_id)
            
            # Add customer filter to metadata filter
            customer_filter = {"customer_id": customer_id}
            if filter_metadata:
                customer_filter.update(filter_metadata)
            
            # Query the customer's namespace
            query_response = self.index.query(
                vector=query_vector,
                top_k=top_k,
                namespace=namespace,
                filter=customer_filter,
                include_metadata=True
            )
            
            # Format results
            results = []
            for match in query_response.matches:
                results.append({
                    "id": match.id,
                    "score": match.score,
                    "metadata": match.metadata
                })
            
            logger.info(f"✅ Retrieved {len(results)} vectors for customer {customer_id}")
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to query vectors for customer {customer_id}: {e}")
            return []
    
    async def delete_customer_data(self, customer_id: str) -> bool:
        """Delete all data for a customer (for GDPR compliance)."""
        try:
            if not self.index:
                await self.initialize()
            
            namespace = self._tenant_namespaces.get(customer_id)
            if not namespace:
                namespace = self.generate_tenant_namespace(customer_id)
            
            # Delete all vectors in the customer's namespace
            # Note: Pinecone doesn't support namespace deletion directly
            # We need to delete by filter
            self.index.delete(
                filter={"customer_id": customer_id},
                namespace=namespace
            )
            
            # Remove from cache
            if customer_id in self._tenant_namespaces:
                del self._tenant_namespaces[customer_id]
            
            logger.info(f"✅ Deleted all data for customer {customer_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete data for customer {customer_id}: {e}")
            return False
    
    async def get_tenant_stats(self, customer_id: str) -> Dict[str, Any]:
        """Get statistics for a tenant's vector usage."""
        try:
            if not self.index:
                await self.initialize()
            
            namespace = self._tenant_namespaces.get(customer_id)
            if not namespace:
                namespace = self.generate_tenant_namespace(customer_id)
            
            # Get index stats (this is a simplified version)
            # In practice, you'd need to implement more sophisticated counting
            stats = {
                "customer_id": customer_id,
                "namespace": namespace,
                "estimated_vector_count": 0,  # Would need to implement counting
                "last_updated": datetime.now().isoformat(),
                "status": "active"
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Failed to get stats for customer {customer_id}: {e}")
            return {}
    
    async def list_customer_conversations(self, customer_id: str) -> List[str]:
        """List all conversation IDs for a customer."""
        try:
            if not self.index:
                await self.initialize()
            
            namespace = self._tenant_namespaces.get(customer_id)
            if not namespace:
                namespace = self.generate_tenant_namespace(customer_id)
            
            # Query for conversation metadata
            # This is a simplified implementation
            conversations = []
            
            # In a real implementation, you'd query for unique conversation_ids
            # from the metadata of vectors in the customer's namespace
            
            return conversations
            
        except Exception as e:
            logger.error(f"❌ Failed to list conversations for customer {customer_id}: {e}")
            return []


# Global instance
_pinecone_tenant_manager = None

async def get_pinecone_tenant_manager() -> PineconeTenantManager:
    """Get the global Pinecone tenant manager instance."""
    global _pinecone_tenant_manager
    
    if _pinecone_tenant_manager is None:
        _pinecone_tenant_manager = PineconeTenantManager()
        await _pinecone_tenant_manager.initialize()
    
    return _pinecone_tenant_manager

"""
Tenant Vector Service - TKC_v5 Commercial Package

Provides tenant-isolated vector database operations for multi-customer deployment.
Extends the existing vector service with customer namespacing and isolation.
"""

import logging
from typing import Dict, List, Any, Optional
import asyncio

from services.vector_store import VectorStoreService
from services.multi_tenant_manager import get_tenant_manager
from services.rag_service import RAGService
from services.semantic_search import SemanticSearchService

logger = logging.getLogger(__name__)


class TenantVectorService:
    """
    Tenant-isolated vector database service.
    
    Provides the same functionality as the base vector service but with
    customer isolation through namespacing.
    """
    
    def __init__(self, customer_id: str):
        self.customer_id = customer_id
        self.tenant_manager = None
        self.vector_service = None
        self.namespace = None
        
    async def initialize(self):
        """Initialize the tenant vector service."""
        try:
            # Get tenant manager
            self.tenant_manager = await get_tenant_manager()
            
            # Get customer namespace
            self.namespace = self.tenant_manager.get_vector_namespace(self.customer_id)
            
            # Initialize base vector service
            self.vector_service = VectorStoreService()
            await self.vector_service.initialize()
            
            logger.info(f"✅ Tenant vector service initialized for {self.customer_id} (namespace: {self.namespace})")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize tenant vector service for {self.customer_id}: {e}")
            raise
    
    async def store_conversation(
        self,
        conversation_id: str,
        messages: List[Dict[str, Any]],
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        Store conversation with tenant isolation.
        
        Args:
            conversation_id: Unique conversation identifier
            messages: List of conversation messages
            metadata: Additional metadata
            
        Returns:
            True if storage successful
        """
        try:
            # Add tenant metadata
            tenant_metadata = {
                'customer_id': self.customer_id,
                'namespace': self.namespace,
                **(metadata or {})
            }
            
            # Store with namespace isolation
            success = await self.vector_service.store_conversation(
                conversation_id=f"{self.namespace}_{conversation_id}",
                messages=messages,
                metadata=tenant_metadata
            )
            
            if success:
                logger.info(f"✅ Conversation stored for tenant {self.customer_id}: {conversation_id}")
            else:
                logger.error(f"❌ Failed to store conversation for tenant {self.customer_id}: {conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error storing conversation for tenant {self.customer_id}: {e}")
            return False
    
    async def search_conversations(
        self,
        query: str,
        limit: int = 5,
        similarity_threshold: float = 0.7,
        metadata_filter: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        Search conversations with tenant isolation.
        
        Args:
            query: Search query
            limit: Maximum number of results
            similarity_threshold: Minimum similarity score
            metadata_filter: Additional metadata filters
            
        Returns:
            List of matching conversations
        """
        try:
            # Add tenant filter to metadata
            tenant_filter = {
                'customer_id': self.customer_id,
                'namespace': self.namespace,
                **(metadata_filter or {})
            }
            
            # Search with tenant isolation
            results = await self.vector_service.search_conversations(
                query=query,
                limit=limit,
                similarity_threshold=similarity_threshold,
                metadata_filter=tenant_filter
            )
            
            # Filter results to ensure tenant isolation (double-check)
            filtered_results = [
                result for result in results
                if result.get('metadata', {}).get('customer_id') == self.customer_id
            ]
            
            logger.info(f"✅ Found {len(filtered_results)} conversations for tenant {self.customer_id}")
            return filtered_results
            
        except Exception as e:
            logger.error(f"❌ Error searching conversations for tenant {self.customer_id}: {e}")
            return []
    
    async def get_conversation_context(
        self,
        conversation_id: str,
        query: str = "",
        max_context_length: int = 1000
    ) -> str:
        """
        Get conversation context with tenant isolation.
        
        Args:
            conversation_id: Conversation identifier
            query: Optional query for context relevance
            max_context_length: Maximum context length
            
        Returns:
            Formatted conversation context
        """
        try:
            # Search for relevant context within tenant
            if query:
                conversations = await self.search_conversations(
                    query=query,
                    limit=3,
                    metadata_filter={'conversation_id': conversation_id}
                )
            else:
                conversations = await self.search_conversations(
                    query=conversation_id,
                    limit=1,
                    metadata_filter={'conversation_id': conversation_id}
                )
            
            if not conversations:
                return ""
            
            # Build context from conversations
            context_parts = []
            total_length = 0
            
            for conv in conversations:
                content = conv.get('content', '')
                if total_length + len(content) <= max_context_length:
                    context_parts.append(content)
                    total_length += len(content)
                else:
                    # Add partial content if it fits
                    remaining_length = max_context_length - total_length
                    if remaining_length > 50:  # Only add if meaningful length
                        context_parts.append(content[:remaining_length] + "...")
                    break
            
            context = "\n\n".join(context_parts)
            logger.info(f"✅ Retrieved {len(context)} characters of context for tenant {self.customer_id}")
            
            return context
            
        except Exception as e:
            logger.error(f"❌ Error getting conversation context for tenant {self.customer_id}: {e}")
            return ""
    
    async def delete_conversation(self, conversation_id: str) -> bool:
        """
        Delete conversation with tenant isolation.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            True if deletion successful
        """
        try:
            # Delete with namespace prefix
            success = await self.vector_service.delete_conversation(
                f"{self.namespace}_{conversation_id}"
            )
            
            if success:
                logger.info(f"✅ Conversation deleted for tenant {self.customer_id}: {conversation_id}")
            else:
                logger.error(f"❌ Failed to delete conversation for tenant {self.customer_id}: {conversation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error deleting conversation for tenant {self.customer_id}: {e}")
            return False
    
    async def get_tenant_stats(self) -> Dict[str, Any]:
        """
        Get statistics for this tenant's vector data.
        
        Returns:
            Dictionary with tenant statistics
        """
        try:
            # This would query the vector database for tenant-specific stats
            # For now, return basic information
            stats = {
                'customer_id': self.customer_id,
                'namespace': self.namespace,
                'total_conversations': 0,  # Would be calculated from vector DB
                'total_vectors': 0,        # Would be calculated from vector DB
                'last_activity': None,     # Would be calculated from vector DB
                'storage_used_mb': 0       # Would be calculated from vector DB
            }
            
            logger.info(f"✅ Retrieved stats for tenant {self.customer_id}")
            return stats
            
        except Exception as e:
            logger.error(f"❌ Error getting stats for tenant {self.customer_id}: {e}")
            return {}


class TenantRAGService:
    """
    Tenant-isolated RAG service.
    
    Provides RAG functionality with customer isolation.
    """
    
    def __init__(self, customer_id: str):
        self.customer_id = customer_id
        self.vector_service = None
        
    async def initialize(self):
        """Initialize the tenant RAG service."""
        try:
            self.vector_service = TenantVectorService(self.customer_id)
            await self.vector_service.initialize()
            
            logger.info(f"✅ Tenant RAG service initialized for {self.customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize tenant RAG service for {self.customer_id}: {e}")
            raise
    
    async def enhance_prompt(
        self,
        query: str,
        conversation_id: str = "",
        max_context_length: int = 1000
    ) -> str:
        """
        Enhance prompt with tenant-specific context.
        
        Args:
            query: Original query
            conversation_id: Optional conversation ID for context
            max_context_length: Maximum context length
            
        Returns:
            Enhanced prompt with context
        """
        try:
            # Get relevant context for this tenant
            if conversation_id:
                context = await self.vector_service.get_conversation_context(
                    conversation_id=conversation_id,
                    query=query,
                    max_context_length=max_context_length
                )
            else:
                # Search for relevant conversations
                conversations = await self.vector_service.search_conversations(
                    query=query,
                    limit=3,
                    similarity_threshold=0.7
                )
                
                context_parts = []
                total_length = 0
                
                for conv in conversations:
                    content = conv.get('content', '')
                    if total_length + len(content) <= max_context_length:
                        context_parts.append(content)
                        total_length += len(content)
                    else:
                        break
                
                context = "\n\n".join(context_parts)
            
            # Enhance the prompt with context
            if context:
                enhanced_prompt = f"""Based on the following relevant context from previous conversations:

{context}

Please respond to: {query}"""
            else:
                enhanced_prompt = query
            
            logger.info(f"✅ Enhanced prompt for tenant {self.customer_id} (context: {len(context)} chars)")
            return enhanced_prompt
            
        except Exception as e:
            logger.error(f"❌ Error enhancing prompt for tenant {self.customer_id}: {e}")
            return query


class TenantSemanticSearchService:
    """
    Tenant-isolated semantic search service.
    """
    
    def __init__(self, customer_id: str):
        self.customer_id = customer_id
        self.vector_service = None
        
    async def initialize(self):
        """Initialize the tenant semantic search service."""
        try:
            self.vector_service = TenantVectorService(self.customer_id)
            await self.vector_service.initialize()
            
            logger.info(f"✅ Tenant semantic search service initialized for {self.customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize tenant semantic search service for {self.customer_id}: {e}")
            raise
    
    async def search_conversations(
        self,
        query: str,
        customer_id: str = None,  # Override for compatibility
        limit: int = 5
    ) -> str:
        """
        Search conversations with tenant isolation.
        
        Args:
            query: Search query
            customer_id: Customer ID (ignored, uses tenant customer_id)
            limit: Maximum number of results
            
        Returns:
            Formatted search results
        """
        try:
            # Use tenant's customer_id, ignore parameter
            conversations = await self.vector_service.search_conversations(
                query=query,
                limit=limit,
                similarity_threshold=0.7
            )
            
            if not conversations:
                return ""
            
            # Format results
            results = []
            for conv in conversations:
                content = conv.get('content', '')
                score = conv.get('score', 0)
                results.append(f"[Score: {score:.2f}] {content}")
            
            formatted_results = "\n\n".join(results)
            logger.info(f"✅ Semantic search completed for tenant {self.customer_id}")
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ Error in semantic search for tenant {self.customer_id}: {e}")
            return ""


# Factory functions for tenant services
async def get_tenant_vector_service(customer_id: str) -> TenantVectorService:
    """Get tenant-isolated vector service."""
    service = TenantVectorService(customer_id)
    await service.initialize()
    return service

async def get_tenant_rag_service(customer_id: str) -> TenantRAGService:
    """Get tenant-isolated RAG service."""
    service = TenantRAGService(customer_id)
    await service.initialize()
    return service

async def get_tenant_semantic_search_service(customer_id: str) -> TenantSemanticSearchService:
    """Get tenant-isolated semantic search service."""
    service = TenantSemanticSearchService(customer_id)
    await service.initialize()
    return service

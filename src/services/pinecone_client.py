"""
Pinecone client service for vector database operations.
"""

import logging
from typing import List, Dict, Any, Optional
from pinecone import Pinecone
from config.settings import get_settings

logger = logging.getLogger(__name__)

class PineconeClient:
    """Client for Pinecone vector database operations."""
    
    def __init__(self):
        """Initialize Pinecone client."""
        self.settings = get_settings()
        self.client = None
        self.index = None
        
    async def initialize(self) -> None:
        """Initialize Pinecone connection."""
        try:
            # Initialize Pinecone client
            self.client = Pinecone(
                api_key=self.settings.pinecone_api_key
            )
            
            # Get or create index
            index_name = self.settings.pinecone_index_name
            if index_name not in [idx.name for idx in self.client.list_indexes()]:
                logger.info(f"Creating Pinecone index: {index_name}")
                self.client.create_index(
                    name=index_name,
                    dimension=1536,  # OpenAI embedding dimension
                    metric="cosine"
                )
            
            self.index = self.client.Index(index_name)
            logger.info(f"Connected to Pinecone index: {index_name}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Pinecone: {e}")
            raise
    
    async def upsert_vectors(
        self, 
        vectors: List[Dict[str, Any]], 
        namespace: Optional[str] = None
    ) -> Dict[str, Any]:
        """Upsert vectors to Pinecone index."""
        try:
            if not self.index:
                await self.initialize()
            
            response = self.index.upsert(
                vectors=vectors,
                namespace=namespace
            )
            
            logger.info(f"Upserted {len(vectors)} vectors to Pinecone")
            return response
            
        except Exception as e:
            logger.error(f"Failed to upsert vectors: {e}")
            raise
    
    async def query_vectors(
        self,
        vector: List[float],
        top_k: int = 10,
        namespace: Optional[str] = None,
        filter_dict: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Query vectors from Pinecone index."""
        try:
            if not self.index:
                await self.initialize()
            
            response = self.index.query(
                vector=vector,
                top_k=top_k,
                namespace=namespace,
                filter=filter_dict,
                include_metadata=True
            )
            
            logger.info(f"Queried Pinecone with top_k={top_k}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to query vectors: {e}")
            raise
    
    async def delete_vectors(
        self,
        ids: List[str],
        namespace: Optional[str] = None
    ) -> Dict[str, Any]:
        """Delete vectors from Pinecone index."""
        try:
            if not self.index:
                await self.initialize()
            
            response = self.index.delete(
                ids=ids,
                namespace=namespace
            )
            
            logger.info(f"Deleted {len(ids)} vectors from Pinecone")
            return response
            
        except Exception as e:
            logger.error(f"Failed to delete vectors: {e}")
            raise
    
    async def get_index_stats(self, namespace: Optional[str] = None) -> Dict[str, Any]:
        """Get index statistics."""
        try:
            if not self.index:
                await self.initialize()
            
            stats = self.index.describe_index_stats()
            logger.info("Retrieved Pinecone index stats")
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get index stats: {e}")
            raise

# Global instance
_pinecone_client = None

async def get_pinecone_client() -> PineconeClient:
    """Get or create Pinecone client instance."""
    global _pinecone_client
    if _pinecone_client is None:
        _pinecone_client = PineconeClient()
        await _pinecone_client.initialize()
    return _pinecone_client

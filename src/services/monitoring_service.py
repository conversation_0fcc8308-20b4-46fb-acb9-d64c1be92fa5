"""
Comprehensive Monitoring Service - TKC_v5 Executive Agent

Provides enterprise-grade monitoring, logging, and alerting for production
agent operations using Google Cloud Operations suite.
"""

import logging
import time
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
from functools import wraps

from google.cloud import monitoring_v3
from google.cloud import logging as cloud_logging
from google.cloud import error_reporting
from google.cloud import trace_v1
import structlog

from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class MonitoringService:
    """
    Comprehensive monitoring service for TKC_v5 Executive Agent.
    
    Provides metrics collection, structured logging, error reporting,
    and distributed tracing for production operations.
    """
    
    def __init__(self):
        self.project_id = settings.google_cloud_project
        self.environment = settings.environment
        
        # Initialize Google Cloud clients
        self.metrics_client = monitoring_v3.MetricServiceClient()
        self.logging_client = cloud_logging.Client(project=self.project_id)
        self.error_client = error_reporting.Client(project=self.project_id)
        self.trace_client = trace_v1.TraceServiceClient()
        
        # Setup structured logging
        self._setup_structured_logging()
        
        # Metrics configuration
        self.project_name = f"projects/{self.project_id}"
        self.custom_metrics = {}
        
        # Performance tracking
        self.performance_metrics = {
            'agent_response_time': [],
            'tool_execution_time': {},
            'error_counts': {},
            'request_counts': 0
        }
        
    def _setup_structured_logging(self):
        """Configure structured logging with Cloud Logging integration."""
        # Setup Cloud Logging handler
        self.logging_client.setup_logging()
        
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        self.struct_logger = structlog.get_logger()
    
    async def log_agent_interaction(self, 
                                  customer_id: str,
                                  interaction_type: str,
                                  details: Dict[str, Any],
                                  duration_ms: Optional[float] = None):
        """Log structured agent interaction data."""
        log_data = {
            'event_type': 'agent_interaction',
            'customer_id': customer_id,
            'interaction_type': interaction_type,
            'timestamp': datetime.now().isoformat(),
            'environment': self.environment,
            'details': details
        }
        
        if duration_ms:
            log_data['duration_ms'] = duration_ms
            self.performance_metrics['agent_response_time'].append(duration_ms)
        
        self.struct_logger.info("Agent interaction", **log_data)
        
        # Send custom metric
        await self._send_custom_metric(
            'agent_interactions_total',
            1,
            labels={
                'customer_id': customer_id,
                'interaction_type': interaction_type,
                'environment': self.environment
            }
        )
    
    async def log_tool_execution(self,
                               tool_name: str,
                               customer_id: str,
                               success: bool,
                               duration_ms: float,
                               error_message: Optional[str] = None):
        """Log tool execution metrics and performance."""
        log_data = {
            'event_type': 'tool_execution',
            'tool_name': tool_name,
            'customer_id': customer_id,
            'success': success,
            'duration_ms': duration_ms,
            'timestamp': datetime.now().isoformat(),
            'environment': self.environment
        }
        
        if error_message:
            log_data['error_message'] = error_message
        
        # Track performance metrics
        if tool_name not in self.performance_metrics['tool_execution_time']:
            self.performance_metrics['tool_execution_time'][tool_name] = []
        self.performance_metrics['tool_execution_time'][tool_name].append(duration_ms)
        
        # Track error counts
        if not success:
            error_key = f"{tool_name}_errors"
            self.performance_metrics['error_counts'][error_key] = \
                self.performance_metrics['error_counts'].get(error_key, 0) + 1
        
        self.struct_logger.info("Tool execution", **log_data)
        
        # Send metrics
        await self._send_custom_metric(
            'tool_executions_total',
            1,
            labels={
                'tool_name': tool_name,
                'customer_id': customer_id,
                'success': str(success),
                'environment': self.environment
            }
        )
        
        await self._send_custom_metric(
            'tool_execution_duration_ms',
            duration_ms,
            labels={
                'tool_name': tool_name,
                'customer_id': customer_id,
                'environment': self.environment
            }
        )
    
    async def log_error(self,
                       error: Exception,
                       context: Dict[str, Any],
                       customer_id: Optional[str] = None):
        """Log and report errors with full context."""
        error_data = {
            'event_type': 'error',
            'error_type': type(error).__name__,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat(),
            'environment': self.environment,
            'context': context
        }
        
        if customer_id:
            error_data['customer_id'] = customer_id
        
        self.struct_logger.error("Application error", **error_data, exc_info=error)
        
        # Report to Cloud Error Reporting
        try:
            self.error_client.report_exception()
        except Exception as e:
            logger.warning(f"Failed to report error to Cloud Error Reporting: {e}")
        
        # Send error metric
        await self._send_custom_metric(
            'errors_total',
            1,
            labels={
                'error_type': type(error).__name__,
                'customer_id': customer_id or 'unknown',
                'environment': self.environment
            }
        )
    
    async def _send_custom_metric(self,
                                metric_name: str,
                                value: float,
                                labels: Dict[str, str]):
        """Send custom metric to Cloud Monitoring."""
        try:
            # Create metric descriptor if it doesn't exist
            if metric_name not in self.custom_metrics:
                await self._create_metric_descriptor(metric_name)
            
            # Create time series data
            series = monitoring_v3.TimeSeries()
            series.metric.type = f"custom.googleapis.com/{metric_name}"
            
            # Add labels
            for key, val in labels.items():
                series.metric.labels[key] = val
            
            series.resource.type = "global"
            
            # Create data point
            now = time.time()
            seconds = int(now)
            nanos = int((now - seconds) * 10 ** 9)
            interval = monitoring_v3.TimeInterval(
                {"end_time": {"seconds": seconds, "nanos": nanos}}
            )
            point = monitoring_v3.Point(
                {"interval": interval, "value": {"double_value": value}}
            )
            series.points = [point]
            
            # Send to Cloud Monitoring
            self.metrics_client.create_time_series(
                name=self.project_name,
                time_series=[series]
            )
            
        except Exception as e:
            logger.warning(f"Failed to send custom metric {metric_name}: {e}")
    
    async def _create_metric_descriptor(self, metric_name: str):
        """Create a custom metric descriptor."""
        try:
            descriptor = monitoring_v3.MetricDescriptor()
            descriptor.type = f"custom.googleapis.com/{metric_name}"
            descriptor.metric_kind = monitoring_v3.MetricDescriptor.MetricKind.GAUGE
            descriptor.value_type = monitoring_v3.MetricDescriptor.ValueType.DOUBLE
            descriptor.description = f"Custom metric for {metric_name}"
            
            # Add common labels
            descriptor.labels.extend([
                monitoring_v3.LabelDescriptor(
                    key="customer_id",
                    value_type=monitoring_v3.LabelDescriptor.ValueType.STRING,
                    description="Customer identifier"
                ),
                monitoring_v3.LabelDescriptor(
                    key="environment",
                    value_type=monitoring_v3.LabelDescriptor.ValueType.STRING,
                    description="Environment (production, staging, development)"
                )
            ])
            
            self.metrics_client.create_metric_descriptor(
                name=self.project_name,
                metric_descriptor=descriptor
            )
            
            self.custom_metrics[metric_name] = descriptor
            
        except Exception as e:
            logger.warning(f"Failed to create metric descriptor {metric_name}: {e}")
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance metrics summary."""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'environment': self.environment,
            'metrics': {}
        }
        
        # Agent response time statistics
        if self.performance_metrics['agent_response_time']:
            response_times = self.performance_metrics['agent_response_time']
            summary['metrics']['agent_response_time'] = {
                'avg_ms': sum(response_times) / len(response_times),
                'min_ms': min(response_times),
                'max_ms': max(response_times),
                'count': len(response_times)
            }
        
        # Tool execution statistics
        tool_stats = {}
        for tool_name, times in self.performance_metrics['tool_execution_time'].items():
            if times:
                tool_stats[tool_name] = {
                    'avg_ms': sum(times) / len(times),
                    'min_ms': min(times),
                    'max_ms': max(times),
                    'count': len(times)
                }
        summary['metrics']['tool_execution_time'] = tool_stats
        
        # Error counts
        summary['metrics']['error_counts'] = self.performance_metrics['error_counts']
        
        # Request counts
        summary['metrics']['total_requests'] = self.performance_metrics['request_counts']
        
        return summary
    
    async def create_alert_policy(self,
                                display_name: str,
                                condition_filter: str,
                                threshold_value: float,
                                comparison: str = "COMPARISON_GREATER_THAN"):
        """Create an alert policy in Cloud Monitoring."""
        try:
            # Create alert policy
            policy = monitoring_v3.AlertPolicy()
            policy.display_name = display_name
            policy.enabled = True
            
            # Create condition
            condition = monitoring_v3.AlertPolicy.Condition()
            condition.display_name = f"{display_name} condition"
            
            # Set threshold condition
            threshold_condition = monitoring_v3.AlertPolicy.Condition.MetricThreshold()
            threshold_condition.filter = condition_filter
            threshold_condition.comparison = getattr(
                monitoring_v3.ComparisonType, comparison
            )
            threshold_condition.threshold_value.double_value = threshold_value
            
            condition.condition_threshold = threshold_condition
            policy.conditions = [condition]
            
            # Create the policy
            created_policy = self.metrics_client.create_alert_policy(
                name=self.project_name,
                alert_policy=policy
            )
            
            logger.info(f"Created alert policy: {created_policy.name}")
            return created_policy
            
        except Exception as e:
            logger.error(f"Failed to create alert policy: {e}")
            return None


def monitor_performance(func):
    """Decorator to monitor function performance."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        monitoring_service = await get_monitoring_service()
        
        try:
            result = await func(*args, **kwargs)
            duration_ms = (time.time() - start_time) * 1000
            
            # Extract customer_id if available
            customer_id = kwargs.get('customer_id') or getattr(args[0], 'customer_id', None) if args else None
            
            await monitoring_service.log_tool_execution(
                tool_name=func.__name__,
                customer_id=customer_id or 'unknown',
                success=True,
                duration_ms=duration_ms
            )
            
            return result
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            customer_id = kwargs.get('customer_id') or getattr(args[0], 'customer_id', None) if args else None
            
            await monitoring_service.log_tool_execution(
                tool_name=func.__name__,
                customer_id=customer_id or 'unknown',
                success=False,
                duration_ms=duration_ms,
                error_message=str(e)
            )
            
            await monitoring_service.log_error(
                error=e,
                context={
                    'function': func.__name__,
                    'args': str(args)[:500],  # Truncate for logging
                    'kwargs': str(kwargs)[:500]
                },
                customer_id=customer_id
            )
            
            raise
    
    return wrapper


# Global monitoring service instance
_monitoring_service = None

async def get_monitoring_service() -> MonitoringService:
    """Get the global monitoring service instance."""
    global _monitoring_service
    
    if _monitoring_service is None:
        _monitoring_service = MonitoringService()
    
    return _monitoring_service

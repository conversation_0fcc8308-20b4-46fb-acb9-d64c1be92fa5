"""
Semantic Search Service for TKC_v5 Milestone 3

This module provides advanced semantic search capabilities for finding relevant conversation context,
customer interactions, and business insights using vector embeddings and similarity matching.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import re

from config.settings import Settings
from services.vector_db_client import get_vector_db_client

logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """Represents a semantic search result."""
    id: str
    content: str
    score: float
    metadata: Dict[str, Any]
    conversation_id: str
    customer_email: Optional[str] = None
    timestamp: Optional[str] = None
    message_type: Optional[str] = None


@dataclass
class SearchQuery:
    """Represents a semantic search query with filters."""
    query: str
    customer_email: Optional[str] = None
    conversation_id: Optional[str] = None
    message_type: Optional[str] = None
    date_range: Optional[Tuple[datetime, datetime]] = None
    min_score: float = 0.7
    max_results: int = 10


class SemanticSearchService:
    """
    Advanced semantic search service for conversation analysis and context retrieval.
    
    Provides intelligent search capabilities across conversation history with filtering,
    ranking, and contextual analysis.
    """
    
    def __init__(self, settings: Settings, mock_mode: bool = False):
        self.settings = settings
        self.mock_mode = mock_mode
        self.vector_db = None
        self._initialized = False
        
        # Search configuration
        self.default_similarity_threshold = 0.7
        self.max_search_results = 50
        self.context_window_size = 3  # Messages before/after for context
    
    async def initialize(self) -> bool:
        """
        Initialize the semantic search service.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Initialize vector database client
            self.vector_db = await get_vector_db_client(self.settings, mock_mode=self.mock_mode)
            
            self._initialized = True
            logger.info("✅ Semantic search service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize semantic search service: {e}")
            return False
    
    async def search(self, search_query: SearchQuery) -> List[SearchResult]:
        """
        Perform semantic search with advanced filtering and ranking.
        
        Args:
            search_query: SearchQuery object with query and filters
            
        Returns:
            List of SearchResult objects ranked by relevance
        """
        if not self._initialized:
            logger.error("Semantic search service not initialized")
            return []
        
        try:
            # Perform vector search
            raw_results = await self.vector_db.search_similar_messages(
                query=search_query.query,
                conversation_id=search_query.conversation_id,
                top_k=min(search_query.max_results, self.max_search_results),
                min_score=search_query.min_score
            )
            
            # Convert to SearchResult objects and apply additional filters
            search_results = []
            for result in raw_results:
                # Apply filters
                if not self._passes_filters(result, search_query):
                    continue
                
                search_result = SearchResult(
                    id=result["id"],
                    content=result["metadata"].get("content", ""),
                    score=result["score"],
                    metadata=result["metadata"],
                    conversation_id=result["metadata"].get("conversation_id", ""),
                    customer_email=result["metadata"].get("customer_email"),
                    timestamp=result["metadata"].get("timestamp"),
                    message_type=result["metadata"].get("message_type")
                )
                
                search_results.append(search_result)
            
            # Apply advanced ranking
            ranked_results = await self._rank_results(search_results, search_query)
            
            logger.info(f"✅ Semantic search completed: {len(ranked_results)} results for query '{search_query.query}'")
            return ranked_results
            
        except Exception as e:
            logger.error(f"❌ Error performing semantic search: {e}")
            return []
    
    def _passes_filters(self, result: Dict[str, Any], search_query: SearchQuery) -> bool:
        """Check if a result passes the search filters."""
        metadata = result["metadata"]
        
        # Customer email filter
        if search_query.customer_email:
            if metadata.get("customer_email") != search_query.customer_email:
                return False
        
        # Message type filter
        if search_query.message_type:
            if metadata.get("message_type") != search_query.message_type:
                return False
        
        # Date range filter
        if search_query.date_range:
            timestamp_str = metadata.get("timestamp")
            if timestamp_str:
                try:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    start_date, end_date = search_query.date_range
                    if not (start_date <= timestamp <= end_date):
                        return False
                except Exception:
                    # If timestamp parsing fails, exclude the result
                    return False
        
        return True
    
    async def _rank_results(self, results: List[SearchResult], search_query: SearchQuery) -> List[SearchResult]:
        """Apply advanced ranking to search results."""
        try:
            # Calculate enhanced scores
            for result in results:
                enhanced_score = await self._calculate_enhanced_score(result, search_query)
                result.score = enhanced_score
            
            # Sort by enhanced score
            results.sort(key=lambda x: x.score, reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error ranking results: {e}")
            return results
    
    async def _calculate_enhanced_score(self, result: SearchResult, search_query: SearchQuery) -> float:
        """Calculate enhanced relevance score with multiple factors."""
        base_score = result.score
        
        # Recency boost (more recent messages get higher scores)
        recency_boost = self._calculate_recency_boost(result.timestamp)
        
        # Customer relevance boost
        customer_boost = self._calculate_customer_boost(result, search_query)
        
        # Content quality boost
        content_boost = self._calculate_content_boost(result.content)
        
        # Combine scores with weights
        enhanced_score = (
            base_score * 0.6 +  # Base semantic similarity (60%)
            recency_boost * 0.2 +  # Recency (20%)
            customer_boost * 0.1 +  # Customer relevance (10%)
            content_boost * 0.1  # Content quality (10%)
        )
        
        return min(enhanced_score, 1.0)  # Cap at 1.0
    
    def _calculate_recency_boost(self, timestamp_str: Optional[str]) -> float:
        """Calculate recency boost based on message age."""
        if not timestamp_str:
            return 0.0
        
        try:
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            age_days = (datetime.now() - timestamp.replace(tzinfo=None)).days
            
            # Exponential decay: newer messages get higher boost
            if age_days <= 1:
                return 1.0
            elif age_days <= 7:
                return 0.8
            elif age_days <= 30:
                return 0.6
            elif age_days <= 90:
                return 0.4
            else:
                return 0.2
                
        except Exception:
            return 0.0
    
    def _calculate_customer_boost(self, result: SearchResult, search_query: SearchQuery) -> float:
        """Calculate customer relevance boost."""
        if search_query.customer_email and result.customer_email == search_query.customer_email:
            return 1.0  # Same customer gets full boost
        return 0.5  # Other customers get partial boost
    
    def _calculate_content_boost(self, content: str) -> float:
        """Calculate content quality boost based on message characteristics."""
        if not content:
            return 0.0
        
        score = 0.5  # Base score
        
        # Length boost (not too short, not too long)
        length = len(content)
        if 50 <= length <= 500:
            score += 0.3
        elif 20 <= length <= 1000:
            score += 0.1
        
        # Question boost (questions often contain important context)
        if '?' in content:
            score += 0.1
        
        # Business terms boost
        business_terms = ['project', 'meeting', 'deadline', 'proposal', 'contract', 'budget', 'timeline']
        if any(term in content.lower() for term in business_terms):
            score += 0.1
        
        return min(score, 1.0)
    
    async def search_by_topic(self, topic: str, max_results: int = 10) -> List[SearchResult]:
        """
        Search for messages related to a specific topic.
        
        Args:
            topic: Topic to search for
            max_results: Maximum number of results
            
        Returns:
            List of relevant messages
        """
        search_query = SearchQuery(
            query=f"topic about {topic}",
            max_results=max_results,
            min_score=0.6  # Lower threshold for topic search
        )
        
        return await self.search(search_query)
    
    async def search_customer_history(
        self,
        customer_email: str,
        query: Optional[str] = None,
        days_back: int = 90
    ) -> List[SearchResult]:
        """
        Search conversation history for a specific customer.
        
        Args:
            customer_email: Customer email address
            query: Optional search query
            days_back: Number of days to search back
            
        Returns:
            List of customer's conversation history
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        search_query = SearchQuery(
            query=query or f"conversations with {customer_email}",
            customer_email=customer_email,
            date_range=(start_date, end_date),
            max_results=50,
            min_score=0.5  # Lower threshold for history search
        )
        
        return await self.search(search_query)
    
    async def find_similar_issues(self, issue_description: str, max_results: int = 5) -> List[SearchResult]:
        """
        Find similar issues or problems from conversation history.
        
        Args:
            issue_description: Description of the issue
            max_results: Maximum number of similar issues to return
            
        Returns:
            List of similar issues with solutions
        """
        search_query = SearchQuery(
            query=f"problem issue: {issue_description}",
            max_results=max_results,
            min_score=0.7
        )
        
        results = await self.search(search_query)
        
        # Filter for messages that likely contain issues/problems
        issue_keywords = ['problem', 'issue', 'error', 'bug', 'trouble', 'help', 'fix', 'solve']
        filtered_results = []
        
        for result in results:
            content_lower = result.content.lower()
            if any(keyword in content_lower for keyword in issue_keywords):
                filtered_results.append(result)
        
        return filtered_results
    
    async def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """
        Get a summary of a conversation using semantic analysis.
        
        Args:
            conversation_id: Conversation ID to summarize
            
        Returns:
            Conversation summary with key topics and insights
        """
        try:
            # Get all messages from the conversation
            messages = await self.vector_db.get_conversation_context(
                conversation_id=conversation_id,
                max_messages=100  # Get more messages for summary
            )
            
            if not messages:
                return {"error": "No messages found for conversation"}
            
            # Analyze conversation
            total_messages = len(messages)
            customer_messages = [m for m in messages if m["metadata"].get("message_type") == "human"]
            assistant_messages = [m for m in messages if m["metadata"].get("message_type") == "assistant"]
            
            # Extract key information
            customer_email = None
            if customer_messages:
                customer_email = customer_messages[0]["metadata"].get("customer_email")
            
            # Get timestamps
            timestamps = [m["metadata"].get("timestamp") for m in messages if m["metadata"].get("timestamp")]
            start_time = min(timestamps) if timestamps else None
            end_time = max(timestamps) if timestamps else None
            
            summary = {
                "conversation_id": conversation_id,
                "customer_email": customer_email,
                "total_messages": total_messages,
                "customer_messages": len(customer_messages),
                "assistant_messages": len(assistant_messages),
                "start_time": start_time,
                "end_time": end_time,
                "duration_analysis": self._analyze_conversation_duration(start_time, end_time),
                "key_topics": await self._extract_key_topics(messages),
                "sentiment_analysis": "Feature coming in future updates"
            }
            
            logger.info(f"✅ Generated conversation summary for {conversation_id}")
            return summary
            
        except Exception as e:
            logger.error(f"❌ Error generating conversation summary: {e}")
            return {"error": str(e)}
    
    def _analyze_conversation_duration(self, start_time: Optional[str], end_time: Optional[str]) -> str:
        """Analyze conversation duration."""
        if not start_time or not end_time:
            return "Duration unknown"
        
        try:
            start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            duration = end - start
            
            if duration.days > 0:
                return f"{duration.days} days"
            elif duration.seconds > 3600:
                hours = duration.seconds // 3600
                return f"{hours} hours"
            else:
                minutes = duration.seconds // 60
                return f"{minutes} minutes"
                
        except Exception:
            return "Duration calculation error"
    
    async def _extract_key_topics(self, messages: List[Dict[str, Any]]) -> List[str]:
        """Extract key topics from conversation messages."""
        # Simplified topic extraction - in production, you might use NLP libraries
        all_content = " ".join([
            msg["metadata"].get("content", "") 
            for msg in messages 
            if msg["metadata"].get("content")
        ])
        
        # Basic keyword extraction
        business_keywords = [
            'project', 'meeting', 'deadline', 'proposal', 'contract', 'budget',
            'timeline', 'delivery', 'requirements', 'scope', 'pricing', 'schedule'
        ]
        
        found_topics = []
        content_lower = all_content.lower()
        
        for keyword in business_keywords:
            if keyword in content_lower:
                found_topics.append(keyword)
        
        return found_topics[:5]  # Return top 5 topics
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on semantic search service.
        
        Returns:
            Health status information
        """
        if not self._initialized:
            return {
                "status": "unhealthy",
                "error": "Semantic search service not initialized"
            }
        
        try:
            # Check vector database health
            vector_health = await self.vector_db.health_check()
            
            return {
                "status": "healthy",
                "vector_database": vector_health,
                "configuration": {
                    "default_similarity_threshold": self.default_similarity_threshold,
                    "max_search_results": self.max_search_results,
                    "context_window_size": self.context_window_size
                }
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global instance
_semantic_search_service: Optional[SemanticSearchService] = None


async def get_semantic_search_service(settings: Settings, mock_mode: bool = False) -> SemanticSearchService:
    """
    Get or create the global semantic search service instance.
    
    Args:
        settings: Application settings
        mock_mode: Whether to use mock mode for testing
        
    Returns:
        SemanticSearchService instance
    """
    global _semantic_search_service
    
    if _semantic_search_service is None:
        _semantic_search_service = SemanticSearchService(settings, mock_mode=mock_mode)
        await _semantic_search_service.initialize()
    
    return _semantic_search_service

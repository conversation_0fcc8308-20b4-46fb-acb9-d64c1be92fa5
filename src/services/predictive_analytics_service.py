"""
Predictive Analytics Service - TKC_v5 Executive Agent

Provides business intelligence, predictive insights, and data-driven
recommendations for executive decision-making.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
from dataclasses import dataclass
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

from services.data_persistence_service import get_data_persistence_service
from services.monitoring_service import get_monitoring_service
from config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class PredictionResult:
    """Result of a predictive analysis."""
    prediction_type: str
    predicted_value: Any
    confidence_score: float
    contributing_factors: List[Dict[str, Any]]
    recommendations: List[str]
    data_quality_score: float
    model_accuracy: float


@dataclass
class BusinessInsight:
    """Business intelligence insight."""
    insight_type: str
    title: str
    description: str
    impact_level: str  # high, medium, low
    urgency: str  # urgent, high, medium, low
    data_points: List[Dict[str, Any]]
    recommended_actions: List[str]
    confidence: float


class PredictiveAnalyticsService:
    """
    Advanced predictive analytics service for business intelligence.
    
    Provides predictive insights, trend analysis, and data-driven
    recommendations for executive decision-making.
    """
    
    def __init__(self):
        self.project_id = settings.google_cloud_project
        self.models = {}
        self.scalers = {}
        self.feature_importance = {}
        
        # Initialize models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize predictive models."""
        # Email response prediction
        self.models['email_response_time'] = RandomForestRegressor(
            n_estimators=100, random_state=42
        )
        
        # Deal closure prediction
        self.models['deal_closure'] = RandomForestClassifier(
            n_estimators=100, random_state=42
        )
        
        # Meeting success prediction
        self.models['meeting_success'] = RandomForestClassifier(
            n_estimators=100, random_state=42
        )
        
        # Lead scoring
        self.models['lead_score'] = RandomForestRegressor(
            n_estimators=100, random_state=42
        )
        
        # Customer churn prediction
        self.models['customer_churn'] = RandomForestClassifier(
            n_estimators=100, random_state=42
        )
        
        # Initialize scalers
        for model_name in self.models.keys():
            self.scalers[model_name] = StandardScaler()
    
    async def predict_email_response_time(self, 
                                        email_data: Dict[str, Any],
                                        customer_id: str) -> PredictionResult:
        """Predict email response time based on historical data."""
        try:
            # Extract features from email data
            features = self._extract_email_features(email_data)
            
            # Get historical data for training if model not trained
            if 'email_response_time' not in self.feature_importance:
                await self._train_email_response_model(customer_id)
            
            # Make prediction
            feature_vector = np.array([features]).reshape(1, -1)
            
            if hasattr(self.scalers['email_response_time'], 'mean_'):
                feature_vector = self.scalers['email_response_time'].transform(feature_vector)
            
            predicted_hours = self.models['email_response_time'].predict(feature_vector)[0]
            
            # Calculate confidence based on model performance
            confidence = self._calculate_model_confidence('email_response_time', features)
            
            # Get contributing factors
            contributing_factors = self._get_feature_importance(
                'email_response_time', features
            )
            
            # Generate recommendations
            recommendations = self._generate_email_recommendations(
                predicted_hours, email_data, contributing_factors
            )
            
            return PredictionResult(
                prediction_type="email_response_time",
                predicted_value=f"{predicted_hours:.1f} hours",
                confidence_score=confidence,
                contributing_factors=contributing_factors,
                recommendations=recommendations,
                data_quality_score=0.8,  # Would be calculated from actual data
                model_accuracy=0.75  # Would be from model validation
            )
            
        except Exception as e:
            logger.error(f"Error predicting email response time: {e}")
            return self._create_fallback_prediction("email_response_time", str(e))
    
    async def predict_deal_closure(self, 
                                 deal_data: Dict[str, Any],
                                 customer_id: str) -> PredictionResult:
        """Predict deal closure probability."""
        try:
            # Extract features from deal data
            features = self._extract_deal_features(deal_data)
            
            # Train model if needed
            if 'deal_closure' not in self.feature_importance:
                await self._train_deal_closure_model(customer_id)
            
            # Make prediction
            feature_vector = np.array([features]).reshape(1, -1)
            
            if hasattr(self.scalers['deal_closure'], 'mean_'):
                feature_vector = self.scalers['deal_closure'].transform(feature_vector)
            
            closure_probability = self.models['deal_closure'].predict_proba(feature_vector)[0][1]
            
            # Calculate confidence
            confidence = self._calculate_model_confidence('deal_closure', features)
            
            # Get contributing factors
            contributing_factors = self._get_feature_importance('deal_closure', features)
            
            # Generate recommendations
            recommendations = self._generate_deal_recommendations(
                closure_probability, deal_data, contributing_factors
            )
            
            return PredictionResult(
                prediction_type="deal_closure",
                predicted_value=f"{closure_probability:.1%}",
                confidence_score=confidence,
                contributing_factors=contributing_factors,
                recommendations=recommendations,
                data_quality_score=0.85,
                model_accuracy=0.78
            )
            
        except Exception as e:
            logger.error(f"Error predicting deal closure: {e}")
            return self._create_fallback_prediction("deal_closure", str(e))
    
    async def analyze_customer_engagement(self, customer_id: str) -> List[BusinessInsight]:
        """Analyze customer engagement patterns and provide insights."""
        try:
            # Get customer data
            persistence_service = await get_data_persistence_service()
            analytics = await persistence_service.get_customer_analytics(customer_id)
            
            insights = []
            
            # Engagement trend analysis
            engagement_insight = await self._analyze_engagement_trends(analytics)
            if engagement_insight:
                insights.append(engagement_insight)
            
            # Communication pattern analysis
            communication_insight = await self._analyze_communication_patterns(analytics)
            if communication_insight:
                insights.append(communication_insight)
            
            # Response time analysis
            response_insight = await self._analyze_response_patterns(analytics)
            if response_insight:
                insights.append(response_insight)
            
            # Opportunity identification
            opportunity_insight = await self._identify_opportunities(analytics)
            if opportunity_insight:
                insights.append(opportunity_insight)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error analyzing customer engagement: {e}")
            return []
    
    async def generate_business_forecast(self, 
                                       customer_id: str,
                                       forecast_period_days: int = 30) -> Dict[str, Any]:
        """Generate business forecast for the specified period."""
        try:
            # Get historical data
            persistence_service = await get_data_persistence_service()
            analytics = await persistence_service.get_customer_analytics(customer_id)
            
            # Generate forecasts for different metrics
            forecasts = {}
            
            # Email volume forecast
            email_forecast = await self._forecast_email_volume(analytics, forecast_period_days)
            forecasts['email_volume'] = email_forecast
            
            # Meeting forecast
            meeting_forecast = await self._forecast_meetings(analytics, forecast_period_days)
            forecasts['meetings'] = meeting_forecast
            
            # Deal pipeline forecast
            deal_forecast = await self._forecast_deals(analytics, forecast_period_days)
            forecasts['deals'] = deal_forecast
            
            # Overall business health score
            health_score = self._calculate_business_health_score(analytics)
            forecasts['business_health_score'] = health_score
            
            # Recommendations
            recommendations = self._generate_forecast_recommendations(forecasts)
            forecasts['recommendations'] = recommendations
            
            return {
                'customer_id': customer_id,
                'forecast_period_days': forecast_period_days,
                'generated_at': datetime.now().isoformat(),
                'forecasts': forecasts,
                'confidence_level': 'medium',  # Would be calculated from model performance
                'data_quality': 'good'
            }
            
        except Exception as e:
            logger.error(f"Error generating business forecast: {e}")
            return {
                'error': str(e),
                'customer_id': customer_id,
                'generated_at': datetime.now().isoformat()
            }
    
    def _extract_email_features(self, email_data: Dict[str, Any]) -> List[float]:
        """Extract numerical features from email data."""
        features = []
        
        # Email length
        body_length = len(email_data.get('body', ''))
        features.append(body_length)
        
        # Subject length
        subject_length = len(email_data.get('subject', ''))
        features.append(subject_length)
        
        # Time of day (hour)
        try:
            timestamp = email_data.get('timestamp', datetime.now().isoformat())
            hour = datetime.fromisoformat(timestamp).hour
            features.append(hour)
        except:
            features.append(12)  # Default to noon
        
        # Day of week
        try:
            timestamp = email_data.get('timestamp', datetime.now().isoformat())
            day_of_week = datetime.fromisoformat(timestamp).weekday()
            features.append(day_of_week)
        except:
            features.append(2)  # Default to Wednesday
        
        # Priority indicators
        priority_keywords = ['urgent', 'asap', 'important', 'priority']
        has_priority = any(keyword in email_data.get('subject', '').lower() or 
                          keyword in email_data.get('body', '').lower() 
                          for keyword in priority_keywords)
        features.append(1.0 if has_priority else 0.0)
        
        # Question count
        question_count = email_data.get('body', '').count('?')
        features.append(question_count)
        
        return features
    
    def _extract_deal_features(self, deal_data: Dict[str, Any]) -> List[float]:
        """Extract numerical features from deal data."""
        features = []
        
        # Deal amount
        amount = deal_data.get('amount', 0)
        features.append(amount)
        
        # Deal age (days)
        try:
            created_date = datetime.fromisoformat(deal_data.get('created_date', ''))
            age_days = (datetime.now() - created_date).days
            features.append(age_days)
        except:
            features.append(0)
        
        # Stage encoding
        stage_encoding = {
            'qualification': 1,
            'proposal': 2,
            'negotiation': 3,
            'closed_won': 4,
            'closed_lost': 0
        }
        stage = deal_data.get('stage', 'qualification')
        features.append(stage_encoding.get(stage, 1))
        
        # Contact interactions (mock data)
        features.append(5)  # Average interactions
        
        # Company size indicator (mock)
        features.append(100)  # Medium company
        
        return features
    
    async def _train_email_response_model(self, customer_id: str):
        """Train email response time prediction model."""
        try:
            # In a real implementation, this would fetch historical email data
            # For now, we'll create mock training data
            
            # Mock historical data
            n_samples = 100
            X = np.random.rand(n_samples, 6)  # 6 features
            y = np.random.exponential(scale=4, size=n_samples)  # Response times in hours
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            X_train_scaled = self.scalers['email_response_time'].fit_transform(X_train)
            X_test_scaled = self.scalers['email_response_time'].transform(X_test)
            
            # Train model
            self.models['email_response_time'].fit(X_train_scaled, y_train)
            
            # Calculate feature importance
            self.feature_importance['email_response_time'] = self.models['email_response_time'].feature_importances_
            
            logger.info(f"Trained email response model for customer {customer_id}")
            
        except Exception as e:
            logger.error(f"Error training email response model: {e}")
    
    async def _train_deal_closure_model(self, customer_id: str):
        """Train deal closure prediction model."""
        try:
            # Mock historical deal data
            n_samples = 200
            X = np.random.rand(n_samples, 5)  # 5 features
            y = np.random.binomial(1, 0.3, n_samples)  # 30% closure rate
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            X_train_scaled = self.scalers['deal_closure'].fit_transform(X_train)
            X_test_scaled = self.scalers['deal_closure'].transform(X_test)
            
            # Train model
            self.models['deal_closure'].fit(X_train_scaled, y_train)
            
            # Calculate feature importance
            self.feature_importance['deal_closure'] = self.models['deal_closure'].feature_importances_
            
            logger.info(f"Trained deal closure model for customer {customer_id}")
            
        except Exception as e:
            logger.error(f"Error training deal closure model: {e}")
    
    def _calculate_model_confidence(self, model_name: str, features: List[float]) -> float:
        """Calculate confidence score for a prediction."""
        # Simple confidence calculation based on feature variance
        # In production, this would be more sophisticated
        
        if model_name not in self.feature_importance:
            return 0.5  # Medium confidence if no training data
        
        # Calculate confidence based on feature importance alignment
        feature_array = np.array(features)
        importance_array = np.array(self.feature_importance[model_name])
        
        # Normalize features
        if len(feature_array) == len(importance_array):
            normalized_features = feature_array / (np.max(feature_array) + 1e-8)
            confidence = np.dot(normalized_features, importance_array) / np.sum(importance_array)
            return max(0.3, min(0.95, confidence))
        
        return 0.7  # Default confidence
    
    def _get_feature_importance(self, model_name: str, features: List[float]) -> List[Dict[str, Any]]:
        """Get feature importance for explanation."""
        if model_name not in self.feature_importance:
            return []
        
        feature_names = {
            'email_response_time': ['body_length', 'subject_length', 'hour', 'day_of_week', 'has_priority', 'question_count'],
            'deal_closure': ['amount', 'age_days', 'stage', 'interactions', 'company_size']
        }
        
        names = feature_names.get(model_name, [f'feature_{i}' for i in range(len(features))])
        importance = self.feature_importance[model_name]
        
        factors = []
        for i, (name, imp, value) in enumerate(zip(names, importance, features)):
            factors.append({
                'factor': name,
                'importance': float(imp),
                'value': float(value),
                'impact': 'high' if imp > 0.2 else 'medium' if imp > 0.1 else 'low'
            })
        
        # Sort by importance
        factors.sort(key=lambda x: x['importance'], reverse=True)
        return factors[:5]  # Top 5 factors
    
    def _generate_email_recommendations(self, 
                                      predicted_hours: float,
                                      email_data: Dict[str, Any],
                                      factors: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations for email handling."""
        recommendations = []
        
        if predicted_hours > 24:
            recommendations.append("This email may require extended response time - consider setting expectations")
            recommendations.append("Break down complex requests into smaller parts")
        elif predicted_hours < 2:
            recommendations.append("Quick response opportunity - prioritize for immediate attention")
        
        # Factor-based recommendations
        for factor in factors[:3]:
            if factor['factor'] == 'has_priority' and factor['value'] > 0:
                recommendations.append("Priority email detected - escalate if needed")
            elif factor['factor'] == 'question_count' and factor['value'] > 2:
                recommendations.append("Multiple questions detected - consider scheduling a call")
        
        return recommendations[:5]
    
    def _generate_deal_recommendations(self, 
                                     probability: float,
                                     deal_data: Dict[str, Any],
                                     factors: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations for deal management."""
        recommendations = []
        
        if probability > 0.7:
            recommendations.append("High closure probability - focus on closing activities")
            recommendations.append("Prepare contract and next steps")
        elif probability < 0.3:
            recommendations.append("Low closure probability - investigate blockers")
            recommendations.append("Consider adjusting approach or qualifying out")
        else:
            recommendations.append("Medium probability - continue nurturing relationship")
            recommendations.append("Identify and address specific concerns")
        
        # Factor-based recommendations
        for factor in factors[:3]:
            if factor['factor'] == 'age_days' and factor['value'] > 90:
                recommendations.append("Deal has been open long - review and refresh strategy")
            elif factor['factor'] == 'amount' and factor['value'] > 50000:
                recommendations.append("High-value deal - ensure executive involvement")
        
        return recommendations[:5]
    
    async def _analyze_engagement_trends(self, analytics: Dict[str, Any]) -> Optional[BusinessInsight]:
        """Analyze customer engagement trends."""
        try:
            total_conversations = analytics.get('total_conversations', 0)
            active_conversations = analytics.get('active_conversations', 0)
            
            if total_conversations == 0:
                return None
            
            engagement_ratio = active_conversations / total_conversations
            
            if engagement_ratio > 0.3:
                impact_level = "high"
                description = f"High engagement detected with {engagement_ratio:.1%} active conversation ratio"
                recommendations = ["Maintain current engagement level", "Consider upselling opportunities"]
            elif engagement_ratio < 0.1:
                impact_level = "medium"
                description = f"Low engagement detected with {engagement_ratio:.1%} active conversation ratio"
                recommendations = ["Implement re-engagement campaign", "Review communication strategy"]
            else:
                return None  # Normal engagement
            
            return BusinessInsight(
                insight_type="engagement_trend",
                title="Customer Engagement Analysis",
                description=description,
                impact_level=impact_level,
                urgency="medium",
                data_points=[
                    {"metric": "total_conversations", "value": total_conversations},
                    {"metric": "active_conversations", "value": active_conversations},
                    {"metric": "engagement_ratio", "value": engagement_ratio}
                ],
                recommended_actions=recommendations,
                confidence=0.8
            )
            
        except Exception as e:
            logger.error(f"Error analyzing engagement trends: {e}")
            return None
    
    async def _analyze_communication_patterns(self, analytics: Dict[str, Any]) -> Optional[BusinessInsight]:
        """Analyze communication patterns."""
        # Simplified analysis - in production would use more sophisticated pattern detection
        return None
    
    async def _analyze_response_patterns(self, analytics: Dict[str, Any]) -> Optional[BusinessInsight]:
        """Analyze response time patterns."""
        # Simplified analysis - in production would analyze actual response times
        return None
    
    async def _identify_opportunities(self, analytics: Dict[str, Any]) -> Optional[BusinessInsight]:
        """Identify business opportunities."""
        # Simplified analysis - in production would use ML to identify patterns
        return None
    
    async def _forecast_email_volume(self, analytics: Dict[str, Any], days: int) -> Dict[str, Any]:
        """Forecast email volume."""
        # Simplified forecast - in production would use time series analysis
        current_volume = analytics.get('usage_stats', {}).get('emails_processed', 0)
        forecasted_volume = current_volume * (days / 7)  # Simple weekly projection
        
        return {
            'current_weekly_volume': current_volume,
            'forecasted_volume': forecasted_volume,
            'trend': 'stable',
            'confidence': 0.7
        }
    
    async def _forecast_meetings(self, analytics: Dict[str, Any], days: int) -> Dict[str, Any]:
        """Forecast meeting volume."""
        current_meetings = analytics.get('usage_stats', {}).get('meetings_scheduled', 0)
        forecasted_meetings = current_meetings * (days / 7)
        
        return {
            'current_weekly_meetings': current_meetings,
            'forecasted_meetings': forecasted_meetings,
            'trend': 'stable',
            'confidence': 0.7
        }
    
    async def _forecast_deals(self, analytics: Dict[str, Any], days: int) -> Dict[str, Any]:
        """Forecast deal activity."""
        current_leads = analytics.get('usage_stats', {}).get('leads_created', 0)
        forecasted_leads = current_leads * (days / 7)
        
        return {
            'current_weekly_leads': current_leads,
            'forecasted_leads': forecasted_leads,
            'trend': 'stable',
            'confidence': 0.7
        }
    
    def _calculate_business_health_score(self, analytics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall business health score."""
        # Simplified scoring - in production would use weighted metrics
        usage_stats = analytics.get('usage_stats', {})
        
        email_score = min(usage_stats.get('emails_processed', 0) / 10, 1.0)
        meeting_score = min(usage_stats.get('meetings_scheduled', 0) / 5, 1.0)
        lead_score = min(usage_stats.get('leads_created', 0) / 3, 1.0)
        
        overall_score = (email_score + meeting_score + lead_score) / 3
        
        return {
            'overall_score': overall_score,
            'email_activity_score': email_score,
            'meeting_activity_score': meeting_score,
            'lead_generation_score': lead_score,
            'health_status': 'good' if overall_score > 0.7 else 'fair' if overall_score > 0.4 else 'needs_attention'
        }
    
    def _generate_forecast_recommendations(self, forecasts: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on forecasts."""
        recommendations = []
        
        # Email volume recommendations
        email_forecast = forecasts.get('email_volume', {})
        if email_forecast.get('forecasted_volume', 0) > 50:
            recommendations.append("High email volume expected - consider automation opportunities")
        
        # Meeting recommendations
        meeting_forecast = forecasts.get('meetings', {})
        if meeting_forecast.get('forecasted_meetings', 0) > 20:
            recommendations.append("Heavy meeting schedule - optimize calendar management")
        
        # Business health recommendations
        health = forecasts.get('business_health_score', {})
        if health.get('health_status') == 'needs_attention':
            recommendations.append("Business activity below optimal - review engagement strategy")
        
        return recommendations[:5]
    
    def _create_fallback_prediction(self, prediction_type: str, error_message: str) -> PredictionResult:
        """Create a fallback prediction result when errors occur."""
        return PredictionResult(
            prediction_type=prediction_type,
            predicted_value="Unable to predict",
            confidence_score=0.0,
            contributing_factors=[],
            recommendations=[f"Error occurred: {error_message}", "Please try again later"],
            data_quality_score=0.0,
            model_accuracy=0.0
        )


# Global service instance
_predictive_analytics_service = None

async def get_predictive_analytics_service() -> PredictiveAnalyticsService:
    """Get the global predictive analytics service instance."""
    global _predictive_analytics_service
    
    if _predictive_analytics_service is None:
        _predictive_analytics_service = PredictiveAnalyticsService()
    
    return _predictive_analytics_service

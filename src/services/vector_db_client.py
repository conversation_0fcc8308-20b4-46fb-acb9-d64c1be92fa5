"""
Vector Database Client for TKC_v5 Milestone 3

This module provides vector database operations for conversation storage and RAG capabilities.
Supports both Pinecone (production) and local mock mode (testing).
"""

import logging
import json
import os
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import numpy as np
from sentence_transformers import SentenceTransformer

from config.settings import Settings

logger = logging.getLogger(__name__)


class VectorDBClient:
    """
    Vector database client with Pinecone integration and local mock mode.
    
    Provides conversation storage, semantic search, and RAG capabilities.
    """
    
    def __init__(self, settings: Settings, mock_mode: bool = False):
        self.settings = settings
        self.mock_mode = mock_mode
        self._pinecone_client = None
        self._index = None
        self._embeddings_model = None
        self._initialized = False
        
        # Mock storage for local testing
        self._mock_vectors: Dict[str, Dict[str, Any]] = {}
        
        # Configuration
        self.dimension = 384  # all-MiniLM-L6-v2 dimension
        self.model_name = "all-MiniLM-L6-v2"
        self.index_name = "tkc-conversations"
    
    async def initialize(self) -> bool:
        """
        Initialize the vector database client.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Initialize embeddings model
            logger.info(f"Loading embeddings model: {self.model_name}")
            self._embeddings_model = SentenceTransformer(self.model_name)
            logger.info("✅ Embeddings model loaded successfully")
            
            if self.mock_mode:
                logger.info("🧪 Running in mock mode - using local storage")
                self._initialized = True
                return True
            
            # Initialize Pinecone client
            await self._initialize_pinecone()
            
            self._initialized = True
            logger.info("✅ Vector database client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize vector database client: {e}")
            return False
    
    async def _initialize_pinecone(self) -> bool:
        """Initialize Pinecone client and index."""
        try:
            # Get Pinecone configuration
            pinecone_config = await self._get_pinecone_config()
            if not pinecone_config:
                logger.warning("⚠️  Pinecone config not found, falling back to mock mode")
                self.mock_mode = True
                return True
            
            # Import Pinecone (only when needed)
            from pinecone import Pinecone
            
            # Initialize client
            self._pinecone_client = Pinecone(api_key=pinecone_config["api_key"])
            
            # Connect to index
            self._index = self._pinecone_client.Index(pinecone_config.get("index_name", self.index_name))
            
            logger.info("✅ Pinecone client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Pinecone: {e}")
            logger.info("🧪 Falling back to mock mode")
            self.mock_mode = True
            return True
    
    async def _get_pinecone_config(self) -> Optional[Dict[str, Any]]:
        """Get Pinecone configuration from Secret Manager or local file."""
        try:
            # Try Secret Manager first
            pinecone_secret = self.settings._get_secret("pinecone-config")
            if pinecone_secret:
                return pinecone_secret
            
            # Fallback to local config file
            config_file = "pinecone_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    if config.get("api_key") != "YOUR_PINECONE_API_KEY_HERE":
                        return config
            
            return None
            
        except Exception as e:
            logger.warning(f"Could not get Pinecone config: {e}")
            return None
    
    def _create_embedding(self, text: str) -> List[float]:
        """Create embedding vector for text."""
        if not self._embeddings_model:
            raise RuntimeError("Embeddings model not initialized")
        
        # Generate embedding
        embedding = self._embeddings_model.encode(text, convert_to_tensor=False)
        return embedding.tolist()
    
    async def store_conversation_message(
        self,
        message_id: str,
        conversation_id: str,
        content: str,
        message_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store a conversation message in the vector database.
        
        Args:
            message_id: Unique message identifier
            conversation_id: Conversation thread ID
            content: Message content
            message_type: Type of message (human, assistant, system)
            metadata: Additional metadata
            
        Returns:
            True if successful, False otherwise
        """
        if not self._initialized:
            logger.error("Vector database client not initialized")
            return False
        
        try:
            # Create embedding
            embedding = self._create_embedding(content)
            
            # Prepare metadata
            full_metadata = {
                "conversation_id": conversation_id,
                "message_type": message_type,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                **(metadata or {})
            }
            
            if self.mock_mode:
                # Store in mock storage
                self._mock_vectors[message_id] = {
                    "vector": embedding,
                    "metadata": full_metadata
                }
                logger.info(f"✅ Message stored in mock storage: {message_id}")
            else:
                # Store in Pinecone
                self._index.upsert(vectors=[(message_id, embedding, full_metadata)])
                logger.info(f"✅ Message stored in Pinecone: {message_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to store message {message_id}: {e}")
            return False
    
    async def search_similar_messages(
        self,
        query: str,
        conversation_id: Optional[str] = None,
        top_k: int = 5,
        min_score: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Search for similar messages using semantic search.
        
        Args:
            query: Search query
            conversation_id: Optional conversation filter
            top_k: Number of results to return
            min_score: Minimum similarity score
            
        Returns:
            List of similar messages with metadata
        """
        if not self._initialized:
            logger.error("Vector database client not initialized")
            return []
        
        try:
            # Create query embedding
            query_embedding = self._create_embedding(query)
            
            if self.mock_mode:
                # Search in mock storage
                results = []
                for msg_id, data in self._mock_vectors.items():
                    # Calculate cosine similarity
                    score = self._cosine_similarity(query_embedding, data["vector"])
                    
                    # Apply filters
                    if score < min_score:
                        continue
                    
                    if conversation_id and data["metadata"].get("conversation_id") != conversation_id:
                        continue
                    
                    results.append({
                        "id": msg_id,
                        "score": score,
                        "metadata": data["metadata"]
                    })
                
                # Sort by score and limit
                results.sort(key=lambda x: x["score"], reverse=True)
                results = results[:top_k]
                
                logger.info(f"✅ Found {len(results)} similar messages in mock storage")
            else:
                # Search in Pinecone
                filter_dict = {}
                if conversation_id:
                    filter_dict["conversation_id"] = conversation_id
                
                query_results = self._index.query(
                    vector=query_embedding,
                    top_k=top_k,
                    include_metadata=True,
                    filter=filter_dict if filter_dict else None
                )
                
                results = []
                for match in query_results.matches:
                    if match.score >= min_score:
                        results.append({
                            "id": match.id,
                            "score": match.score,
                            "metadata": match.metadata
                        })
                
                logger.info(f"✅ Found {len(results)} similar messages in Pinecone")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to search similar messages: {e}")
            return []
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        vec1_np = np.array(vec1)
        vec2_np = np.array(vec2)
        
        dot_product = np.dot(vec1_np, vec2_np)
        norm1 = np.linalg.norm(vec1_np)
        norm2 = np.linalg.norm(vec2_np)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
    
    async def get_conversation_context(
        self,
        conversation_id: str,
        max_messages: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get recent conversation context for a specific conversation.
        
        Args:
            conversation_id: Conversation thread ID
            max_messages: Maximum number of messages to return
            
        Returns:
            List of conversation messages
        """
        if not self._initialized:
            logger.error("Vector database client not initialized")
            return []
        
        try:
            if self.mock_mode:
                # Get from mock storage
                messages = []
                for msg_id, data in self._mock_vectors.items():
                    if data["metadata"].get("conversation_id") == conversation_id:
                        messages.append({
                            "id": msg_id,
                            "metadata": data["metadata"]
                        })
                
                # Sort by timestamp
                messages.sort(key=lambda x: x["metadata"].get("timestamp", ""))
                messages = messages[-max_messages:]  # Get most recent
                
                logger.info(f"✅ Retrieved {len(messages)} context messages from mock storage")
            else:
                # Query Pinecone for conversation messages
                # Note: This is a simplified approach - in production, you might want
                # to use a separate metadata store for efficient conversation retrieval
                query_results = self._index.query(
                    vector=[0.0] * self.dimension,  # Dummy vector
                    top_k=max_messages,
                    include_metadata=True,
                    filter={"conversation_id": conversation_id}
                )
                
                messages = []
                for match in query_results.matches:
                    messages.append({
                        "id": match.id,
                        "metadata": match.metadata
                    })
                
                logger.info(f"✅ Retrieved {len(messages)} context messages from Pinecone")
            
            return messages
            
        except Exception as e:
            logger.error(f"❌ Failed to get conversation context: {e}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on vector database.
        
        Returns:
            Health status information
        """
        if not self._initialized:
            return {
                "status": "unhealthy",
                "error": "Vector database client not initialized"
            }
        
        try:
            if self.mock_mode:
                return {
                    "status": "healthy",
                    "mode": "mock",
                    "stored_vectors": len(self._mock_vectors),
                    "model": self.model_name,
                    "dimension": self.dimension
                }
            else:
                # Test Pinecone connection
                stats = self._index.describe_index_stats()
                return {
                    "status": "healthy",
                    "mode": "pinecone",
                    "index_name": self.index_name,
                    "total_vectors": stats.total_vector_count,
                    "dimension": stats.dimension,
                    "model": self.model_name
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global instance
_vector_db_client: Optional[VectorDBClient] = None


async def get_vector_db_client(settings: Settings, mock_mode: bool = False) -> VectorDBClient:
    """
    Get or create the global vector database client instance.
    
    Args:
        settings: Application settings
        mock_mode: Whether to use mock mode for testing
        
    Returns:
        VectorDBClient instance
    """
    global _vector_db_client
    
    if _vector_db_client is None:
        _vector_db_client = VectorDBClient(settings, mock_mode=mock_mode)
        await _vector_db_client.initialize()
    
    return _vector_db_client

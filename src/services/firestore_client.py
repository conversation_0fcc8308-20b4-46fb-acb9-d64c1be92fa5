"""
Firestore client service for document database operations.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from google.cloud import firestore
from google.cloud.firestore_v1.base_query import FieldFilter
from config.settings import get_settings

logger = logging.getLogger(__name__)

class FirestoreClient:
    """Client for Firestore document database operations."""
    
    def __init__(self):
        """Initialize Firestore client."""
        self.settings = get_settings()
        self.client = None
        
    async def initialize(self) -> None:
        """Initialize Firestore connection."""
        try:
            # Initialize Firestore client
            self.client = firestore.Client(
                project=self.settings.google_cloud_project
            )
            
            logger.info(f"Connected to Firestore project: {self.settings.google_cloud_project}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Firestore: {e}")
            raise
    
    async def create_document(
        self, 
        collection: str, 
        document_id: Optional[str] = None,
        data: Dict[str, Any] = None
    ) -> str:
        """Create a document in Firestore."""
        try:
            if not self.client:
                await self.initialize()
            
            collection_ref = self.client.collection(collection)
            
            if document_id:
                doc_ref = collection_ref.document(document_id)
                doc_ref.set(data or {})
                created_id = document_id
            else:
                doc_ref = collection_ref.add(data or {})
                created_id = doc_ref[1].id
            
            logger.info(f"Created document {created_id} in collection {collection}")
            return created_id
            
        except Exception as e:
            logger.error(f"Failed to create document: {e}")
            raise
    
    async def get_document(
        self, 
        collection: str, 
        document_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get a document from Firestore."""
        try:
            if not self.client:
                await self.initialize()
            
            doc_ref = self.client.collection(collection).document(document_id)
            doc = doc_ref.get()
            
            if doc.exists:
                logger.info(f"Retrieved document {document_id} from collection {collection}")
                return doc.to_dict()
            else:
                logger.warning(f"Document {document_id} not found in collection {collection}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get document: {e}")
            raise
    
    async def update_document(
        self, 
        collection: str, 
        document_id: str,
        data: Dict[str, Any],
        merge: bool = True
    ) -> None:
        """Update a document in Firestore."""
        try:
            if not self.client:
                await self.initialize()
            
            doc_ref = self.client.collection(collection).document(document_id)
            
            if merge:
                doc_ref.set(data, merge=True)
            else:
                doc_ref.update(data)
            
            logger.info(f"Updated document {document_id} in collection {collection}")
            
        except Exception as e:
            logger.error(f"Failed to update document: {e}")
            raise
    
    async def delete_document(
        self, 
        collection: str, 
        document_id: str
    ) -> None:
        """Delete a document from Firestore."""
        try:
            if not self.client:
                await self.initialize()
            
            doc_ref = self.client.collection(collection).document(document_id)
            doc_ref.delete()
            
            logger.info(f"Deleted document {document_id} from collection {collection}")
            
        except Exception as e:
            logger.error(f"Failed to delete document: {e}")
            raise
    
    async def query_collection(
        self,
        collection: str,
        filters: Optional[List[Dict[str, Any]]] = None,
        order_by: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Query documents from a collection."""
        try:
            if not self.client:
                await self.initialize()
            
            query = self.client.collection(collection)
            
            # Apply filters
            if filters:
                for filter_dict in filters:
                    field = filter_dict.get('field')
                    operator = filter_dict.get('operator', '==')
                    value = filter_dict.get('value')
                    
                    if field and value is not None:
                        query = query.where(
                            filter=FieldFilter(field, operator, value)
                        )
            
            # Apply ordering
            if order_by:
                query = query.order_by(order_by)
            
            # Apply limit
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            results = []
            
            for doc in docs:
                doc_data = doc.to_dict()
                doc_data['_id'] = doc.id
                results.append(doc_data)
            
            logger.info(f"Queried collection {collection}, found {len(results)} documents")
            return results
            
        except Exception as e:
            logger.error(f"Failed to query collection: {e}")
            raise
    
    async def batch_write(
        self,
        operations: List[Dict[str, Any]]
    ) -> None:
        """Perform batch write operations."""
        try:
            if not self.client:
                await self.initialize()
            
            batch = self.client.batch()
            
            for operation in operations:
                op_type = operation.get('type')
                collection = operation.get('collection')
                document_id = operation.get('document_id')
                data = operation.get('data', {})
                
                doc_ref = self.client.collection(collection).document(document_id)
                
                if op_type == 'set':
                    batch.set(doc_ref, data)
                elif op_type == 'update':
                    batch.update(doc_ref, data)
                elif op_type == 'delete':
                    batch.delete(doc_ref)
            
            batch.commit()
            logger.info(f"Completed batch write with {len(operations)} operations")
            
        except Exception as e:
            logger.error(f"Failed to perform batch write: {e}")
            raise

# Global instance
_firestore_client = None

async def get_firestore_client() -> FirestoreClient:
    """Get or create Firestore client instance."""
    global _firestore_client
    if _firestore_client is None:
        _firestore_client = FirestoreClient()
        await _firestore_client.initialize()
    return _firestore_client

#!/bin/bash

# TKC_v5 Monitoring Dashboard Deployment Script

set -e

PROJECT_ID="vertex-ai-agent-yzdlnjey"
SERVICE_NAME="tkc-monitoring-dashboard"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 Deploying TKC_v5 Monitoring Dashboard"
echo "Project: ${PROJECT_ID}"
echo "Service: ${SERVICE_NAME}"
echo "Region: ${REGION}"

# Build and push Docker image
echo "📦 Building Docker image..."
docker build -t ${IMAGE_NAME} .

echo "📤 Pushing image to Google Container Registry..."
docker push ${IMAGE_NAME}

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --port 8081 \
    --memory 512Mi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 2 \
    --set-env-vars "FLASK_ENV=production" \
    --set-env-vars "DASHBOARD_USERNAME=admin" \
    --set-env-vars "DASHBOARD_PASSWORD=tkc_monitor_2025" \
    --project ${PROJECT_ID}

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --platform managed \
    --region ${REGION} \
    --format 'value(status.url)' \
    --project ${PROJECT_ID})

echo "✅ Deployment complete!"
echo "🌐 Dashboard URL: ${SERVICE_URL}"
echo "🔐 Username: admin"
echo "🔐 Password: tkc_monitor_2025"
echo ""
echo "📊 Access your monitoring dashboard at: ${SERVICE_URL}"

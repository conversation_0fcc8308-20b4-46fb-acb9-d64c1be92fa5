"""
TKC_v5 Agent Platform Monitoring Dashboard

A lightweight web-based monitoring interface for real-time visibility
into our agent infrastructure and backend systems.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import os
import pytz

from flask import Flask, render_template, jsonify, request, session, redirect, url_for
from flask_httpauth import HTTPBasicAuth
import redis.asyncio as redis
import pinecone
from google.cloud import secretmanager, monitoring_v3
import httpx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')
auth = HTTPBasicAuth()

# Configuration
PROJECT_ID = "vertex-ai-agent-yzdlnjey"
CLOUD_RUN_SERVICE_URL = "https://vertex-ai-agent-1072222703018.us-central1.run.app"
REFRESH_INTERVAL = 30  # seconds

# Authentication
DASHBOARD_USERNAME = os.environ.get('DASHBOARD_USERNAME', 'admin')
DASHBOARD_PASSWORD = os.environ.get('DASHBOARD_PASSWORD', 'tkc_monitor_2025')

@auth.verify_password
def verify_password(username, password):
    return username == DASHBOARD_USERNAME and password == DASHBOARD_PASSWORD


class SystemMonitor:
    """Main monitoring class that collects system metrics."""
    
    def __init__(self):
        self.project_id = PROJECT_ID
        self.secret_client = secretmanager.SecretManagerServiceClient()
        self.monitoring_client = monitoring_v3.MetricServiceClient()
        self.redis_client = None
        self.pinecone_initialized = False
        self.denver_tz = pytz.timezone('America/Denver')
        
    async def initialize(self):
        """Initialize monitoring connections."""
        try:
            # Initialize Redis connection
            await self._init_redis()
            
            # Initialize Pinecone connection
            await self._init_pinecone()
            
            logger.info("✅ System monitor initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize system monitor: {e}")

    def format_timestamp(self, timestamp_str: str) -> str:
        """Convert timestamp to Denver timezone with detailed formatting."""
        try:
            # Parse the timestamp (assuming it's in UTC)
            if isinstance(timestamp_str, str):
                dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            else:
                dt = timestamp_str

            # Convert to Denver timezone
            if dt.tzinfo is None:
                dt = pytz.UTC.localize(dt)

            denver_time = dt.astimezone(self.denver_tz)

            # Format with detailed info
            return denver_time.strftime("%Y-%m-%d %H:%M:%S %Z")
        except Exception as e:
            logger.error(f"Error formatting timestamp {timestamp_str}: {e}")
            return str(timestamp_str)

    async def _init_redis(self):
        """Initialize Redis connection."""
        try:
            # Get Redis configuration from Secret Manager
            redis_config = await self._get_secret("redis-config")
            
            self.redis_client = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                password=redis_config.get('password'),
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("✅ Redis connection established")
            
        except Exception as e:
            logger.error(f"❌ Redis connection failed: {e}")
            self.redis_client = None
    
    async def _init_pinecone(self):
        """Initialize Pinecone connection."""
        try:
            # Get Pinecone configuration from Secret Manager
            pinecone_config = await self._get_secret("pinecone-config")
            
            from pinecone import Pinecone
            self.pinecone_client = Pinecone(api_key=pinecone_config['api_key'])
            
            self.pinecone_initialized = True
            logger.info("✅ Pinecone connection established")
            
        except Exception as e:
            logger.error(f"❌ Pinecone connection failed: {e}")
            self.pinecone_initialized = False
    
    async def _get_secret(self, secret_name: str) -> Dict[str, Any]:
        """Get secret from Secret Manager."""
        try:
            secret_path = f"projects/{self.project_id}/secrets/{secret_name}/versions/latest"
            response = self.secret_client.access_secret_version(request={"name": secret_path})
            return json.loads(response.payload.data.decode("UTF-8"))
        except Exception as e:
            logger.error(f"❌ Failed to get secret {secret_name}: {e}")
            return {}
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents."""
        agents = {
            "executive": {"name": "Executive Agent", "status": "unknown", "response_time": 0, "error_rate": 0},
            "sales_dev": {"name": "Sales Development Agent", "status": "unknown", "response_time": 0, "error_rate": 0},
            "marketing": {"name": "Marketing Content Agent", "status": "unknown", "response_time": 0, "error_rate": 0}
        }
        
        try:
            # Test main service health
            async with httpx.AsyncClient(timeout=10.0) as client:
                start_time = datetime.now()
                response = await client.get(f"{CLOUD_RUN_SERVICE_URL}/health")
                response_time = (datetime.now() - start_time).total_seconds()
                
                if response.status_code == 200:
                    agents["executive"]["status"] = "online"
                    agents["executive"]["response_time"] = response_time
                else:
                    agents["executive"]["status"] = "error"
                    
        except Exception as e:
            logger.error(f"❌ Agent health check failed: {e}")
            agents["executive"]["status"] = "offline"
        
        # For now, Sales Dev and Marketing agents inherit Executive status
        # In production, these would have separate health endpoints
        for agent_key in ["sales_dev", "marketing"]:
            agents[agent_key]["status"] = agents["executive"]["status"]
            agents[agent_key]["response_time"] = agents["executive"]["response_time"]
        
        return agents
    
    async def get_infrastructure_status(self) -> Dict[str, Any]:
        """Get infrastructure component status."""
        infrastructure = {
            "vector_db": {"status": "unknown", "vector_count": 0, "namespaces": 0},
            "redis": {"status": "unknown", "memory_usage": 0, "active_sessions": 0},
            "cloud_run": {"status": "unknown", "cpu_usage": 0, "memory_usage": 0, "request_count": 0},
            "secret_manager": {"status": "unknown", "last_updated": None}
        }
        
        # Check Vector Database (Pinecone)
        try:
            if self.pinecone_initialized:
                indexes = pinecone.list_indexes()
                if indexes:
                    index_name = indexes[0]  # Use first available index
                    index = pinecone.Index(index_name)
                    stats = index.describe_index_stats()
                    
                    infrastructure["vector_db"]["status"] = "online"
                    infrastructure["vector_db"]["vector_count"] = stats.total_vector_count
                    infrastructure["vector_db"]["namespaces"] = len(stats.namespaces) if stats.namespaces else 0
                else:
                    infrastructure["vector_db"]["status"] = "no_indexes"
            else:
                infrastructure["vector_db"]["status"] = "offline"
                
        except Exception as e:
            logger.error(f"❌ Vector DB status check failed: {e}")
            infrastructure["vector_db"]["status"] = "error"
        
        # Check Redis
        try:
            if self.redis_client:
                info = await self.redis_client.info()
                infrastructure["redis"]["status"] = "online"
                infrastructure["redis"]["memory_usage"] = info.get('used_memory_human', '0B')
                infrastructure["redis"]["active_sessions"] = info.get('connected_clients', 0)
            else:
                infrastructure["redis"]["status"] = "offline"
                
        except Exception as e:
            logger.error(f"❌ Redis status check failed: {e}")
            infrastructure["redis"]["status"] = "error"
        
        # Check Cloud Run (simplified - would use GCP Monitoring API in production)
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{CLOUD_RUN_SERVICE_URL}/health")
                if response.status_code == 200:
                    infrastructure["cloud_run"]["status"] = "online"
                    # Mock metrics for demo
                    infrastructure["cloud_run"]["cpu_usage"] = 25.5
                    infrastructure["cloud_run"]["memory_usage"] = 45.2
                    infrastructure["cloud_run"]["request_count"] = 1247
                else:
                    infrastructure["cloud_run"]["status"] = "error"
                    
        except Exception as e:
            logger.error(f"❌ Cloud Run status check failed: {e}")
            infrastructure["cloud_run"]["status"] = "offline"
        
        # Check Secret Manager
        try:
            # Test access to a known secret
            await self._get_secret("redis-config")
            infrastructure["secret_manager"]["status"] = "online"
            infrastructure["secret_manager"]["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
        except Exception as e:
            logger.error(f"❌ Secret Manager status check failed: {e}")
            infrastructure["secret_manager"]["status"] = "error"
        
        return infrastructure
    
    async def get_tenant_overview(self) -> Dict[str, Any]:
        """Get multi-tenant metrics overview."""
        tenant_data = {
            "active_tenants": 0,
            "total_conversations": 0,
            "total_vectors": 0,
            "tenants": []
        }
        
        try:
            if self.redis_client:
                # Scan for tenant keys to count active tenants
                tenant_keys = set()
                async for key in self.redis_client.scan_iter(match="tkc_*:*"):
                    # Extract tenant prefix
                    tenant_prefix = key.split(':')[0]
                    tenant_keys.add(tenant_prefix)
                
                tenant_data["active_tenants"] = len(tenant_keys)
                
                # Mock tenant details for demo
                for i, tenant_key in enumerate(list(tenant_keys)[:5]):  # Show first 5
                    tenant_data["tenants"].append({
                        "id": f"customer_{i+1}",
                        "namespace": tenant_key,
                        "conversations": 12 + i * 3,
                        "vectors": 45 + i * 15,
                        "last_activity": (datetime.now() - timedelta(hours=i)).strftime("%H:%M")
                    })
                
                tenant_data["total_conversations"] = sum(t["conversations"] for t in tenant_data["tenants"])
                tenant_data["total_vectors"] = sum(t["vectors"] for t in tenant_data["tenants"])
                
        except Exception as e:
            logger.error(f"❌ Tenant overview failed: {e}")
        
        return tenant_data
    
    async def get_recent_activity(self) -> List[Dict[str, Any]]:
        """Get recent system activity with detailed drill-down data."""
        activities = []

        try:
            # Mock recent activities with detailed metadata for drill-down
            base_time = datetime.now(pytz.UTC)

            mock_activities = [
                {
                    "type": "agent_interaction",
                    "message": "Sales Development Agent qualified new lead",
                    "severity": "info",
                    "details": {
                        "agent_id": "sales_dev_001",
                        "lead_score": 85,
                        "email_processed": "<EMAIL>",
                        "classification": "hot_lead",
                        "crm_sync": True,
                        "processing_time_ms": 1250
                    }
                },
                {
                    "type": "email_processing",
                    "message": "Gmail Agent filtered 47 emails, classified 12 as actionable",
                    "severity": "info",
                    "details": {
                        "total_emails": 47,
                        "filtered_out": 35,
                        "classified_actionable": 12,
                        "spam_detected": 8,
                        "auto_responses": 15,
                        "processing_batch_id": "batch_20250127_001"
                    }
                },
                {
                    "type": "tool_usage",
                    "message": "CRM integration: Created contact in HubSpot",
                    "severity": "info",
                    "details": {
                        "tool": "hubspot_crm",
                        "action": "create_contact",
                        "contact_id": "hs_12345678",
                        "tenant": "customer_3",
                        "api_response_time": 850,
                        "data_synced": ["email", "name", "company", "lead_score"]
                    }
                },
                {
                    "type": "tenant_activity",
                    "message": "New customer onboarded: test_customer_3",
                    "severity": "success",
                    "details": {
                        "tenant_id": "test_customer_3",
                        "namespace_created": "tkc_test_customer_3",
                        "vector_index_initialized": True,
                        "redis_keys_created": 15,
                        "initial_agents": ["executive", "marketing", "sales"],
                        "onboarding_duration_sec": 45
                    }
                },
                {
                    "type": "vector_operations",
                    "message": "Vector database sync completed - 1,247 embeddings processed",
                    "severity": "info",
                    "details": {
                        "vectors_processed": 1247,
                        "new_vectors": 89,
                        "updated_vectors": 23,
                        "deleted_vectors": 5,
                        "sync_duration_sec": 12.5,
                        "namespaces_affected": ["customer_1", "customer_2", "customer_3"]
                    }
                },
                {
                    "type": "rate_limiting",
                    "message": "Rate limit exceeded for customer_2 - 15 requests queued",
                    "severity": "warning",
                    "details": {
                        "tenant": "customer_2",
                        "limit_type": "api_calls_per_minute",
                        "current_rate": 125,
                        "limit": 100,
                        "queued_requests": 15,
                        "estimated_delay_sec": 45,
                        "retry_after": "2025-01-27T20:18:30Z"
                    }
                },
                {
                    "type": "performance",
                    "message": "Average response time: 2.3s across 156 requests",
                    "severity": "info",
                    "details": {
                        "avg_response_time_ms": 2300,
                        "total_requests": 156,
                        "p95_response_time_ms": 4200,
                        "p99_response_time_ms": 7800,
                        "slowest_endpoint": "/api/agents/chat",
                        "fastest_endpoint": "/api/health"
                    }
                },
                {
                    "type": "agent_interaction",
                    "message": "Marketing Agent created blog post draft",
                    "severity": "info",
                    "details": {
                        "agent_id": "marketing_001",
                        "content_type": "blog_post",
                        "word_count": 1250,
                        "seo_score": 78,
                        "readability_score": 85,
                        "topics": ["AI automation", "business efficiency"],
                        "estimated_read_time": "5 min"
                    }
                }
            ]

            for i, activity in enumerate(mock_activities):
                timestamp = base_time - timedelta(minutes=i*7, seconds=i*15)
                activities.append({
                    "id": f"activity_{i+1}",
                    "timestamp": self.format_timestamp(timestamp),
                    "timestamp_raw": timestamp.isoformat(),
                    "type": activity["type"],
                    "message": activity["message"],
                    "severity": activity["severity"],
                    "details": activity.get("details", {}),
                    "expandable": True
                })

        except Exception as e:
            logger.error(f"❌ Recent activity fetch failed: {e}")

        return activities


# Global monitor instance
monitor = SystemMonitor()


@app.route('/')
@auth.login_required
def dashboard():
    """Main dashboard page."""
    return render_template('dashboard.html', refresh_interval=REFRESH_INTERVAL)


@app.route('/api/status')
@auth.login_required
def get_status():
    """API endpoint for dashboard data."""
    try:
        # Collect all monitoring data using asyncio.run
        agent_status = asyncio.run(monitor.get_agent_status())
        infrastructure_status = asyncio.run(monitor.get_infrastructure_status())
        tenant_overview = asyncio.run(monitor.get_tenant_overview())
        recent_activity = asyncio.run(monitor.get_recent_activity())
        
        return jsonify({
            "timestamp": monitor.format_timestamp(datetime.now()),
            "timestamp_raw": datetime.now().isoformat(),
            "agents": agent_status,
            "infrastructure": infrastructure_status,
            "tenants": tenant_overview,
            "activity": recent_activity,
            "system_health": "healthy"  # Overall system health
        })
        
    except Exception as e:
        logger.error(f"❌ Status API error: {e}")
        return jsonify({
            "error": str(e),
            "timestamp": datetime.now().isoformat(),
            "system_health": "error"
        }), 500


@app.route('/api/health')
def health_check():
    """Simple health check endpoint."""
    return jsonify({
        "status": "healthy",
        "timestamp": monitor.format_timestamp(datetime.now()),
        "service": "TKC_v5 Monitoring Dashboard"
    })


@app.route('/api/activity/<activity_id>')
@auth.login_required
def get_activity_details(activity_id):
    """Get detailed information for a specific activity."""
    try:
        # In a real implementation, this would query the database
        # For now, we'll return mock detailed data based on activity ID

        base_time = datetime.now(pytz.UTC)

        activity_templates = {
            "activity_1": {
                "type": "agent_interaction",
                "summary": "Sales Development Agent qualified new lead",
                "details": {
                    "agent_id": "sales_dev_001",
                    "agent_name": "Sales Development Agent",
                    "lead_score": 85,
                    "email_processed": "<EMAIL>",
                    "classification": "hot_lead",
                    "crm_sync": True,
                    "processing_time_ms": 1250,
                    "conversation_id": "conv_20250127_001",
                    "message_count": 3,
                    "tools_used": ["email_classifier", "crm_integration", "lead_scorer"],
                    "confidence_score": 0.92
                },
                "conversation_preview": [
                    {"role": "user", "content": "I'm interested in your AI automation services for my manufacturing company..."},
                    {"role": "assistant", "content": "I'd be happy to help you explore our AI automation solutions. Can you tell me more about your current processes?"},
                    {"role": "user", "content": "We're processing about 500 orders daily and struggling with inventory management..."}
                ],
                "next_actions": [
                    "Schedule demo call",
                    "Send pricing information",
                    "Connect with technical team"
                ]
            },
            "activity_2": {
                "type": "email_processing",
                "summary": "Gmail Agent filtered 47 emails, classified 12 as actionable",
                "details": {
                    "total_emails": 47,
                    "filtered_out": 35,
                    "classified_actionable": 12,
                    "spam_detected": 8,
                    "auto_responses": 15,
                    "processing_batch_id": "batch_20250127_001",
                    "filter_rules_applied": [
                        "newsletter_filter",
                        "promotional_filter",
                        "auto_reply_filter",
                        "spam_filter"
                    ],
                    "classification_breakdown": {
                        "sales_inquiries": 5,
                        "support_requests": 3,
                        "partnership_opportunities": 2,
                        "job_applications": 2
                    }
                },
                "sample_emails": [
                    {
                        "subject": "Partnership Opportunity - AI Integration",
                        "from": "<EMAIL>",
                        "classification": "partnership_opportunity",
                        "confidence": 0.89,
                        "action_taken": "forwarded_to_business_dev"
                    },
                    {
                        "subject": "Question about your automation pricing",
                        "from": "<EMAIL>",
                        "classification": "sales_inquiry",
                        "confidence": 0.95,
                        "action_taken": "assigned_to_sales_agent"
                    }
                ]
            }
        }

        if activity_id in activity_templates:
            activity = activity_templates[activity_id].copy()
            activity.update({
                "id": activity_id,
                "timestamp": monitor.format_timestamp(base_time - timedelta(minutes=int(activity_id.split('_')[1]) * 7)),
                "timestamp_raw": (base_time - timedelta(minutes=int(activity_id.split('_')[1]) * 7)).isoformat()
            })
            return jsonify(activity)
        else:
            return jsonify({"error": "Activity not found"}), 404

    except Exception as e:
        logger.error(f"❌ Activity details API error: {e}")
        return jsonify({"error": "Failed to fetch activity details"}), 500


@app.route('/api/agents/<agent_id>/metrics')
@auth.login_required
def get_agent_metrics(agent_id):
    """Get detailed metrics for a specific agent."""
    try:
        # Mock agent metrics data
        agent_metrics = {
            "sales_dev_001": {
                "agent_id": "sales_dev_001",
                "name": "Sales Development Agent",
                "status": "active",
                "uptime_hours": 72.5,
                "total_interactions": 156,
                "successful_interactions": 142,
                "success_rate": 0.91,
                "avg_response_time_ms": 2100,
                "leads_qualified": 23,
                "leads_converted": 8,
                "conversion_rate": 0.35,
                "last_24h": {
                    "interactions": 28,
                    "leads_qualified": 5,
                    "avg_response_time_ms": 1950,
                    "peak_hour": "14:00-15:00",
                    "busiest_period": "Monday 2-4 PM"
                },
                "tools_usage": {
                    "email_classifier": 89,
                    "crm_integration": 67,
                    "lead_scorer": 45,
                    "calendar_scheduler": 23
                },
                "performance_trends": [
                    {"hour": "08:00", "interactions": 5, "response_time": 2200},
                    {"hour": "09:00", "interactions": 8, "response_time": 2100},
                    {"hour": "10:00", "interactions": 12, "response_time": 1900},
                    {"hour": "11:00", "interactions": 15, "response_time": 1850}
                ]
            },
            "marketing_001": {
                "agent_id": "marketing_001",
                "name": "Marketing Content Agent",
                "status": "active",
                "uptime_hours": 68.2,
                "total_interactions": 89,
                "successful_interactions": 84,
                "success_rate": 0.94,
                "avg_response_time_ms": 1800,
                "content_created": 45,
                "content_published": 38,
                "engagement_score": 0.78,
                "last_24h": {
                    "interactions": 15,
                    "content_pieces": 3,
                    "avg_response_time_ms": 1650,
                    "peak_hour": "10:00-11:00",
                    "busiest_period": "Tuesday 10-12 PM"
                },
                "tools_usage": {
                    "content_generator": 45,
                    "seo_optimizer": 38,
                    "social_scheduler": 22,
                    "analytics_tracker": 15
                }
            }
        }

        if agent_id in agent_metrics:
            return jsonify(agent_metrics[agent_id])
        else:
            return jsonify({"error": "Agent not found"}), 404

    except Exception as e:
        logger.error(f"❌ Agent metrics API error: {e}")
        return jsonify({"error": "Failed to fetch agent metrics"}), 500


@app.route('/api/tenants/<tenant_id>/details')
@auth.login_required
def get_tenant_details(tenant_id):
    """Get detailed information for a specific tenant."""
    try:
        # Mock tenant details
        tenant_details = {
            "customer_1": {
                "tenant_id": "customer_1",
                "name": "TechCorp Industries",
                "namespace": "tkc_customer_1",
                "status": "active",
                "onboarded_date": "2025-01-15",
                "subscription_tier": "enterprise",
                "agents_deployed": ["executive", "marketing", "sales"],
                "total_conversations": 89,
                "total_messages": 1247,
                "vector_count": 3456,
                "storage_used_mb": 125.7,
                "api_calls_today": 234,
                "api_limit_daily": 1000,
                "last_activity": monitor.format_timestamp(datetime.now() - timedelta(minutes=15)),
                "usage_stats": {
                    "peak_usage_hour": "14:00-15:00",
                    "avg_daily_conversations": 12,
                    "most_used_agent": "sales",
                    "top_tools": ["crm_integration", "email_processor", "calendar"]
                },
                "recent_conversations": [
                    {
                        "id": "conv_001",
                        "agent": "sales",
                        "started": monitor.format_timestamp(datetime.now() - timedelta(hours=2)),
                        "messages": 8,
                        "status": "completed",
                        "outcome": "lead_qualified"
                    },
                    {
                        "id": "conv_002",
                        "agent": "marketing",
                        "started": monitor.format_timestamp(datetime.now() - timedelta(hours=4)),
                        "messages": 12,
                        "status": "completed",
                        "outcome": "content_created"
                    }
                ]
            },
            "customer_2": {
                "tenant_id": "customer_2",
                "name": "SmallBiz Solutions",
                "namespace": "tkc_customer_2",
                "status": "active",
                "onboarded_date": "2025-01-20",
                "subscription_tier": "professional",
                "agents_deployed": ["executive", "sales"],
                "total_conversations": 45,
                "total_messages": 678,
                "vector_count": 1234,
                "storage_used_mb": 67.3,
                "api_calls_today": 89,
                "api_limit_daily": 500,
                "last_activity": monitor.format_timestamp(datetime.now() - timedelta(minutes=45))
            }
        }

        if tenant_id in tenant_details:
            return jsonify(tenant_details[tenant_id])
        else:
            return jsonify({"error": "Tenant not found"}), 404

    except Exception as e:
        logger.error(f"❌ Tenant details API error: {e}")
        return jsonify({"error": "Failed to fetch tenant details"}), 500


# Initialize monitor on startup
async def initialize_monitor():
    """Initialize the monitor before first request."""
    await monitor.initialize()


if __name__ == '__main__':
    # Initialize monitor
    asyncio.run(monitor.initialize())
    
    # Run Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 8081)),
        debug=os.environ.get('FLASK_ENV') == 'development'
    )

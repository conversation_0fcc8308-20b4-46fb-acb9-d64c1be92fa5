#!/usr/bin/env python3
"""
Local development runner for TKC_v5 Monitoring Dashboard

Run this script to start the monitoring dashboard locally for development and testing.
"""

import os
import sys
import asyncio
from app import app, monitor

def setup_environment():
    """Set up environment variables for local development."""
    os.environ.setdefault('FLASK_ENV', 'development')
    os.environ.setdefault('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')
    os.environ.setdefault('DASHBOARD_USERNAME', 'admin')
    os.environ.setdefault('DASHBOARD_PASSWORD', 'tkc_monitor_2025')
    os.environ.setdefault('PORT', '8081')

def initialize_and_run():
    """Initialize the monitor and run the Flask app."""
    print("🚀 Starting TKC_v5 Monitoring Dashboard")
    print("=" * 50)

    # Initialize the monitor
    print("🔧 Initializing system monitor...")
    asyncio.run(monitor.initialize())

    print("✅ Monitor initialized successfully")
    print("🌐 Starting web server...")
    print(f"📊 Dashboard will be available at: http://localhost:8081")
    print(f"🔐 Username: admin")
    print(f"🔐 Password: tkc_monitor_2025")
    print("=" * 50)

    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 8081)),
        debug=True,
        use_reloader=False  # Disable reloader to avoid async issues
    )

def main():
    """Main entry point."""
    try:
        setup_environment()
        
        # Check if we're in the right directory
        if not os.path.exists('templates/dashboard.html'):
            print("❌ Error: templates/dashboard.html not found")
            print("Please run this script from the monitoring/ directory")
            sys.exit(1)
        
        # Run the initialization and Flask app
        initialize_and_run()
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down monitoring dashboard...")
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()

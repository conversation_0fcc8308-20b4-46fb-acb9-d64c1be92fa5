# TKC_v5 Agent Platform - Monitoring Dashboard

A lightweight web-based monitoring interface providing real-time visibility into the TKC_v5 agent platform backend systems.

## 🎯 **Purpose**

This monitoring dashboard is designed as an internal tool for development and monitoring purposes during Milestone 4 testing and deployment. It provides comprehensive visibility into:

- **Agent Status**: Health and performance of all AI agents
- **Infrastructure Monitoring**: Core services status and metrics
- **Multi-Tenant Overview**: Customer isolation and resource usage
- **Recent Activity**: System events and performance monitoring

## 📊 **Dashboard Components**

### **1. Agent Status Panel**
- ✅ **Executive Agent**: Main orchestrator status and response times
- ✅ **Sales Development Agent**: Lead qualification and CRM integration status
- ✅ **Marketing Content Agent**: Content creation and campaign management status
- 📊 **Metrics**: Response times, error rates, availability

### **2. Infrastructure Monitoring**
- 🔍 **Vector Database (Pinecone)**: Connection status, vector count, namespace usage
- 💾 **Redis Cache**: Connection status, memory usage, active sessions
- ☁️ **Cloud Run Services**: Deployment status, CPU/memory usage, request counts
- 🔐 **Secret Manager**: Configuration status and last updated timestamps

### **3. Multi-Tenant Overview**
- 👥 **Active Tenants**: Count of customers using the platform
- 📈 **Resource Usage**: Per-tenant vector storage, Redis keys, conversation counts
- ⚙️ **Configuration Status**: Tenant setup and isolation verification

### **4. Recent Activity Feed**
- 🤖 **Agent Interactions**: Tool usage and conversation events
- ⚠️ **System Alerts**: Error logs and performance anomalies
- 👤 **Customer Events**: Onboarding and configuration changes
- 📊 **Performance Metrics**: Response times and system health

## 🚀 **Quick Start**

### **Local Development**

1. **Install Dependencies**:
   ```bash
   cd monitoring/
   pip install -r requirements.txt
   ```

2. **Set up Google Cloud Authentication**:
   ```bash
   gcloud auth application-default login
   gcloud config set project vertex-ai-agent-yzdlnjey
   ```

3. **Run Locally**:
   ```bash
   python run_local.py
   ```

4. **Access Dashboard**:
   - URL: http://localhost:8081
   - Username: `admin`
   - Password: `tkc_monitor_2025`

### **Cloud Deployment**

1. **Make Deploy Script Executable**:
   ```bash
   chmod +x deploy.sh
   ```

2. **Deploy to Cloud Run**:
   ```bash
   ./deploy.sh
   ```

3. **Access Deployed Dashboard**:
   - The script will output the Cloud Run URL
   - Use the same credentials: `admin` / `tkc_monitor_2025`

## 🔧 **Configuration**

### **Environment Variables**

| Variable | Default | Description |
|----------|---------|-------------|
| `FLASK_ENV` | `development` | Flask environment mode |
| `FLASK_SECRET_KEY` | `dev-secret-key...` | Flask session secret |
| `DASHBOARD_USERNAME` | `admin` | Dashboard login username |
| `DASHBOARD_PASSWORD` | `tkc_monitor_2025` | Dashboard login password |
| `PORT` | `8081` | Web server port |

### **Authentication**

The dashboard uses HTTP Basic Authentication with configurable credentials. In production, consider:
- Using stronger passwords
- Implementing OAuth integration
- Adding IP whitelisting
- Using HTTPS only

## 📱 **Features**

### **Real-time Updates**
- ⏱️ **Auto-refresh**: Dashboard updates every 30 seconds
- 🔄 **Manual Refresh**: Click refresh indicator for immediate update
- 📊 **Live Metrics**: Real-time system health and performance data

### **Responsive Design**
- 💻 **Desktop**: Three-column layout with full feature set
- 📱 **Mobile**: Single-column layout optimized for touch
- 🎨 **Modern UI**: Glass-morphism design with smooth animations

### **Error Handling**
- ⚠️ **Connection Issues**: Graceful handling of backend connectivity problems
- 🔄 **Retry Logic**: Automatic retry for failed requests
- 📝 **Error Display**: Clear error messages with troubleshooting hints

## 🏗️ **Architecture**

### **Backend Components**
```
MonitoringDashboard
├── SystemMonitor
│   ├── Agent Health Checks
│   ├── Infrastructure Status
│   ├── Tenant Metrics
│   └── Activity Logging
├── Flask Web Server
├── Authentication Layer
└── API Endpoints
```

### **Data Sources**
- **TKC_v5 Agent Platform**: Health checks and metrics
- **Google Cloud Monitoring**: Infrastructure metrics
- **Pinecone**: Vector database statistics
- **Redis**: Cache and session data
- **Secret Manager**: Configuration status

### **Technology Stack**
- **Backend**: Flask + Python 3.11
- **Frontend**: Vanilla JavaScript + CSS3
- **Deployment**: Docker + Google Cloud Run
- **Monitoring**: Google Cloud APIs + Custom metrics

## 🔍 **Monitoring Capabilities**

### **System Health Indicators**
- 🟢 **Online**: Service responding normally
- 🟡 **Warning**: Performance degraded or minor issues
- 🔴 **Offline**: Service unavailable or critical error
- ⚪ **Unknown**: Status cannot be determined

### **Performance Metrics**
- **Response Times**: Average API response times
- **Error Rates**: Percentage of failed requests
- **Resource Usage**: CPU, memory, and storage utilization
- **Throughput**: Requests per minute and conversation volume

### **Multi-Tenant Monitoring**
- **Isolation Verification**: Ensures customer data separation
- **Resource Allocation**: Per-tenant usage tracking
- **Configuration Status**: Tenant setup and health
- **Activity Monitoring**: Per-customer interaction tracking

## 🚨 **Alerts and Notifications**

### **Current Alerts**
- High error rates (>5%)
- Slow response times (>5 seconds)
- Infrastructure failures
- Multi-tenant isolation issues

### **Future Enhancements**
- Email/Slack notifications
- Custom alert thresholds
- Historical trend analysis
- Automated incident response

## 🔒 **Security Considerations**

### **Access Control**
- HTTP Basic Authentication required
- Configurable credentials via environment variables
- No sensitive data exposed in logs
- Secure communication with backend services

### **Data Protection**
- No customer data displayed in dashboard
- Aggregated metrics only
- Secure credential storage
- Audit logging for access

## 📈 **Future Roadmap**

### **Phase 1 Enhancements** (Next 2 weeks)
- Historical data visualization
- Custom alert configuration
- Export capabilities for metrics
- Enhanced mobile experience

### **Phase 2 Features** (Month 2)
- Organizational chart view of agent relationships
- Interactive data flow visualization
- Advanced filtering and search
- Integration with external monitoring tools

### **Phase 3 Vision** (Month 3+)
- Predictive analytics and anomaly detection
- Automated scaling recommendations
- Customer-facing health status page
- Advanced reporting and analytics

## 🛠️ **Development**

### **Project Structure**
```
monitoring/
├── app.py                 # Main Flask application
├── templates/
│   └── dashboard.html     # Dashboard UI template
├── requirements.txt       # Python dependencies
├── Dockerfile            # Container configuration
├── deploy.sh             # Cloud deployment script
├── run_local.py          # Local development runner
└── README.md             # This file
```

### **Adding New Metrics**
1. Extend `SystemMonitor` class in `app.py`
2. Add new API endpoint for data
3. Update dashboard template with new UI components
4. Test locally before deployment

### **Customization**
- Modify `dashboard.html` for UI changes
- Update `SystemMonitor` class for new data sources
- Adjust refresh intervals and thresholds as needed
- Add new authentication methods if required

## 📞 **Support**

For issues or questions about the monitoring dashboard:
1. Check the Recent Activity feed for system alerts
2. Review logs in Google Cloud Console
3. Verify backend service health
4. Contact the development team for assistance

---

**Built for TKC_v5 Agent Platform Milestone 4**  
**Version**: 1.0.0  
**Last Updated**: 2025-07-26

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TKC_v5 Agent Platform - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: #2d3748;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header .subtitle {
            color: #718096;
            font-size: 0.9rem;
            margin-top: 0.25rem;
        }

        .header .last-updated {
            float: right;
            color: #4a5568;
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card h2 {
            color: #2d3748;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-online { background: #48bb78; }
        .status-offline { background: #f56565; }
        .status-warning { background: #ed8936; }
        .status-unknown { background: #a0aec0; }
        .status-error { background: #e53e3e; }

        .metric-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .metric-row:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #4a5568;
        }

        .metric-value {
            font-weight: 600;
            color: #2d3748;
        }

        .activity-feed {
            grid-column: 1 / -1;
            max-height: 400px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #718096;
            min-width: 60px;
        }

        .activity-type {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            min-width: 80px;
            text-align: center;
        }

        .activity-type.info { background: #bee3f8; color: #2b6cb0; }
        .activity-type.success { background: #c6f6d5; color: #276749; }
        .activity-type.warning { background: #fbd38d; color: #b7791f; }
        .activity-type.error { background: #fed7d7; color: #c53030; }

        .activity-message {
            flex: 1;
            color: #4a5568;
        }

        /* Enhanced Interactive Features */
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            gap: 1rem;
            flex-wrap: wrap;
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 1.5rem;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .filter-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-controls select,
        .filter-controls input {
            padding: 0.5rem 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #2d3748;
            font-size: 0.9rem;
            min-width: 120px;
        }

        .filter-controls label {
            color: #4a5568;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .timezone-info {
            color: #718096;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .activity-item {
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 0.25rem 0;
            padding: 0.75rem;
        }

        .activity-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(4px);
        }

        .activity-expand {
            margin-left: auto;
            color: #a0aec0;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .activity-item:hover .activity-expand {
            opacity: 1;
        }

        .activity-details {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(102, 126, 234, 0.02);
            border-radius: 8px;
            border-left: 4px solid #667eea;
            animation: slideDown 0.3s ease;
        }

        .activity-details.expanded {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .detail-section {
            margin-bottom: 1rem;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .detail-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        .detail-content {
            font-size: 0.9rem;
            color: #4a5568;
            line-height: 1.4;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.75rem;
        }

        .detail-item {
            background: white;
            padding: 0.75rem;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .detail-item strong {
            color: #2d3748;
            display: block;
            margin-bottom: 0.25rem;
        }

        .metric-row,
        .activity-item {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .metric-row:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 6px;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            font-size: 0.8rem;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            max-width: 300px;
            line-height: 1.3;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .tooltip.visible {
            opacity: 1;
        }

        .refresh-indicator {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .refresh-indicator:hover {
            transform: scale(1.05);
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .tenant-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .tenant-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .tenant-item:last-child {
            border-bottom: none;
        }

        .tenant-id {
            font-weight: 500;
            color: #2d3748;
        }

        .tenant-stats {
            font-size: 0.8rem;
            color: #718096;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #718096;
        }

        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(72, 187, 120, 0.9);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .refresh-indicator.show {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                padding: 1rem;
            }
            
            .header .last-updated {
                float: none;
                margin-top: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 TKC_v5 Agent Platform</h1>
        <div class="subtitle">Real-time Monitoring Dashboard</div>
        <div class="last-updated" id="lastUpdated">Loading...</div>
    </div>

    <div class="container">
        <div id="errorContainer"></div>

        <!-- Enhanced Controls -->
        <div class="controls">
            <div class="filter-controls">
                <label for="activityFilter">Filter:</label>
                <select id="activityFilter">
                    <option value="all">All Activities</option>
                    <option value="agent_interaction">Agent Interactions</option>
                    <option value="email_processing">Email Processing</option>
                    <option value="tool_usage">Tool Usage</option>
                    <option value="tenant_activity">Tenant Activity</option>
                    <option value="vector_operations">Vector Operations</option>
                    <option value="rate_limiting">Rate Limiting</option>
                    <option value="performance">Performance</option>
                    <option value="error">Errors</option>
                </select>

                <label for="timeRange">Time Range:</label>
                <select id="timeRange">
                    <option value="1h">Last Hour</option>
                    <option value="6h">Last 6 Hours</option>
                    <option value="24h" selected>Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                </select>

                <label for="searchFilter">Search:</label>
                <input type="text" id="searchFilter" placeholder="Search activities..." />
            </div>

            <div class="timezone-info">
                🕐 Denver Time (MST/MDT)
            </div>
        </div>

        <div class="grid" id="dashboardGrid">
            <!-- Agent Status Panel -->
            <div class="card">
                <h2>🤖 Agent Status</h2>
                <div id="agentStatus">
                    <div class="loading">Loading agent status...</div>
                </div>
            </div>

            <!-- Infrastructure Monitoring -->
            <div class="card">
                <h2>🏗️ Infrastructure</h2>
                <div id="infrastructureStatus">
                    <div class="loading">Loading infrastructure status...</div>
                </div>
            </div>

            <!-- Multi-Tenant Overview -->
            <div class="card">
                <h2>👥 Multi-Tenant Overview</h2>
                <div id="tenantOverview">
                    <div class="loading">Loading tenant data...</div>
                </div>
            </div>

            <!-- Recent Activity Feed -->
            <div class="card activity-feed">
                <h2>📊 Recent Activity</h2>
                <div id="activityFeed">
                    <div class="loading">Loading recent activity...</div>
                </div>
            </div>
        </div>
    </div>

    <div class="refresh-indicator" id="refreshIndicator">
        🔄 Refreshing...
    </div>

    <script>
        class MonitoringDashboard {
            constructor() {
                this.refreshInterval = {{ refresh_interval }} * 1000; // Convert to milliseconds
                this.isLoading = false;
                this.init();
            }

            init() {
                this.loadData();
                setInterval(() => this.loadData(), this.refreshInterval);
            }

            async loadData() {
                if (this.isLoading) return;
                
                this.isLoading = true;
                this.showRefreshIndicator();

                try {
                    const response = await fetch('/api/status');
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    this.updateDashboard(data);
                    this.clearError();
                    
                } catch (error) {
                    console.error('Failed to load dashboard data:', error);
                    this.showError(`Failed to load data: ${error.message}`);
                } finally {
                    this.isLoading = false;
                    this.hideRefreshIndicator();
                }
            }

            updateDashboard(data) {
                this.updateLastUpdated(data.timestamp);
                this.updateAgentStatus(data.agents);
                this.updateInfrastructureStatus(data.infrastructure);
                this.updateTenantOverview(data.tenants);
                this.updateActivityFeed(data.activity);
            }

            updateLastUpdated(timestamp) {
                const date = new Date(timestamp);
                document.getElementById('lastUpdated').textContent = 
                    `Last updated: ${date.toLocaleTimeString()}`;
            }

            updateAgentStatus(agents) {
                const container = document.getElementById('agentStatus');
                container.innerHTML = '';

                Object.entries(agents).forEach(([key, agent]) => {
                    const statusClass = `status-${agent.status}`;
                    const responseTime = agent.response_time ? `${agent.response_time.toFixed(2)}s` : 'N/A';
                    
                    container.innerHTML += `
                        <div class="metric-row">
                            <div class="metric-label">
                                <span class="status-indicator ${statusClass}"></span>
                                ${agent.name}
                            </div>
                            <div class="metric-value">${responseTime}</div>
                        </div>
                    `;
                });
            }

            updateInfrastructureStatus(infrastructure) {
                const container = document.getElementById('infrastructureStatus');
                container.innerHTML = '';

                const components = [
                    {
                        name: 'Vector Database',
                        status: infrastructure.vector_db.status,
                        value: `${infrastructure.vector_db.vector_count} vectors`
                    },
                    {
                        name: 'Redis Cache',
                        status: infrastructure.redis.status,
                        value: infrastructure.redis.memory_usage || 'N/A'
                    },
                    {
                        name: 'Cloud Run',
                        status: infrastructure.cloud_run.status,
                        value: `${infrastructure.cloud_run.cpu_usage || 0}% CPU`
                    },
                    {
                        name: 'Secret Manager',
                        status: infrastructure.secret_manager.status,
                        value: 'Configured'
                    }
                ];

                components.forEach(component => {
                    const statusClass = `status-${component.status}`;
                    container.innerHTML += `
                        <div class="metric-row">
                            <div class="metric-label">
                                <span class="status-indicator ${statusClass}"></span>
                                ${component.name}
                            </div>
                            <div class="metric-value">${component.value}</div>
                        </div>
                    `;
                });
            }

            updateTenantOverview(tenants) {
                const container = document.getElementById('tenantOverview');
                
                container.innerHTML = `
                    <div class="metric-row">
                        <div class="metric-label">Active Tenants</div>
                        <div class="metric-value">${tenants.active_tenants}</div>
                    </div>
                    <div class="metric-row">
                        <div class="metric-label">Total Conversations</div>
                        <div class="metric-value">${tenants.total_conversations}</div>
                    </div>
                    <div class="metric-row">
                        <div class="metric-label">Total Vectors</div>
                        <div class="metric-value">${tenants.total_vectors}</div>
                    </div>
                `;

                if (tenants.tenants && tenants.tenants.length > 0) {
                    container.innerHTML += '<div style="margin-top: 1rem; font-weight: 600; color: #4a5568;">Recent Tenants:</div>';
                    container.innerHTML += '<div class="tenant-list">';
                    
                    tenants.tenants.forEach(tenant => {
                        container.innerHTML += `
                            <div class="tenant-item">
                                <div class="tenant-id">${tenant.id}</div>
                                <div class="tenant-stats">${tenant.conversations} conv, ${tenant.vectors} vec</div>
                            </div>
                        `;
                    });
                    
                    container.innerHTML += '</div>';
                }
            }

            updateActivityFeed(activities) {
                const container = document.getElementById('activityFeed');
                container.innerHTML = '';

                if (!activities || activities.length === 0) {
                    container.innerHTML = '<div class="loading">No recent activity</div>';
                    return;
                }

                // Apply filters
                const filteredActivities = this.filterActivities(activities);

                filteredActivities.forEach((activity, index) => {
                    const activityElement = document.createElement('div');
                    activityElement.className = 'activity-item';
                    activityElement.dataset.activityId = activity.id || `activity_${index + 1}`;

                    activityElement.innerHTML = `
                        <div class="activity-time">${activity.timestamp}</div>
                        <div class="activity-type ${activity.severity}">${activity.type.replace(/_/g, ' ').toUpperCase()}</div>
                        <div class="activity-message">${activity.message}</div>
                        <div class="activity-expand">Click for details →</div>
                        <div class="activity-details" id="details_${activity.id || `activity_${index + 1}`}">
                            <div class="loading-spinner"></div>
                            Loading detailed information...
                        </div>
                    `;

                    // Add click handler for drill-down
                    activityElement.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleActivityDetails(activity.id || `activity_${index + 1}`);
                    });

                    // Add hover tooltip
                    activityElement.addEventListener('mouseenter', (e) => {
                        this.showTooltip(e, activity);
                    });

                    activityElement.addEventListener('mouseleave', () => {
                        this.hideTooltip();
                    });

                    container.appendChild(activityElement);
                });
            }

            filterActivities(activities) {
                const typeFilter = document.getElementById('activityFilter').value;
                const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

                return activities.filter(activity => {
                    const matchesType = typeFilter === 'all' || activity.type === typeFilter;
                    const matchesSearch = !searchFilter ||
                        activity.message.toLowerCase().includes(searchFilter) ||
                        activity.type.toLowerCase().includes(searchFilter);

                    return matchesType && matchesSearch;
                });
            }

            async toggleActivityDetails(activityId) {
                const detailsElement = document.getElementById(`details_${activityId}`);

                if (detailsElement.classList.contains('expanded')) {
                    detailsElement.classList.remove('expanded');
                    return;
                }

                // Load detailed data
                try {
                    const response = await fetch(`/api/activity/${activityId}`);
                    if (response.ok) {
                        const details = await response.json();
                        this.renderActivityDetails(detailsElement, details);
                    } else {
                        detailsElement.innerHTML = '<div class="detail-content">Details not available</div>';
                    }
                } catch (error) {
                    detailsElement.innerHTML = '<div class="detail-content">Error loading details</div>';
                }

                detailsElement.classList.add('expanded');
            }

            renderActivityDetails(container, details) {
                let html = '';

                if (details.details) {
                    html += '<div class="detail-section">';
                    html += '<div class="detail-title">📊 Detailed Metrics</div>';
                    html += '<div class="detail-grid">';

                    Object.entries(details.details).forEach(([key, value]) => {
                        if (typeof value === 'object' && value !== null) {
                            html += `<div class="detail-item"><strong>${this.formatKey(key)}:</strong> ${JSON.stringify(value, null, 2)}</div>`;
                        } else {
                            html += `<div class="detail-item"><strong>${this.formatKey(key)}:</strong> ${value}</div>`;
                        }
                    });

                    html += '</div></div>';
                }

                if (details.conversation_preview) {
                    html += '<div class="detail-section">';
                    html += '<div class="detail-title">💬 Conversation Preview</div>';
                    html += '<div class="detail-content">';
                    details.conversation_preview.forEach(msg => {
                        html += `<div style="margin-bottom: 0.5rem;"><strong>${msg.role}:</strong> ${msg.content}</div>`;
                    });
                    html += '</div></div>';
                }

                if (details.sample_emails) {
                    html += '<div class="detail-section">';
                    html += '<div class="detail-title">📧 Sample Emails</div>';
                    html += '<div class="detail-grid">';
                    details.sample_emails.forEach(email => {
                        html += `
                            <div class="detail-item">
                                <strong>Subject:</strong> ${email.subject}<br>
                                <strong>From:</strong> ${email.from}<br>
                                <strong>Classification:</strong> ${email.classification}<br>
                                <strong>Confidence:</strong> ${(email.confidence * 100).toFixed(1)}%
                            </div>
                        `;
                    });
                    html += '</div></div>';
                }

                if (details.next_actions) {
                    html += '<div class="detail-section">';
                    html += '<div class="detail-title">🎯 Next Actions</div>';
                    html += '<div class="detail-content">';
                    details.next_actions.forEach(action => {
                        html += `<div>• ${action}</div>`;
                    });
                    html += '</div></div>';
                }

                container.innerHTML = html;
            }

            formatKey(key) {
                return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            }

            showTooltip(event, activity) {
                const tooltip = this.getOrCreateTooltip();

                let content = `<strong>${activity.type.replace(/_/g, ' ').toUpperCase()}</strong><br>`;
                content += `Time: ${activity.timestamp}<br>`;
                content += `Status: ${activity.severity}<br>`;

                if (activity.details) {
                    const detailCount = Object.keys(activity.details).length;
                    content += `Details: ${detailCount} metrics available<br>`;
                }

                content += '<em>Click to expand details</em>';

                tooltip.innerHTML = content;
                tooltip.style.left = event.pageX + 10 + 'px';
                tooltip.style.top = event.pageY - 10 + 'px';
                tooltip.classList.add('visible');
            }

            hideTooltip() {
                const tooltip = document.getElementById('tooltip');
                if (tooltip) {
                    tooltip.classList.remove('visible');
                }
            }

            getOrCreateTooltip() {
                let tooltip = document.getElementById('tooltip');
                if (!tooltip) {
                    tooltip = document.createElement('div');
                    tooltip.id = 'tooltip';
                    tooltip.className = 'tooltip';
                    document.body.appendChild(tooltip);
                }
                return tooltip;
            }

            showRefreshIndicator() {
                const indicator = document.getElementById('refreshIndicator');
                indicator.classList.add('show');
            }

            hideRefreshIndicator() {
                const indicator = document.getElementById('refreshIndicator');
                setTimeout(() => {
                    indicator.classList.remove('show');
                }, 1000);
            }

            showError(message) {
                const container = document.getElementById('errorContainer');
                container.innerHTML = `<div class="error">⚠️ ${message}</div>`;
            }

            clearError() {
                const container = document.getElementById('errorContainer');
                container.innerHTML = '';
            }
        }

            setupEventListeners() {
                // Filter change handlers
                document.getElementById('activityFilter').addEventListener('change', () => {
                    this.refreshData();
                });

                document.getElementById('timeRange').addEventListener('change', () => {
                    this.refreshData();
                });

                document.getElementById('searchFilter').addEventListener('input', () => {
                    // Debounce search
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(() => {
                        this.refreshData();
                    }, 300);
                });

                // Manual refresh handler
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.refresh-indicator')) {
                        this.refreshData();
                    }
                });

                // Add hover handlers for metric rows
                document.addEventListener('mouseenter', (e) => {
                    if (e.target.closest('.metric-row')) {
                        const row = e.target.closest('.metric-row');
                        const label = row.querySelector('.metric-label').textContent.trim();
                        const value = row.querySelector('.metric-value').textContent.trim();

                        this.showTooltip(e, {
                            type: 'metric',
                            message: `${label}: ${value}`,
                            details: { metric: label, value: value }
                        });
                    }
                }, true);

                document.addEventListener('mouseleave', (e) => {
                    if (e.target.closest('.metric-row')) {
                        this.hideTooltip();
                    }
                }, true);
            }
        }

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const dashboard = new MonitoringDashboard();
            dashboard.setupEventListeners();
        });
    </script>
</body>
</html>

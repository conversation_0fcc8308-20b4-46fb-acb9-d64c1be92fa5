"""
Comprehensive Test Suite for TKC_v5 Executive Agent Core

Tests the core agent functionality, tool integration, and business workflows
with mock services for isolated testing.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import json

from src.agent.core import ExecutiveAgent
from src.agent.state import Agent<PERSON>tate, TaskRequest, TaskType, create_initial_state
from src.services.monitoring_service import MonitoringService


class TestExecutiveAgentCore:
    """Test suite for Executive Agent core functionality."""
    
    @pytest.fixture
    async def mock_agent(self):
        """Create a mock Executive Agent for testing."""
        with patch('src.agent.core.get_settings') as mock_settings:
            mock_settings.return_value.google_cloud_project = 'test-project'
            mock_settings.return_value.model_name = 'gemini-2.5-flash'
            mock_settings.return_value.environment = 'test'
            
            agent = ExecutiveAgent()
            
            # Mock the LLM
            agent.llm = AsyncMock()
            agent.llm_with_tools = AsyncMock()
            
            # Mock Redis checkpointer
            agent.checkpointer = AsyncMock()
            
            return agent
    
    @pytest.fixture
    def sample_task_request(self):
        """Create a sample task request for testing."""
        return TaskRequest(
            task_type=TaskType.EMAIL_MANAGEMENT,
            description="Process recent emails and create drafts for business inquiries",
            customer_id="test_customer",
            user_email="<EMAIL>",
            context={"timezone": "America/Denver"}
        )
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, mock_agent):
        """Test that the agent initializes correctly."""
        assert mock_agent is not None
        assert hasattr(mock_agent, 'tools')
        assert len(mock_agent.tools) > 0
        assert hasattr(mock_agent, 'llm_with_tools')
    
    @pytest.mark.asyncio
    async def test_create_initial_state(self, sample_task_request):
        """Test initial state creation."""
        state = create_initial_state(sample_task_request)
        
        assert state.task_id is not None
        assert state.customer_id == "test_customer"
        assert state.task_type == TaskType.EMAIL_MANAGEMENT
        assert state.status.value == "pending"
        assert len(state.messages) == 1  # Initial human message
    
    @pytest.mark.asyncio
    async def test_agent_process_task(self, mock_agent, sample_task_request):
        """Test agent task processing workflow."""
        # Mock LLM response
        mock_response = Mock()
        mock_response.content = "I'll help you process emails and create drafts."
        mock_response.tool_calls = []
        mock_agent.llm_with_tools.invoke.return_value = mock_response
        
        # Process task
        result = await mock_agent.process_task(sample_task_request)
        
        assert result is not None
        assert result.task_id == sample_task_request.task_id
        assert result.customer_id == "test_customer"
        
        # Verify LLM was called
        mock_agent.llm_with_tools.invoke.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_tool_execution_monitoring(self, mock_agent):
        """Test that tool execution is properly monitored."""
        with patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring:
            mock_monitoring_service = AsyncMock()
            mock_monitoring.return_value = mock_monitoring_service
            
            # Mock a tool call
            mock_tool_call = Mock()
            mock_tool_call.name = "create_email_draft"
            mock_tool_call.args = {"to": "<EMAIL>", "subject": "Test", "body": "Test body"}
            
            # Execute tool (this would normally be done by the agent)
            await mock_agent._execute_tool_call(mock_tool_call, "test_customer")
            
            # Verify monitoring was called
            mock_monitoring_service.log_tool_execution.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, mock_agent, sample_task_request):
        """Test agent error handling."""
        # Mock LLM to raise an exception
        mock_agent.llm_with_tools.invoke.side_effect = Exception("Test error")
        
        with patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring:
            mock_monitoring_service = AsyncMock()
            mock_monitoring.return_value = mock_monitoring_service
            
            # Process task should handle the error gracefully
            result = await mock_agent.process_task(sample_task_request)
            
            # Verify error was logged
            mock_monitoring_service.log_error.assert_called_once()
            
            # Result should indicate failure
            assert result.status.value == "failed"
    
    @pytest.mark.asyncio
    async def test_conversation_persistence(self, mock_agent, sample_task_request):
        """Test conversation state persistence."""
        # Mock checkpointer
        mock_agent.checkpointer.aput.return_value = None
        mock_agent.checkpointer.aget.return_value = None
        
        # Process task
        await mock_agent.process_task(sample_task_request)
        
        # Verify state was saved
        mock_agent.checkpointer.aput.assert_called()
    
    @pytest.mark.asyncio
    async def test_customer_isolation(self, mock_agent):
        """Test that customer data is properly isolated."""
        customer1_request = TaskRequest(
            task_type=TaskType.EMAIL_MANAGEMENT,
            description="Customer 1 task",
            customer_id="customer_1",
            user_email="<EMAIL>"
        )
        
        customer2_request = TaskRequest(
            task_type=TaskType.EMAIL_MANAGEMENT,
            description="Customer 2 task",
            customer_id="customer_2",
            user_email="<EMAIL>"
        )
        
        # Mock LLM responses
        mock_agent.llm_with_tools.invoke.return_value = Mock(content="Response", tool_calls=[])
        
        # Process tasks for different customers
        result1 = await mock_agent.process_task(customer1_request)
        result2 = await mock_agent.process_task(customer2_request)
        
        # Verify customer isolation
        assert result1.customer_id == "customer_1"
        assert result2.customer_id == "customer_2"
        assert result1.task_id != result2.task_id


class TestAgentTools:
    """Test suite for agent tool integration."""
    
    @pytest.mark.asyncio
    async def test_gmail_tools_integration(self):
        """Test Gmail tools integration."""
        from src.tools.gmail_tools import create_email_draft
        
        # Mock Gmail client
        with patch('src.services.gmail_client.create_gmail_client') as mock_gmail:
            mock_client = AsyncMock()
            mock_gmail.return_value = mock_client
            mock_client.create_draft.return_value = "draft_123"
            
            # Test email draft creation
            result = await create_email_draft(
                to="<EMAIL>",
                subject="Test Subject",
                body="Test body",
                customer_id="test_customer"
            )
            
            assert "draft_123" in result
            mock_client.create_draft.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_calendar_tools_integration(self):
        """Test Calendar tools integration."""
        from src.tools.calendar_tools import check_availability
        
        # Mock Calendar client
        with patch('src.services.calendar_client.get_calendar_client') as mock_calendar:
            mock_client = AsyncMock()
            mock_calendar.return_value = mock_client
            mock_client.check_availability.return_value = [
                {"start": "09:00", "end": "10:00"},
                {"start": "14:00", "end": "15:00"}
            ]
            
            # Test availability check
            result = await check_availability(
                date="2025-01-28",
                start_time="09:00",
                end_time="17:00",
                customer_id="test_customer"
            )
            
            assert "Available slots" in result
            mock_client.check_availability.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_crm_tools_integration(self):
        """Test CRM tools integration."""
        from src.tools.crm_pipeline_tools import create_lead
        
        # Mock CRM client
        with patch('src.services.crm_client.get_crm_client') as mock_crm:
            mock_client = AsyncMock()
            mock_crm.return_value = mock_client
            mock_client.create_contact.return_value = "contact_123"
            mock_client.update_contact.return_value = True
            mock_client.log_activity.return_value = True
            
            # Test lead creation
            from src.tools.crm_pipeline_tools import LeadRequest
            lead_request = LeadRequest(
                email="<EMAIL>",
                name="Test Lead",
                company="Test Company",
                lead_source="website"
            )
            
            result = await create_lead(lead_request)
            
            assert "Lead created successfully" in result
            mock_client.create_contact.assert_called_once()


class TestMonitoringIntegration:
    """Test suite for monitoring and observability."""
    
    @pytest.mark.asyncio
    async def test_monitoring_service_initialization(self):
        """Test monitoring service initialization."""
        with patch('google.cloud.monitoring_v3.MetricServiceClient'):
            with patch('google.cloud.logging.Client'):
                with patch('google.cloud.error_reporting.Client'):
                    monitoring_service = MonitoringService()
                    assert monitoring_service is not None
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self):
        """Test performance metrics tracking."""
        with patch('google.cloud.monitoring_v3.MetricServiceClient'):
            with patch('google.cloud.logging.Client'):
                with patch('google.cloud.error_reporting.Client'):
                    monitoring_service = MonitoringService()
                    
                    # Test agent interaction logging
                    await monitoring_service.log_agent_interaction(
                        customer_id="test_customer",
                        interaction_type="email_processing",
                        details={"emails_processed": 5},
                        duration_ms=1500.0
                    )
                    
                    # Test tool execution logging
                    await monitoring_service.log_tool_execution(
                        tool_name="create_email_draft",
                        customer_id="test_customer",
                        success=True,
                        duration_ms=500.0
                    )
                    
                    # Verify metrics are tracked
                    summary = await monitoring_service.get_performance_summary()
                    assert "metrics" in summary
    
    @pytest.mark.asyncio
    async def test_error_reporting(self):
        """Test error reporting functionality."""
        with patch('google.cloud.monitoring_v3.MetricServiceClient'):
            with patch('google.cloud.logging.Client'):
                with patch('google.cloud.error_reporting.Client') as mock_error_client:
                    mock_error_client.return_value.report_exception = Mock()
                    
                    monitoring_service = MonitoringService()
                    
                    # Test error logging
                    test_error = ValueError("Test error")
                    await monitoring_service.log_error(
                        error=test_error,
                        context={"operation": "test_operation"},
                        customer_id="test_customer"
                    )
                    
                    # Verify error was reported
                    mock_error_client.return_value.report_exception.assert_called_once()


class TestDataPersistence:
    """Test suite for data persistence and multi-tenancy."""
    
    @pytest.mark.asyncio
    async def test_tenant_isolation(self):
        """Test tenant data isolation."""
        from src.services.pinecone_tenant_manager import PineconeTenantManager
        
        with patch('pinecone.Pinecone'):
            tenant_manager = PineconeTenantManager()
            
            # Test namespace generation
            namespace1 = tenant_manager.generate_tenant_namespace("customer_1")
            namespace2 = tenant_manager.generate_tenant_namespace("customer_2")
            
            # Verify namespaces are different and consistent
            assert namespace1 != namespace2
            assert namespace1 == tenant_manager.generate_tenant_namespace("customer_1")
    
    @pytest.mark.asyncio
    async def test_conversation_storage(self):
        """Test conversation data storage."""
        from src.services.data_persistence_service import DataPersistenceService
        
        with patch('google.cloud.firestore.Client'):
            with patch('redis.asyncio.from_url'):
                persistence_service = DataPersistenceService()
                
                # Mock conversation data
                conversation_data = {
                    "id": "conv_123",
                    "participants": ["<EMAIL>"],
                    "subject": "Test Conversation",
                    "messages": [
                        {"role": "user", "content": "Hello"},
                        {"role": "assistant", "content": "Hi there!"}
                    ]
                }
                
                # Test conversation storage
                result = await persistence_service.store_conversation(
                    customer_id="test_customer",
                    conversation_data=conversation_data
                )
                
                assert result == "conv_123"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

"""
Integration Tests for TKC_v5 Executive Agent

Tests end-to-end workflows and service integrations with real or realistic
service interactions for comprehensive system validation.
"""

import pytest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import json

from src.agent.core import ExecutiveAgent
from src.agent.state import TaskRequest, TaskType
from src.api.health_endpoints import health_check, get_metrics


class TestEndToEndWorkflows:
    """Test complete business workflows end-to-end."""
    
    @pytest.mark.asyncio
    async def test_email_processing_workflow(self):
        """Test complete email processing workflow."""
        # Mock all external services
        with patch('src.services.gmail_client.create_gmail_client') as mock_gmail, \
             patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring, \
             patch('src.services.redis_checkpointer.get_redis_checkpointer') as mock_redis:
            
            # Setup mocks
            mock_gmail_client = AsyncMock()
            mock_gmail.return_value = mock_gmail_client
            mock_gmail_client.get_recent_emails.return_value = [
                {
                    "id": "email_1",
                    "from": "<EMAIL>",
                    "subject": "Project Inquiry",
                    "body": "I'm interested in your AI automation services.",
                    "thread_id": "thread_1"
                }
            ]
            mock_gmail_client.create_draft.return_value = "draft_123"
            
            mock_monitoring_service = AsyncMock()
            mock_monitoring.return_value = mock_monitoring_service
            
            mock_redis_checkpointer = AsyncMock()
            mock_redis.return_value = mock_redis_checkpointer
            
            # Create agent and process email task
            agent = ExecutiveAgent()
            agent.llm_with_tools = AsyncMock()
            
            # Mock LLM response with tool calls
            mock_response = Mock()
            mock_response.content = "I'll process the recent emails and create drafts."
            mock_response.tool_calls = [
                Mock(
                    name="get_recent_emails",
                    args={"limit": 10, "customer_id": "test_customer"}
                ),
                Mock(
                    name="create_email_draft",
                    args={
                        "to": "<EMAIL>",
                        "subject": "Re: Project Inquiry",
                        "body": "Thank you for your interest in our AI automation services...",
                        "customer_id": "test_customer"
                    }
                )
            ]
            agent.llm_with_tools.invoke.return_value = mock_response
            
            # Create task request
            task_request = TaskRequest(
                task_type=TaskType.EMAIL_MANAGEMENT,
                description="Process recent emails and create drafts for business inquiries",
                customer_id="test_customer",
                user_email="<EMAIL>"
            )
            
            # Process task
            result = await agent.process_task(task_request)
            
            # Verify workflow completion
            assert result is not None
            assert result.customer_id == "test_customer"
            
            # Verify services were called
            mock_gmail_client.get_recent_emails.assert_called()
            mock_gmail_client.create_draft.assert_called()
            mock_monitoring_service.log_agent_interaction.assert_called()
    
    @pytest.mark.asyncio
    async def test_meeting_scheduling_workflow(self):
        """Test complete meeting scheduling workflow."""
        with patch('src.services.calendar_client.get_calendar_client') as mock_calendar, \
             patch('src.services.gmail_client.create_gmail_client') as mock_gmail, \
             patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring:
            
            # Setup mocks
            mock_calendar_client = AsyncMock()
            mock_calendar.return_value = mock_calendar_client
            mock_calendar_client.check_availability.return_value = [
                {"start": "14:00", "end": "15:00", "date": "2025-01-28"}
            ]
            mock_calendar_client.create_event.return_value = "event_123"
            
            mock_gmail_client = AsyncMock()
            mock_gmail.return_value = mock_gmail_client
            mock_gmail_client.create_draft.return_value = "draft_456"
            
            mock_monitoring_service = AsyncMock()
            mock_monitoring.return_value = mock_monitoring_service
            
            # Create agent
            agent = ExecutiveAgent()
            agent.llm_with_tools = AsyncMock()
            
            # Mock LLM response for meeting scheduling
            mock_response = Mock()
            mock_response.content = "I'll schedule the meeting for you."
            mock_response.tool_calls = [
                Mock(
                    name="check_availability",
                    args={
                        "date": "2025-01-28",
                        "start_time": "14:00",
                        "end_time": "15:00",
                        "customer_id": "test_customer"
                    }
                ),
                Mock(
                    name="schedule_meeting",
                    args={
                        "title": "Strategy Discussion",
                        "start_time": "2025-01-28T14:00:00",
                        "end_time": "2025-01-28T15:00:00",
                        "attendees": ["<EMAIL>"],
                        "customer_id": "test_customer"
                    }
                )
            ]
            agent.llm_with_tools.invoke.return_value = mock_response
            
            # Create task request
            task_request = TaskRequest(
                task_type=TaskType.CALENDAR_MANAGEMENT,
                description="Schedule a <NAME_EMAIL> for tomorrow at 2 PM",
                customer_id="test_customer",
                user_email="<EMAIL>"
            )
            
            # Process task
            result = await agent.process_task(task_request)
            
            # Verify workflow completion
            assert result is not None
            mock_calendar_client.check_availability.assert_called()
            mock_calendar_client.create_event.assert_called()
    
    @pytest.mark.asyncio
    async def test_crm_lead_management_workflow(self):
        """Test complete CRM lead management workflow."""
        with patch('src.services.crm_client.get_crm_client') as mock_crm, \
             patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring:
            
            # Setup mocks
            mock_crm_client = AsyncMock()
            mock_crm.return_value = mock_crm_client
            mock_crm_client.create_contact.return_value = "contact_123"
            mock_crm_client.update_contact.return_value = True
            mock_crm_client.log_activity.return_value = True
            mock_crm_client.create_deal.return_value = "deal_456"
            
            mock_monitoring_service = AsyncMock()
            mock_monitoring.return_value = mock_monitoring_service
            
            # Create agent
            agent = ExecutiveAgent()
            agent.llm_with_tools = AsyncMock()
            
            # Mock LLM response for CRM operations
            mock_response = Mock()
            mock_response.content = "I'll create the lead and deal in the CRM."
            mock_response.tool_calls = [
                Mock(
                    name="create_lead",
                    args={
                        "email": "<EMAIL>",
                        "name": "John Prospect",
                        "company": "Prospect Corp",
                        "lead_source": "website"
                    }
                ),
                Mock(
                    name="create_deal",
                    args={
                        "deal_name": "Prospect Corp - AI Implementation",
                        "contact_email": "<EMAIL>",
                        "amount": 50000,
                        "stage": "qualification"
                    }
                )
            ]
            agent.llm_with_tools.invoke.return_value = mock_response
            
            # Create task request
            task_request = TaskRequest(
                task_type=TaskType.CRM_MANAGEMENT,
                description="Create a lead for John Prospect from Prospect Corp and set up a deal",
                customer_id="test_customer",
                user_email="<EMAIL>"
            )
            
            # Process task
            result = await agent.process_task(task_request)
            
            # Verify workflow completion
            assert result is not None
            mock_crm_client.create_contact.assert_called()
            mock_crm_client.create_deal.assert_called()


class TestServiceIntegration:
    """Test integration between different services."""
    
    @pytest.mark.asyncio
    async def test_monitoring_integration(self):
        """Test monitoring service integration."""
        from src.services.monitoring_service import monitor_performance
        
        # Create a test function with monitoring decorator
        @monitor_performance
        async def test_function(customer_id: str):
            await asyncio.sleep(0.1)  # Simulate work
            return "success"
        
        with patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring:
            mock_monitoring_service = AsyncMock()
            mock_monitoring.return_value = mock_monitoring_service
            
            # Call monitored function
            result = await test_function(customer_id="test_customer")
            
            # Verify monitoring was called
            assert result == "success"
            mock_monitoring_service.log_tool_execution.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_data_persistence_integration(self):
        """Test data persistence across services."""
        from src.services.data_persistence_service import DataPersistenceService
        
        with patch('google.cloud.firestore.Client') as mock_firestore, \
             patch('redis.asyncio.from_url') as mock_redis, \
             patch('src.services.pinecone_tenant_manager.get_pinecone_tenant_manager') as mock_pinecone:
            
            # Setup mocks
            mock_firestore_client = Mock()
            mock_firestore.return_value = mock_firestore_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            mock_pinecone_manager = AsyncMock()
            mock_pinecone.return_value = mock_pinecone_manager
            
            # Create persistence service
            persistence_service = DataPersistenceService()
            persistence_service.firestore_client = mock_firestore_client
            persistence_service.redis_client = mock_redis_client
            persistence_service.pinecone_manager = mock_pinecone_manager
            
            # Test conversation storage
            conversation_data = {
                "id": "conv_123",
                "participants": ["<EMAIL>"],
                "messages": [{"role": "user", "content": "Hello"}],
                "vectors": [{"values": [0.1] * 1536, "metadata": {"text": "Hello"}}]
            }
            
            result = await persistence_service.store_conversation(
                customer_id="test_customer",
                conversation_data=conversation_data
            )
            
            # Verify all storage layers were used
            assert result == "conv_123"
            mock_pinecone_manager.store_conversation_vectors.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_health_check_integration(self):
        """Test health check endpoint integration."""
        with patch('src.services.redis_checkpointer.get_redis_checkpointer') as mock_redis, \
             patch('src.services.pinecone_tenant_manager.get_pinecone_tenant_manager') as mock_pinecone, \
             patch('src.services.data_persistence_service.get_data_persistence_service') as mock_persistence, \
             patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring:
            
            # Setup healthy mocks
            mock_redis_checkpointer = AsyncMock()
            mock_redis_checkpointer.redis_client.ping.return_value = True
            mock_redis.return_value = mock_redis_checkpointer
            
            mock_pinecone_manager = AsyncMock()
            mock_pinecone_manager.index = Mock()  # Not None = healthy
            mock_pinecone.return_value = mock_pinecone_manager
            
            mock_persistence_service = AsyncMock()
            mock_persistence_service.firestore_client = Mock()  # Not None = healthy
            mock_persistence.return_value = mock_persistence_service
            
            mock_monitoring_service = AsyncMock()
            mock_monitoring_service.metrics_client = Mock()  # Not None = healthy
            mock_monitoring.return_value = mock_monitoring_service
            
            # Call health check
            health_response = await health_check()
            
            # Verify health check response
            assert health_response.status == "healthy"
            assert "redis" in health_response.services
            assert "pinecone" in health_response.services
            assert health_response.services["redis"] == "healthy"
            assert health_response.services["pinecone"] == "healthy"


class TestErrorHandlingAndResilience:
    """Test error handling and system resilience."""
    
    @pytest.mark.asyncio
    async def test_service_failure_resilience(self):
        """Test system resilience when services fail."""
        with patch('src.services.gmail_client.create_gmail_client') as mock_gmail, \
             patch('src.services.monitoring_service.get_monitoring_service') as mock_monitoring:
            
            # Setup Gmail to fail
            mock_gmail.side_effect = Exception("Gmail service unavailable")
            
            mock_monitoring_service = AsyncMock()
            mock_monitoring.return_value = mock_monitoring_service
            
            # Create agent
            agent = ExecutiveAgent()
            agent.llm_with_tools = AsyncMock()
            
            # Mock LLM response that tries to use Gmail
            mock_response = Mock()
            mock_response.content = "I'll try to process emails."
            mock_response.tool_calls = [
                Mock(
                    name="get_recent_emails",
                    args={"limit": 10, "customer_id": "test_customer"}
                )
            ]
            agent.llm_with_tools.invoke.return_value = mock_response
            
            # Create task request
            task_request = TaskRequest(
                task_type=TaskType.EMAIL_MANAGEMENT,
                description="Process recent emails",
                customer_id="test_customer",
                user_email="<EMAIL>"
            )
            
            # Process task - should handle failure gracefully
            result = await agent.process_task(task_request)
            
            # Verify error was handled and logged
            assert result is not None
            mock_monitoring_service.log_error.assert_called()
    
    @pytest.mark.asyncio
    async def test_partial_service_degradation(self):
        """Test system behavior with partial service degradation."""
        # Test health check with some services down
        with patch('src.api.health_endpoints.check_redis_health') as mock_redis_check, \
             patch('src.api.health_endpoints.check_pinecone_health') as mock_pinecone_check, \
             patch('src.api.health_endpoints.check_data_persistence_health') as mock_persistence_check, \
             patch('src.api.health_endpoints.check_monitoring_health') as mock_monitoring_check:
            
            # Redis is down, others are healthy
            mock_redis_check.return_value = False
            mock_pinecone_check.return_value = True
            mock_persistence_check.return_value = True
            mock_monitoring_check.return_value = True
            
            # Call health check
            health_response = await health_check()
            
            # Verify partial degradation is reported
            assert health_response.status == "unhealthy"  # Overall unhealthy due to Redis
            assert health_response.services["redis"] == "unhealthy"
            assert health_response.services["pinecone"] == "healthy"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

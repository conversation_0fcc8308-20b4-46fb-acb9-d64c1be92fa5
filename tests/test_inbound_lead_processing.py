"""
Test Suite for Inbound Lead Processing System

Comprehensive tests for form submission processing, lead enrichment,
CRM integration, and automated follow-up workflows.
"""

import pytest
import asyncio
import json
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from src.webhooks.form_handler import (
    FormSubmission, 
    InboundLeadProcessor, 
    LeadProcessingResult,
    get_lead_processor
)
from src.services.lead_enrichment_service import LeadEnrichmentService
from src.services.lead_notification_service import LeadNotificationService


class TestFormSubmission:
    """Test FormSubmission model validation and processing."""
    
    def test_form_submission_validation(self):
        """Test form submission data validation."""
        # Valid submission
        submission = FormSubmission(
            form_type="demo_request",
            email="<EMAIL>",
            name="Test User",
            company="Test Company",
            phone="******-0123",
            message="Interested in a demo",
            source="website"
        )
        
        assert submission.form_type == "demo_request"
        assert submission.email == "<EMAIL>"
        assert submission.name == "Test User"
        assert submission.company == "Test Company"
        
    def test_form_submission_defaults(self):
        """Test default values for optional fields."""
        submission = FormSubmission(
            form_type="contact",
            email="<EMAIL>",
            name="Test User"
        )
        
        assert submission.company == ""
        assert submission.phone == ""
        assert submission.message == ""
        assert submission.source == "website"
        assert submission.utm_campaign == ""
        assert submission.metadata == {}
        
    def test_form_submission_timestamp(self):
        """Test automatic timestamp generation."""
        submission = FormSubmission(
            form_type="contact",
            email="<EMAIL>",
            name="Test User"
        )
        
        # Should have a valid ISO timestamp
        timestamp = datetime.fromisoformat(submission.timestamp.replace('Z', '+00:00'))
        assert isinstance(timestamp, datetime)


class TestInboundLeadProcessor:
    """Test InboundLeadProcessor functionality."""
    
    @pytest.fixture
    def mock_executive_agent(self):
        """Mock Executive Agent for testing."""
        agent = Mock()
        agent.execute_tool = AsyncMock()
        return agent
    
    @pytest.fixture
    def sample_submission(self):
        """Sample form submission for testing."""
        return FormSubmission(
            form_type="demo_request",
            email="<EMAIL>",
            name="John Prospect",
            company="Prospect Corp",
            phone="******-0123",
            message="Interested in a demo of your platform",
            source="website",
            utm_campaign="q4-demo-campaign",
            utm_source="google",
            utm_medium="cpc",
            page_url="https://tkcgroup.co/demo"
        )
    
    @pytest.fixture
    async def processor(self, mock_executive_agent):
        """Create processor instance with mocked dependencies."""
        processor = InboundLeadProcessor(mock_executive_agent)
        
        # Mock service dependencies
        processor.redis_client = Mock()
        processor.redis_client.set = AsyncMock()
        processor.redis_client.lpush = AsyncMock()
        
        processor.crm_client = Mock()
        processor.crm_client.get_contact_by_email = AsyncMock(return_value=None)
        processor.crm_client.create_contact = AsyncMock(return_value="contact_123")
        processor.crm_client.create_deal = AsyncMock(return_value="deal_456")
        
        return processor
    
    async def test_normalize_submission_data(self, processor, sample_submission):
        """Test form data normalization."""
        normalized = await processor._normalize_submission_data(sample_submission)
        
        assert normalized['email'] == "<EMAIL>"
        assert normalized['first_name'] == "John"
        assert normalized['last_name'] == "Prospect"
        assert normalized['full_name'] == "John Prospect"
        assert normalized['company'] == "Prospect Corp"
        assert normalized['lead_source'] == "google"
        assert normalized['form_type'] == "demo_request"
        
    async def test_calculate_lead_score(self, processor, sample_submission):
        """Test lead scoring algorithm."""
        score = await processor._calculate_lead_score(sample_submission, None)
        
        # Demo request (50) + company (20) + phone (15) + message length (15) + UTM (10) = 110, capped at 100
        assert score == 100
        
    async def test_calculate_lead_score_existing_contact(self, processor, sample_submission):
        """Test lead scoring with existing contact."""
        existing_contact = {"id": "123", "email": "<EMAIL>"}
        score = await processor._calculate_lead_score(sample_submission, existing_contact)
        
        # Should include existing contact bonus
        assert score == 100  # Capped at 100
        
    async def test_determine_lead_priority(self, processor):
        """Test lead priority determination."""
        # High priority - demo request
        priority = await processor._determine_lead_priority(
            FormSubmission(form_type="demo_request", email="<EMAIL>", name="Test"),
            75
        )
        assert priority == "high"
        
        # High priority - high score
        priority = await processor._determine_lead_priority(
            FormSubmission(form_type="contact", email="<EMAIL>", name="Test"),
            75
        )
        assert priority == "high"
        
        # Medium priority
        priority = await processor._determine_lead_priority(
            FormSubmission(form_type="contact", email="<EMAIL>", name="Test"),
            55
        )
        assert priority == "medium"
        
        # Low priority
        priority = await processor._determine_lead_priority(
            FormSubmission(form_type="lead_capture", email="<EMAIL>", name="Test"),
            30
        )
        assert priority == "low"
    
    async def test_sync_to_crm_new_contact(self, processor, sample_submission):
        """Test CRM sync for new contact."""
        enrichment_data = {"lead_score": 85, "priority": "high"}
        
        result = await processor._sync_to_crm(sample_submission, enrichment_data, None)
        
        # Should create contact
        processor.crm_client.create_contact.assert_called_once()
        
        # Should create deal for high priority
        processor.crm_client.create_deal.assert_called_once()
        
        assert result["contact_id"] == "contact_123"
        assert result["deal_id"] == "deal_456"
        assert result["updated_existing"] is False
    
    async def test_sync_to_crm_existing_contact(self, processor, sample_submission):
        """Test CRM sync for existing contact."""
        existing_contact = {"id": "existing_123"}
        enrichment_data = {"lead_score": 45, "priority": "medium"}
        
        processor.crm_client.update_contact = AsyncMock()
        
        result = await processor._sync_to_crm(sample_submission, enrichment_data, existing_contact)
        
        # Should update existing contact
        processor.crm_client.update_contact.assert_called_once()
        
        # Should not create deal for medium priority
        processor.crm_client.create_deal.assert_not_called()
        
        assert result["contact_id"] == "existing_123"
        assert result["deal_id"] is None
        assert result["updated_existing"] is True
    
    async def test_schedule_follow_up(self, processor, sample_submission):
        """Test follow-up scheduling."""
        crm_result = {"contact_id": "contact_123"}
        
        # Mock agent tool execution
        processor.executive_agent.execute_tool.return_value = "Email sequence created successfully"
        
        result = await processor._schedule_follow_up(sample_submission, crm_result)
        
        # Should call email sequence tool
        processor.executive_agent.execute_tool.assert_called_once_with(
            "create_email_sequence",
            recipient_email="<EMAIL>",
            sequence_type="immediate_response",
            lead_name="John Prospect",
            company_name="Prospect Corp",
            delay_days="0,1,3",
            custom_content="Thank you for your demo_request submission. Message: Interested in a demo of your platform"
        )
        
        assert result["scheduled"] is True
        assert "strategy" in result
        assert result["strategy"]["type"] == "immediate_response"
    
    async def test_store_lead_record(self, processor, sample_submission):
        """Test lead record storage."""
        enrichment_data = {"lead_score": 85}
        crm_result = {"contact_id": "contact_123"}
        
        lead_id = await processor._store_lead_record(sample_submission, enrichment_data, crm_result)
        
        # Should generate lead ID
        assert lead_id.startswith("lead_prospect@company.com_")
        
        # Should store in Redis
        processor.redis_client.set.assert_called_once()
        processor.redis_client.lpush.assert_called_once_with("lead_processing_queue", lead_id)
    
    async def test_notify_agents(self, processor, sample_submission):
        """Test agent notifications."""
        crm_result = {"contact_id": "contact_123", "deal_id": "deal_456"}
        lead_id = "lead_123"
        
        await processor._notify_agents(sample_submission, crm_result, lead_id)
        
        # Should notify both agents
        assert processor.redis_client.lpush.call_count == 2
        
        # Check notification content
        calls = processor.redis_client.lpush.call_args_list
        executive_call = calls[0]
        sales_call = calls[1]
        
        assert executive_call[0][0] == "executive_agent_notifications"
        assert sales_call[0][0] == "sales_dev_agent_notifications"
    
    async def test_process_form_submission_success(self, processor, sample_submission):
        """Test complete form submission processing."""
        # Mock enrichment
        with patch.object(processor, '_enrich_lead_data', return_value={"lead_score": 85, "priority": "high"}):
            result = await processor.process_form_submission(sample_submission)
        
        assert result.success is True
        assert result.lead_id.startswith("lead_prospect@company.com_")
        assert result.crm_contact_id == "contact_123"
        assert result.crm_deal_id == "deal_456"
        assert result.follow_up_scheduled is True
    
    async def test_process_form_submission_failure(self, processor, sample_submission):
        """Test form submission processing with failure."""
        # Mock CRM failure
        processor.crm_client.create_contact.side_effect = Exception("CRM API Error")
        
        result = await processor.process_form_submission(sample_submission)
        
        assert result.success is False
        assert "CRM API Error" in result.error_message


class TestLeadEnrichmentService:
    """Test Lead Enrichment Service functionality."""
    
    @pytest.fixture
    def enrichment_service(self):
        """Create enrichment service instance."""
        return LeadEnrichmentService()
    
    @pytest.fixture
    def sample_lead_data(self):
        """Sample lead data for testing."""
        return {
            "email": "<EMAIL>",
            "name": "John Prospect",
            "company": "Prospect Corp",
            "form_type": "demo_request",
            "message": "Interested in a demo of your platform"
        }
    
    def test_extract_domain_from_email(self, enrichment_service):
        """Test domain extraction from email."""
        domain = enrichment_service._extract_domain_from_email("<EMAIL>")
        assert domain == "example.com"
        
        domain = enrichment_service._extract_domain_from_email("invalid-email")
        assert domain == ""
    
    async def test_analyze_intent_signals(self, enrichment_service, sample_lead_data):
        """Test intent signal analysis."""
        enrichment_data = {"company_size": 150, "technologies": ["salesforce", "hubspot"]}
        
        signals = await enrichment_service._analyze_intent_signals(sample_lead_data, enrichment_data)
        
        assert "high_intent_form" in signals  # demo_request
        assert "high_intent_keywords" in signals  # "demo" in message
        assert "enterprise_company" in signals  # company_size > 100
        assert "uses_crm" in signals  # salesforce in tech stack
    
    async def test_calculate_icp_fit(self, enrichment_service, sample_lead_data):
        """Test ICP fit calculation."""
        enrichment_data = {
            "company_size": 75,  # 20 points
            "industry": "technology",  # 25 points
            "annual_revenue": 2000000,  # 20 points
            "technologies": ["wordpress", "google-analytics", "salesforce", "hubspot", "slack", "zoom"],  # 15 points
            "location": "United States"  # 10 points
        }
        
        score = await enrichment_service._calculate_icp_fit(sample_lead_data, enrichment_data)
        assert score == 90  # 20 + 25 + 20 + 15 + 10
    
    async def test_calculate_enriched_score(self, enrichment_service, sample_lead_data):
        """Test enriched lead score calculation."""
        sample_lead_data["lead_score"] = 60
        enrichment_data = {
            "icp_fit_score": 80,
            "intent_signals": ["high_intent_form", "high_intent_keywords"]
        }
        
        score = await enrichment_service._calculate_enriched_score(sample_lead_data, enrichment_data)
        # 60 + (80 * 0.3) + (2 * 5) = 60 + 24 + 10 = 94
        assert score == 94
    
    async def test_enrich_lead_complete_workflow(self, enrichment_service, sample_lead_data):
        """Test complete lead enrichment workflow."""
        # Mock external API calls
        with patch.object(enrichment_service, '_enrich_contact_info', return_value={"email_verified": True}), \
             patch.object(enrichment_service, '_enrich_company_info', return_value={"company_size": 100}), \
             patch.object(enrichment_service, '_enrich_technology_stack', return_value={"technologies": ["salesforce"]}):
            
            result = await enrichment_service.enrich_lead(sample_lead_data)
            
            assert "email_verified" in result
            assert "company_size" in result
            assert "technologies" in result
            assert "enriched_lead_score" in result
            assert "ai_insights" in result


class TestLeadNotificationService:
    """Test Lead Notification Service functionality."""
    
    @pytest.fixture
    def notification_service(self):
        """Create notification service instance."""
        return LeadNotificationService("test-project")
    
    def test_notification_service_initialization(self, notification_service):
        """Test service initialization."""
        assert notification_service.project_id == "test-project"
        assert "form-submissions" in notification_service.topics['form_submissions']
        assert "executive-agent-notifications" in notification_service.subscriptions['executive_agent']
    
    async def test_publish_form_submission(self, notification_service):
        """Test form submission event publishing."""
        submission_data = {
            "form_type": "demo_request",
            "email": "<EMAIL>",
            "priority": "high"
        }
        
        # Mock publisher
        notification_service.publisher = Mock()
        notification_service.publisher.publish = Mock()
        notification_service.publisher.publish.return_value.result = Mock(return_value="msg_123")
        
        message_id = await notification_service.publish_form_submission(submission_data)
        
        assert message_id == "msg_123"
        notification_service.publisher.publish.assert_called_once()
    
    async def test_notify_agents(self, notification_service):
        """Test agent notification."""
        notification_data = {
            "type": "new_lead",
            "lead_id": "lead_123",
            "priority": "high"
        }
        
        # Mock publisher
        notification_service.publisher = Mock()
        notification_service.publisher.publish = Mock()
        notification_service.publisher.publish.return_value.result = Mock(return_value="msg_456")
        
        message_ids = await notification_service.notify_agents(notification_data)
        
        assert len(message_ids) == 1
        assert message_ids[0] == "msg_456"


class TestIntegrationWorkflow:
    """Integration tests for complete workflow."""
    
    @pytest.fixture
    def complete_submission(self):
        """Complete form submission for integration testing."""
        return {
            "form_type": "demo_request",
            "email": "<EMAIL>",
            "name": "Integration Test",
            "company": "Test Corp",
            "phone": "******-0123",
            "message": "This is an integration test for demo request",
            "source": "website",
            "utm_campaign": "test-campaign",
            "utm_source": "google",
            "utm_medium": "cpc",
            "page_url": "https://tkcgroup.co/demo",
            "metadata": {"test": True}
        }
    
    async def test_webhook_endpoint_integration(self, complete_submission):
        """Test webhook endpoint with complete submission."""
        # This would be an actual HTTP test against the deployed endpoint
        # For now, we'll test the data flow
        
        submission = FormSubmission(**complete_submission)
        
        # Validate submission
        assert submission.form_type == "demo_request"
        assert submission.email == "<EMAIL>"
        assert submission.metadata["test"] is True
        
        # This represents what the webhook would do
        assert isinstance(submission, FormSubmission)
    
    async def test_end_to_end_processing(self, complete_submission):
        """Test end-to-end lead processing workflow."""
        # Mock all external dependencies
        with patch('src.webhooks.form_handler.get_redis_checkpointer') as mock_redis, \
             patch('src.webhooks.form_handler.get_crm_client') as mock_crm, \
             patch('src.services.lead_enrichment_service.LeadEnrichmentService') as mock_enrichment:
            
            # Setup mocks
            mock_redis.return_value = Mock()
            mock_crm.return_value = Mock()
            mock_enrichment.return_value = Mock()
            
            # Create submission
            submission = FormSubmission(**complete_submission)
            
            # This would test the complete workflow
            assert submission.form_type == "demo_request"
            assert submission.email == "<EMAIL>"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])

[tool:pytest]
# Pytest configuration for TKC_v5 Executive Agent

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 6.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --asyncio-mode=auto
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    external: Tests that require external services
    gmail: Tests that require Gmail API
    calendar: Tests that require Calendar API
    crm: Tests that require CRM integration
    redis: Tests that require Redis
    pinecone: Tests that require Pinecone
    firestore: Tests that require Firestore
    monitoring: Tests that require monitoring services
    
# Test timeout
timeout = 300

# Asyncio configuration
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:google.*
    ignore::UserWarning:urllib3.*

# Environment variables for testing
env =
    ENVIRONMENT = test
    GOOGLE_CLOUD_PROJECT = test-project
    LOG_LEVEL = DEBUG

# Core FastAPI and web framework
fastapi>=0.110.0
uvicorn[standard]>=0.30.0
pydantic>=2.8.0
requests>=2.32.0

# Core LangChain dependencies (essential for agent)
langchain-core>=0.3.0
langchain>=0.3.0
langchain-google-vertexai>=2.0.0
langgraph>=0.2.0
langgraph-checkpoint-redis>=0.0.8

# Basic Google Cloud
google-cloud-secret-manager>=2.20.0
google-auth>=2.30.0
google-api-python-client>=2.140.0
google-cloud-firestore>=2.16.0

# Google Cloud Monitoring and Logging (for monitoring_service.py)
google-cloud-monitoring>=2.15.0
google-cloud-logging>=3.8.0
google-cloud-error-reporting>=1.9.0
google-cloud-trace>=1.13.0
structlog>=23.1.0

# Data persistence and caching
redis>=5.0.0

# Vector database
pinecone>=5.0.0
sentence-transformers>=3.0.0

# Data science and ML dependencies
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0

# Basic utilities
python-dotenv>=1.0.0
